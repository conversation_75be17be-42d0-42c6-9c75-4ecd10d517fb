# DD-Platform-Service 项目分析文档 (v2 版)

## 📋 项目概述

DD-Platform-Service 是一个基于 NestJS 的现代化企业风险评估服务平台，采用严格的七层分层架构设计。该平台专注于为企业提供全方位的风险评估、尽职调查、监控预警和 AI 智能分析服务，是一个功能完备的企业级 SaaS 平台。

### 🎯 核心业务价值

- **企业风险评估**: 基于多维度数据的智能风险评分和分析
- **尽职调查服务**: 全面的企业背景调查和资质核验
- **实时监控预警**: 持续监控企业风险变化，及时预警
- **AI 智能分析**: 基于机器学习的智能分析和决策支持
- **批量处理能力**: 支持大规模企业数据的批量处理
- **多租户架构**: 支持多组织、多产品的 SaaS 化服务

### 📊 项目规模

- **代码规模**: 953+ TypeScript 文件，约 24 万行代码
- **模块数量**: 8 个主要业务域，50+ 子模块
- **数据库表**: 80+ 核心业务实体
- **API 接口**: 200+ RESTful API 端点
- **测试覆盖**: Jest 单元测试和集成测试

---

## 🏗️ 技术架构

### 核心技术栈

| 技术分类       | 技术选型          | 版本  | 用途说明                              |
| -------------- | ----------------- | ----- | ------------------------------------- |
| **后端框架**   | NestJS            | 9.x   | 基于 TypeScript 的 Node.js 企业级框架 |
| **编程语言**   | TypeScript        | 4.3+  | 强类型 JavaScript 超集                |
| **数据库**     | MySQL             | 8.0+  | 主要关系型数据库                      |
| **文档数据库** | MongoDB           | 3.6+  | 非结构化数据存储                      |
| **搜索引擎**   | Elasticsearch     | 7.x   | 全文搜索和数据分析                    |
| **缓存系统**   | Redis             | 5.x+  | 内存数据库和缓存                      |
| **消息队列**   | Kafka/Pulsar      | -     | 异步消息处理和事件驱动                |
| **ORM 框架**   | TypeORM           | 0.2.x | 对象关系映射                          |
| **API 文档**   | Swagger/OpenAPI   | 3.0   | 自动化 API 文档生成                   |
| **测试框架**   | Jest              | 29.x  | 单元测试和集成测试                    |
| **监控系统**   | Sentry/NewRelic   | -     | 应用性能监控和错误追踪                |
| **容器化**     | Docker/Kubernetes | -     | 应用容器化和编排                      |

### 架构设计原则

1. **分层架构**: 严格的六层分层设计，确保职责分离
2. **领域驱动**: 按业务域组织代码，体现业务专家语言
3. **依赖倒置**: 单向依赖关系，高层不依赖低层实现
4. **微服务友好**: 模块化设计支持未来微服务拆分
5. **事件驱动**: 基于消息队列的异步处理架构
6. **配置驱动**: 外部化配置，支持多环境部署

---

## 📁 六层分层架构

### 架构层级图

```
┌─────────────────────────────────────────────────────────────┐
│                    app (应用层)                              │
│                 应用启动和模块组装                            │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                 modules (业务功能层)                         │
│              10个业务域 × 50+子模块                          │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                 domain (领域概念层)                          │
│              业务实体和领域模型定义                           │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                commons (公共基础层)                          │
│               纯工具函数和基础类型                            │
└─────────────────────────────────────────────────────────────┘

        横切关注点层级 (可被主要层级依赖)
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  core (框架层)   │  │ client (SDK层)  │  │testing (测试层) │
│   框架基础设施   │  │   客户端SDK     │  │   测试工具      │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

### 各层职责详解

#### 1. Commons Layer (公共基础层)

- **职责**: 提供纯工具函数和基础类型定义
- **特点**: 无业务含义，无副作用，可被任何层使用
- **主要内容**:
  - 通用工具函数 (日期、字符串、数组处理)
  - 系统级常量定义
  - 基础类型和接口
  - 通用异常类
  - 分布式锁实现

#### 2. Domain Layer (领域概念层)

- **职责**: 定义核心业务概念和领域模型
- **特点**: 保持业务逻辑纯净性，不依赖技术实现
- **主要内容**:
  - 80+ 核心业务实体 (DiligenceHistoryEntity, RiskModelEntity 等)
  - 业务枚举值和常量
  - 值对象定义
  - 领域模型和业务规则
  - 业务相关工具函数

#### 3. Modules Layer (业务功能层)

- **职责**: 实现具体业务功能和用例编排，包含技术实现和外部系统集成
- **特点**: 按业务域组织，体现领域驱动设计，集成外部系统
- **10 个主要业务域**:
  1. **risk-assessment** - 风险评估域 (尽职调查、风险模型、监控预警)
  2. **data-processing** - 数据处理域 (外部 API 集成、数据源管理、数据清洗)
  3. **batch-processing** - 批处理域 (批量任务管理)
  4. **ai-intelligence** - AI 智能分析域 (AI 分析器、QA 评估)
  5. **communication** - 通信域 (消息推送、WebSocket)
  6. **company-management** - 企业管理域 (企业信息管理)
  7. **user-management** - 用户管理域 (用户账户、权限管理)
  8. **system** - 系统管理域 (定时任务、配置管理)
  9. **verification** - 验证服务域 (数据验证、合规检查)
  10. **internal** - 内部服务域 (内部 API、系统监控)

#### 4. App Layer (应用层)

- **职责**: 应用启动、模块组装和健康检查
- **特点**: 应用入口点和顶层配置
- **主要功能**:
  - 应用模块组装 (AppModule)
  - 健康检查和监控
  - 全局配置管理
  - 优雅关闭处理

#### 5. Core Layer (框架核心层) - 横切关注点

- **职责**: 提供 NestJS 框架级别的基础设施
- **特点**: 框架相关的全局配置、守卫、拦截器
- **主要功能**:
  - 配置管理 (ConfigService)
  - 数据库配置
  - 全局守卫和过滤器
  - 全局拦截器
  - 自定义装饰器
  - 搜索服务封装

#### 6. Client Layer (客户端 SDK 层) - 横切关注点

- **职责**: 提供客户端 SDK 和 API 调用封装
- **特点**: 面向外部使用者，独立发布
- **主要功能**:
  - SDK 核心封装
  - 各业务 API 客户端
  - 客户端类型定义
  - 使用文档和示例

### 依赖关系

**主要业务层级依赖链**:

```
app (应用层)
    ↓
modules (业务功能层) - 包含技术实现和外部系统集成
    ↓
domain (领域概念层)
    ↓
commons (公共基础层)
```

**横切关注点依赖**:

```
core (框架核心层) ← 可被 app/modules 层依赖
client (客户端SDK层) ← 独立存在，通过HTTP调用业务层
testing (测试层) ← 仅测试环境使用
```

---

## 🎯 核心业务模块分析

### 1. 风险评估业务域 (Risk Assessment)

#### 1.1 尽职调查模块 (Diligence)

**核心功能**:

- 企业风险评估请求处理和调度
- 多维度风险数据采集和分析
- 企业风险快照生成和管理
- PDF 报告生成和导出

**关键服务**:

- `EvaluationService`: 风险评估核心逻辑
- `RiskScoreService`: 风险评分计算
- `DiligenceSnapshotService`: 快照生成和管理
- `DiligenceHistoryService`: 历史记录管理

**技术特点**:

- 支持单企业和批量尽调
- 异步快照生成机制
- 维度级别的详细快照
- 完整的评估历史追溯

#### 1.2 风险模型模块 (Risk Model)

**核心功能**:

- 风险评分卡配置和管理
- 风险指标体系定义
- 模型版本管理和发布
- 专业化模型定制 (ICBC、BOC 等)

**关键服务**:

- `RiskModelService`: 模型 CRUD 和业务逻辑
- `RiskModelInitService`: 标准模型初始化
- 专业化初始化服务

**技术特点**:

- 支持多租户模型隔离
- 灵活的指标配置体系
- 模型版本控制
- 权限范围检查

#### 1.3 监控预警模块 (Monitor)

**核心功能**:

- 企业风险持续监控 (每日 6:00 定时任务)
- 监控组和企业管理
- 动态风险预警生成
- 监控基线管理 (每 2 小时执行，7:30-23:30)
- 企业关联方日志记录 (每日 7:00)

**关键服务**:

- `MonitorJobService`: 定时监控任务和数据清理
- `MonitorBaselineJobService`: 基线任务服务
- `MonitorCompanyService`: 监控企业管理
- `MonitorDynamicService`: 动态预警生成

**技术特点**:

- 定时任务驱动的监控机制 (Cron 调度)
- 基于基线的变化检测
- 智能动态生成策略
- 批量消息处理优化
- 自动数据清理 (每周六执行)

### 2. 数据处理业务域 (Data Processing)

#### 2.1 数据源管理 (Data Source)

**核心功能**:

- 多源数据整合 (企业库、征信、司法、违法违规等)
- 统一访问接口提供
- 数据源适配和格式转换
- 缓存优化和性能提升

**关键组件**:

- `CompanyApiSource`: 企业基础信息 API
- `CreditApiSource`: 征信数据源
- `JudgementSource`: 司法数据源
- `ViolationSource`: 违法违规数据源
- `RelatedCompanySource`: 关联方数据源

#### 2.2 数据清洗器 (Data Cleaner)

**核心功能**:

- 尽调数据清理 (批次数据、快照数据)
- 定期清理任务自动执行
- 智能清理策略 (基于数据引用关系)
- 清理监控报告生成

### 3. 批处理业务域 (Batch Processing)

**核心功能**:

- 批量尽调任务管理
- 批次状态跟踪和监控
- 批量企业风险评估
- 批次结果统计和报告

**关键实体**:

- `BatchEntity`: 批次任务管理
- `BatchJobEntity`: 批次作业详情
- `BatchResultEntity`: 批次执行结果
- `BatchDiligenceEntity`: 批次尽调关联

### 4. AI 智能分析业务域 (AI Intelligence)

#### 4.1 AI 分析器 (AI Analyzer)

**核心功能**:

- AI 分析报告生成
- 智能问答系统
- 企业风险智能评估
- 自然语言处理

**技术特点**:

- 支持 DeepSeek-R1、DeepSeek-V3 等多种模型
- 流式响应实时输出
- 基于 Redis 的使用限制 (测试环境 100 次/日)
- 令牌加密存储
- 多轮对话上下文管理

#### 4.2 QA 评估 (QA Module)

**核心功能**:

- 风险模型验证和评估
- 模型泛化能力测试
- 专家标注数据管理
- 模型性能监控

### 5. 通信业务域 (Communication)

#### 5.1 消息推送 (Push)

**核心功能**:

- 多渠道消息推送 (邮件、短信、WebSocket)
- 推送规则配置和管理
- 消息模板管理
- 推送状态跟踪

#### 5.2 WebSocket 通信 (WebSocket)

**核心功能**:

- 实时消息推送
- 用户在线状态管理
- 实时通知和预警
- 双向通信支持

### 6. 企业管理业务域 (Company Management)

**核心功能**:

- 企业基础信息 CRUD 管理
- 企业信息搜索和查询
- 企业资质认证和验证
- 企业关联关系管理

**技术特点**:

- 多维度企业搜索
- 智能搜索建议
- 工商信息验证
- 数据标准化处理

### 7. 用户管理业务域 (User Management)

**核心功能**:

- 用户账户管理
- 权限控制和角色管理
- 用户认证和授权
- 用户行为审计

### 8. 系统管理业务域 (System)

#### 8.1 定时任务 (Schedule)

**核心功能**:

- 监控动态生成任务 (每日 6:00)
- 基线动态生成任务 (每 2 小时，7:30-23:30)
- 企业关联方日志任务 (每日 7:00)
- 数据清理任务 (每周六)

#### 8.2 其他系统模块

- **Schema**: 数据库模式管理
- **Dimension**: 维度配置管理
- **Group**: 分组管理
- **Metric**: 指标管理

### 9. 验证服务域 (Verification)

**核心功能**:

- 企业信息验证
- 数据质量检查
- 合规性验证
- 数据一致性校验

### 10. 内部服务域 (Internal)

**核心功能**:

- 内部 API 服务
- 系统间通信
- 内部工具和实用程序
- 系统监控和健康检查

---

## 🔧 技术架构深度分析

### 数据库设计

#### 主要数据实体

- **DiligenceHistoryEntity**: 尽调历史记录
- **RiskModelEntity**: 风险模型配置
- **MonitorGroupEntity**: 监控组配置
- **MonitorCompanyEntity**: 监控企业关联
- **MonitorMetricsDynamicEntity**: 监控动态记录
- **BatchEntity**: 批量任务管理
- **UserEntity**: 用户信息管理

#### 数据库策略

- **主库**: MySQL 存储核心业务数据
- **文档库**: MongoDB 存储非结构化数据
- **搜索库**: Elasticsearch 存储快照和搜索数据
- **缓存**: Redis 缓存热点数据

### 外部系统集成

#### 数据源集成 (在 data-processing 模块中)

- **企业查询 API**: 企业基础信息获取
- **征信数据源**: 信用评估数据
- **司法数据源**: 法律风险数据
- **违法违规数据**: 合规风险数据
- **关联方数据**: 企业关系图谱

#### 技术服务集成 (在各业务模块中)

- **消息队列**: Kafka/Pulsar 异步处理
- **搜索服务**: Elasticsearch 全文搜索
- **文件存储**: 阿里云 OSS 文件管理
- **邮件服务**: 邮件通知发送
- **短信服务**: 短信通知发送

### 缓存策略

#### 多层缓存架构

1. **应用层缓存**: LRU Cache 内存缓存
2. **分布式缓存**: Redis 集群缓存
3. **声明式缓存**: @type-cacheable 装饰器
4. **智能缓存**: 基于业务规则的缓存失效

#### 缓存实现

```typescript
// 声明式缓存示例
@Cacheable({
  cacheKey: 'risk_model',
  ttlSeconds: 3600,
})
async getRiskModel(modelId: number): Promise<RiskModelEntity> {
  return this.riskModelRepo.findOne(modelId);
}
```

### 消息队列架构

#### 队列类型

- **Kafka**: 高吞吐量事件流处理
- **Pulsar**: 企业级消息队列
- **RabbitMQ**: 可靠消息传递

#### 主要队列

- `continuousDiligenceQueue`: 持续尽调队列
- `snapshotQueue`: 快照生成队列
- `batchDiligenceQueue`: 批量尽调队列
- `messageQueue`: 消息推送队列

### 监控和日志

#### 监控体系

- **应用监控**: Sentry 错误追踪
- **性能监控**: NewRelic APM
- **健康检查**: NestJS Terminus
- **自定义指标**: 业务指标监控

#### 日志策略

- **结构化日志**: JSON 格式日志
- **分级日志**: ERROR/WARN/INFO/DEBUG
- **链路追踪**: 请求链路跟踪
- **日志聚合**: 集中式日志管理

---

## 🚀 性能优化策略

### 1. 数据库优化

- **索引优化**: 基于查询模式的索引设计
- **查询优化**: SQL 查询性能调优
- **连接池**: 数据库连接池管理
- **读写分离**: 主从数据库架构

### 2. 缓存优化

- **缓存预热**: 系统启动时预加载热点数据
- **缓存穿透**: 布隆过滤器防护
- **缓存雪崩**: 随机过期时间
- **缓存更新**: 基于事件的缓存失效

### 3. 异步处理

- **消息队列**: 异步任务处理
- **批量处理**: 批量操作优化
- **并发控制**: 合理的并发策略
- **背压处理**: 流量控制机制

### 4. 代码优化

- **懒加载**: 按需加载机制
- **对象池**: 对象复用策略
- **内存管理**: 内存泄漏防护
- **算法优化**: 核心算法性能调优

---

## 📊 质量保证体系

### 测试策略

- **单元测试**: Jest 单元测试，目标覆盖率 > 80%
- **集成测试**: API 集成测试
- **端到端测试**: 业务流程测试
- **性能测试**: 负载和压力测试

### 代码质量

- **TypeScript**: 强类型约束
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **SonarQube**: 代码质量分析

### 安全保障

- **身份认证**: JWT 令牌机制
- **权限控制**: RBAC 权限模型
- **数据加密**: 敏感数据加密
- **安全审计**: 操作日志记录

---

## 📈 项目价值总结

DD-Platform-Service 是一个技术先进、架构合理的企业级风险评估平台，具有以下核心价值：

### 技术价值

- **现代化架构**: 六层分层架构，支持大规模团队协作
- **高性能设计**: 多层缓存、异步处理、性能优化
- **可扩展性**: 模块化设计，支持水平扩展
- **高可用性**: 容错机制、监控告警、优雅降级

### 业务价值

- **全面风险评估**: 多维度企业风险分析
- **实时监控预警**: 持续风险监控和及时预警
- **智能分析能力**: AI 驱动的智能分析
- **高效批处理**: 支持大规模数据处理

### 商业价值

- **SaaS 化服务**: 多租户架构，支持规模化运营
- **API 开放能力**: 支持第三方集成和生态建设
- **数据资产**: 积累大量企业风险数据
- **技术壁垒**: 形成技术和数据双重壁垒

该项目代表了现代企业级应用开发的最佳实践，为企业数字化转型和风险管理提供了强有力的技术支撑。
