# 快照 ID 生成配置说明

## 📖 背景

在监控模型中，我们需要对两次监控生成的快照进行比对，判断数据是新增、删除还是变更。原有的快照 ID 生成逻辑使用整个数据对象进行 hash 计算，但某些字段（如`QccIndustry`、`updateTime`等）的变化并不代表业务数据的实质性变化，却会导致生成不同的快照 ID，影响比对的准确性。

## 🎯 解决方案

我们引入了**可配置的快照 ID 生成机制**，允许在维度策略配置中指定哪些字段参与快照 ID 的计算，从而实现精确的快照比对。

## 🔧 配置说明

### 配置结构

```typescript
interface SnapshotIdConfigPO {
  mode: 'include' | 'exclude' | 'default'; // 配置模式
  includeFields?: string[]; // 包含的字段列表
  excludeFields?: string[]; // 排除的字段列表
  description?: string; // 配置说明
}
```

### 配置模式说明

1. **`default`模式**：使用原有的默认逻辑，向后兼容
2. **`include`模式**：只有指定的字段参与快照 ID 生成
3. **`exclude`模式**：排除指定的字段，其他字段参与快照 ID 生成

## 📝 使用示例

### 在监控模型中配置

```typescript
// src/modules/risk-assessment/risk-model/init_mode/model.init.monitor.cmb.cx.service.ts
{
  dimStrategyName: '对外投资监控',
  dimKey: DimensionTypeEnums.OutwardInvestment,
  fields: [
    // ... 其他字段配置
  ],
  extendJson: {
    snapshotIdConfig: {
      mode: 'exclude',
      excludeFields: [
        'QccIndustry',      // 行业分类变更不影响投资关系
        'updateTime',       // 更新时间不影响投资实质
        'lastModified',     // 最后修改时间不影响投资实质
        'syncTime',         // 同步时间不影响投资实质
        'dataVersion',      // 数据版本不影响投资实质
        'sourceSystem'      // 来源系统不影响投资实质
      ],
      description: '监控模型快照比对时，排除不影响投资关系判断的变动字段'
    }
  }
}
```

### Include 模式示例

```typescript
{
  extendJson: {
    snapshotIdConfig: {
      mode: 'include',
      includeFields: [
        'companyName',        // 公司名称
        'companyKeyNo',       // 公司编号
        'investmentAmount',   // 投资金额
        'investmentRatio'     // 投资比例
      ],
      description: '只使用核心投资信息生成快照ID'
    }
  }
}
```

## 🔍 工作原理

### 字段过滤流程

```mermaid
graph TD
    A[原始数据对象] --> B{检查配置模式}
    B -->|default| C[使用默认逻辑]
    B -->|include| D[只保留includeFields]
    B -->|exclude| E[排除excludeFields]
    D --> F[生成过滤后对象]
    E --> F
    F --> G[键排序 + JSON序列化]
    G --> H[加入companyId和orgId]
    H --> I[MD5 Hash生成]
    I --> J[快照ID]
    C --> K[原有ID逻辑]
```

### 代码调用示例

```typescript
import { getDimensionIdForSnapshot } from './utils.snapshot';

// 获取维度策略的快照ID配置
const dimensionStrategy = await getDimensionHitStrategy(riskModel, DimensionTypeEnums.OutwardInvestment, strategyId, manager);

const snapshotIdConfig = dimensionStrategy?.getSnapshotIdConfig();

// 生成快照ID
const snapshotId = getDimensionIdForSnapshot(
  DimensionTypeEnums.OutwardInvestment,
  dataItem,
  companyId,
  orgId,
  snapshotIdConfig, // 传入配置
);
```

## ✅ 最佳实践

### 1. 选择合适的模式

- **`exclude`模式**：推荐用于大多数场景，只需要排除不重要的字段
- **`include`模式**：适用于明确知道哪些字段重要的场景
- **`default`模式**：保持向后兼容，不需要特殊处理的场景

### 2. 常见排除字段

```typescript
const commonExcludeFields = [
  'updateTime', // 更新时间
  'lastModified', // 最后修改时间
  'syncTime', // 同步时间
  'dataVersion', // 数据版本
  'sourceSystem', // 来源系统
  'createTime', // 创建时间
  'modifyTime', // 修改时间
  'lastSyncDate', // 最后同步日期
  'dataSource', // 数据来源
  'batchId', // 批次ID
  'processId', // 处理ID
];
```

### 3. 业务特定排除字段

根据不同的业务维度，排除对业务判断无实际影响的字段：

```typescript
// 对外投资维度
const outwardInvestmentExcludes = [
  'QccIndustry', // 行业分类（经常变动）
  'industryCode', // 行业代码
  'statusDesc', // 状态描述
  'remark', // 备注信息
];

// 风险变更维度
const riskChangeExcludes = [
  'changeSource', // 变更来源
  'noticeUrl', // 公告链接
  'publishSource', // 发布来源
];
```

## 🚀 效果与收益

### 1. 解决的问题

- ✅ **准确的快照比对**：排除无关字段变动的干扰
- ✅ **减少误报**：避免将字段更新误判为业务数据变更
- ✅ **提高监控精度**：聚焦真正有意义的业务变化
- ✅ **数据隔离**：通过加入 companyId 和 orgId 确保不同公司/组织的数据生成不同快照 ID

### 2. 性能优化

- 🚀 **减少存储**：避免因无关字段变动产生的重复快照
- 🚀 **提升查询效率**：减少不必要的差异数据
- 🚀 **降低计算开销**：精确匹配减少比对计算量

## 🔧 调试与故障排除

### 查看生成的快照 ID

```typescript
// 开启调试模式
const debugConfig = {
  mode: 'exclude',
  excludeFields: ['QccIndustry', 'updateTime'],
  description: '调试配置',
};

console.log('原始数据:', originalItem);
console.log('过滤后数据:', filterItemForSnapshotId(originalItem, debugConfig));
console.log('生成的快照ID:', generateConfigurableSnapshotId(DimensionTypeEnums.OutwardInvestment, originalItem, companyId, orgId, debugConfig));
```

### 测试配置效果

```typescript
// 测试字段变更对ID生成的影响
const testFieldImpact = (item1, item2, config) => {
  const id1 = generateConfigurableSnapshotId(dimensionKey, item1, companyId, orgId, config);
  const id2 = generateConfigurableSnapshotId(dimensionKey, item2, companyId, orgId, config);

  console.log('ID相同:', id1 === id2);
  console.log('ID1:', id1);
  console.log('ID2:', id2);
};
```

## 📋 注意事项

1. **配置测试**：新增配置后，务必通过测试验证效果
2. **向后兼容**：默认模式保持原有逻辑不变
3. **字段选择**：仔细选择要排除/包含的字段，避免影响业务判断
4. **文档更新**：配置变更时及时更新相关文档

---

**生成时间：2025-01-15 14:30:00**
