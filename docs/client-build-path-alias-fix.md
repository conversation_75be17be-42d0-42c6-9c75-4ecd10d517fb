# 客户端构建路径别名修复方案

生成时间：2025-06-17 09:41:00

## 问题描述

在使用 `yarn build:client` 命令构建客户端包时，TypeScript 编译后的 JavaScript 文件中仍然保留了路径别名（如 `@domain/`、`@commons/` 等），导致其他项目引入该包时出现 "Cannot find module" 错误。

例如：

```javascript
// 编译前的 TypeScript
import { RiskModelTypeEnums } from '@domain/enums/RiskModelTypeEnums';

// 编译后的 JavaScript（问题版本）
const RiskModelTypeEnums_1 = require('@domain/enums/RiskModelTypeEnums');

// 修复后的 JavaScript（正确版本）
const RiskModelTypeEnums_1 = require('../../enums/RiskModelTypeEnums');
```

## 解决方案

### 1. 自定义路径别名修复脚本

创建了 `scripts/fix-client-aliases.js` 脚本，该脚本会：

- 扫描 `dist_client` 目录下的所有 JavaScript 文件
- 将路径别名转换为相对路径
- 根据文件的目录深度计算正确的相对路径

### 2. 构建流程集成

修改了 `package.json` 中的 `build:client` 命令：

```json
{
  "scripts": {
    "build:client": "rimraf dist_client && tsc --build tsconfig.lib.json && node scripts/fix-client-aliases.js && node scripts/test-client-build.js"
  }
}
```

### 3. 自动化测试验证

创建了 `scripts/test-client-build.js` 脚本来验证修复效果：

- 检查关键文件是否还包含路径别名
- 验证路径是否正确转换为相对路径
- 确保构建结果可以被外部项目正常引入

## 技术实现

### 路径别名映射

```javascript
const aliasMap = {
  '@domain/': ' domain/',
  '@commons/': ' commons/',
  '@modules/': ' modules/',
  '@client/': ' client/',
  '@libs/': ' client/model/',
};
```

### 动态路径计算

脚本会根据文件在 `dist_client` 目录中的深度自动计算正确的相对路径：

```javascript
const relativePath = path.relative(distClientDir, filePath);
const depth = relativePath.split(path.sep).length - 1;
const backPath = '../'.repeat(depth);
```

### 正则表达式替换

使用正则表达式精确匹配和替换 require 语句中的路径别名：

```javascript
const regex = new RegExp(`require\\("${alias.replace('/', '\\/')}([^"]+)"\\)`, 'g');
const newContent = content.replace(regex, (match, subpath) => {
  const newPath = backPath + replacement + subpath;
  return `require("${newPath}")`;
});
```

## 使用方法

### 构建客户端包

```bash
yarn build:client
```

该命令会：

1. 清理 `dist_client` 目录
2. 使用 TypeScript 编译器构建
3. 自动修复路径别名
4. 验证修复结果

### 验证构建结果

```bash
node scripts/test-client-build.js
```

## 修复效果

### 修复前

```javascript
const LevelGroupDimensionPO_1 = require('@domain/enums/dimension/LevelGroupDimensionPO');
const CreditRateResult_1 = require('@domain/enums/dimension/CreditRateResult');
const diligence_constants_1 = require('@domain/constants/diligence.constants');
const RiskModelTypeEnums_1 = require('@domain/enums/RiskModelTypeEnums');
```

### 修复后

```javascript
const LevelGroupDimensionPO_1 = require('../../enums/dimension/LevelGroupDimensionPO');
const CreditRateResult_1 = require('../../enums/dimension/CreditRateResult');
const diligence_constants_1 = require('../../constants/diligence.constants');
const RiskModelTypeEnums_1 = require('../../enums/RiskModelTypeEnums');
```

## 注意事项

1. **第三方依赖**：客户端代码中使用了一些服务器端依赖（如 `@kezhaozhao/qcc-logger`），这些依赖可能在客户端环境中不可用。建议在客户端代码中使用浏览器兼容的替代方案。

2. **路径映射配置**：如果修改了 `tsconfig.lib.json` 中的路径别名配置，需要同步更新 `scripts/fix-client-aliases.js` 中的 `aliasMap`。

3. **构建验证**：每次构建后都会自动运行验证脚本，确保路径别名修复成功。

## 相关文件

- `scripts/fix-client-aliases.js` - 路径别名修复脚本
- `scripts/test-client-build.js` - 构建结果验证脚本
- `tsconfig.lib.json` - 客户端 TypeScript 配置
- `package.json` - 构建命令配置

## 总结

通过实现自定义的路径别名修复脚本，成功解决了 TypeScript 编译后路径别名无法被外部项目正确解析的问题。该方案具有以下优势：

- ✅ 自动化处理，无需手动干预
- ✅ 集成到构建流程中
- ✅ 包含自动化验证
- ✅ 支持任意深度的目录结构
- ✅ 可维护性强，易于扩展
