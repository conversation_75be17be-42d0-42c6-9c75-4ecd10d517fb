# [业务域名称] 业务域

> 📋 **模板说明**: 这是业务域 README 文档模板，请根据实际业务域情况修改内容

## 📝 概述

[简要描述该业务域的核心职责和业务价值]

## 🎯 业务职责

- [职责 1]
- [职责 2]
- [职责 3]

## 🏗️ 子模块结构

| 子模块     | 路径      | 核心功能   | 状态      |
| ---------- | --------- | ---------- | --------- |
| [子模块 1] | `[path]/` | [功能描述] | ✅ 已实现 |
| [子模块 2] | `[path]/` | [功能描述] | 🚧 开发中 |
| [子模块 3] | `[path]/` | [功能描述] | 📋 计划中 |

## 📁 目录结构

```
domain-name/
├── README.md                          # 本文档
├── sub-module-1/                      # [子模块1名称]
│   ├── sub-module-1.module.ts         # NestJS模块
│   ├── sub-module-1.controller.ts     # 控制器
│   ├── sub-module-1.service.ts        # 业务服务
│   ├── utils/                         # 业务工具类
│   │   ├── [business-logic].company.utils.ts
│   │   ├── [data-processor].company.utils.ts
│   │   └── [validator].company.utils.ts
│   ├── exceptions/                    # 专用异常
│   │   ├── [entity]-not-found.exception.ts
│   │   └── [entity]-validation.exception.ts
│   └── __tests__/                     # 测试文件
│       ├── [entity].controller.unittest.spec.ts
│       ├── [entity].service.unittest.spec.ts
│       ├── [entity].service.integration.spec.ts
│       ├── utils/
│       │   └── [util].unittest.spec.ts
│       └── [entity].e2e-spec.ts
├── sub-module-2/                      # [子模块2名称]
│   └── ...                           # 同上结构
└── __tests__/                         # 业务域级测试
    ├── [domain].integration.spec.ts
    └── [domain].e2e-spec.ts
```

## 🔗 依赖关系

### 对外依赖

- **@infrastructure**: [具体依赖的基础设施服务]
- **@domain**: [具体依赖的领域实体和服务]
- **@commons**: [具体依赖的公共工具]

### 内部依赖

- **子模块间**: [描述子模块间的依赖关系]

### 对外提供

- **服务接口**: [对其他业务域提供的服务]
- **数据接口**: [对外暴露的数据模型]

## 🛠️ 核心业务工具类

### [子模块 1] 工具类

| 工具类              | 文件路径                                     | 主要功能   |
| ------------------- | -------------------------------------------- | ---------- |
| `[UtilClass1]Utils` | `sub-module-1/utils/[file].company.utils.ts` | [功能描述] |
| `[UtilClass2]Utils` | `sub-module-1/utils/[file].company.utils.ts` | [功能描述] |

#### 使用示例

```typescript
import { [UtilClass]Utils } from './utils/[file].company.utils';

// 示例用法
const result = [UtilClass]Utils.processData(inputData);
```

### [子模块 2] 工具类

[同上格式描述其他子模块的工具类]

## 📊 业务流程

### 主要业务流程

```mermaid
graph TD
    A[开始] --> B[步骤1]
    B --> C[步骤2]
    C --> D[步骤3]
    D --> E[结束]
```

### 流程说明

1. **步骤 1**: [详细说明]
2. **步骤 2**: [详细说明]
3. **步骤 3**: [详细说明]

## 🔌 API 接口

### [子模块 1] 接口

| 方法     | 路径              | 描述       | 状态 |
| -------- | ----------------- | ---------- | ---- |
| `GET`    | `/api/[path]`     | [功能描述] | ✅   |
| `POST`   | `/api/[path]`     | [功能描述] | ✅   |
| `PUT`    | `/api/[path]/:id` | [功能描述] | 🚧   |
| `DELETE` | `/api/[path]/:id` | [功能描述] | 📋   |

#### 接口示例

```typescript
// GET /api/[path]
interface [Entity]QueryDto {
  page?: number;
  limit?: number;
  filter?: string;
}

interface [Entity]ResponseDto {
  id: string;
  name: string;
  // ... 其他字段
}
```

## 🧪 测试策略

### 测试覆盖范围

- **单元测试**: 覆盖所有业务工具类和服务方法
- **集成测试**: 覆盖子模块间的业务流程
- **端到端测试**: 覆盖完整的用户场景

### 测试数据准备

```typescript
// 测试用户生成示例
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

const [testOrgId, testUserId] = generateUniqueTestIds('[domain-name].unittest.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
```

### 关键测试场景

1. **正常业务流程**

   - [场景描述]
   - 测试文件: `__tests__/[scenario].integration.spec.ts`

2. **异常处理**

   - [异常场景描述]
   - 测试文件: `__tests__/[error-scenario].unittest.spec.ts`

3. **边界条件**
   - [边界条件描述]
   - 测试文件: `__tests__/[boundary].unittest.spec.ts`

## 📈 性能考虑

### 性能关键点

- [性能关键点 1]: [优化方案]
- [性能关键点 2]: [优化方案]

### 监控指标

- [指标 1]: [监控方法]
- [指标 2]: [监控方法]

## 🔧 配置管理

### 环境配置

```typescript
// 配置示例
interface [Domain]Config {
  [setting1]: string;
  [setting2]: number;
  [setting3]: boolean;
}
```

### 配置文件

- 开发环境: `config/development.yaml`
- 测试环境: `config/test.yaml`
- 生产环境: `config/production.yaml`

## 🚀 部署说明

### 部署依赖

- [依赖 1]: [版本要求]
- [依赖 2]: [版本要求]

### 部署检查

```bash
# 健康检查
curl http://localhost:3000/api/[domain]/health

# 功能测试
npm run test:[domain]
```

## 📋 开发指南

### 新增子模块

1. 按照标准结构创建目录
2. 实现核心业务逻辑
3. 编写业务工具类
4. 完善测试覆盖
5. 更新本文档

### 新增业务工具类

1. 确定工具类归属的子模块
2. 按照命名规范创建文件: `[function-name].company.utils.ts`
3. 实现纯函数业务逻辑
4. 编写单元测试
5. 更新工具类列表

### 代码规范

- 遵循项目整体的 TypeScript 规范
- 业务工具类保持无状态和纯函数特性
- 错误处理使用专用异常类
- API 接口使用 DTO 进行数据验证

## 🔄 版本历史

| 版本   | 日期       | 变更内容   | 作者   |
| ------ | ---------- | ---------- | ------ |
| v1.0.0 | YYYY-MM-DD | 初始版本   | [作者] |
| v1.1.0 | YYYY-MM-DD | [变更说明] | [作者] |

## 🤝 贡献指南

### 开发流程

1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request
5. 代码评审
6. 合并主分支

### 问题反馈

如遇到问题，请在 [Issue Tracker] 中创建问题，包含：

- 问题描述
- 复现步骤
- 期望结果
- 实际结果
- 环境信息

---

📚 **相关文档**：

- [业务功能层总览](../README.md)
- [架构设计文档](../../docs/architecture/)
- [API 文档](../../docs/api/)
- [测试指南](../../docs/testing/)
