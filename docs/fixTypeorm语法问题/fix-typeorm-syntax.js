#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取所有TypeORM相关的错误文件
function getErrorFiles() {
  try {
    execSync('yarn tsc --noEmit', { stdio: 'pipe' });
    return [];
  } catch (error) {
    const output = error.stdout.toString();
    const lines = output.split('\n');
    const files = new Set();

    for (const line of lines) {
      if (
        line.includes("does not exist in type 'Find") ||
        line.includes("Type 'number' has no properties in common with type 'FindOneOptions") ||
        line.includes('Expected 2 arguments, but got 3')
      ) {
        const match = line.match(/^([^:(]+):/);
        if (match) {
          files.add(match[1]);
        }
      }
    }

    return Array.from(files);
  }
}

// 修复单个文件
function fixFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  const originalContent = content;

  // 修复模式1: findOne(Entity, {field: value}) -> findOne(Entity, {where: {field: value}})
  content = content.replace(/(\w+\.findOne\([^,]+,\s*)({[^{}]*})(\s*[,);])/g, (match, prefix, obj, suffix) => {
    // 跳过已经有where的情况
    if (obj.includes('where:') || obj.includes('relations:') || obj.includes('cache:') || obj.includes('select:')) {
      return match;
    }
    return `${prefix}{ where: ${obj} }${suffix}`;
  });

  // 修复模式2: find(Entity, {field: value}) -> find(Entity, {where: {field: value}})
  content = content.replace(/(\w+\.find\([^,]+,\s*)({[^{}]*})(\s*[,);])/g, (match, prefix, obj, suffix) => {
    // 跳过已经有where的情况
    if (obj.includes('where:') || obj.includes('relations:') || obj.includes('cache:') || obj.includes('select:')) {
      return match;
    }
    return `${prefix}{ where: ${obj} }${suffix}`;
  });

  // 修复模式3: count({field: value}) -> count({where: {field: value}})
  content = content.replace(/(\w+\.count\(\s*)({[^{}]*})(\s*\))/g, (match, prefix, obj, suffix) => {
    if (obj.includes('where:')) {
      return match;
    }
    return `${prefix}{ where: ${obj} }${suffix}`;
  });

  // 修复模式4: findOne(Entity, id) -> findOne(Entity, {where: {id: id}})
  content = content.replace(/(\w+\.findOne\([^,]+,\s*)(\w+\.\w+)(\s*[,);])/g, '$1{ where: { id: $2 } }$3');

  // 修复模式5: findOne(Entity, id, {options}) -> findOne(Entity, {where: {id: id}, ...options})
  content = content.replace(/(\w+\.findOne\([^,]+,\s*)(\w+\.\w+),\s*({[^{}]*})/g, (match, prefix, id, options) => {
    const optionsContent = options.slice(1, -1).trim();
    if (optionsContent) {
      return `${prefix}{ where: { id: ${id} }, ${optionsContent} }`;
    } else {
      return `${prefix}{ where: { id: ${id} } }`;
    }
  });

  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed: ${filePath}`);
    modified = true;
  }

  return modified;
}

// 主函数
function main() {
  console.log('Getting TypeORM error files...');
  const errorFiles = getErrorFiles();

  if (errorFiles.length === 0) {
    console.log('No TypeORM errors found!');
    return;
  }

  console.log(`Found ${errorFiles.length} files with TypeORM errors`);

  let totalFixed = 0;
  for (const filePath of errorFiles) {
    console.log(`\nFixing ${filePath}...`);
    if (fixFile(filePath)) {
      totalFixed++;
    }
  }

  console.log(`\nTypeORM syntax fix completed! Fixed ${totalFixed} files.`);
}

main();
