#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取所有TypeORM相关的错误
function getTypeOrmErrors() {
  try {
    execSync('yarn tsc --noEmit', { stdio: 'pipe' });
    return [];
  } catch (error) {
    const output = error.stdout.toString();
    const lines = output.split('\n');
    const errors = [];
    
    for (const line of lines) {
      if (line.includes('does not exist in type \'Find') && 
          (line.includes('Options<') || line.includes('ManyOptions<'))) {
        const match = line.match(/^([^:]+):(\d+):(\d+)/);
        if (match) {
          const [, filePath, lineNum, colNum] = match;
          errors.push({
            file: filePath,
            line: parseInt(lineNum),
            column: parseInt(colNum),
            content: line
          });
        }
      }
    }
    
    return errors;
  }
}

// 修复单个文件中的错误
function fixFileErrors(filePath, errors) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  let modified = false;

  // 按行号倒序排序，这样修改时不会影响后面的行号
  const fileErrors = errors.filter(e => e.file === filePath).sort((a, b) => b.line - a.line);

  for (const error of fileErrors) {
    const lineIndex = error.line - 1;
    if (lineIndex >= 0 && lineIndex < lines.length) {
      const line = lines[lineIndex];
      
      // 匹配 findOne, find, count 等方法调用
      const patterns = [
        // findOne(Entity, {field: value}) -> findOne(Entity, {where: {field: value}})
        {
          regex: /(\w+\.findOne\([^,]+,\s*)({[^{}]*})(\s*[,)])/,
          replacement: (match, prefix, obj, suffix) => {
            if (obj.includes('where:') || obj.includes('relations:') || obj.includes('cache:')) {
              return match; // 已经有复杂结构，跳过
            }
            return `${prefix}{ where: ${obj} }${suffix}`;
          }
        },
        // find(Entity, {field: value}) -> find(Entity, {where: {field: value}})
        {
          regex: /(\w+\.find\([^,]+,\s*)({[^{}]*})(\s*[,)])/,
          replacement: (match, prefix, obj, suffix) => {
            if (obj.includes('where:') || obj.includes('relations:') || obj.includes('cache:')) {
              return match; // 已经有复杂结构，跳过
            }
            return `${prefix}{ where: ${obj} }${suffix}`;
          }
        },
        // count({field: value}) -> count({where: {field: value}})
        {
          regex: /(\w+\.count\(\s*)({[^{}]*})(\s*\))/,
          replacement: (match, prefix, obj, suffix) => {
            if (obj.includes('where:')) {
              return match; // 已经有where，跳过
            }
            return `${prefix}{ where: ${obj} }${suffix}`;
          }
        },
        // findOne(Entity, id) -> findOne(Entity, {where: {id: id}})
        {
          regex: /(\w+\.findOne\([^,]+,\s*)(\w+\.\w+)(\s*[,)])/,
          replacement: '$1{ where: { id: $2 } }$3'
        }
      ];

      let newLine = line;
      for (const pattern of patterns) {
        if (typeof pattern.replacement === 'function') {
          newLine = newLine.replace(pattern.regex, pattern.replacement);
        } else {
          newLine = newLine.replace(pattern.regex, pattern.replacement);
        }
      }

      if (newLine !== line) {
        lines[lineIndex] = newLine;
        modified = true;
        console.log(`Fixed line ${error.line} in ${filePath}`);
      }
    }
  }

  if (modified) {
    fs.writeFileSync(filePath, lines.join('\n'), 'utf8');
    console.log(`Updated: ${filePath}`);
  }
}

// 主函数
function main() {
  console.log('Getting TypeORM errors...');
  const errors = getTypeOrmErrors();
  
  if (errors.length === 0) {
    console.log('No TypeORM errors found!');
    return;
  }

  console.log(`Found ${errors.length} TypeORM errors`);
  
  // 按文件分组
  const fileGroups = {};
  for (const error of errors) {
    if (!fileGroups[error.file]) {
      fileGroups[error.file] = [];
    }
    fileGroups[error.file].push(error);
  }

  // 修复每个文件
  for (const filePath of Object.keys(fileGroups)) {
    console.log(`\nFixing ${filePath}...`);
    fixFileErrors(filePath, fileGroups[filePath]);
  }

  console.log('\nTypeORM syntax fix completed!');
  
  // 再次检查错误
  console.log('\nChecking remaining errors...');
  const remainingErrors = getTypeOrmErrors();
  console.log(`Remaining TypeORM errors: ${remainingErrors.length}`);
}

main();
