#!/bin/bash

# 修复多行的TypeORM语法错误

echo "开始修复多行TypeORM语法错误..."

# 使用perl来处理多行模式
find src -name "*.spec.ts" -type f | while read file; do
    echo "处理文件: $file"
    
    # 修复多行的findOne调用
    perl -i -pe '
        BEGIN { undef $/; }
        s/\.findOne\(([^,]+),\s*\{\s*([^}]+)\s*\}\)/\.findOne($1, { where: { $2 } })/gs;
        s/\.find\(([^,]+),\s*\{\s*([^}]+)\s*\}\)/\.find($1, { where: { $2 } })/gs;
        s/\.count\(\s*\{\s*([^}]+)\s*\}\)/\.count({ where: { $1 } })/gs;
        s/where:\s*\{\s*where:\s*\{/where: {/gs;
    ' "$file"
done

echo "多行TypeORM语法修复完成！"

# 检查修复结果
echo "检查修复结果..."
yarn tsc --noEmit 2>&1 | grep -E "(does not exist in type|Type.*has no properties|Expected 2 arguments)" | wc -l
