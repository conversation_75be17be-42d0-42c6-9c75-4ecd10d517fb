#!/bin/bash

# 批量修复TypeORM语法错误

echo "开始修复TypeORM语法错误..."

# 获取所有需要修复的.spec.ts文件
files=$(find src -name "*.spec.ts" -type f)

for file in $files; do
    echo "修复文件: $file"
    
    # 修复 find 方法调用
    sed -i '' 's/\.find(\([^,]*\), { \([^}]*\) })/\.find(\1, { where: { \2 } })/g' "$file"
    
    # 修复 findOne 方法调用 (简单对象)
    sed -i '' 's/\.findOne(\([^,]*\), { \([^}]*\) })/\.findOne(\1, { where: { \2 } })/g' "$file"
    
    # 修复 count 方法调用
    sed -i '' 's/\.count({ \([^}]*\) })/\.count({ where: { \1 } })/g' "$file"
    
    # 修复 findOne(Entity, id) 格式
    sed -i '' 's/\.findOne(\([^,]*\), \([a-zA-Z][a-zA-Z0-9_]*\.[a-zA-Z][a-zA-Z0-9_]*\))/\.findOne(\1, { where: { id: \2 } })/g' "$file"
    
    # 修复已经有where但格式不对的情况
    sed -i '' 's/where: { where: {/where: {/g' "$file"
done

# 修复特殊的三参数findOne调用
echo "修复特殊的三参数findOne调用..."

# 查找并修复 findOne(Entity, id, {options}) 格式
find src -name "*.spec.ts" -type f -exec grep -l "findOne.*,.*,.*{" {} \; | while read file; do
    echo "修复三参数findOne: $file"
    # 这个需要更复杂的处理，暂时跳过，手动处理
done

echo "TypeORM语法修复完成！"

# 检查修复结果
echo "检查修复结果..."
yarn tsc --noEmit 2>&1 | grep -E "(does not exist in type|Type.*has no properties|Expected 2 arguments)" | wc -l
