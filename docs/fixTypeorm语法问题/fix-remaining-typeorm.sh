#!/bin/bash

# 修复 TypeORM 语法错误的脚本

# 修复 findOne 调用
find src -name "*.spec.ts" -type f -exec sed -i '' 's/\.findOne(\([^,]*\), {\([^}]*\)}/\.findOne(\1, { where: {\2} }/g' {} \;

# 修复 find 调用
find src -name "*.spec.ts" -type f -exec sed -i '' 's/\.find(\([^,]*\), {\([^}]*\)}/\.find(\1, { where: {\2} }/g' {} \;

# 修复 count 调用
find src -name "*.spec.ts" -type f -exec sed -i '' 's/\.count({\([^}]*\)}/\.count({ where: {\1} }/g' {} \;

# 修复已经有 where 的情况（避免重复）
find src -name "*.spec.ts" -type f -exec sed -i '' 's/where: { where: {/where: {/g' {} \;

echo "TypeORM syntax fix completed!"
