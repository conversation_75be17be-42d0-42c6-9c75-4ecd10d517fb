import { DDPlatformClientOptions, KzzHttpException } from './model';
import { InternalServerErrorException } from '@nestjs/common';
import { Logger } from 'log4js';
import { v4 as uuidv4 } from 'uuid';
import { Request } from 'express';
// import { KzzHttpException } from '@commons/exceptions/KzzHttpException';
import axios, { AxiosInstance } from 'axios';
// import { API_BASE } from '@commons/constants/common';
import { JwtService } from '@nestjs/jwt';
import { API_BASE } from '../commons/constants/common';

export class BaseClient {
  readonly clientOptions: DDPlatformClientOptions;
  protected readonly httpService: AxiosInstance;
  protected serverURL: string;
  protected jwtService: JwtService;
  protected jwtSecret: string;

  constructor(options: DDPlatformClientOptions) {
    this.clientOptions = options;
    this.serverURL = `${this.clientOptions.server}${API_BASE}`;
    this.jwtService = new JwtService();
    this.jwtSecret = options.jwtSecret;
    let { axiosConfig } = options;
    if (axiosConfig) {
      axiosConfig.timeout = axiosConfig.timeout || 10000;
    } else {
      axiosConfig = {
        timeout: 10000,
      };
    }
    this.httpService = axios.create(axiosConfig);
  }

  protected getHeaders(req: Request) {
    const requestId = req?.headers['x-kzz-request-id']?.toString();
    const aliyunRequestId = req?.headers['x-request-id'];
    const headers = Object.assign({}, this.clientOptions.axiosConfig?.headers, {
      'x-kzz-request-id': requestId || uuidv4(),
      'x-kzz-request-from': this.clientOptions.requestFrom,
    });
    if (aliyunRequestId) {
      Object.assign(headers, {
        'x-request-id': aliyunRequestId,
      });
    }
    return headers;
  }

  protected handleError(err, logger: Logger) {
    logger.error(err);
    // 检测是否是 KzzExceptionResponse 格式的错误,一般内部服务正常请求抛出错误的话都会抛出这个格式
    // new HttpException(err.message, err.status);

    // if (axios.isAxiosError(err)) {
    //   if (err.response) {
    //     // 服务器有正常响应
    //     const t = err.response.data as KzzExceptionResponse;
    //     if (t.error && t.code && t.statusCode) {
    //       // 说明是 内部服务抛出的错误
    //       throw new KzzHttpException({
    //         ...t,
    //       });
    //     } else {
    //       throw new KzzHttpException(Object.assign({ ...t }, { message: err.response.data || err.message }));
    //     }
    //   }
    //   //服务器未正常响应， 比如 超时
    //   throw new KzzHttpException({
    //     error: err.message,
    //     statusCode: err.response?.status || 500,
    //     path: err.config.url.replace(this.serverURL, ''),
    //     method: err.config.method,
    //     code: err.response?.status || 500,
    //     timestamp: new Date().toISOString(),
    //   });
    //
    //
    // }
    const httpException = KzzHttpException.getError(err);
    if (httpException) {
      throw httpException;
    }
    throw new InternalServerErrorException(err.message);
  }
}
