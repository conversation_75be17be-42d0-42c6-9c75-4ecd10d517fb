# DD 平台 SDK 核心模块 (DD Platform SDK Core)

## 📝 概述

DD 平台 SDK 核心模块提供统一的客户端接口，方便第三方系统集成和使用 DD 平台的各种服务。

## 🎯 职责

- 提供简洁易用的 SDK 接口
- 封装 HTTP 请求和响应处理
- 统一错误处理和重试机制
- 支持多种认证方式
- 提供 TypeScript 类型支持

## 📁 预期文件结构

```
sdk/
├── README.md                    # 本说明文件
├── dd-client.ts                 # SDK主客户端类
├── auth.manager.ts              # 认证管理器
├── http.client.ts               # HTTP客户端封装
├── config.manager.ts            # 配置管理器
├── error.handler.ts             # 错误处理器
├── retry.manager.ts             # 重试管理器
└── __tests__/                   # 测试文件
    ├── dd-client.spec.ts
    └── auth.manager.spec.ts
```

## 💡 使用示例

### 基础用法

```typescript
import { DDClient } from '@dd-platform/sdk';

const client = new DDClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.dd-platform.com',
  timeout: 30000,
});

// 查询企业信息
const company = await client.company.getByCode('91110000000000000X');

// 创建尽调项目
const diligence = await client.diligence.create({
  companyId: company.id,
  type: 'BASIC',
  description: '基础尽调项目',
});

// 获取风险评估结果
const riskResult = await client.riskModel.calculate({
  companyId: company.id,
  modelId: 'default-model',
});
```

### 高级配置

```typescript
const client = new DDClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.dd-platform.com',
  timeout: 30000,
  retryConfig: {
    maxRetries: 3,
    retryDelay: 1000,
    retryCondition: (error) => error.status >= 500,
  },
  interceptors: {
    request: (config) => {
      // 添加自定义请求头
      config.headers['X-Custom-Header'] = 'value';
      return config;
    },
    response: (response) => {
      // 处理响应数据
      return response;
    },
  },
});
```

## 🔧 SDK 特性

- **类型安全**: 完整的 TypeScript 类型定义
- **自动重试**: 智能的重试机制和错误处理
- **请求缓存**: 可配置的响应缓存策略
- **认证管理**: 支持 API Key、JWT 等认证方式
- **日志记录**: 详细的请求和响应日志
- **错误处理**: 统一的错误格式和处理

## 📊 API 模块

- **company**: 企业管理相关 API
- **diligence**: 尽职调查相关 API
- **riskModel**: 风险模型相关 API
- **monitor**: 监控管理相关 API
- **user**: 用户管理相关 API

## 🔗 相关模块

- 依赖：@client/api-clients, @client/types
- 集成：HTTP 客户端、认证系统
