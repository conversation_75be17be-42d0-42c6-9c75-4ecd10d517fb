import { BaseClient } from './base.client';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DDPlatformClientOptions, PlatformUser, UserSocketSearchRequest } from './model';
import { Request } from 'express';
import { UserSocketDetailRequest, UserSocketSaveRequest, UserSocketUpdateRequest } from '@domain/model/user';

export class UserSocketClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(UserSocketClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
  }

  // private async setJwt(headers, user: PlatformUser) {
  //   const jwtToken = await this.jwtService.signAsync(user, {
  //     secret: this.jwtSecret,
  //     expiresIn: 600 + 's',
  //   });
  //   headers['authorization'] = 'Bearer ' + jwtToken;
  // }

  public async saveUserSocket(user: PlatformUser, params: UserSocketSaveRequest, req?: Request | any) {
    try {
      // const headers = this.getHeaders(req);
      // await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/userSocket/external/save`,
        data: params,
        // headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getUserSocket(user: PlatformUser, params: UserSocketDetailRequest, req?: Request | any) {
    try {
      // const headers = this.getHeaders(req);
      // await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/userSocket/external/detail`,
        data: params,
        // headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
  public async updateUserSocket(user: PlatformUser, params: UserSocketUpdateRequest, req?: Request | any) {
    try {
      // const headers = this.getHeaders(req);
      // await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/userSocket/external/update`,
        data: params,
        // headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async searchUserSocketUserSocket(user: PlatformUser, params: UserSocketSearchRequest, req?: Request | any) {
    try {
      // const headers = this.getHeaders(req);
      // await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/userSocket/external/search`,
        data: params,
        // headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
