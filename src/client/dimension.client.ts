import { BaseClient } from './base.client';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Request } from 'express';
import { UpdateDimensionHitStrategyRequest, UpdateFieldToHitStrategyRequest } from '@domain/model/dimension';
import { DDPlatformClientOptions } from './model';
import { PlatformUser } from '@domain/model/common';

export class DimensionClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(DimensionClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
  }

  private async setJwt(headers, user: PlatformUser) {
    const jwtToken = await this.jwtService.signAsync(user, {
      secret: this.jwtSecret,
      expiresIn: 600 + 's',
    });
    headers['authorization'] = 'Bearer ' + jwtToken;
  }

  /**
   * 修改模型策略
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async editStrategy(user: PlatformUser, requestBody: UpdateDimensionHitStrategyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/dimension/external/strategy/edit`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 修改模型策略属性
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async editFieldsToStrategy(user: PlatformUser, requestBody: UpdateFieldToHitStrategyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/dimension/external/strategy/fields/edit`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
