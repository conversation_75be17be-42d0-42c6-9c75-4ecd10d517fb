# client api-clients risk-model 接口定义 (Interfaces)

## 📝 概述

此目录包含 client api-clients risk-model 模块的 TypeScript 接口定义，确保类型安全和代码一致性。

## 🎯 职责

- 定义服务接口契约
- 提供类型安全保障
- 统一数据结构规范
- 支持依赖注入和 mock

## 📁 文件类型

- `*.service.interface.ts` - 服务接口定义
- `*.repository.interface.ts` - 仓储接口定义
- `*.config.interface.ts` - 配置接口定义
- `*.response.interface.ts` - 响应接口定义

## 💡 使用示例

```typescript
export interface IUserService {
  findById(id: string): Promise<User>;
  create(userData: CreateUserDto): Promise<User>;
  update(id: string, userData: UpdateUserDto): Promise<User>;
  delete(id: string): Promise<void>;
}
```

## 🔗 相关模块

- 被实现：服务类
- 被使用：依赖注入、测试 mock
