# client api-clients risk-model 数据传输对象 (DTOs)

## 📝 概述

此目录包含 client api-clients risk-model 模块的数据传输对象定义，用于 API 请求/响应和数据验证。

## 🎯 职责

- 定义 API 请求参数结构
- 定义 API 响应数据结构
- 实现数据验证规则
- 提供类型安全保障

## 📁 文件类型

- `create-*.dto.ts` - 创建操作的 DTO
- `update-*.dto.ts` - 更新操作的 DTO
- `query-*.dto.ts` - 查询参数的 DTO
- `response-*.dto.ts` - 响应数据的 DTO

## 💡 使用示例

```typescript
import { CreateUserDto } from './create-user.dto';

@Controller('users')
export class UserController {
  @Post()
  async create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }
}
```

## 🔗 相关模块

- 被使用：控制器和服务
- 集成：class-validator, class-transformer
