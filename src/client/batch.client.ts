import { BaseClient } from './base.client';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import {
  DDPlatformClientOptions,
  DeleteVerificationImportRequest,
  ExecuteVerificationImportRequest,
  PlatformUser,
  SearchVerificationImportRequest,
  SearchVerificationRequest,
  UpateVerificationImportRequest,
  VerificationImportFileRequest,
  ExecuteMonitorImportRequest,
  UpdateMonitorImportRequest,
  DeleteMonitorImportRequest,
  MonitorImportFileRequest,
  SearchMonitorBatchImportRequest,
  SearchMonitorBatchImportResultRequest,
  RetryAllErrorMonitorBatchImportRequest,
  RetryMonitorImportItemRequest,
} from './model';
import { Request } from 'express';
import { SearchBatchRequest } from '@domain/model/batch/request/SearchBatchRequest';

export class BatchClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(BatchClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
  }

  private async setJwt(headers, user: PlatformUser) {
    const jwtToken = await this.jwtService.signAsync(user, {
      secret: this.jwtSecret,
      expiresIn: 600 + 's',
    });
    headers['authorization'] = 'Bearer ' + jwtToken;
  }

  public async getDiligencePdf(user: PlatformUser, diligenceId: number, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/export/external/diligencePdf/${diligenceId}`,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async batchSearch(user: PlatformUser, params: SearchBatchRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/search`,
        data: params,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getBatchEntity(user: PlatformUser, batchId: number, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'GET',
        url: `${this.serverURL}/batch/detail/${batchId}`,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async matchBatchVerificationPersonCompany(user: PlatformUser, body: VerificationImportFileRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/verification/excel`,
        data: body,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async executeBatchVerificationPersonCompany(user: PlatformUser, body: ExecuteVerificationImportRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/verification/excel/execute`,
        data: body,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async searchMatchCompany(user: PlatformUser, body: SearchVerificationImportRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/verification/excel/item/search`,
        data: body,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async upateVerificationImport(user: PlatformUser, body: UpateVerificationImportRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/verification/excel/item/update`,
        data: body,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async deleteVerificationImport(user: PlatformUser, data: DeleteVerificationImportRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/verification/excel/item/delete`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getVerificaitonResultRegularExport(user: PlatformUser, data: SearchVerificationRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/export/external/verification/result/regular`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getVerificaitonResultDeepExport(user: PlatformUser, data: SearchVerificationRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/export/external/verification/result/deep`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getVerificaitonRecordExport(user: PlatformUser, data: SearchVerificationRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/export/external/verification/record`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getVerificaitonPackageRecordExport(user: PlatformUser, data: SearchVerificationRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/export/external/verification/package/record`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async matchBatchMonitorCompany(user: PlatformUser, body: MonitorImportFileRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/monitor/excel`,
        data: body,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async executeBatchMonitorCompany(user: PlatformUser, body: ExecuteMonitorImportRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/monitor/excel/execute`,
        data: body,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async searchMatchMonitorCompany(user: PlatformUser, body: SearchMonitorBatchImportRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/monitor/excel/item/search`,
        data: body,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async updateMonitorCompanyImport(user: PlatformUser, body: UpdateMonitorImportRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/monitor/excel/item/update`,
        data: body,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async deleteMonitorCompanyImport(user: PlatformUser, data: DeleteMonitorImportRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/monitor/excel/item/delete`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async searchBatchMonitorCompanyImportResult(user: PlatformUser, data: SearchMonitorBatchImportResultRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/monitor/excel/search/result`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async retryAllMonitorImport(user: PlatformUser, data: RetryAllErrorMonitorBatchImportRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/monitor/excel/execute/retryAll`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
  public async retryMonitorImportItem(user: PlatformUser, data: RetryMonitorImportItemRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/import/external/monitor/excel/execute/retryItem`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getMonitorFailRecordExport(user: PlatformUser, data: SearchMonitorBatchImportResultRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/batch/export/external/monitor/fail/record`,
        data: data,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
