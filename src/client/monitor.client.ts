import { BaseClient } from './base.client';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Request } from 'express';
import {
  AddMonitorCompanyRequest,
  AddMonitorGroupRequest,
  AddMonitorRelatedCompanyRequest,
  BatchDeleteMonitorCompanyRequest,
  CompanyRelatedListParams,
  CompanyRelatedMonitorAllRequest,
  DDPlatformClientOptions,
  DynamicChartXmindAggsRequest,
  GetGroupListRequest,
  QueryMonitorCompanyDynamicRequest,
  QueryMonitorDynamicDetialsRequest,
  QueryRelatedHashKeyRequest,
  RemoveMonitorGroupRequest,
  SearchMetricDynamicRequest,
  SearchMonitorCompanyRelatedPartyRequest,
  SearchMonitorCompanyRequest,
  TodayHighRiskDynamicsAggsRequest,
  UpdateMonitorGroupRequest,
} from './model';
import { PlatformUser } from '@domain/model/common';
import { GetMonitorDynamicRemarkRequest, MonitorDynamicHandleRequest, TransferMonitorCompanyRequest } from '@domain/model/monitor';
import { RelatedCompanyChartRequest } from '@domain/model/monitor/RelatedCompanyChartRequest';

export class MonitorClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(MonitorClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
  }

  private async setJwt(headers, user: PlatformUser) {
    const jwtToken = await this.jwtService.signAsync(user, {
      secret: this.jwtSecret,
      expiresIn: 600 + 's',
    });
    headers['authorization'] = 'Bearer ' + jwtToken;
  }

  // public async strategicCustomerPushMessage(user: PlatformUser, requestBody: StrategicCustomerPushMessageRequest, req?: Request | any){
  //   try {
  //     const headers = this.getHeaders(req);
  //     await this.setJwt(headers, user);
  //     const res = await this.httpService.request({
  //       method: 'POST',
  //       url: `${this.serverURL}/monitor/external/strategic/customer/message`,
  //       data: requestBody,
  //       headers,
  //     });
  //     return res.data;
  //   } catch (e) {
  //     this.handleError(e, this.logger);
  //   }
  // }

  // /**
  //  * 初始化监控(创建默认分组及配置等)
  //  * @param user
  //  * @param req
  //  * @returns
  //  */
  // public async initMonitor(user: PlatformUser, req?: Request | any) {
  //   try {
  //     const headers = this.getHeaders(req);
  //     await this.setJwt(headers, user);
  //     const res = await this.httpService.request({
  //       method: 'POST',
  //       url: `${this.serverURL}/monitor/external/init`,
  //       headers,
  //     });
  //     return res.data;
  //   } catch (e) {
  //     this.handleError(e, this.logger);
  //   }
  // }

  /**
   * 获取监控列表
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async searchMonitor(user: PlatformUser, requestBody: SearchMonitorCompanyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取动态列表
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async searchDynamic(user: PlatformUser, requestBody: SearchMetricDynamicRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取关联方动态图表
   * @param user
   * @param requestBody
   * @param req
   */
  public async searchRelatedChartDynamics(user: PlatformUser, requestBody: RelatedCompanyChartRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/search/related/chart`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取监控分组设置详情
   * @param user
   * @param groupId
   * @param req
   * @returns
   */
  public async getGroupSettings(user: PlatformUser, groupId: number, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/group/details/${groupId}`,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 开启关闭分组
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  // public async enableMonitorGroup(user: PlatformUser, requestBody: EnableMonitorGroupRequest, req?: Request | any) {
  //   try {
  //     const headers = this.getHeaders(req);
  //     await this.setJwt(headers, user);
  //     const res = await this.httpService.request({
  //       method: 'POST',
  //       url: `${this.serverURL}/monitor/external/group/enable`,
  //       data: requestBody,
  //       headers,
  //     });
  //     return res.data;
  //   } catch (e) {
  //     this.handleError(e, this.logger);
  //   }
  // }

  /**
   * 更新分组设置
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async updateMonitorGroup(user: PlatformUser, requestBody: UpdateMonitorGroupRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/group/update`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 添加分组
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async addGroup(user: PlatformUser, requestBody: AddMonitorGroupRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/group/add`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 批量添加公司
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async addCompany(user: PlatformUser, requestBody: AddMonitorCompanyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/add`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 批量删除公司
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */

  /* public async removeCompany(user: PlatformUser, requestBody: RemoveMonitorCompanyRequest, req?: Request | any) {
     try {
       const headers = this.getHeaders(req);
       await this.setJwt(headers, user);
       const res = await this.httpService.request({
         method: 'POST',
         url: `${this.serverURL}/monitor/external/company/remove`,
         data: requestBody,
         headers,
       });
       return res.data;
     } catch (e) {
       this.handleError(e, this.logger);
     }
   }*/

  /**
   * 分组列表
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getGroupList(user: PlatformUser, requestBody: GetGroupListRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/group/list`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取动态列表参数以及工作区数据
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async dynamicAggAndWorkSpaceSearch(user: PlatformUser, requestBody: SearchMetricDynamicRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/aggAndWorkSpace/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取动态列表重点关注
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async dynamicFocus(user: PlatformUser, requestBody: SearchMetricDynamicRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/focus`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 删除分组
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async removeGroup(user: PlatformUser, requestBody: RemoveMonitorGroupRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/group/remove`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 监控列表查询聚合
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async companyAggSearch(user: PlatformUser, requestBody: SearchMonitorCompanyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/agg/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /* /!**
    * 监控列表移组
    * @param user
    * @param requestBody
    * @param req
    * @returns
    *!/
   public async moveGroup(user: PlatformUser, requestBody: MonitorCompanyMoveGroupRequest, req?: Request | any) {
     try {
       const headers = this.getHeaders(req);
       await this.setJwt(headers, user);
       const res = await this.httpService.request({
         method: 'POST',
         url: `${this.serverURL}/monitor/external/company/moveGroup`,
         data: requestBody,
         headers,
       });
       return res.data;
     } catch (e) {
       this.handleError(e, this.logger);
     }
   }*/

  /**
   * 添加关联方监控
   * @param user
   * @param requestBody
   * @param req
   */
  public async addRelated(user: PlatformUser, requestBody: AddMonitorRelatedCompanyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/addRelated`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async searchRelatedCompany(user: PlatformUser, requestBody: CompanyRelatedListParams, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/data/external/CompanyRelatedList`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async queryCompanyDetailDynamic(user: PlatformUser, requestBody: QueryMonitorCompanyDynamicRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/companyDynamic/query`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async queryCompanyDetailDynamicAgg(user: PlatformUser, requestBody: QueryMonitorCompanyDynamicRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/companyDynamic/queryAgg`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async dynamicCardAggs(user: PlatformUser, requestBody: SearchMetricDynamicRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/aggs/card`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async dynamicAnalyzeHighRisk(user: PlatformUser, requestBody: TodayHighRiskDynamicsAggsRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/chart/highRisk`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async dynamicChartAggs(user: PlatformUser, requestBody: SearchMetricDynamicRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/chart/summary`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async dynamicAnalyzeXmind(user: PlatformUser, requestBody: DynamicChartXmindAggsRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/chart/xmind`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async dynamicHandle(currentUser: PlatformUser, searchBody: MonitorDynamicHandleRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, currentUser);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/handle`,
        data: searchBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getDynamicRemark(currentUser: PlatformUser, searchBody: GetMonitorDynamicRemarkRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, currentUser);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/remark`,
        data: searchBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async searchDynamicHashKey(user: PlatformUser, requestBody: QueryRelatedHashKeyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/searchDynamicHashKey`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async searchMonitorCompanyRelated(user: PlatformUser, requestBody: SearchMonitorCompanyRelatedPartyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/related/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async searchDynamicContent(user: PlatformUser, requestBody: QueryMonitorDynamicDetialsRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/dynamic/content/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async companyTransfer(user: PlatformUser, requestBody: TransferMonitorCompanyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/transfer`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async batchCompanyDelete(user: PlatformUser, requestBody: BatchDeleteMonitorCompanyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/delete`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async addAllMonitorCompanyRelated(user: PlatformUser, requestBody: CompanyRelatedMonitorAllRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/addAllMonitorCompanyRelated`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getUnMonitorCompanyCount(user: PlatformUser, requestBody: CompanyRelatedListParams, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/getUnMonitorCompanyRelatedCount`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async addAllMonitorCompanyRelatedByRelatedDynamic(user: PlatformUser, requestBody: QueryMonitorDynamicDetialsRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/relatedDynamic/addAllMonitorCompanyRelated`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async relatedDynamicGetUnMonitorCompanyRelatedCount(user: PlatformUser, requestBody: QueryMonitorDynamicDetialsRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/relatedDynamic/getUnMonitorCompanyRelatedCount`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 根据查询结果删除全部企业
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async removeAllCompanyBySearch(user: PlatformUser, requestBody: SearchMonitorCompanyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/search/removeAll`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 根据关联方查询结果删除全部关联方企业
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async removeAllCompanyByRelatedSearch(user: PlatformUser, requestBody: SearchMonitorCompanyRelatedPartyRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/monitor/external/company/relatedSearch/removeAll`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
