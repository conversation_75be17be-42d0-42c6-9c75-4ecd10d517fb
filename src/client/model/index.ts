import { AxiosRequestConfig } from 'axios';

export class DDPlatformClientOptions {
  server: string;
  requestFrom: string;
  jwtSecret: string;
  axiosConfig?: AxiosRequestConfig;
}

export class ClientRegisterOptions {
  useFactory: (...args: any[]) => Promise<DDPlatformClientOptions>;
  inject?: any[];
}

// 基础类型导出
export { PlatformUser, AffectedResponse } from '../../domain/model/common';
export { ProductCodeEnums } from '../../domain/enums/ProductCodeEnums';
export { DataStatusEnums } from '../../domain/enums/DataStatusEnums';

// 尽调相关导出
export { DiligenceResponse } from '../../domain/model/diligence/req&res/DiligenceResponseV2';
export { SingleCompanyDiligenceRequest } from '../../domain/model/diligence/req&res/SingleCompanyDiligenceRequest';
export { GetDiligenceResultRequest } from '../../domain/model/diligence/req&res/GetDiligenceResultRequest';
export { DimensionHitDetailsClientRequest } from '../../domain/model/diligence/details/request';
export { HitDetailsBaseResponse } from '../../domain/model/diligence/details/response';
export * from '../../domain/model/diligence/history';
export * from '../../domain/constants/diligence.constants';

// 批量处理相关导出
export * from '../../domain/enums/batch';
export * from '../../domain/model/batch';

// 风险模型相关导出
export * from '../../domain/model/riskModel';

// 指标相关导出
export * from '../../domain/model/metric';

// 维度相关导出
export * from '../../domain/model/dimension';
export * from '../../domain/enums/dimension/DimensionFieldCompareTypeEnums';
export * from '../../domain/enums/dimension/DimensionFieldInputTypeEnums';
export * from '../../domain/enums/dimension/DimensionFieldSearchTypeEnums';
export * from '../../domain/enums/dimension/DimensionFiledDataTypeEnums';
export * from '../../domain/enums/dimension/DimensionLevel1Enums';
export * from '../../domain/enums/dimension/FieldValueEnums';
export * from '../../domain/enums/dimension/IndicatorTypeEnums';
export * from '../../domain/enums/dimension/LogicOperatorEnums';
export * from '../../domain/enums/dimension/NebulaRelatedEdgeEnums';
export * from '../../domain/enums/dimension/NebulaRiskTagEnums';
export * from '../../domain/enums/dimension/RelatedTypeEnums';
export * from '../../domain/enums/dimension/UnitEnums';
export * from '../../domain/enums/dimension/CreditRateResult';

// 监控相关导出
export * from '../../domain/model/monitor';

// 数据相关导出
export * from '../../domain/model/data/request';

// 用户相关导出
export * from '../../domain/model/user';

// 推送相关导出
export * from '../../domain/model/push';

// 消息相关导出
export * from '../../domain/model/message';

// AI分析相关导出
export * from '../../domain/model/aiAnalyzer';

// 验证相关导出
export * from '../../domain/model/verification';

// 其他模型导出
export * from '../../domain/model/ScoreSettingPO';
export * from '../../domain/model/StrategyExtendPO';
export * from '../../domain/model/QueryHitStrategyPO';

export * from '../../domain/model';

export * from '@commons/exceptions/KzzHttpException';
