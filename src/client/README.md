# 客户端 SDK 层 (Client SDK Layer)

## 📝 概述

客户端 SDK 层提供对外的软件开发包，方便第三方系统集成使用。

## 🎯 职责

- 提供易用的 SDK 接口
- 封装 HTTP 请求细节
- 提供类型定义支持
- 提供使用示例和文档

## 📁 目录结构

```
client/
├── sdk/               # SDK核心模块
│   ├── dd-platform.sdk.ts          # 主SDK类
│   ├── sdk.module.ts               # SDK模块定义
│   └── interfaces/                 # SDK接口定义
│       ├── sdk-config.interface.ts
│       └── sdk-response.interface.ts
├── api-clients/       # API客户端封装
│   ├── diligence/     # 尽职调查客户端
│   │   ├── diligence.client.ts
│   │   ├── dto/
│   │   │   ├── diligence-request.dto.ts
│   │   │   └── diligence-response.dto.ts
│   │   └── interfaces/
│   ├── risk-model/    # 风险模型客户端
│   │   ├── risk-model.client.ts
│   │   ├── dto/
│   │   └── interfaces/
│   ├── monitor/       # 监控客户端
│   │   ├── monitor.client.ts
│   │   ├── dto/
│   │   └── interfaces/
│   ├── company/       # 企业信息客户端
│   │   ├── company.client.ts
│   │   ├── dto/
│   │   └── interfaces/
│   └── common/        # 通用客户端功能
│       ├── base.client.ts          # 基础客户端类
│       ├── http.client.ts          # HTTP客户端封装
│       └── auth.client.ts          # 认证客户端
├── types/             # SDK类型定义
│   ├── index.ts                    # 统一导出
│   ├── api.types.ts                # API类型
│   ├── config.types.ts             # 配置类型
│   └── response.types.ts           # 响应类型
├── utils/             # SDK工具函数
│   ├── request.company.utils.ts            # 请求工具
│   ├── response.company.utils.ts           # 响应处理工具
│   ├── validation.company.utils.ts         # 数据验证工具
│   └── error.company.utils.ts              # 错误处理工具
├── examples/          # 使用示例
│   ├── basic-usage.example.ts      # 基础使用示例
│   ├── diligence.example.ts        # 尽职调查示例
│   ├── risk-model.example.ts       # 风险模型示例
│   └── monitor.example.ts          # 监控示例
└── docs/              # SDK文档
    ├── README.md                   # SDK使用文档
    ├── API.md                      # API参考文档
    ├── GUIDE.md                    # 快速开始指南
    └── CHANGELOG.md                # 变更日志
```

## 🔗 依赖关系

- 依赖：@shared（类型定义）
- 被依赖：外部系统

## 📋 使用规范

1. 提供简洁易用的 API
2. 完善的类型支持
3. 详细的文档和示例
4. 良好的错误处理
5. 支持 TypeScript 和 JavaScript

## SDK 设计原则

- **简单易用**: API 设计简洁明了，易于理解和使用
- **类型安全**: 完整的 TypeScript 类型定义
- **模块化**: 按功能模块组织，支持按需导入
- **兼容性**: 支持多种环境（Node.js、浏览器）
- **可扩展**: 支持插件和中间件机制

## 核心功能

### SDK 核心

- 统一的配置管理
- 认证和授权
- 请求/响应拦截器
- 错误处理和重试

### API 客户端

- **尽职调查客户端**: 企业尽职调查相关 API
- **风险模型客户端**: 风险评估和模型相关 API
- **监控客户端**: 企业监控和预警相关 API
- **企业客户端**: 企业信息查询和管理相关 API

### 工具函数

- 请求参数处理
- 响应数据转换
- 数据验证和校验
- 错误处理和日志

## 使用示例

```typescript
import { DDPlatformSDK } from '@dd-platform/sdk';

// 初始化SDK
const sdk = new DDPlatformSDK({
  baseURL: 'https://api.dd-platform.com',
  apiKey: 'your-api-key',
  timeout: 30000,
});

// 使用尽职调查API
const diligenceResult = await sdk.diligence.create({
  companyName: '测试公司',
  socialCreditCode: '*********',
  // ... 其他参数
});

// 使用风险模型API
const riskScore = await sdk.riskModel.calculate({
  companyId: 'company-123',
  modelType: 'default',
});
```

## 迁移计划

重组现有的客户端代码：

- `src/client.ts` → 重构为完整的 SDK 结构
- 抽取各个业务模块的客户端代码
- 完善类型定义和文档
- 添加使用示例和测试

## 发布计划

- 支持 npm 包发布
- 提供 CDN 版本
- 完善的版本管理
- 详细的发布说明
