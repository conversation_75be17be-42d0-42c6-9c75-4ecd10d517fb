import { BaseClient } from './base.client';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Request } from 'express';
import { UpdateMetricRequest } from '@domain/model/metric';
import { DDPlatformClientOptions, SearchMetricsRequest } from './model';
import { PlatformUser } from '@domain/model/common';

export class MetricClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(MetricClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
  }

  private async setJwt(headers, user: PlatformUser) {
    const jwtToken = await this.jwtService.signAsync(user, {
      secret: this.jwtSecret,
      expiresIn: 600 + 's',
    });
    headers['authorization'] = 'Bearer ' + jwtToken;
  }

  /**
   * 修改模型名称、开关、风险等级定义
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async updateMetric(user: PlatformUser, requestBody: UpdateMetricRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/metric/external/update`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 修改模型名称、开关、风险等级定义
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async searchMetrics(user: PlatformUser, requestBody: SearchMetricsRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/metric/external/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
