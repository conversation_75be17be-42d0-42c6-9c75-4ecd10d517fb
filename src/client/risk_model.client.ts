import { QccLogger } from '@kezhaozhao/qcc-logger';
// import { ESDetailResopne, ESResponse } from '@kezhaozhao/search-utils';
import { Logger } from 'log4js';
import { Request } from 'express';
import { BaseClient } from './base.client';
import {
  CheckWhetherDeprecateRequest,
  CopyResourceRequest,
  DDPlatformClientOptions,
  PublishRiskModelRequest,
  ResourceEditableCheckRequest,
  TurnOnOrOffRiskModelRequest,
  UpdateRiskModelRequest,
} from './model';
import { API_BASE } from '@commons/constants/common';
import { PlatformUser } from '@domain/model/common';
import { JwtService } from '@nestjs/jwt';
import { GetDimensionHitStrategiesRequest, SearchRiskModelBranchRequest, SearchRiskModelRequest, SearchRiskModelResponse } from '@domain/model/riskModel';

export class RiskModelClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(RiskModelClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
    this.serverURL = `${this.clientOptions.server}${API_BASE}`;
    this.jwtService = new JwtService();
    this.jwtSecret = clientOptions.jwtSecret;
  }

  private async setJwt(headers, user: PlatformUser) {
    const jwtToken = await this.jwtService.signAsync(user, {
      secret: this.jwtSecret,
      expiresIn: 600 + 's',
    });
    headers['authorization'] = 'Bearer ' + jwtToken;
  }

  /**
   * 获取指定模型详情
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async checkWhetherCanEdit(user: PlatformUser, requestBody: ResourceEditableCheckRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/editable/check`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取指定模型详情
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async resourceCopy(user: PlatformUser, requestBody: CopyResourceRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/copy`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取指定模型详情
   * @param user
   * @param id
   * @param req
   * @returns
   */
  public async getRiskModelDetail(user: PlatformUser, id: number, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'GET',
        url: `${this.serverURL}/risk-model/external/details/${id}`,
        headers,
      });

      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取用户模型分组列表
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getRiskModelList(user: PlatformUser, requestBody: SearchRiskModelRequest, req?: Request | any): Promise<SearchRiskModelResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取指定分组内的模型列表
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getRiskModelBranchList(user: PlatformUser, requestBody: SearchRiskModelBranchRequest, req?: Request | any): Promise<SearchRiskModelResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/branch/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 修改模型名称、开关、风险等级定义
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async editRiskModel(user: PlatformUser, requestBody: UpdateRiskModelRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/edit`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 模型发布
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async publishModelFromClient(user: PlatformUser, requestBody: PublishRiskModelRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/publish`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 模型状态变更
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async turnOnOrOffModel(user: PlatformUser, requestBody: TurnOnOrOffRiskModelRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/turn`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 废弃模型
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async deprecateModel(user: PlatformUser, id: number, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/deprecate/${id}`,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 检查模型是否可废弃
   * @param user
   * @param requestBody
   * @param req
   */
  public async checkWhetherDeprecate(user: PlatformUser, requestBody: CheckWhetherDeprecateRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/check/deprecate`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 根据 strategyIds 查询维度策略规则
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getDimensionHitStrategies(user: PlatformUser, requestBody: GetDimensionHitStrategiesRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/risk-model/external/getDimensionHitStrategies`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
