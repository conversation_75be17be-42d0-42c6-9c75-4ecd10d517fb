import { DiligenceClient } from './diligence.client';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';

describe('test', () => {
  it('should pass', async () => {
    const client = new DiligenceClient({
      // server: 'http://api.test.greatld.com',
      server: 'http://127.0.0.1:7001',
      requestFrom: 'test',
      // jwtSecret: 'eyJhbGciOiJSUzI1NiIsImtpZCI6IiJ9.eyJpc3MiOiJrd',
      jwtSecret: '11111',
    });
    try {
      // @ts-ignore
      const t = await client.getBundleSearch(
        {
          userId: 102716,
          loginUserId: 137,
          currentProduct: ProductCodeEnums.Pro,
          currentOrg: 1003263,
          orgName: '风险洞察开发测试',
        },
        {
          product: 'test',
        },
      );
      expect(t).toBeDefined();
    } catch (e) {
      expect(e).not.toBeNull();
    }
  });
});
