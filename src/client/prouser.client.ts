import { BaseClient } from './base.client';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Request } from 'express';
import { DDPlatformClientOptions } from './model';
import { PlatformUser } from '@domain/model/common';
import { UserCheckDimissionRequest } from '@domain/model/user/UserCheckDimissionRequest';

export class ProUserClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(ProUserClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
  }

  private async setJwt(headers, user: PlatformUser) {
    const jwtToken = await this.jwtService.signAsync(user, {
      secret: this.jwtSecret,
      expiresIn: 600 + 's',
    });
    headers['authorization'] = 'Bearer ' + jwtToken;
  }

  /**
   *
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async checkDimission(user: PlatformUser, requestBody: UserCheckDimissionRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/user/external/checkDimission`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
