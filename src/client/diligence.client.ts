import { QccLogger } from '@kezhaozhao/qcc-logger';
// import { ESDetailResopne, ESResponse } from '@kezhaozhao/search-utils';
import { Logger } from 'log4js';
import { Request } from 'express';
import { BaseClient } from './base.client';
import { DDPlatformClientOptions, HitDetailsQueryParams } from './model';
import { DiligenceResponse, PaidCheckResponse } from '@domain/model/diligence/req&res/DiligenceResponseV2';
import { SingleCompanyDiligenceRequest } from '@domain/model/diligence/req&res/SingleCompanyDiligenceRequest';
import { GetDiligenceResultRequest } from '@domain/model/diligence/req&res/GetDiligenceResultRequest';
import { PlatformUser } from '@domain/model/common';
import { DimensionHitDetailsClientRequest } from '@domain/model/diligence/details/request';
import { HitDetailsBaseResponse } from '@domain/model/diligence/details/response';
import { DiligenceBundleSearchRequest, DiligenceHistoryRequest, DiligenceHistoryResponse } from '@domain/model/diligence/history';

export class DiligenceClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(DiligenceClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
  }

  private async setJwt(headers, user: PlatformUser) {
    const jwtToken = await this.jwtService.signAsync(user, {
      secret: this.jwtSecret,
      expiresIn: 600 + 's',
    });
    headers['authorization'] = 'Bearer ' + jwtToken;
  }

  /**
   * 针对指定公司执行尽调
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async runDiligence(user: PlatformUser, requestBody: SingleCompanyDiligenceRequest, req?: Request | any): Promise<DiligenceResponse[]> {
    try {
      const headers = this.getHeaders(req);
      // const jwtToken = await this.jwtService.signAsync(user, {
      //   secret: this.jwtSecret,
      //   expiresIn: 600 + 's',
      // });
      // headers['authorization'] = 'Bearer ' + jwtToken;
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/diligence/run`,
        data: requestBody,
        headers,
      });

      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 查看指定公司的指定尽调详情
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getDiligenceDetails(user: PlatformUser, requestBody: GetDiligenceResultRequest, req?: Request | any): Promise<DiligenceResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/diligence/details`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 查询尽调列表
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getDiligenceList(user: PlatformUser, requestBody: DiligenceHistoryRequest, req?: Request | any): Promise<DiligenceHistoryResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/diligence/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 尽调列表查询项(聚合)
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getAggSearch(user: PlatformUser, requestBody: DiligenceHistoryRequest, req?: Request | any): Promise<DiligenceHistoryResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/diligence/agg/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 查看某个维度的尽调详情
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getDiligenceDimensionDetails(
    user: PlatformUser,
    requestBody: DimensionHitDetailsClientRequest,
    req?: Request | any,
  ): Promise<HitDetailsBaseResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const { key } = requestBody;
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/diligence/dimension/details/${key}`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getBundleSearch(user: PlatformUser, requestBody: DiligenceBundleSearchRequest, req?: Request | any): Promise<DiligenceHistoryResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/diligence/bundleSearch`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 查看裁判文书详情
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getDataDetails(user: PlatformUser, requestBody: HitDetailsQueryParams, req?: Request | any): Promise<HitDetailsBaseResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const { key } = requestBody;
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/data/external/${key}`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 对尽调额度进行预计算
   * @param user
   * @param requestBody
   * @param req
   * @returns
   */
  public async getPaidCheck(user: PlatformUser, requestBody: SingleCompanyDiligenceRequest, req?: Request | any): Promise<PaidCheckResponse> {
    try {
      const headers = this.getHeaders(req);
      // const jwtToken = await this.jwtService.signAsync(user, {
      //   secret: this.jwtSecret,
      //   expiresIn: 600 + 's',
      // });
      // headers['authorization'] = 'Bearer ' + jwtToken;
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/diligence/paidCheck`,
        data: requestBody,
        headers,
      });

      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
