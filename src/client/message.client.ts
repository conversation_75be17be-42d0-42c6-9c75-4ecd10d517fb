import { BaseClient } from './base.client';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DDPlatformClientOptions, MessageRequest, MessageSearchRequest, PlatformUser } from './model';
import { Request } from 'express';

export class MessageClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(MessageClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
  }

  private async setJwt(headers, user: PlatformUser) {
    const jwtToken = await this.jwtService.signAsync(user, {
      secret: this.jwtSecret,
      expiresIn: 600 + 's',
    });
    headers['authorization'] = 'Bearer ' + jwtToken;
  }

  /**
   * @description 查询消息列表
   * @param user
   * @param requestBody
   * @param req
   */
  public async getMessages(user: PlatformUser, requestBody: MessageSearchRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/message/external/search`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /*
   * @description 查询消息数量
   */
  public async getMessagesCount(user: PlatformUser, requestBody: MessageSearchRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/message/external/count`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * @description 标记消息已读
   * @param user
   * @param messageId
   * @param req
   */
  public async readMessage(user: PlatformUser, messageId: number, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/message/external/read/${messageId}`,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * @description 按类型标记消息已读
   * @param user
   * @param requestBody
   * @param req
   */
  public async readAllMessages(user: PlatformUser, requestBody: MessageRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/message/external/read_all`,
        data: requestBody,
        headers,
      });
      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
