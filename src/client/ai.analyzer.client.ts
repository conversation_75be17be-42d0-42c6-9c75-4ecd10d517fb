import { BaseClient } from './base.client';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { AiReportCommentRequest, DDPlatformClientOptions, PlatformUser } from './model';
import { Request } from 'express';
import {
  AiGetReportRequest,
  AiReportAnalyzeRequest,
  AiSearchPromptsRequest,
  ReportChatResponse,
  SearchPromptsResponse,
  UpdatePromptRequest,
} from '@domain/model/aiAnalyzer';
import { AiReportsEntity } from '@domain/entities/AiReportsEntity';
import { AiReportCommentEntity } from '@domain/entities/AiReportCommentEntity';

export class AiAnalyzerClient extends BaseClient {
  private readonly logger: Logger = QccLogger.getLogger(AiAnalyzerClient.name);

  constructor(clientOptions: DDPlatformClientOptions) {
    super(clientOptions);
  }

  private async setJwt(headers, user: PlatformUser) {
    const jwtToken = await this.jwtService.signAsync(user, {
      secret: this.jwtSecret,
      expiresIn: 600 + 's',
    });
    headers['authorization'] = 'Bearer ' + jwtToken;
  }

  /**
   * 获取内置的提示词ID
   * @param user
   * @param requestBody
   * @param req
   */
  public async searchPrompts(user: PlatformUser, requestBody: AiSearchPromptsRequest, req?: Request | any): Promise<SearchPromptsResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/ai-analyzer/external/prompt/search`,
        data: requestBody,
        headers,
      });

      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 修改提示词
   * @param user
   * @param requestBody
   * @param req
   */
  public async updatePrompt(user: PlatformUser, requestBody: UpdatePromptRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/ai-analyzer/external/prompt/update`,
        data: requestBody,
        headers,
      });

      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 帮忙阅读报告并生成新的报告
   * @param user
   * @param requestBody
   * @param req
   */
  public async reportChat(user: PlatformUser, requestBody: AiReportAnalyzeRequest, req?: Request | any): Promise<ReportChatResponse> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/ai-analyzer/external/report/chat`,
        data: requestBody,
        headers,
      });

      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 阅读报告并返回新的报告（流方式）
   * @param user
   * @param requestBody
   * @param req
   */
  public async reportChatStream(user: PlatformUser, requestBody: AiReportAnalyzeRequest, req?: Request | any) {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const response = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/ai-analyzer/external/report/chat/stream`,
        data: requestBody,
        headers,
        responseType: 'stream', // 设置响应类型为流式响应
      });
      return response.data;

      return response.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 获取报告
   * @param user
   * @param requestBody
   * @param req
   */
  public async getReport(user: PlatformUser, requestBody: AiGetReportRequest, req?: Request | any): Promise<AiReportsEntity> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/ai-analyzer/external/report/details`,
        data: requestBody,
        headers,
      });

      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  /**
   * 报告添加评论
   * @param user
   * @param requestBody
   * @param req
   */
  public async addCommentToReport(user: PlatformUser, requestBody: AiReportCommentRequest, req?: Request | any): Promise<AiReportCommentEntity> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/ai-analyzer/external/report/comment`,
        data: requestBody,
        headers,
      });

      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }

  public async getReportChatStatus(user: PlatformUser, req?: Request | any): Promise<boolean> {
    try {
      const headers = this.getHeaders(req);
      await this.setJwt(headers, user);
      const res = await this.httpService.request({
        method: 'POST',
        url: `${this.serverURL}/ai-analyzer/external/report/chat/status`,
        data: {},
        headers,
      });

      return res.data;
    } catch (e) {
      this.handleError(e, this.logger);
    }
  }
}
