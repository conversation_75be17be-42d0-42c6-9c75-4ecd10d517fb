import { Test } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigService } from '@core/config/config.service';
import { ConfigModule } from '@core/config/config.module';
import { RedisModule } from '@kezhaozhao/nestjs-redis';
import { EntityManager, getRepository, In } from 'typeorm';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';
import { clearDiligenceTestData, prepareTestDiligenceData } from './diligence.test.utils';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { getTestOrgId } from './constants.test.utils';
import { clearSnapshotEsData, fillSnapshotTestData } from './snapshot.test.utils';
import { DiligenceSnapshotEsService } from '@modules/risk-assessment/diligence/snapshot/diligence.snapshot.es.service';
import { prepareBatchTestData } from './batch.test.utils';
import { generateUniqueTestIds, getTestUser } from './test.user';

jest.setTimeout(60 * 1000);
const [testOrgId, testUserId] = generateUniqueTestIds('test.utils.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);
describe.skip('test.utils', () => {
  let entityManager: EntityManager;
  let snapshotEsService: DiligenceSnapshotEsService;
  beforeAll(async () => {
    const moudle = await Test.createTestingModule({
      providers: [DiligenceSnapshotEsService],
      imports: [
        ConfigModule,
        RedisModule.forRootAsync({
          useFactory: (configService: ConfigService) => configService.redis, // or use async method
          inject: [ConfigService],
        }),
        TypeOrmModule.forRootAsync({
          useFactory: async (configService: ConfigService) => configService.typeorm,
          inject: [ConfigService],
        }),
        TypeOrmModule.forFeature([DiligenceHistoryEntity]),
      ],
    }).compile();
    entityManager = getRepository(DiligenceHistoryEntity).manager;
    snapshotEsService = moudle.get(DiligenceSnapshotEsService);
  });

  it('生成测试用 batch', async () => {
    await clearDiligenceTestData(entityManager, testOrgId);
    const batch = await prepareBatchTestData(entityManager, 2);
    expect(batch.length).toBe(2);
    const c = await entityManager.count(BatchEntity, { batchId: In(batch.map((b) => b.batchId)) });
    expect(c).toBe(2);
    await clearDiligenceTestData(entityManager, testOrgId);
    const c1 = await entityManager.count(BatchEntity, { batchId: In(batch.map((b) => b.batchId)) });
    expect(c1).toBe(0);
  });
  beforeEach(async () => {
    await clearDiligenceTestData(entityManager, testOrgId);
  });
  afterEach(async () => {
    await clearDiligenceTestData(entityManager, testOrgId);
  });

  it('生成测试数据 - 尽调和batch', async () => {
    const orgId = getTestOrgId();
    const batchSize = 2;
    const batch = await prepareBatchTestData(entityManager, batchSize);
    const diligence = await prepareTestDiligenceData(entityManager, {
      batchIdCurrent: batch[0].batchId,
      batchIdPrevious: batch[1].batchId,
      orgId,
      userIds: [testUserId],
    });
    const c = await entityManager.count(DiligenceHistoryEntity, { orgId });
    expect(c).toBe(diligence.length);
    await clearDiligenceTestData(entityManager, testOrgId);
    const c1 = await entityManager.count(DiligenceHistoryEntity, { orgId });
    expect(c1).toBe(0);
    const c2 = await entityManager.count(BatchEntity, { orgId });
    expect(c2).toBe(0);
  });

  it('测试数据，尽调和快照', async () => {
    await clearSnapshotEsData(testOrgId, snapshotEsService);
    const batch = await prepareBatchTestData(entityManager);
    const diligenceList = await prepareTestDiligenceData(entityManager, {
      batchIdCurrent: batch[0].batchId,
      batchIdPrevious: batch[1].batchId,
      orgId: testOrgId,
      userIds: [testUserId],
    });
    const snapshotTestData = await fillSnapshotTestData(snapshotEsService, diligenceList, testUser);
    expect(snapshotTestData.diligenceEntities.length).toBe(diligenceList.length / 2);
  });
});
