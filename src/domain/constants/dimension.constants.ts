import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DetailsParamEnums } from '@domain/enums/diligence/DetailsParamEnums';
import { DimensionSourceEnums } from '@domain/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '@domain/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '@domain/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFieldInputTypeEnums } from '@domain/enums/dimension/DimensionFieldInputTypeEnums';
import { DimensionFieldTypeEnums } from '@domain/enums/dimension/DimensionFiledDataTypeEnums';
import { TargetInvestigationEnums, TargetScopeEnums } from '@domain/enums/dimension/FieldValueEnums';
import { IndicatorTypeEnums } from '@domain/enums/dimension/IndicatorTypeEnums';
import { NebulaRelatedEdgeEnums } from '@domain/enums/dimension/NebulaRelatedEdgeEnums';
import { NebulaRiskTagEnums } from '@domain/enums/dimension/NebulaRiskTagEnums';
import { RelatedTypeEnums } from '@domain/enums/dimension/RelatedTypeEnums';
import { BlackTypeItems } from './blacklist.outer.constants';
import { CourtCaseReason, CourtRole, CourtType } from './case.constants';
import { CompanyCertificationTypeMap } from './company-certification.constants';
import {
  ApplicationProgressConstant,
  CompanyDetailScopeEnums,
  EntStatusList,
  FinancialInstitutions,
  hasCertificationRevoked,
  HasCompanyCircularShareholder,
  HasCompanyNaturalPersonShareholder,
  hasEmployeeStockPlatform,
  HasTerminateConstant,
  IndustrialChainCoreCompanyContant,
  InternationPatentStatusConstant,
  IsHistoryPatentConstant,
  IsInstitutionalInvestorConstant,
  IsStateOwnedConstant,
  OutwardInvestmentStatisticsConstant,
  PatentIsTransferConstant,
  PatentOutflowConstant,
  PatentStableConstant,
  PatentStatisticsConstant,
  PatentStatusConstant,
  PatentTypeConstant,
  PledgeTypeConstant,
  RecruitmentStatisticsConstant,
  ShareholdRoleConstant,
  SourcesInvestInstiteRankConstant,
} from './company.constants';
import { GetFields } from './dimension.base.fields.constants';
import {
  CompanyDetailChangeDimensionFields,
  CompanyDetailDimensionFields,
  RelatedCompanyDetailDimensionFields,
} from './dimension/company.detail.dimension.fields';
import {
  BusinessAnomaliesWithSamePhoneAndAddressDimensionFields,
  MoneyLaunderingDimensionFields,
  RelatedAnnouncementDimensionFields,
  RelatedCompaniesDimensionFields,
  RelatedCompanyChangeDimensionFields,
} from './dimension/related.company.dimension.fields';
import { RelatedRiskChangeDimensionFields } from './dimension/related.risk.change.dimension.fields';
import { RiskChangeDimensionFields } from './dimension/risk.change.dimension.fields';
import { CaseReasonTypeMap, CaseRoleMap, CaseTargetInvestigation, CaseTypes, CreditTargetScope, PersonCaseTargetInvestigation } from './judgement.constants';
import { AssociationTypeConst, RelationTypeConst } from './model.constants';
import { AllTopicTypes } from './news.constants';
import { SanctionsListCode } from './ovs.sanctions.constants';
import { BusinessAbnormalType, PenaltiesType, ProcessingAgencyLevelOneMap, ProcessingAgencyLevelTwoMap, punishReasonTypeMap } from './punish.constants';
import { QfkRiskItemConstants } from './qfk.risk.item.constants';
import { YearPeriodType } from './recruitment.constants';
import { RelatedRiskTypeMap, RelatedRiskTypesMap, RelatedRoleTypeMap, RelatedTypeMap } from './related.constants';
import { BaseLineDateSelect, ChangeStatusMap, RealRegistrationErrorMap, RegisCapitalTrendMap, RiskChangeCategoryList } from './risk.change.constants';
import { RelationShipList } from './violation.constants';
import { GbIndustryCompStatsTypeConstant, GbIndustryCompStatsTypeEnum } from '@domain/constants/industry.statistics.constants';

const templateString = '匹配到目标主体 <em class="#level#">【#name#】</em>';

export const BaseDimensionDefinitionForGenerating = {
  [DimensionTypeEnums.CompanyDetailChange]: {
    key: DimensionTypeEnums.CompanyDetailChange,
    name: '工商基本信息变化',
    dimensionFields: CompanyDetailChangeDimensionFields,
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '工商基本信息变化',
  },
  [DimensionTypeEnums.RelatedCompanyDetail]: {
    key: DimensionTypeEnums.RelatedCompanyDetail,
    name: '关联方工商基本信息',
    dimensionFields: RelatedCompanyDetailDimensionFields,
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '关联方工商基本信息',
  },
  [DimensionTypeEnums.IPOProcess]: {
    key: DimensionTypeEnums.IPOProcess,
    name: '上市进程',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 1,
      },
      {
        fieldName: '申报进度',
        fieldKey: DimensionFieldKeyEnums.applicationProgress,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '申报进度: 1-辅导备案, 2-已经受理，3-已问询，4-上市委会议, 5-提交注册, 6-注册结果',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1],
        options: ApplicationProgressConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '是否中止/终止',
        fieldKey: DimensionFieldKeyEnums.hasTerminate,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '是否终止 1-是，2-否',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: HasTerminateConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },

      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/QccDetail/Register/Detail',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '上市进程',
  },
  [DimensionTypeEnums.OutwardInvestmentAnalysis]: {
    key: DimensionTypeEnums.OutwardInvestmentAnalysis,
    name: '对外投资分析',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '对外投资统计维度',
        fieldKey: DimensionFieldKeyEnums.outwardInvestmentStatistics,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '统计维度 1-注销占比',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: OutwardInvestmentStatisticsConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '成立日期',
        fieldKey: DimensionFieldKeyEnums.registerDate,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '成立日期： 近xx天',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
        defaultValue: [365],
        options: [{ unit: '天', min: 1, max: 365 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '区间左值',
        fieldKey: DimensionFieldKeyEnums.leftRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '专利占比左区间>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [50],
        options: [{ unit: '%', min: 50, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '区间右值',
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '占比右区间>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [50],
        options: [{ unit: '%', min: 50, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'startdate',
          order: 'DESC',
          fieldSnapshot: 'StartDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/ECILocal/SearchByPromoterWithFundedRatioByKeyNo',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体占比【#ratio#】',
    type: IndicatorTypeEnums.generalItems,
    description: '对外投资分析',
  },

  [DimensionTypeEnums.CreditCheckCount]: {
    key: DimensionTypeEnums.CreditCheckCount,
    name: '企业失信统计',
    dimensionFields: [
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 1,
      },
      {
        fieldName: '排查范围',
        fieldKey: DimensionFieldKeyEnums.targetScope,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:失信被执行人，被执行人，限制高消费',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetScopeEnums.ShiXinCount],
        options: CreditTargetScope,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 4, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 5, [-1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'courtdate',
          order: 'DESC',
          fieldSnapshot: 'courtdate',
        },
      ]),
    ],
    sourcePath: '/api/ECILocal/GetCountInfos',
    source: DimensionSourceEnums.EnterpriseLib,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业失信统计',
  },

  [DimensionTypeEnums.TradeDetail]: {
    key: DimensionTypeEnums.TradeDetail,
    name: '交易明细',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '交易金额同比',
        fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '交易金额同比 交易金额同比 > 50% ',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        defaultValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '变更趋势',
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '变更趋势 1增加, 2减少',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: ChangeStatusMap,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'announcement_date',
          order: 'DESC',
          fieldSnapshot: 'AnnounceDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/FinancialInfo/Ashare/TradeDetails',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '交易金额明细信息',
  },

  [DimensionTypeEnums.SupplierOrCustomer]: {
    key: DimensionTypeEnums.SupplierOrCustomer,
    name: '企业供应商和客户',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '合作类型',
        fieldKey: DimensionFieldKeyEnums.cooperationType,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '关系类型：0-供应商，1-客户',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [0],
        options: [
          { value: 0, label: '供应商' },
          { value: 1, label: '客户' },
        ],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '交易金额同比',
        fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '交易金额同比 交易金额同比 > 50% ',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        defaultValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '变更趋势',
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '变更趋势 1增加, 2减少',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: ChangeStatusMap,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'announcementDate',
          order: 'DESC',
          fieldSnapshot: 'AnnouncementDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/QccSearch/List/GetSupplierCustomerV2',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业供应商和客户信息',
  },

  [DimensionTypeEnums.OutwardInvestmentChange]: {
    key: DimensionTypeEnums.OutwardInvestmentChange,
    name: '对外投资变更信息',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.ActualController],
        options: CaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 0,
      },
      {
        fieldName: '直接持股比例',
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '持股比例>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
        defaultValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '企业状态',
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        dataType: DimensionFieldTypeEnums.String,
        comment: '企业状态 注销99, 吊销90',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['99', '90'],
        options: EntStatusList,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },

      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'startdate',
          order: 'DESC',
          fieldSnapshot: 'StartDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/ECILocal/SearchByPromoterWithFundedRatioByKeyNo',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '对外投资变更信息',
  },

  [DimensionTypeEnums.OutwardInvestment]: {
    key: DimensionTypeEnums.OutwardInvestment,
    name: '对外投资信息',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.ActualController],
        options: CaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 0,
      },
      {
        fieldName: '直接持股比例',
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '持股比例>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
        defaultValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '企业状态',
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        dataType: DimensionFieldTypeEnums.String,
        comment: '企业状态 注销99, 吊销90',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['99', '90'],
        options: EntStatusList,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '成立日期',
        fieldKey: DimensionFieldKeyEnums.registerDate,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '成立日期： 近xx天',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
        defaultValue: [365],
        options: [{ unit: '天', min: 1, max: 365 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      {
        fieldName: '经营范围',
        fieldKey: DimensionFieldKeyEnums.companySocpe,
        dataType: DimensionFieldTypeEnums.String,
        comment: '企业经营范围关键词',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['土地开发', '地产开发', '商品房销售', '房地产项目投资'],
        options: [
          { value: '土地开发', label: '土地开发' },
          { value: '地产开发', label: '地产开发' },
          { value: '商品房销售', label: '商品房销售' },
          { value: '房地产项目投资', label: '房地产项目投资' },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
      {
        fieldName: '企查查行业',
        fieldKey: DimensionFieldKeyEnums.qccIndustry,
        dataType: DimensionFieldTypeEnums.String,
        comment: '企查查行业',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['28-2801'],
        options: [{ value: '28-2801', label: '房地产开发' }],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 5,
      },
      {
        fieldName: '国标行业',
        fieldKey: DimensionFieldKeyEnums.companyIndustry,
        dataType: DimensionFieldTypeEnums.String,
        comment: '国标行业',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['K-70-701'],
        options: [{ value: 'K-70-701', label: '房地产开发经营' }],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 6,
      },
      {
        fieldName: '企业名称',
        fieldKey: DimensionFieldKeyEnums.companyName,
        dataType: DimensionFieldTypeEnums.String,
        comment: '企业名称包含关键词',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['房地产开发'],
        options: [
          { value: '房地产开发', label: '房地产开发' },
          { value: '小额贷款', label: '小额贷款' },
          { value: '互联网金融', label: '互联网金融' },
          { value: '典当', label: '典当' },
          { value: '保理', label: '保理' },
          { value: '担保', label: '担保' },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 7,
      },
      {
        fieldName: '排除企业名称',
        fieldKey: DimensionFieldKeyEnums.excludeCompanyName,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排除企业名称包含关键词',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['有限合伙'],
        options: [{ value: '有限合伙', label: '有限合伙' }],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 8,
      },

      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'startdate',
          order: 'DESC',
          fieldSnapshot: 'StartDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/ECILocal/SearchByPromoterWithFundedRatioByKeyNo',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '对外投资信息',
  },
  [DimensionTypeEnums.ActuralControllerInformation]: {
    key: DimensionTypeEnums.ActuralControllerInformation,
    name: '实控人信息',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '总持股比例',
        fieldKey: DimensionFieldKeyEnums.percentTotal,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '总持股比例<50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
        defaultValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '控制时间',
        fieldKey: DimensionFieldKeyEnums.controllerTime,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '控制时间: 超过XX年',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        defaultValue: [1],
        options: [{ unit: '月', min: 1, max: 999 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '是否国资委',
        fieldKey: DimensionFieldKeyEnums.isStateOwned,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '是否国资委 1-是，2-否',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: IsStateOwnedConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/Relation/GetSuspectedActualControllerV5',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '实控人信息',
  },

  [DimensionTypeEnums.ShareholderInformation]: {
    key: DimensionTypeEnums.ShareholderInformation,
    name: '股东信息',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '持股比例',
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '持股比例>20%',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
        defaultValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '股东角色',
        fieldKey: DimensionFieldKeyEnums.shareholdRole,
        dataType: DimensionFieldTypeEnums.String,
        comment: '股东角色： majorShareholder-大股东 actualController-实际控制人 beneficialOwner-受益所有人',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: ['majorShareholder'],
        options: ShareholdRoleConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '是否是投资机构',
        fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '是否有投资机构 1-是，2-否',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: IsInstitutionalInvestorConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'stockpercent',
          order: 'DESC',
          fieldSnapshot: 'StockPercentValue',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/ECILocal/GetPartnerWithGroup',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '股东信息',
  },

  [DimensionTypeEnums.ControllerCompany]: {
    key: DimensionTypeEnums.ControllerCompany,
    name: '控制企业',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.ActualController],
        options: CaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 0,
      },
      {
        fieldName: '成立日期',
        fieldKey: DimensionFieldKeyEnums.registerDate,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '成立日期： 近xx天',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
        defaultValue: [90],
        options: [{ unit: '天', min: 1, max: 365 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '总持股比例',
        fieldKey: DimensionFieldKeyEnums.percentTotal,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '总持股比例<50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
        defaultValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '实缴资本异常',
        fieldKey: DimensionFieldKeyEnums.realRegistrationError,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '实缴资本异常 1增加, 2减少',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: RealRegistrationErrorMap,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      {
        fieldName: '企业状态',
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        dataType: DimensionFieldTypeEnums.String,
        comment: '企业状态 注销99, 吊销90',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['99', '90'],
        options: EntStatusList,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'startdate',
          order: 'DESC',
          fieldSnapshot: 'StartDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 104, [0]),
    ],
    sourcePath: '/api/VIP/GetHoldingCompany',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '控制企业',
  },

  [DimensionTypeEnums.RelatedCompanyChange]: {
    key: DimensionTypeEnums.RelatedCompanyChange,
    name: '关联方企业变化',
    dimensionFields: RelatedCompanyChangeDimensionFields,
    source: DimensionSourceEnums.RelatedCompany,
    sourcePath: '/relatedParty/companyStatus',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业多个关联方成员发生变化',
  },
  [DimensionTypeEnums.PledgeMerger]: {
    key: DimensionTypeEnums.PledgeMerger,
    name: '动产抵押',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 101, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 102, [1]),
      GetFields(DimensionFieldKeyEnums.range, 103, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 104, [
        {
          field: 'registerstartdate',
          order: 'DESC',
          fieldSnapshot: 'registerstartdate',
        },
      ]),
    ],
    source: DimensionSourceEnums.PledgeMergerES,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.AssetInvestigationAndFreezing]: {
    key: DimensionTypeEnums.AssetInvestigationAndFreezing,
    name: '资产查冻',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 101, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 102, [1]),
      GetFields(DimensionFieldKeyEnums.range, 103, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 104, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'publishdate',
        },
      ]),
      {
        fieldName: '案件状态',
        fieldKey: DimensionFieldKeyEnums.assetFreezeStatus,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '案件状态，1 查封 2 解封',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: [
          { label: '查封', value: 1 },
          { label: '解封', value: 2 },
        ],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 6,
      },
    ],
    source: DimensionSourceEnums.AssetES,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.ListedEntityRiskChange]: {
    key: DimensionTypeEnums.ListedEntityRiskChange,
    name: '财务变动风险动态',
    dimensionFields: [
      ...RelatedRiskChangeDimensionFields,
      GetFields(DimensionFieldKeyEnums.cycle, 101, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 102, [1]),
      GetFields(DimensionFieldKeyEnums.range, 103, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 104, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.IntellectualPropertyPledge]: {
    key: DimensionTypeEnums.IntellectualPropertyPledge,
    name: '知识产权出资',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '出质知产类型',
        fieldKey: DimensionFieldKeyEnums.pledgeType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '出质知产类型：1专利 2商标',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        fieldValue: [1, 2],
        options: PledgeTypeConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/QccSearch/List/IPRPledge',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '知识产权出资信息',
  },
  [DimensionTypeEnums.ScientificAchievement]: {
    key: DimensionTypeEnums.ScientificAchievement,
    name: '科技成果',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'registdate',
          order: 'DESC',
          fieldSnapshot: 'RegistDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/Enterprise/Achievements',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '科技成果信息',
  },
  [DimensionTypeEnums.InternationPatent]: {
    key: DimensionTypeEnums.InternationPatent,
    name: '国际专利信息',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '国际专利状态',
        fieldKey: DimensionFieldKeyEnums.internationPatentStatus,
        dataType: DimensionFieldTypeEnums.String,
        comment: '国际专利状态',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        fieldValue: ['ZT005002', 'ZT006002'],
        options: InternationPatentStatusConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'applicationdate',
          order: 'DESC',
          fieldSnapshot: 'ApplicationDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/QccSearch/SingleApp/InternationalPatent',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '国际专利信息',
  },
  [DimensionTypeEnums.PatentInfo]: {
    key: DimensionTypeEnums.PatentInfo,
    name: '专利信息',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '是否转让获得',
        fieldKey: DimensionFieldKeyEnums.patentIsTransfer,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '是否是转让获得： 1-是，0-否',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: PatentIsTransferConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '专利类型',
        fieldKey: DimensionFieldKeyEnums.patentType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '专利类型： "1": 发明公布 "2": 发明授权 "4": 外观设计',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['1', '2', '4'],
        options: PatentTypeConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
      {
        fieldName: '专利状态',
        fieldKey: DimensionFieldKeyEnums.patentStatus,
        dataType: DimensionFieldTypeEnums.String,
        comment:
          "'ZT002001': '有效-授权', 'ZT002003': '有效-部分无效', 'ZT001001': '审中-公布', 'ZT001003': '审中-实质审查', 'ZT003001': '无效-公布驳回', 'ZT003002': '无效-公布撤回', 'ZT003003': '无效-公布视为撤回', 'ZT003010': '无效-公布视为放弃'",
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['ZT002001'],
        options: PatentStatusConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 5,
      },
      {
        fieldName: '历史专利',
        fieldKey: DimensionFieldKeyEnums.isHistoryPatent,
        dataType: DimensionFieldTypeEnums.String,
        comment: '历史专利： 1: 是 2: 不是',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: IsHistoryPatentConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 9,
      },
      {
        fieldName: '专利流出方向',
        fieldKey: DimensionFieldKeyEnums.patentOutflow,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '专利流出方向 1:个人 2:关联方 3:非关联方',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1, 2, 3],
        options: PatentOutflowConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 10,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'applicationdate',
          order: 'DESC',
          fieldSnapshot: 'ApplicationDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/QccSearch/IPRSearch/Patent',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '专利信息',
  },
  [DimensionTypeEnums.BatchCompanyDetail]: {
    key: DimensionTypeEnums.BatchCompanyDetail,
    name: '批量查询企业详情',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '企业详情排查范围',
        fieldKey: DimensionFieldKeyEnums.CompanyDetailSelectedScopes,
        dataType: DimensionFieldTypeEnums.String,
        comment: '企业详情排查范围',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [CompanyDetailScopeEnums.ShortStatus],
        options: [{ value: 'ShortStatus', label: '经营状态' }],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
      {
        fieldName: '企业状态',
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        dataType: DimensionFieldTypeEnums.String,
        comment: '企业状态：注销、吊销',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['99', '90'],
        options: EntStatusList,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'registdate',
          order: 'DESC',
          fieldSnapshot: 'RegistDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
      GetFields(DimensionFieldKeyEnums.hitCount, 105, [20]),
    ],
    sourcePath: '/api/ECILocal/GetDetailsAllInOne',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '批量查询企业详情',
  },
  [DimensionTypeEnums.PatentAnalysis]: {
    key: DimensionTypeEnums.PatentAnalysis,
    name: '专利分析',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '专利统计维度',
        fieldKey: DimensionFieldKeyEnums.patentStatistics,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '专利统计维度 1-发明专利占比',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: PatentStatisticsConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '年份周期',
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '年份周期： 1: 一期 2: 二期 3: 三期',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1],
        options: YearPeriodType,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '区间左值',
        fieldKey: DimensionFieldKeyEnums.leftRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '专利占比左区间>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [50],
        options: [{ unit: '%', min: 50, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '区间右值',
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '占比右区间>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [50],
        options: [{ unit: '%', min: 50, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      {
        fieldName: '专利类型',
        fieldKey: DimensionFieldKeyEnums.patentType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '专利类型： "1": 发明公布 "2": 发明授权 "4": 外观设计',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['1', '2', '4'],
        options: PatentTypeConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
      {
        fieldName: '专利状态',
        fieldKey: DimensionFieldKeyEnums.patentStatus,
        dataType: DimensionFieldTypeEnums.String,
        comment:
          "'ZT002001': '有效-授权', 'ZT002003': '有效-部分无效', 'ZT001001': '审中-公布', 'ZT001003': '审中-实质审查', 'ZT003001': '无效-公布驳回', 'ZT003002': '无效-公布撤回', 'ZT003003': '无效-公布视为撤回', 'ZT003010': '无效-公布视为放弃'",
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['ZT002001'],
        options: PatentStatusConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 5,
      },
      {
        fieldName: '专利稳定性',
        fieldKey: DimensionFieldKeyEnums.patentStable,
        dataType: DimensionFieldTypeEnums.String,
        comment: '专利稳定性： 3: 任意3个年度有专利申请 2: 任意2个年度有专利申请 1: 任意1个年度有专利申请 0 无专利申请',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [3],
        options: PatentStableConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 7,
      },
      {
        fieldName: '历史专利',
        fieldKey: DimensionFieldKeyEnums.isHistoryPatent,
        dataType: DimensionFieldTypeEnums.String,
        comment: '历史专利： 1: 是 2: 不是',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: IsHistoryPatentConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 9,
      },
      {
        fieldName: 'n期平均值X',
        fieldKey: DimensionFieldKeyEnums.avgXn,
        dataType: DimensionFieldTypeEnums.Number,
        comment: 'n期平均值X: Xn=N期内招聘本科以上学历数量/N期内招聘总数量，X=(X1+X2+...+XN)/N',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [80],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 10,
      },
      {
        fieldName: 'n期变异系数CV',
        fieldKey: DimensionFieldKeyEnums.cvXn,
        dataType: DimensionFieldTypeEnums.Number,
        comment: 'n期变异系数CV: n期标准差与平均值的比值 CV < 20',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
        defaultValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 11,
      },
      {
        fieldName: 'n期变异系数CV区间左值',
        fieldKey: DimensionFieldKeyEnums.leftCvXn,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '专利占比左区间>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [50],
        options: [{ unit: '%', min: 50, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: 'n期变异系数CV区间右值',
        fieldKey: DimensionFieldKeyEnums.rightCvXn,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '占比右区间>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [50],
        options: [{ unit: '%', min: 50, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'applicationdate',
          order: 'DESC',
          fieldSnapshot: 'ApplicationDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
    ],
    sourcePath: '/api/QccSearch/IPRSearch/Patent',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '专利信息',
  },
  [DimensionTypeEnums.MainMembersChangeFrequency]: {
    key: DimensionTypeEnums.MainMembersChangeFrequency,
    name: '变更记录',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '董监高法变动频率',
        fieldKey: DimensionFieldKeyEnums.MainMembersChangeFrequency,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '董监高法变动频率',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        defaultValue: [3],
        options: [{ unit: '个', min: 1, max: 50 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '股权变更频率',
        fieldKey: DimensionFieldKeyEnums.equityChangeFrequency,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '股权变更频率',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        defaultValue: [3],
        options: [{ unit: '个', min: 1, max: 50 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
    ],
    sourcePath: '/api/EciLocal/ChangeRecords',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '股权变更频率',
  },
  [DimensionTypeEnums.MainMembersChangeWithinCycle]: {
    key: DimensionTypeEnums.MainMembersChangeWithinCycle,
    name: '周期内主要人员变更',
    source: DimensionSourceEnums.RiskChange,
    dimensionFields: [
      {
        fieldName: '股权变更频率',
        fieldKey: DimensionFieldKeyEnums.equityChangeFrequency,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '股权变更频率',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        defaultValue: [3],
        options: [{ unit: '次', min: 1, max: 50 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '风险动态类型',
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '风险动态类型 72-主要人员变更',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [72],
        options: RiskChangeCategoryList,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '董监高法变动频率',
        fieldKey: DimensionFieldKeyEnums.MainMembersChangeFrequency,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '董监高法变动频率',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        defaultValue: [3],
        options: [{ unit: '个', min: 1, max: 50 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
    ],
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '周期内主要人员变更频率',
  },
  [DimensionTypeEnums.RelatedCompanyMassRegistrationCancellation]: {
    key: DimensionTypeEnums.RelatedCompanyMassRegistrationCancellation,
    name: '关联方企业集中注册或注销',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '命中个数',
        fieldKey: DimensionFieldKeyEnums.hitCount,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '命中个数',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        defaultValue: [3],
        options: [{ unit: '个', min: 1, max: 50 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '关联方异动类型',
        fieldKey: DimensionFieldKeyEnums.relatedPartyAbnormalityType,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '关联方异动类型',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: [
          { value: 1, label: '注册' },
          { value: 2, label: '注销' },
        ],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
    ],
    sourcePath: '/relatedParty/companyStatus',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '关联方企业集中注册或注销',
  },
  [DimensionTypeEnums.ProvincialHonor]: {
    key: DimensionTypeEnums.ProvincialHonor,
    name: '省级以上荣誉',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '省级荣誉数量',
        fieldKey: DimensionFieldKeyEnums.provincialHonorCount,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '省级荣誉数量',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        defaultValue: [3],
        options: [{ unit: '个', min: 1, max: 50 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '有无省级以上荣誉被取消',
        fieldKey: DimensionFieldKeyEnums.HasCertificationRevoked,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '有无省级以上荣誉被取消',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1],
        options: hasCertificationRevoked,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '荣誉等级',
        fieldKey: DimensionFieldKeyEnums.honorLevel,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '荣誉等级',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1],
        options: [
          { value: 0, label: '无法判断' },
          { value: 1, label: '国家级' },
          { value: 2, label: '省级' },
          { value: 3, label: '市级' },
          { value: 4, label: '区/县级' },
          { value: 5, label: '园区级' },
        ],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
    ],
    sourcePath: '/api/QccSearch/List/HonorCertificationV2',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '省级以上荣誉',
  },
  [DimensionTypeEnums.EquityStructureAbnormal]: {
    key: DimensionTypeEnums.EquityStructureAbnormal,
    name: '股权结构异常',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '关联层级',
        fieldKey: DimensionFieldKeyEnums.depth,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '关联层级 1,2,3',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '企业有无自然人股东',
        fieldKey: DimensionFieldKeyEnums.NoNaturalPersonShareholder,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '企业有无自然人股东',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1],
        options: HasCompanyNaturalPersonShareholder,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '企业关联方循环持股',
        fieldKey: DimensionFieldKeyEnums.CircularShareholdingBetweenRelatedParties,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '企业有无关联方循环持股',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1],
        options: HasCompanyCircularShareholder,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '企业一级股东异常',
        fieldKey: DimensionFieldKeyEnums.primaryShareholderAbnormality,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '企业一级股东异常',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [],
        options: HasCompanyNaturalPersonShareholder,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '企业二级股东异常',
        fieldKey: DimensionFieldKeyEnums.secondaryShareholderAbnormality,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '企业二级股东异常',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [],
        options: HasCompanyNaturalPersonShareholder,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
    ],
    sourcePath: '/deep/loopInvestment',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '股权结构异常',
  },
  [DimensionTypeEnums.EmployeeStockPlatform]: {
    key: DimensionTypeEnums.EmployeeStockPlatform,
    name: '员工持股平台',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '企业有无员工持股平台',
        fieldKey: DimensionFieldKeyEnums.HasEmployeeStockPlatform,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '企业有无员工持股平台',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1],
        options: hasEmployeeStockPlatform,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
    ],
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '员工持股平台',
  },
  [DimensionTypeEnums.EquityFinancing]: {
    key: DimensionTypeEnums.EquityFinancing,
    name: '股权融资',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '投资机构的上榜榜单来源',
        fieldKey: DimensionFieldKeyEnums.sourcesInvestInstiteRank,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '投资机构的上榜榜单来源: 中文匹配',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        options: SourcesInvestInstiteRankConstant,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '是否是投资机构',
        fieldKey: DimensionFieldKeyEnums.isInstitutionalInvestor,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '是否有投资机构 1-是，2-否',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: IsInstitutionalInvestorConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '是否产业链核型企业',
        fieldKey: DimensionFieldKeyEnums.isIndustrialChainCoreCompany,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '是否产业链核心企业 1-是，2-不是',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: IndustrialChainCoreCompanyContant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'financedate',
          order: 'DESC',
          fieldSnapshot: 'FinanceDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
      GetFields(DimensionFieldKeyEnums.hitCount, 3, [20]),
    ],
    sourcePath: '/api/Financial/Event/SearchInvestEvents',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '股权融资信息',
  },
  [DimensionTypeEnums.RecruitmentAnalysis]: {
    key: DimensionTypeEnums.RecruitmentAnalysis,
    name: '招聘分析',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '统计维度',
        fieldKey: DimensionFieldKeyEnums.recruitmentStatistics,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '统计维度 1-本科以上招聘占比',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: RecruitmentStatisticsConstant,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '年份周期',
        fieldKey: DimensionFieldKeyEnums.yearPeriod,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '年份周期： 1: 一期 2: 二期 3: 三期',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1],
        options: YearPeriodType,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: 'n期平均值X',
        fieldKey: DimensionFieldKeyEnums.avgXn,
        dataType: DimensionFieldTypeEnums.Number,
        comment: 'n期平均值X: Xn=N期内招聘本科以上学历数量/N期内招聘总数量，X=(X1+X2+...+XN)/N',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [80],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      {
        fieldName: 'n期变异系数CV',
        fieldKey: DimensionFieldKeyEnums.cvXn,
        dataType: DimensionFieldTypeEnums.Number,
        comment: 'n期变异系数CV: n期标准差与平均值的比值 CV < 20',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
        defaultValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
      {
        fieldName: 'n期变异系数CV区间左值',
        fieldKey: DimensionFieldKeyEnums.leftCvXn,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '专利占比左区间>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [50],
        options: [{ unit: '%', min: 50, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: 'n期变异系数CV区间右值',
        fieldKey: DimensionFieldKeyEnums.rightCvXn,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '占比右区间>50%',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [50],
        options: [{ unit: '%', min: 50, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.cycle, 100, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 102, [
        {
          field: 'publishtime',
          order: 'DESC',
          fieldSnapshot: 'PublishTime',
        },
      ]),
    ],
    sourcePath: '/api/QccSearch/SingleApp/Recruitment',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业招聘信息',
  },
  [DimensionTypeEnums.QCCCreditRate]: {
    key: DimensionTypeEnums.QCCCreditRate,
    name: '企查分',
    source: DimensionSourceEnums.EnterpriseLib,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#分</em>',
    type: IndicatorTypeEnums.generalItems,
    dimensionFields: [
      {
        fieldName: '企查分',
        fieldKey: DimensionFieldKeyEnums.qccCreditScore,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '企查分信用评分大于等于0分',
        defaultValue: [0],
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        options: [{ unit: '分', min: 0, max: 10000 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
    ],
    description: '该企业企查分异常',
  },
  [DimensionTypeEnums.FinancialInstitution]: {
    key: DimensionTypeEnums.FinancialInstitution,
    name: '金融机构',
    dimensionFields: [
      {
        fieldName: '金融机构类型',
        fieldKey: DimensionFieldKeyEnums.financialInstitutionType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '数据频道机构类型',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['1', '3', '4', '6', '2', '5', '9', '14'],
        options: FinancialInstitutions,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/GetFinancialInstitutions',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业命中金融机构',
  },
  [DimensionTypeEnums.RealCapitalException]: {
    key: DimensionTypeEnums.RealCapitalException,
    name: '实缴异常',
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.duration, 1, [60], DimensionFieldCompareTypeEnums.GreaterThanOrEqual),
      {
        fieldName: '实缴比例',
        fieldKey: DimensionFieldKeyEnums.registrationRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '注册资本与实缴资本之比小于 100%',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
        defaultValue: [100],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
    ],
    description: '该企业实缴异常',
  },
  [DimensionTypeEnums.QfkRisk6302]: {
    key: DimensionTypeEnums.QfkRisk6302,
    name: '员工数据不明',
    source: DimensionSourceEnums.Pro,
    sourcePath: '',
    // detailSource: DimensionSourceEnums.Pro,
    // detailSourcePath: 'getControlScatterDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6302',
    description: '该企业年报信息中未披露员工数据',
  },
  [DimensionTypeEnums.OvsSanction]: {
    key: DimensionTypeEnums.OvsSanction,
    name: '出口管制合规风险',
    dimensionFields: [
      {
        fieldName: '制裁名单',
        fieldKey: DimensionFieldKeyEnums.sanctionListCodes,
        dataType: DimensionFieldTypeEnums.String,
        comment: '默认值为 出口管制合规风险企业清单 [ForeignExportControlList]',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['ForeignExportControlList'],
        options: SanctionsListCode,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 4, [3]),
      GetFields(DimensionFieldKeyEnums.hitCount, 3, [20]),
    ],
    source: DimensionSourceEnums.OvsSanctionES,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '出口管制合规风险',
  },
  [DimensionTypeEnums.QfkRisk6612]: {
    key: DimensionTypeEnums.QfkRisk6612,
    name: '实际控制人控制企业边境贸易区占比较高',
    source: DimensionSourceEnums.CompanyDetail,
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'startDate',
          order: 'DESC',
          fieldSnapshot: 'startDate',
        },
      ]),
    ],
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getActualContrCtlInTradeZoneHighRateDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6612',
    description: '实际控制人控制企业边境贸易区占比较高',
  },
  [DimensionTypeEnums.QfkRisk6803]: {
    key: DimensionTypeEnums.QfkRisk6803,
    name: '控制权分散',
    source: DimensionSourceEnums.Pro,
    sourcePath: 'getControlScatterDetail',
    // detailSource: DimensionSourceEnums.Pro,
    // detailSourcePath: 'getControlScatterDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6803',
    description: '控制权分散',
  },
  [DimensionTypeEnums.QfkRisk6615]: {
    key: DimensionTypeEnums.QfkRisk6615,
    name: '实际控制人无法识别',
    source: DimensionSourceEnums.Pro,
    sourcePath: '',
    // detailSource: DimensionSourceEnums.Pro,
    // detailSourcePath: 'getLegalRepresentActualDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6615',
    description: '实际控制人无法识别或穿透边界以外',
  },
  [DimensionTypeEnums.QfkRisk1410]: {
    key: DimensionTypeEnums.QfkRisk1410,
    name: '来自高风险I类国家或地区',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getISanctionedCountriesDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '1410',
    description: '来自高风险I类国家或地区',
  },
  [DimensionTypeEnums.QfkRisk1411]: {
    key: DimensionTypeEnums.QfkRisk1411,
    name: '来自 II 类、III 类高风险详情',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getIIAndIIISanctionedCountriesDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '1411',
    description: '来自 II 类、III 类高风险详情',
  },
  [DimensionTypeEnums.QfkRisk1312]: {
    key: DimensionTypeEnums.QfkRisk1312,
    name: '支付或者融资担保业务许可被中止续展或已到期',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getPaymentRetireDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '1312',
    description: '支付或者融资担保业务许可被中止续展或已到期',
  },
  [DimensionTypeEnums.QfkRisk7099]: {
    key: DimensionTypeEnums.QfkRisk7099,
    name: '关联方企业集中注册且均无实缴资本',
    source: DimensionSourceEnums.Pro,
    sourcePath: 'getRelatedCorpMemberCentralRegisterDetail',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'startDate',
          order: 'DESC',
          fieldSnapshot: 'startDate',
        },
      ]),
    ],
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '7099',
    description: '关联方企业集中注册且均无实缴资本',
  },
  [DimensionTypeEnums.QfkRisk6609]: {
    key: DimensionTypeEnums.QfkRisk6609,
    name: '实际控制人控制企业集中注册且均无实缴资本',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getActualContrCtlNoPaidDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6609',
    description: '实际控制人控制企业集中注册且均无实缴资本',
  },
  [DimensionTypeEnums.QfkRisk6709]: {
    key: DimensionTypeEnums.QfkRisk6709,
    name: '法定代表人控制企业集中注册且均无实缴资本',
    source: DimensionSourceEnums.Pro,
    sourcePath: 'getLegalFocusNoPaidDetail',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'startDate',
          order: 'DESC',
          fieldSnapshot: 'startDate',
        },
      ]),
    ],
    // detailSource: DimensionSourceEnums.Pro,
    // detailSourcePath: 'getActualContrCtlNoPaidDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6709',
    description: '法定代表人控制企业集中注册且均无实缴资本',
  },
  [DimensionTypeEnums.QfkRisk6710]: {
    key: DimensionTypeEnums.QfkRisk6710,
    name: '法定代表人控制企业涉及高风险行业',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getLegalHighRisklDetail',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'startDate',
          order: 'DESC',
          fieldSnapshot: 'startDate',
        },
      ]),
    ],
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6710',
    description: '法定代表人控制企业涉及高风险行业',
  },
  [DimensionTypeEnums.QfkRisk2010]: {
    key: DimensionTypeEnums.QfkRisk2010,
    name: '联系方式或注册地址重复',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getSameTelListDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '2010',
    description: '联系方式或注册地址重复',
  },
  [DimensionTypeEnums.QfkRisk2310]: {
    key: DimensionTypeEnums.QfkRisk2310,
    name: '同实际控制人企业众多增加不确定风险',
    source: DimensionSourceEnums.Pro,
    sourcePath: 'getLegalRepresentActualDetail',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'percentTotal',
          order: 'DESC',
          fieldSnapshot: 'percentTotalNumber',
        },
      ]),
    ],
    // detailSource: DimensionSourceEnums.Pro,
    // detailSourcePath: 'getLegalRepresentActualDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '2310',
    description: '同实际控制人企业众多增加不确定风险',
  },
  [DimensionTypeEnums.QfkRisk6802]: {
    key: DimensionTypeEnums.QfkRisk6802,
    name: '所有权与经营权分离',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getSeparationOwnerManageDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6802',
    description: '该企业主要人员与自然人股东无重叠',
  },
  [DimensionTypeEnums.QfkRisk6610]: {
    key: DimensionTypeEnums.QfkRisk6610,
    name: '实际控制人控制企业涉及高风险行业',
    source: DimensionSourceEnums.CompanyDetail,
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'startDate',
          order: 'DESC',
          fieldSnapshot: 'startDate',
        },
      ]),
    ],
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getActualContrCtlHighRiskScopeDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6610',
    description: '实际控制人控制企业涉及高风险行业',
  },
  [DimensionTypeEnums.QfkRisk6907]: {
    key: DimensionTypeEnums.QfkRisk6907,
    name: '注册资本降幅过大',
    source: DimensionSourceEnums.Pro,
    sourcePath: 'getCorpCapitalFrequencyChangeDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6907',
    description: '注册资本降幅过大',
  },
  [DimensionTypeEnums.QfkRisk6611]: {
    key: DimensionTypeEnums.QfkRisk6611,
    name: '实际控制人控制企业位于边境贸易区',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getActualContrCtlInTradeZoneDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6611',
    description: '实际控制人控制企业位于边境贸易区',
  },
  [DimensionTypeEnums.QfkRisk2210]: {
    key: DimensionTypeEnums.QfkRisk2210,
    name: '同法定代表人企业众多且地区分散',
    source: DimensionSourceEnums.Pro,
    sourcePath: 'getEnterprisesInDiffRegionsDetail',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'stockPercent',
          order: 'DESC',
          fieldSnapshot: 'stockPercentNumber',
        },
        {
          field: 'regCap',
          order: 'DESC',
          fieldSnapshot: 'regCapNumber',
        },
      ]),
    ],
    // detailSource: DimensionSourceEnums.Pro,
    // detailSourcePath: 'getEnterprisesInDiffRegionsDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '2210',
    description: '同法定代表人企业众多且地区分散',
  },
  [DimensionTypeEnums.BeneficialOwnersControlNumerousEnterprises]: {
    key: DimensionTypeEnums.BeneficialOwnersControlNumerousEnterprises,
    name: '受益所有人控制企业众多',
    source: DimensionSourceEnums.CompanyDetail,
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'startDate',
          order: 'DESC',
          fieldSnapshot: 'startDate',
        },
      ]),
    ],
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getBeneficialOwnerMultiCtlDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">受益所有人控制#count#条企业</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6613',
    description: '受益所有人控制企业众多',
  },
  [DimensionTypeEnums.ReviewAndInvestigation]: {
    key: DimensionTypeEnums.ReviewAndInvestigation,
    name: '审查调查',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'getAcctExecutiveReivewDetail',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '7203',
    description: '企业主要人员受到的执纪审查、党纪政务处分信息',
  },
  [DimensionTypeEnums.RelatedCompanies]: {
    key: DimensionTypeEnums.RelatedCompanies,
    name: '关联方企业',
    dimensionFields: RelatedCompaniesDimensionFields,
    source: DimensionSourceEnums.RelatedCompany,
    sourcePath: '/relatedParty/companyStatus',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业多个关联方成员存在注销或吊销信息',
  },
  [DimensionTypeEnums.BusinessAnomalies]: {
    key: DimensionTypeEnums.BusinessAnomalies,
    name: '关联方异常信息',
    dimensionFields: [
      {
        fieldName: '关联方类型',
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '关联方类型',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [NebulaRelatedEdgeEnums.Employ],
        options: RelatedTypeMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '风险类型',
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '关联方风险类型：经营异常',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [NebulaRiskTagEnums.Exception],
        options: RelatedRiskTypesMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 2, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 3, [1]),
    ],
    source: DimensionSourceEnums.RelatedCompany,
    sourcePath: '/relatedParty/companyException',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业多个关联方成员存在异常信息',
  },
  [DimensionTypeEnums.SeriousViolation]: {
    key: DimensionTypeEnums.SeriousViolation,
    name: '关联方违法事项',
    dimensionFields: [
      {
        fieldName: '关联方类型',
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '关联方类型',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [NebulaRelatedEdgeEnums.Employ],
        options: RelatedTypeMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '风险类型',
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '风险类型：经营异常、严重违法、失信被执行人、税收违法',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [NebulaRiskTagEnums.SeriousViolation, NebulaRiskTagEnums.BadCreditExecuted, NebulaRiskTagEnums.TaxIllegal],
        options: RelatedRiskTypesMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 3, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 4, [1]),
    ],
    source: DimensionSourceEnums.RelatedCompany,
    sourcePath: '/relatedParty/seriousViolation',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业关联方存在严重违法事项',
  },
  [DimensionTypeEnums.BusinessAnomaliesWithSamePhoneAndAddress]: {
    key: DimensionTypeEnums.BusinessAnomaliesWithSamePhoneAndAddress,
    name: '同地址或同联系方式关联方异常信息',
    dimensionFields: BusinessAnomaliesWithSamePhoneAndAddressDimensionFields,
    source: DimensionSourceEnums.RelatedCompany,
    sourcePath: '/relatedParty/sameContactException',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '同联系方式或同地址企业存在异常',
  },
  [DimensionTypeEnums.MoneyLaundering]: {
    key: DimensionTypeEnums.MoneyLaundering,
    name: '关联方刑事案件',
    dimensionFields: MoneyLaunderingDimensionFields,
    source: DimensionSourceEnums.RelatedCompany,
    sourcePath: '/relatedParty/caseOfMoneyLaundering',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业关联方成员曾发生过洗钱类刑事案件',
  },
  [DimensionTypeEnums.RelatedAnnouncement]: {
    key: DimensionTypeEnums.RelatedAnnouncement,
    name: '关联方开庭公告',
    dimensionFields: RelatedAnnouncementDimensionFields,
    source: DimensionSourceEnums.RelatedCompany,
    sourcePath: '/relatedParty/announcement',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '关联方成员企业有开庭公告信息',
  },
  [DimensionTypeEnums.CourtSessionAnnouncement]: {
    key: DimensionTypeEnums.CourtSessionAnnouncement,
    name: '开庭公告',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.isValid, 0, [1]),
      GetFields(DimensionFieldKeyEnums.cycle, 1, [-1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 2, [0]),
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'liandate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
      {
        fieldName: '角色类型',
        fieldKey: DimensionFieldKeyEnums.courtType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '角色类型： prosecutor-原告 defendant-被告 third-第三人 others-其他',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: ['prosecutor'],
        options: CourtType,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
      {
        fieldName: '案由',
        fieldKey: DimensionFieldKeyEnums.courtCaseReason,
        dataType: DimensionFieldTypeEnums.String,
        // https://thoughts.teambition.com/workspaces/6321a5f1cb218e00459c527d/docs/6686034262ca100001ac5edf
        comment: '角色类型：买卖合同纠纷',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: ['买卖合同纠纷'],
        options: CourtCaseReason,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 5,
      },
      {
        fieldName: '身份',
        fieldKey: DimensionFieldKeyEnums.courtRole,
        dataType: DimensionFieldTypeEnums.String,
        comment: '身份类型：11-原告, 13-上诉人, 12-申请执行人, 14-申请人, 21-被告, 23-被上诉人, 22-被执行人, 24-被申请人, 91-第三人, 99-其他',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['11'],
        options: CourtRole,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 6,
      },
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/GetCourtNoticeList',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业开庭公告',
  },
  [DimensionTypeEnums.ExternalRelatedRisk]: {
    key: DimensionTypeEnums.ExternalRelatedRisk,
    name: '外部关联风险',
    dimensionFields: [
      {
        fieldName: '关联关系',
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '关联方角色类型：-1:不限 1 法人 2 股东 3 主要人员 4 对外投资 5 分支机构，默认不限',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [-1],
        options: RelatedRoleTypeMap,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '风险类型',
        fieldKey: DimensionFieldKeyEnums.relatedRiskType,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '风险类型: 22 失信被执行人;  24 被执行人;  23 限制高消费;  25 终本案件; 41 严重违法; 42 行政处罚; 53 经营异常',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [22],
        options: RelatedRiskTypeMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 2, [3]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: 'RiskScan/RelatedRiskV3',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在外部关联风险',
  },
  [DimensionTypeEnums.ViolationProcessings]: {
    key: DimensionTypeEnums.ViolationProcessings,
    name: '违规处理',
    dimensionFields: [
      {
        fieldName: '关联类型',
        fieldKey: DimensionFieldKeyEnums.relationShips,
        dataType: DimensionFieldTypeEnums.String,
        comment: '与处罚主体关系类型',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['1', '3', '5'],
        options: RelationShipList,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 2, [3]),
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'publicdate',
          order: 'DESC',
          fieldSnapshot: 'publicdate',
        },
      ]),
    ],
    source: DimensionSourceEnums.Violation,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '违规处理',
  },
  [DimensionTypeEnums.RiskChange]: {
    key: DimensionTypeEnums.RiskChange,
    name: '风险动态',
    dimensionFields: [
      ...RiskChangeDimensionFields,
      GetFields(DimensionFieldKeyEnums.cycle, 100, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.range, 102, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 103, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 105, [1]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.RelatedRiskChange]: {
    key: DimensionTypeEnums.RelatedRiskChange,
    name: '关联方风险动态',
    dimensionFields: [
      {
        fieldName: '风险动态类型',
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '风险动态类型 129-上市进程',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [129],
        options: RiskChangeCategoryList,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '关联方类型',
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '关联方类型',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [RelatedTypeEnums.MajorityInvestment],
        options: [{ value: RelatedTypeEnums.MajorityInvestment, label: '子公司（对外投资(>50%的企业)）' }],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 100, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 101, [1]),
      GetFields(DimensionFieldKeyEnums.range, 102, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 103, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.RecentInvestCancellationsRiskChange]: {
    key: DimensionTypeEnums.RecentInvestCancellationsRiskChange,
    name: '近期对外投资企业大量注销或吊销',
    dimensionFields: [
      ...RelatedRiskChangeDimensionFields,
      GetFields(DimensionFieldKeyEnums.hitCount, 1, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 9, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 11, [1]),
      GetFields(DimensionFieldKeyEnums.range, 13, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 15, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.ActualControllerRiskChange]: {
    key: DimensionTypeEnums.ActualControllerRiskChange,
    name: '公司实控人近x个月内新增控股子公司数量超过xx个',
    dimensionFields: [
      ...RelatedRiskChangeDimensionFields,
      GetFields(DimensionFieldKeyEnums.hitCount, 1, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 9, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 11, [1]),
      GetFields(DimensionFieldKeyEnums.range, 13, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 15, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.InvestCompanyRiskChange]: {
    key: DimensionTypeEnums.ControlCompanyRiskChange,
    name: '主体对外投资企业',
    dimensionFields: [
      ...RelatedRiskChangeDimensionFields,
      GetFields(DimensionFieldKeyEnums.hitCount, 1, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 9, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 11, [1]),
      GetFields(DimensionFieldKeyEnums.range, 13, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 15, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.ControlCompanyRiskChange]: {
    key: DimensionTypeEnums.ControlCompanyRiskChange,
    name: '主体控制企业',
    dimensionFields: [
      ...RelatedRiskChangeDimensionFields,
      GetFields(DimensionFieldKeyEnums.hitCount, 1, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 9, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 11, [1]),
      GetFields(DimensionFieldKeyEnums.range, 13, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 15, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.GuarantorRiskChange]: {
    key: DimensionTypeEnums.GuarantorRiskChange,
    name: '主体担保机构',
    dimensionFields: [
      ...RelatedRiskChangeDimensionFields,
      GetFields(DimensionFieldKeyEnums.hitCount, 1, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 9, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 11, [1]),
      GetFields(DimensionFieldKeyEnums.range, 13, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 15, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.SSERelatedRiskChange]: {
    key: DimensionTypeEnums.SSERelatedRiskChange,
    name: '主体上交所规则关联方风险动态',
    dimensionFields: [
      ...RelatedRiskChangeDimensionFields,
      GetFields(DimensionFieldKeyEnums.hitCount, 1, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 9, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 11, [1]),
      GetFields(DimensionFieldKeyEnums.range, 13, [{ min: 1735546427, max: 1735546427 }]),
      GetFields(DimensionFieldKeyEnums.sortField, 15, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置风险动态相关所有维度',
  },
  [DimensionTypeEnums.CompanyDetail]: {
    key: DimensionTypeEnums.CompanyDetail,
    name: '工商基本信息',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.duration, 1, [12]),
      GetFields(DimensionFieldKeyEnums.registrationAmount, 2, [100]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      ...CompanyDetailDimensionFields,
    ],
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置工商基本信息相关所有维度',
  },
  [DimensionTypeEnums.Judgement]: {
    key: DimensionTypeEnums.Judgement,
    name: '裁判文书',
    dimensionFields: [
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: CaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 7,
      },
      {
        fieldName: '涉案金额',
        fieldKey: DimensionFieldKeyEnums.amountInvolved,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '涉案总金额 大于等于 0 元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [0],
        options: [{ unit: '元', min: 0, max: ********9999 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '案件身份',
        fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
        dataType: DimensionFieldTypeEnums.String,
        comment: '裁判文书身份(包含) defendant-被告 prosecutor-原告 thirdpartyrole-第三人',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['defendant', 'prosecutor', 'thirdpartyrole'],
        options: CaseRoleMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '案件类型',
        fieldKey: DimensionFieldKeyEnums.CaseType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '默认值为 xs-刑事案件，ms-民事案件',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['xs'],
        options: CaseTypes,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      {
        fieldName: '案由类别',
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '默认值为 洗钱罪 [A030437]',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [['A030437']],
        options: CaseReasonTypeMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 4, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 5, [-1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'courtdate',
          order: 'DESC',
          fieldSnapshot: 'courtdate',
        },
      ]),
    ],
    source: DimensionSourceEnums.Judgement,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置裁判文书相关所有维度',
  },
  [DimensionTypeEnums.JudicialCase]: {
    key: DimensionTypeEnums.JudicialCase,
    name: '司法案件',
    dimensionFields: [
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: CaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 7,
      },
      {
        fieldName: '案件身份',
        fieldKey: DimensionFieldKeyEnums.JudicialCaseRoleType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '司法身份(包含) P:上诉人，D:被上诉人,O:其他',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['P', 'D', 'O'],
        options: [
          { label: '上诉人', value: 'P' },
          { label: '被上诉人', value: 'D' },
          { label: '其他', value: 'O' },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '案件类型',
        fieldKey: DimensionFieldKeyEnums.CaseType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '默认值为 xs-刑事案件，ms-民事案件',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['xs'],
        options: CaseTypes,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      {
        fieldName: '案件标签',
        fieldKey: DimensionFieldKeyEnums.CaseTag,
        dataType: DimensionFieldTypeEnums.String,
        comment: '默认值为 8 判决文书、6 立案信息、3 开庭公告',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [6],
        options: [
          { label: '判决文书', value: 8 },
          { label: '立案信息', value: 6 },
          { label: '开庭公告', value: 3 },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
      {
        fieldName: '是否执行完毕',
        fieldKey: DimensionFieldKeyEnums.isExecuteFinish,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '默认值为 0',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [0],
        options: [
          { label: '未执行完毕', value: 0 },
          { label: '执行完毕', value: 1 },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
      {
        fieldName: '案由类别',
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '默认值为 洗钱罪 [A030437]',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [['A030437']],
        options: CaseReasonTypeMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 5,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 4, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 5, [-1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'courtdate',
          order: 'DESC',
          fieldSnapshot: 'courtdate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/SingleApp/GetCaseListByKeyNo',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体存在#count#条记录，总金额为#UnfulfilledAmt#',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置司法案件相关所有维度',
  },
  [DimensionTypeEnums.CaseRegistration]: {
    key: DimensionTypeEnums.CaseRegistration,
    name: '立案信息',
    dimensionFields: [
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: CaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 7,
      },
      {
        fieldName: '案件身份',
        fieldKey: DimensionFieldKeyEnums.CaseRegistrationRoleType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '11:原告,13:上诉人,12:申请执行人,14:申请人,21:被告,23:被上诉人,22:被执行人,24:被申请人,91:第三人,99:其他',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['21', '22', '23', '24'],
        options: [
          { label: '原告', value: '11' },
          { label: '上诉人', value: '13' },
          { label: '申请执行人', value: '12' },
          { label: '申请人', value: '14' },
          { label: '被告', value: '21' },
          { label: '被上诉人', value: '23' },
          { label: '被执行人', value: '22' },
          { label: '被申请人', value: '24' },
          { label: '第三人', value: '91' },
          { label: '其他', value: '99' },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '案件类型',
        fieldKey: DimensionFieldKeyEnums.CaseType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '默认值为 xs-刑事案件，ms-民事案件',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['xs'],
        options: CaseTypes,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      {
        fieldName: '案由类别',
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '默认值为 洗钱罪 [A030437]',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [['A030437']],
        options: CaseReasonTypeMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 5,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 4, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 5, [-1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'courtdate',
          order: 'DESC',
          fieldSnapshot: 'courtdate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/GetListOfLiAn',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置司法案件相关所有维度',
  },
  [DimensionTypeEnums.QfkRisk]: {
    key: DimensionTypeEnums.QfkRisk,
    name: '企业风险',
    dimensionFields: [
      {
        fieldName: '企风控风险指标具体指标项',
        fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
        dataType: DimensionFieldTypeEnums.String,
        comment: 'value 1510 - 被列入严重违法失信企业名录, 目前只支持单选',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['1510'],
        options: QfkRiskItemConstants,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
    ],
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '可配置基于专业版所有旧规则指标的有无命中',
  },
  [DimensionTypeEnums.FakeSOES]: {
    key: DimensionTypeEnums.FakeSOES,
    name: '假冒国企',
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 被列为<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '3101',
    description: '企业因假冒国企被公示',
  },
  [DimensionTypeEnums.NoCapital]: {
    key: DimensionTypeEnums.NoCapital,
    name: '久无实缴',
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6310',
    description: '未获得企业实缴资本信息',
  },
  [DimensionTypeEnums.LowCapital]: {
    key: DimensionTypeEnums.LowCapital,
    name: '注册资本过低',
    dimensionFields: [GetFields(DimensionFieldKeyEnums.registrationAmount, 0, [100])],
    source: DimensionSourceEnums.CompanyDetail,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】#operator##amountW#</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '将企业注册资本作为准入门槛',
  },
  [DimensionTypeEnums.CompanyShell]: {
    key: DimensionTypeEnums.CompanyShell,
    name: '疑似空壳企业',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.Pro,
    detailSourcePath: 'shellScan/getResult',
    status: DataStatusEnums.Enabled,
    template: templateString,
    type: IndicatorTypeEnums.generalItems,
    typeCode: '0',
    description: '企业疑似空壳企业',
  },
  //已废弃，原因：误报较多，且信息滞后
  [DimensionTypeEnums.FraudList]: {
    key: DimensionTypeEnums.FraudList,
    name: '涉诈高风险名单',
    source: DimensionSourceEnums.CompanyDetail,
    sourcePath: '',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '1310',
    description: '企业被列入涉诈高风险名单',
    isHidden: true,
  },
  [DimensionTypeEnums.EstablishedTime]: {
    key: DimensionTypeEnums.EstablishedTime,
    name: '新成立企业',
    dimensionFields: [GetFields(DimensionFieldKeyEnums.duration, 0, [12])],
    source: DimensionSourceEnums.CompanyDetail,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【成立时长】#operator##amountMonth#</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '将企业成立时长作为准入门槛',
  },
  [DimensionTypeEnums.Certification]: {
    key: DimensionTypeEnums.Certification,
    name: '资质筛查',
    dimensionFields: [
      {
        fieldName: '营业执照',
        fieldKey: DimensionFieldKeyEnums.businessLicense,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '营业执照是否缺失或即将到期',
        defaultValue: ['0'],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '资质证书',
        fieldKey: DimensionFieldKeyEnums.certification,
        dataType: DimensionFieldTypeEnums.String,
        comment: '资质证书列表是否有缺失或即将到期',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
        defaultValue: ['*********'],
        options: CompanyCertificationTypeMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '临近到期时间',
        fieldKey: DimensionFieldKeyEnums.nearExpirationType,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '临近到期时间, 1-近7天；2-近一个月；3-近3个月； ',
        defaultCompareType: DimensionFieldCompareTypeEnums.Between,
        defaultValue: [2],
        options: [
          { label: '近7天', value: 1 },
          { label: '近一个月', value: 2 },
          { label: '近3个月', value: 3 },
        ],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Radio,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.sortField, 10, [{ field: 'index', order: 'DESC', fieldSnapshot: 'index' }]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/IPR/Certification/Summary',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#项资质缺失或临期 </em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业关键资质缺失或即将到期',
  },
  [DimensionTypeEnums.TaxationOffences]: {
    key: DimensionTypeEnums.TaxationOffences,
    name: '税收违法',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [-1], DimensionFieldCompareTypeEnums.Equal),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [-1]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#存在【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因虚开发票、偷税等税务违法行为被处罚',
  },
  [DimensionTypeEnums.Bankruptcy]: {
    key: DimensionTypeEnums.Bankruptcy,
    name: '破产重整',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [-1], DimensionFieldCompareTypeEnums.Equal),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'riskdate',
          order: 'DESC',
          fieldSnapshot: 'RiskDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    detailSource: DimensionSourceEnums.EnterpriseLib,
    detailSourcePath: '/api/QccSearch/SmallSearch/BankRuptcy',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业进入破产重整程序',
  },
  [DimensionTypeEnums.FreezeEquity]: {
    key: DimensionTypeEnums.FreezeEquity,
    name: '股权冻结',
    dimensionFields: [
      {
        fieldName: '股权数额',
        fieldKey: DimensionFieldKeyEnums.equityAmount,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '股权数额大于等于20万元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [20],
        options: [{ unit: '万元', min: 0, max: ******** }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 1, [-1]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'liandate',
          order: 'DESC',
          fieldSnapshot: 'LianDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template:
      '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，冻结股权数额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业股权被法院冻结无法进行变更',
  },
  [DimensionTypeEnums.PersonExecution]: {
    key: DimensionTypeEnums.PersonExecution,
    name: '被执行人信息',
    dimensionFields: [
      {
        fieldName: '被执行总金额',
        fieldKey: DimensionFieldKeyEnums.executionSum,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '被执行总金额大于等于100000元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [100000],
        options: [{ unit: '元', min: 0, max: ********9999 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 1, [-1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 1, [0]),
      GetFields(DimensionFieldKeyEnums.isValid, 2, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'liandate',
          order: 'DESC',
          fieldSnapshot: 'LiAnDate',
        },
      ]),
    ],
    // 不使用信用接口原因: 信用数据 未对属于同一司法案件的执行标的做去重处理
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/SearchZhiXing', //'/api/Court/SearchZhiXing',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，涉及金额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因未履行法院判决而被强制执行',
  },
  [DimensionTypeEnums.JudicialAuction]: {
    key: DimensionTypeEnums.JudicialAuction,
    name: '司法拍卖信息',
    dimensionFields: [GetFields(DimensionFieldKeyEnums.cycle, 0, [3])],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/GetJudicialSaleList',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被法院强制执行拍卖处理资产用于清偿债务',
  },
  [DimensionTypeEnums.ContractBreach]: {
    key: DimensionTypeEnums.ContractBreach,
    name: '合同违约',
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/ContractBreach/ContractBreachInfo',
    status: DataStatusEnums.Enabled,
    template:
      '匹配到目标主体 <em class="#level#">【#name#】次数 #count#次</em>，涉案金额：<em class="#level#">#amountW#</em>，违约等级：<em class="#level#">#degree#</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业涉及合同违约案件分析结果',
  },
  [DimensionTypeEnums.CompanyCredit]: {
    key: DimensionTypeEnums.CompanyCredit,
    name: '被列入严重违法失信企业名录',
    source: DimensionSourceEnums.EnterpriseLib, // 不需要走 credit es 直接用企业库countInfo接口
    sourcePath: '/api/QccSearch/List/SeriousViolation', //'/api/dimension/gov-supervision/get-serious-violation-list',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'AddDate',
          order: 'DESC',
          fieldSnapshot: 'AddDate',
        },
      ]),
    ],
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被市场监管部门列入严重违法失信企业名录',
  },
  [DimensionTypeEnums.CompanyCreditHistory]: {
    key: DimensionTypeEnums.CompanyCreditHistory,
    name: '被列入严重违法失信企业名录（历史）',
    source: DimensionSourceEnums.EnterpriseLib, // 不需要走 credit es 直接用企业库countInfo接口
    sourcePath: '/api/QccSearch/List/SeriousViolation', //'/api/dimension/gov-supervision/get-serious-violation-list',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'AddDate',
          order: 'DESC',
          fieldSnapshot: 'AddDate',
        },
      ]),
    ],
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业曾被市场监管部门列入严重违法失信企业名录',
  },
  [DimensionTypeEnums.OperationAbnormal]: {
    key: DimensionTypeEnums.OperationAbnormal,
    name: '被列入经营异常名录（历史）',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.hitCount, 0, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      {
        fieldName: '经营异常类型',
        fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '经营异常类型',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
        options: BusinessAbnormalType,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'occurrencedate',
          order: 'DESC',
          fieldSnapshot: 'CurrenceDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业曾被市场监管部门列为经营异常名录',
  },
  [DimensionTypeEnums.TaxArrearsNotice]: {
    key: DimensionTypeEnums.TaxArrearsNotice,
    name: '欠税公告',
    dimensionFields: [
      // 欠税公告 企业库接口暂不支持时间过滤
      // getAndSetDefaultDimensionFields(DimensionFieldKeyEnums.cycle, [3]),
      {
        fieldName: '欠税金额',
        fieldKey: DimensionFieldKeyEnums.taxArrearsAmount,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '欠税金额 大于等于 1万元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [1],
        options: [{ unit: '万元', min: 0, max: ******** }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 11,
      },
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'liandate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Tax/GetListOfOweNoticeNew',
    status: DataStatusEnums.Enabled,
    // 匹配到目标主体 近3年欠税公告  XX条记录，欠税余额：XXX万元
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，欠税余额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业未在规定的期限缴纳税款',
  },
  [DimensionTypeEnums.SpotCheck]: {
    key: DimensionTypeEnums.SpotCheck,
    name: '抽查检查-不合格',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业在抽查检查中存在不合格记录',
  },
  [DimensionTypeEnums.AdministrativePenalties2]: {
    key: DimensionTypeEnums.AdministrativePenalties2,
    name: '涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'punishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因涉及涉及商业贿赂、垄断行为或政府采购活动违法行为而被行政处罚',
  },
  [DimensionTypeEnums.AdministrativePenalties3]: {
    key: DimensionTypeEnums.AdministrativePenalties3,
    name: '3年前涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'punishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业3年前曾因涉及涉及商业贿赂、垄断行为或政府采购活动违法行为而被行政处罚',
  },
  [DimensionTypeEnums.AdministrativePenalties]: {
    key: DimensionTypeEnums.AdministrativePenalties,
    name: '行政处罚',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      {
        fieldName: '处罚事由类型',
        fieldKey: DimensionFieldKeyEnums.punishReasonType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '处罚事由类型 包含 ',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['201', '202', '203', '301', '0'],
        options: punishReasonTypeMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '处罚类型',
        fieldKey: DimensionFieldKeyEnums.penaltiesType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '处罚类型 包含 ',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['0908', '0911', '0915', '0910', '0914', '0913', '0903', '0904', '0901', '0902', '0905', '0906', '0907', '0909'],
        options: PenaltiesType,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      {
        fieldName: '处罚金额',
        fieldKey: DimensionFieldKeyEnums.penaltiesAmount,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '处罚金额 大于等于 10 万元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [10],
        options: [{ unit: '万元', min: 0, max: ******** }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 3,
      },
      {
        fieldName: '处理机构（含地方）',
        fieldKey: DimensionFieldKeyEnums.ProcessingAgencyLevelOne,
        dataType: DimensionFieldTypeEnums.String,
        comment: '处理机构类型: 人民银行-1; 证监会-2; 金融监管局-3; 交易所-4; 网信办-10',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['1'],
        options: ProcessingAgencyLevelOneMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
      {
        fieldName: '处理机构(二级)',
        fieldKey: DimensionFieldKeyEnums.ProcessingAgencyLevelTwo,
        dataType: DimensionFieldTypeEnums.String,
        comment:
          '处理机构类型: 中国人民银行-101; ' +
          '国家外汇管理局-102; 金融监管局-301; 银保监会（原）-302; ' +
          '上交所-401; 深交所-402; 北交所-403; 股转系统-404; 上期所-405; 郑商所-406; ' +
          '中金所-407; 联交所-408; 大商所-409; 股权交易中心-410; 中央网信办-1001; 地方网信-1002',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['101'],
        options: ProcessingAgencyLevelTwoMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 5,
      },
      {
        fieldName: '红牌处罚',
        fieldKey: DimensionFieldKeyEnums.punishRedCard,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '是否为红牌处罚',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: [
          { label: '非红牌处罚', value: 0 },
          { label: '是红牌处罚', value: 1 },
        ],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 6,
      },
      GetFields(DimensionFieldKeyEnums.hitCount, 8, [0]),
      GetFields(DimensionFieldKeyEnums.isValid, 9, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'punishdate',
          order: 'DESC',
          fieldSnapshot: 'PunishDate',
        },
      ]),
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 11,
      },
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
    ],
    source: DimensionSourceEnums.SupervisePunish,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template:
      '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被行政处罚',
  },
  [DimensionTypeEnums.TaxPenalties]: {
    key: DimensionTypeEnums.TaxPenalties,
    name: '税务处罚',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      {
        fieldName: '处罚类型',
        fieldKey: DimensionFieldKeyEnums.penaltiesType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '处罚类型 包含 ',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['0908', '0911', '0915', '0910', '0914', '0913', '0903', '0904', '0901', '0902', '0905', '0906', '0907', '0909'],
        options: PenaltiesType,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '处罚金额',
        fieldKey: DimensionFieldKeyEnums.penaltiesAmount,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '处罚金额 大于等于 100000 元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [100000],
        options: [{ unit: '元', min: 0, max: ********9999 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.isValid, 3, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'punishdate',
          order: 'DESC',
          fieldSnapshot: 'PunishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.SupervisePunish,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template:
      '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被税务处罚',
  },
  [DimensionTypeEnums.EnvironmentalPenalties]: {
    key: DimensionTypeEnums.EnvironmentalPenalties,
    name: '环保处罚',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'punishdate',
          order: 'DESC',
          fieldSnapshot: 'PunishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    // 匹配到目标主体 近3年环保处罚xx条记录，罚款总金额：
    template:
      '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em><span class="#isHidden#">，罚款总金额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因违规排放等问题造成环境污染而收到处罚',
  },
  [DimensionTypeEnums.TaxCallNotice]: {
    key: DimensionTypeEnums.TaxCallNotice,
    name: '税务催缴公告',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publicdate',
          order: 'DESC',
          fieldSnapshot: 'publicdate',
        },
      ]),
    ],
    source: DimensionSourceEnums.TaxAnnouncement,
    sourcePath: '',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因未及时缴纳税款被税务部门公告催缴',
    isHidden: true,
  },
  [DimensionTypeEnums.TaxReminder]: {
    key: DimensionTypeEnums.TaxReminder,
    name: '税务催报',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/GetTaxReminders',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因未按照规定期限进行纳税申报被税务部门公告催报',
  },
  [DimensionTypeEnums.TaxCallNoticeV2]: {
    key: DimensionTypeEnums.TaxCallNoticeV2,
    name: '税务催缴',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      {
        fieldName: '欠缴金额',
        fieldKey: DimensionFieldKeyEnums.AmountOwed,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '欠缴金额 大于等于 100000 元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [100000],
        options: [{ unit: '元', min: 0, max: ********9999 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/GetTaxReminders',
    status: DataStatusEnums.Enabled,
    template:
      '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录<span class="#isHidden#">，涉及欠缴金额<em class="#level#">#amountW#</em></span></em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因未及时缴纳税款被税务部门公告催缴',
  },
  [DimensionTypeEnums.BondDefaults]: {
    key: DimensionTypeEnums.BondDefaults,
    name: '债券违约',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publicdate',
          order: 'DESC',
          fieldSnapshot: 'MaturityDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
    ],
    // 排查 切换到 信用接口，
    // 累计违约本金(亿元) ： Amount(亿元) - Amount2（亿元）
    // 累计违约利息(亿元) ： Amount2
    // 债券类型： Casereasontype
    // 首次违约日期：PublishDate
    // 到期日期：LianDate
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    // 维度详情使用企业库接口
    detailSource: DimensionSourceEnums.EnterpriseLib,
    detailSourcePath: '/api/FinancialInfo/Bond/BondDefaultList',
    status: DataStatusEnums.Enabled,
    // 匹配到目标主体 存在债权违约，累计违约本金：XX万元，累计违约利息：XX万元；目标主体存在偿债风险，建议收紧授信
    template:
      '匹配到目标主体 <em class="#level#">【#name#】#count#条记录</em><span class="#isHiddenY#">，累计违约本金：<em class="#level#">#amountY#</em></span><span class="#isHidden#">，累计违约利息：<em class="#level#">#amount2Y#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业有债券到期未偿违约记录',
  },
  [DimensionTypeEnums.BillDefaults]: {
    key: DimensionTypeEnums.BillDefaults,
    name: '票据违约',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/BillDefault',
    status: DataStatusEnums.Enabled,
    // 匹配到目标主体 票据违约  XX条记录
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在未能按约定履行支付义务的票据交易记录',
  },
  [DimensionTypeEnums.ChattelMortgage]: {
    key: DimensionTypeEnums.ChattelMortgage,
    name: '动产抵押',
    dimensionFields: [GetFields(DimensionFieldKeyEnums.isValid, 0, [1])],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/MPledge',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业将动产抵押获取融资的行为（信息来源于工商公示）',
  },
  [DimensionTypeEnums.EquityPledge]: {
    key: DimensionTypeEnums.EquityPledge,
    name: '股权出质',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'liandate',
          order: 'DESC',
          fieldSnapshot: 'RegDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.Pledge,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业有股权出质',
  },
  [DimensionTypeEnums.GbIndustryCompStatistic]: {
    key: DimensionTypeEnums.GbIndustryCompStatistic,
    name: '国标行业统计',
    dimensionFields: [
      {
        fieldName: '统计类型',
        fieldKey: DimensionFieldKeyEnums.gbIndustryCompStatisticType,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '国标行业统计类型：0专利；1 软著',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        options: GbIndustryCompStatsTypeConstant,
        defaultValue: [GbIndustryCompStatsTypeEnum.Patent],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '区间左值',
        fieldKey: DimensionFieldKeyEnums.leftRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '占比左区间',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '区间右值',
        fieldKey: DimensionFieldKeyEnums.rightRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '占比右区间',
        defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
        defaultValue: [100],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
    ],
    source: DimensionSourceEnums.GbIndustryCompStatistic,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '国标行业统计',
  },
  [DimensionTypeEnums.LandMortgage]: {
    key: DimensionTypeEnums.LandMortgage,
    name: '土地抵押',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      {
        fieldName: '土地抵押金额',
        fieldKey: DimensionFieldKeyEnums.landMortgageAmount,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '土地抵押金额 大于等于 0 元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [0],
        options: [{ unit: '元', min: 0, max: ********9999 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/LandMgmt/GetListOfLandMortgage',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录,涉及抵押金额<em class="#level#">#amountW#</em></span></em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业将土地进行抵押获取融资的行为',
  },
  [DimensionTypeEnums.GuaranteeRisk]: {
    key: DimensionTypeEnums.GuaranteeRisk,
    name: '担保风险',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      {
        fieldName: '担保风险金额',
        fieldKey: DimensionFieldKeyEnums.guaranteedprincipal,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '土地抵押金额 大于等于 0 元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [0],
        options: [{ unit: '元', min: 0, max: ********9999 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/GuarantorRisk',
    status: DataStatusEnums.Enabled,
    // 匹配到目标主体 担保风险  XX条记录，被保证债权本金：XX万元
    template:
      '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，被保证债权本金：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在担保风险信息',
  },
  [DimensionTypeEnums.NoTender]: {
    key: DimensionTypeEnums.NoTender,
    name: '无招投标记录',
    dimensionFields: [GetFields(DimensionFieldKeyEnums.cycle, 0, [1])],
    source: DimensionSourceEnums.Tender,
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体<em class="#level#">【#name#】</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '未发现企业存在招投标记录',
  },
  [DimensionTypeEnums.NegativeNews]: {
    key: DimensionTypeEnums.NegativeNews,
    name: '负面新闻',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.hitCount, 1, [0]),
      {
        fieldName: '新闻主体类型',
        fieldKey: DimensionFieldKeyEnums.topics,
        dataType: DimensionFieldTypeEnums.String,
        comment: '新闻主体类型 包含',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['all'],
        options: AllTopicTypes,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishtime',
          order: 'DESC',
          fieldSnapshot: 'publishtime',
        },
      ]),
    ],
    source: DimensionSourceEnums.NegativeNews,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因负面信息被新闻报道',
  },
  [DimensionTypeEnums.CustomerPartnerInvestigation]: {
    key: DimensionTypeEnums.CustomerPartnerInvestigation,
    name: '与第三方列表企业存在投资任职关联',
    dimensionFields: [
      {
        fieldName: '投资任职关联类型',
        fieldKey: DimensionFieldKeyEnums.types,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '投资任职关联类型 包含',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [
          {
            key: DetailsParamEnums.ShareholdingRelationship,
            keyName: RelationTypeConst[DetailsParamEnums.ShareholdingRelationship],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.InvestorsRelationship,
            keyName: RelationTypeConst[DetailsParamEnums.InvestorsRelationship],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.EmploymentRelationship,
            keyName: RelationTypeConst[DetailsParamEnums.EmploymentRelationship],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HisShareholdingRelationship,
            keyName: RelationTypeConst[DetailsParamEnums.HisShareholdingRelationship],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HisInvestorsRelationship,
            keyName: RelationTypeConst[DetailsParamEnums.HisInvestorsRelationship],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HisLegalAndEmploy,
            keyName: RelationTypeConst[DetailsParamEnums.HisLegalAndEmploy],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.ActualController,
            keyName: RelationTypeConst[DetailsParamEnums.ActualController],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.MainInfoUpdateBeneficiary,
            keyName: RelationTypeConst[DetailsParamEnums.MainInfoUpdateBeneficiary],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.Branch,
            keyName: RelationTypeConst[DetailsParamEnums.Branch],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.Hold,
            keyName: RelationTypeConst[DetailsParamEnums.Hold],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.ShareholdingRatio,
            keyName: RelationTypeConst[DetailsParamEnums.ShareholdingRatio],
            value: 0.01,
            status: DataStatusEnums.Enabled,
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '关联层级',
        fieldKey: DimensionFieldKeyEnums.depth,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '关联层级 1,2,3',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '数据范围（黑名单，第三方）',
        fieldKey: DimensionFieldKeyEnums.dataRange,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '第三方范围过滤条件',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
        defaultValue: [
          {
            key: DetailsParamEnums.Groups,
            keyName: '第三方分组',
            value: [],
            nameList: [], //分组名称
          },
          {
            key: DetailsParamEnums.Labels,
            keyName: '第三方标签',
            value: [],
            nameList: [], //标签名称
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
    ],
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业与第三方列表企业存在股权、人员交叉任职、相同实际控制人等投资任职关联',
  },
  [DimensionTypeEnums.CustomerSuspectedRelation]: {
    key: DimensionTypeEnums.CustomerSuspectedRelation,
    name: '与第三方列表企业存在交叉重叠疑似关联',
    dimensionFields: [
      {
        fieldName: '交叉重叠疑似关联类型',
        fieldKey: DimensionFieldKeyEnums.types,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '投资任职关联类型 包含',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [
          {
            key: DetailsParamEnums.Guarantee,
            keyName: RelationTypeConst[DetailsParamEnums.Guarantee],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.EquityPledgeRelation,
            keyName: RelationTypeConst[DetailsParamEnums.EquityPledgeRelation],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HasPhone,
            keyName: RelationTypeConst[DetailsParamEnums.HasPhone],
            status: DataStatusEnums.Enabled,
          },
          // 暂不支持网址
          // {
          //   key: DetailsParamEnums.WebsiteRelation,
          //   keyName: RelationTypeConst[DetailsParamEnums.WebsiteRelation],
          //   status: DataStatusEnums.Enable,
          // },
          {
            key: DetailsParamEnums.HasAddress,
            keyName: RelationTypeConst[DetailsParamEnums.HasAddress],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HasEmail,
            keyName: RelationTypeConst[DetailsParamEnums.HasEmail],
            status: DataStatusEnums.Enabled,
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '数据范围（黑名单，第三方）',
        fieldKey: DimensionFieldKeyEnums.dataRange,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '第三方范围过滤条件',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
        defaultValue: [
          {
            key: DetailsParamEnums.Groups,
            keyName: '第三方分组',
            value: [],
            nameList: [], //分组名称
          },
          {
            key: DetailsParamEnums.Labels,
            keyName: '第三方标签',
            value: [],
            nameList: [], //标签名称
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
    ],
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业与第三方列表企业存在相互担保关联、股权出质关联、相同联系方式等疑似关联关系',
  },
  [DimensionTypeEnums.HitInnerBlackList]: {
    key: DimensionTypeEnums.HitInnerBlackList,
    name: '被列入内部黑名单',
    dimensionFields: [GetFields(DimensionFieldKeyEnums.isValid, 1, [-1])],
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【被列入内部黑名单】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被列入了内部黑名单列表',
  },
  [DimensionTypeEnums.EmploymentRelationship]: {
    key: DimensionTypeEnums.EmploymentRelationship,
    name: '与内部黑名单列表存在人员关联',
    dimensionFields: [GetFields(DimensionFieldKeyEnums.isValid, 1, [-1])],
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【与内部黑名单列表存在人员关联】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '目标主体主要任职人员是否被列入内部黑名单',
    isHidden: true,
  },
  [DimensionTypeEnums.BlacklistPartnerInvestigation]: {
    key: DimensionTypeEnums.BlacklistPartnerInvestigation,
    name: '与内部黑名单企业存在投资任职关联',
    dimensionFields: [
      {
        fieldName: '投资任职关联类型',
        fieldKey: DimensionFieldKeyEnums.types,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '投资任职关联类型 包含',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [
          {
            key: DetailsParamEnums.ForeignInvestment,
            keyName: RelationTypeConst[DetailsParamEnums.ForeignInvestment],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.Shareholder,
            keyName: RelationTypeConst[DetailsParamEnums.Shareholder],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.EmploymentRelationship,
            keyName: RelationTypeConst[DetailsParamEnums.EmploymentRelationship],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HisForeignInvestment,
            keyName: RelationTypeConst[DetailsParamEnums.HisForeignInvestment],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HisShareholder,
            keyName: RelationTypeConst[DetailsParamEnums.HisShareholder],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HisLegalAndEmploy,
            keyName: RelationTypeConst[DetailsParamEnums.HisLegalAndEmploy],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.ActualController,
            keyName: RelationTypeConst[DetailsParamEnums.ActualController],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.MainInfoUpdateBeneficiary,
            keyName: RelationTypeConst[DetailsParamEnums.MainInfoUpdateBeneficiary],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.Branch,
            keyName: RelationTypeConst[DetailsParamEnums.Branch],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.Hold,
            keyName: RelationTypeConst[DetailsParamEnums.Hold],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.ShareholdingRatio,
            keyName: RelationTypeConst[DetailsParamEnums.ShareholdingRatio],
            value: 5,
            status: DataStatusEnums.Enabled,
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '关联层级',
        fieldKey: DimensionFieldKeyEnums.depth,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '关联层级 1,2,3',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '数据范围（黑名单，第三方）',
        fieldKey: DimensionFieldKeyEnums.dataRange,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '黑名单范围过滤条件',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
        defaultValue: [
          {
            key: DetailsParamEnums.Groups,
            keyName: '黑名单分组',
            value: [],
            nameList: [], //分组名称
          },
          {
            key: DetailsParamEnums.Labels,
            keyName: '黑名单标签',
            value: [],
            nameList: [], //标签名称
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
    ],
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【与内部黑名单企业存在投资任职关联】 #count#条记录</em>',
    sort: 7,
    type: IndicatorTypeEnums.generalItems,
    description: '企业与内部黑名单企业存在股权、人员交叉任职、相同实际控制人等投资任职关联',
  },
  [DimensionTypeEnums.BlacklistSuspectedRelation]: {
    key: DimensionTypeEnums.BlacklistSuspectedRelation,
    name: '与内部黑名单企业存在疑似关联关系',
    dimensionFields: [
      {
        fieldName: '交叉重叠疑似关联类型',
        fieldKey: DimensionFieldKeyEnums.types,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '投资任职关联类型 包含',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [
          {
            key: DetailsParamEnums.Guarantee,
            keyName: RelationTypeConst[DetailsParamEnums.Guarantee],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.EquityPledgeRelation,
            keyName: RelationTypeConst[DetailsParamEnums.EquityPledgeRelation],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HasPhone,
            keyName: RelationTypeConst[DetailsParamEnums.HasPhone],
            status: DataStatusEnums.Enabled,
          },
          // 数据暂不支持
          // {
          //   key: DetailsParamEnums.WebsiteRelation,
          //   keyName: RelationTypeConst[DetailsParamEnums.WebsiteRelation],
          //   status: DataStatusEnums.Enable,
          // },
          {
            key: DetailsParamEnums.HasAddress,
            keyName: RelationTypeConst[DetailsParamEnums.HasAddress],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.HasEmail,
            keyName: RelationTypeConst[DetailsParamEnums.HasEmail],
            status: DataStatusEnums.Enabled,
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '数据范围（黑名单，第三方）',
        fieldKey: DimensionFieldKeyEnums.dataRange,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '黑名单范围过滤条件',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
        defaultValue: [
          {
            key: DetailsParamEnums.Groups,
            keyName: '黑名单分组',
            value: [],
            nameList: [], //分组名称
          },
          {
            key: DetailsParamEnums.Labels,
            keyName: '黑名单标签',
            value: [],
            nameList: [], //标签名称
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
    ],
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【与内部黑名单企业存在疑似关联关系】 #count#条记录</em>',
    sort: 7,
    type: IndicatorTypeEnums.generalItems,
    description: '企业与内部黑名单企业相互担保关联、股权出质关联、相同联系方式等疑似关联关系',
  },
  [DimensionTypeEnums.SecurityNotice]: {
    key: DimensionTypeEnums.SecurityNotice,
    name: '公安通告',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'publishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因涉嫌非法吸收公众存款等问题被公安部门通告',
  },
  [DimensionTypeEnums.RegulateFinance]: {
    key: DimensionTypeEnums.RegulateFinance,
    name: '监管处罚',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.hitCount, 1, [0]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'punishdate',
          order: 'DESC',
          fieldSnapshot: 'PunishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在监管行政处罚信息',
  },
  [DimensionTypeEnums.CancellationOfFiling]: {
    key: DimensionTypeEnums.CancellationOfFiling,
    name: '注销备案',
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/Enliq',
    status: DataStatusEnums.Enabled,
    template: templateString,
    type: IndicatorTypeEnums.generalItems,
    description: '企业在工商提交注销备案信息',
  },
  [DimensionTypeEnums.BusinessAbnormal2]: {
    // 目前测试发现 专业版接口 如果命中了 经营状态非存续 就不会命中简易注销
    key: DimensionTypeEnums.BusinessAbnormal2,
    name: '简易注销',
    source: DimensionSourceEnums.CompanyDetail,
    detailSource: DimensionSourceEnums.EnterpriseLib,
    detailSourcePath: '/api/QccSearch/List/SimpleCancellation',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '5010',
    description: '企业在工商提交简易注销流程',
  },
  [DimensionTypeEnums.BusinessAbnormal1]: {
    key: DimensionTypeEnums.BusinessAbnormal1,
    name: '经营状态非存续',
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: templateString,
    type: IndicatorTypeEnums.generalItems,
    typeCode: '2110',
    description: '企业在工商为“非存续状态”，如“注销、吊销”等',
  },
  [DimensionTypeEnums.BusinessAbnormal5]: {
    key: DimensionTypeEnums.BusinessAbnormal5,
    name: '疑似停业歇业停产或被吊销证照',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'punishdate',
          order: 'DESC',
          fieldSnapshot: 'PunishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '6314',
    description: '企业疑似停业歇业停产或被吊销证照',
  },
  [DimensionTypeEnums.BusinessAbnormal7]: {
    key: DimensionTypeEnums.BusinessAbnormal7,
    name: '无统一社会信用代码',
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: templateString,
    type: IndicatorTypeEnums.generalItems,
    description: '企业未取得统一社会信用代码',
  },
  [DimensionTypeEnums.BusinessAbnormal8]: {
    key: DimensionTypeEnums.BusinessAbnormal8,
    name: '临近经营期限',
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>，营业期限：<em class="#level#">#start# 至 #end#</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业工商注册的经营期限临近到期',
  },
  [DimensionTypeEnums.BusinessAbnormal6]: {
    key: DimensionTypeEnums.BusinessAbnormal6,
    name: '经营期限已过有效期',
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】</em>，营业期限：<em class="#level#">#start# 至 #end#</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业工商注册的经营期限已到期',
  },
  [DimensionTypeEnums.PersonCreditCurrent]: {
    key: DimensionTypeEnums.PersonCreditCurrent,
    name: '被列入失信被执行人',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [-1], DimensionFieldCompareTypeEnums.Equal),
      GetFields(DimensionFieldKeyEnums.sortField, 0, [
        {
          field: 'pubdate',
          order: 'DESC',
          fieldSnapshot: 'PublicDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:当前法人、历史法人、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 1,
      },
    ],
    // 不使用信用接口原因: 信用数据 未对属于同一司法案件的执行标的做去重处理
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/SearchShiXin',
    status: DataStatusEnums.Enabled,
    template:
      '匹配到目标主体 <em class="#level#">【#name#】#count#条记录</em><span class="#isHidden#">，涉及被执行金额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被加入失信被执行人名单',
  },
  [DimensionTypeEnums.RestrictedConsumptionCurrent]: {
    key: DimensionTypeEnums.RestrictedConsumptionCurrent,
    name: '被列入限制高消费名单',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [-1], DimensionFieldCompareTypeEnums.Equal),
      GetFields(DimensionFieldKeyEnums.sortField, 1, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#</em><em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被加入限制高消费名单',
  },
  [DimensionTypeEnums.MainMembersPersonCreditCurrent]: {
    key: DimensionTypeEnums.MainMembersPersonCreditCurrent,
    name: '主要人员被列入失信被执行人',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
    ],
    isVirtualDimension: 0,
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    detailSource: DimensionSourceEnums.CreditAPI,
    detailSourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，涉及金额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业的主要人员（董监高法）被列入失信被执行人',
  },
  [DimensionTypeEnums.MainMembersRestrictedConsumptionCurrent]: {
    key: DimensionTypeEnums.MainMembersRestrictedConsumptionCurrent,
    name: '主要人员被列入限制高消费',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [-1], DimensionFieldCompareTypeEnums.Equal),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业相关人员有“限制高消费”',
  },
  [DimensionTypeEnums.MainMembersRestrictedOutbound]: {
    key: DimensionTypeEnums.MainMembersRestrictedOutbound,
    name: '主要人员被列入限制出境',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 1, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [-1], DimensionFieldCompareTypeEnums.Equal),
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:当前法人、历史法人、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    detailSource: DimensionSourceEnums.CreditAPI,
    detailSourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业主要人员（董监高法）被列入限制出境名单',
  },
  [DimensionTypeEnums.PersonCreditHistory]: {
    key: DimensionTypeEnums.PersonCreditHistory,
    name: '历史失信被执行人',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.sortField, 1, [
        {
          field: 'pubdate',
          order: 'DESC',
          fieldSnapshot: 'PublicDate',
        },
      ]),
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:当前法人、历史法人、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
    ],
    // 不使用信用接口原因: 信用数据 未对属于同一司法案件的执行标的做去重处理
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/SearchShiXin',
    status: DataStatusEnums.Enabled,
    template:
      '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em><span class="#isHidden#">，涉及被执行金额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业曾被列入失信被执行人名单',
  },
  [DimensionTypeEnums.RestrictedConsumptionHistory]: {
    key: DimensionTypeEnums.RestrictedConsumptionHistory,
    name: '历史限制高消费',
    dimensionFields: [
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#</em><em class="#level#">【#name#】#count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业曾被列入限制高消费名单',
  },
  [DimensionTypeEnums.CompanyOrMainMembersCriminalOffence]: {
    key: DimensionTypeEnums.CompanyOrMainMembersCriminalOffence,
    name: '公司及主要人员涉刑事犯罪（3年内）',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'LastestDate',
          order: 'DESC',
          fieldSnapshot: 'LastestDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.Case,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及主要人员（董监高法）涉及到刑事犯罪相关诉讼',
  },
  [DimensionTypeEnums.CompanyOrMainMembersCriminalOffenceHistory]: {
    key: DimensionTypeEnums.CompanyOrMainMembersCriminalOffenceHistory,
    name: '公司及主要人员涉刑事犯罪（3年以上及其他）',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'LastestDate',
          order: 'DESC',
          fieldSnapshot: 'LastestDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.Case,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及主要人员（董监高法）曾发生过涉及到刑事犯罪相关诉讼',
  },
  [DimensionTypeEnums.CompanyOrMainMembersCriminalInvolve]: {
    key: DimensionTypeEnums.CompanyOrMainMembersCriminalInvolve,
    name: '近3年涉贪污受贿裁判相关提及方',
    dimensionFields: [
      {
        fieldName: '关联对象',
        fieldKey: DimensionFieldKeyEnums.associateObject,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '关联对象类型',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [
          {
            key: DetailsParamEnums.LegalRepresentative,
            keyName: AssociationTypeConst[DetailsParamEnums.LegalRepresentative],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.ActualController,
            keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.MajorShareholder,
            keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.Shareholder,
            keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
            status: DataStatusEnums.Enabled,
          },
        ],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.isValid, 1, [-1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'courtdate',
          order: 'DESC',
          fieldSnapshot: 'courtdate',
        },
      ]),
    ],
    source: DimensionSourceEnums.Judgement,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及股东、参股企业涉及到贪污受贿类案件被裁判文书提及',
  },
  [DimensionTypeEnums.CompanyOrMainMembersCriminalInvolveHistory]: {
    key: DimensionTypeEnums.CompanyOrMainMembersCriminalInvolveHistory,
    name: '涉贪污受贿裁判相关提及方（3年以上及其他）',
    dimensionFields: [
      {
        fieldName: '关联对象',
        fieldKey: DimensionFieldKeyEnums.associateObject,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '关联对象类型',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [
          {
            key: DetailsParamEnums.LegalRepresentative,
            keyName: AssociationTypeConst[DetailsParamEnums.LegalRepresentative],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.ActualController,
            keyName: AssociationTypeConst[DetailsParamEnums.ActualController],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.MajorShareholder,
            keyName: AssociationTypeConst[DetailsParamEnums.MajorShareholder],
            status: DataStatusEnums.Enabled,
          },
          {
            key: DetailsParamEnums.Shareholder,
            keyName: AssociationTypeConst[DetailsParamEnums.Shareholder],
            status: DataStatusEnums.Enabled,
          },
        ],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'courtdate',
          order: 'DESC',
          fieldSnapshot: 'courtdate',
        },
      ]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [-1]),
    ],
    source: DimensionSourceEnums.Judgement,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业及股东、参股企业涉及到贪污受贿类案件被裁判文书提及',
  },
  [DimensionTypeEnums.SalesContractDispute]: {
    key: DimensionTypeEnums.SalesContractDispute,
    name: '买卖合同纠纷',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      {
        fieldName: '被告占比',
        fieldKey: DimensionFieldKeyEnums.percentAsDefendant,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '作为被告方占比，如50',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'judgedate',
          order: 'DESC',
          fieldSnapshot: 'judgedate',
        },
      ]),
    ],
    source: DimensionSourceEnums.Judgement,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录，作为被告方占比大于等于#percent#%</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因买卖合同纠纷被诉占比过大',
  },
  [DimensionTypeEnums.LaborContractDispute]: {
    key: DimensionTypeEnums.LaborContractDispute,
    name: '劳动纠纷',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'LastestDate',
          order: 'DESC',
          fieldSnapshot: 'LastestDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.Case,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.keyItems,
    description: '企业存在劳动纠纷案件',
  },
  [DimensionTypeEnums.MajorDispute]: {
    key: DimensionTypeEnums.MajorDispute,
    name: '重大纠纷',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      {
        fieldName: '涉案金额',
        fieldKey: DimensionFieldKeyEnums.amountInvolved,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '涉案总金额 大于等于 200000 元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [200000],
        options: [{ unit: '元', min: 0, max: ********9999 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '案件身份（排除）',
        fieldKey: DimensionFieldKeyEnums.judgementRoleExclude,
        dataType: DimensionFieldTypeEnums.String,
        comment: '裁判文书身份类型（排除） prosecutor-原告 thirdpartyrole-第三人',
        defaultCompareType: DimensionFieldCompareTypeEnums.ExceptAll,
        defaultValue: ['prosecutor', 'thirdpartyrole'],
        options: CaseRoleMap,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'judgedate',
          order: 'DESC',
          fieldSnapshot: 'judgedate',
        },
      ]),
    ],
    source: DimensionSourceEnums.Judgement,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template:
      '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录<span class="#isHidden#">，涉及案件金额：<em class="#level#">#amountW#</em></span></em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业涉诉案件涉及金额过大',
  },
  [DimensionTypeEnums.EndExecutionCase]: {
    key: DimensionTypeEnums.EndExecutionCase,
    name: '终本案件',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      {
        fieldName: '未履行总金额',
        fieldKey: DimensionFieldKeyEnums.failure,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '未履行总金额 大于等于 100000 元',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [100000],
        options: [{ unit: '元', min: 0, max: ********9999 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'enddate',
          order: 'DESC',
          fieldSnapshot: 'EndDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Court/GetEndExecutionCaseListByKeyNo',
    status: DataStatusEnums.Enabled,
    template:
      '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em><span class="#isHidden#">，未履行总金额：<em class="#level#">#amountW#</em></span>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业涉及终本案件',
  },
  [DimensionTypeEnums.BusinessAbnormal4]: {
    key: DimensionTypeEnums.BusinessAbnormal4,
    name: '被列入税务非正常户',
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    typeCode: '1910',
    description: '企业因未及时申报纳税并无法联系，被税务机关停止办税',
  },
  [DimensionTypeEnums.TaxUnnormals]: {
    key: DimensionTypeEnums.TaxUnnormals,
    name: '被列入税务非正常户',
    dimensionFields: [
      // GetFields(DimensionFieldKeyEnums.cycle, 0, [-1]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'courtdate',
          order: 'DESC',
          fieldSnapshot: 'courtdate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Risk/GetTaxUnnormals',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业因未及时申报纳税并无法联系，被税务机关停止办税',
  },
  [DimensionTypeEnums.BusinessAbnormal3]: {
    key: DimensionTypeEnums.BusinessAbnormal3,
    name: '被列入经营异常名录',
    dimensionFields: [
      {
        fieldName: '经营异常类型',
        fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '列入原因',
        defaultValue: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        options: BusinessAbnormalType,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 1,
      },
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [-1], DimensionFieldCompareTypeEnums.Equal),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'occurrencedate',
          order: 'DESC',
          fieldSnapshot: 'CurrenceDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业被市场监管部门列为经营异常名录',
  },
  [DimensionTypeEnums.ProductQualityProblem6]: {
    key: DimensionTypeEnums.ProductQualityProblem6,
    name: '未准入境',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    detailSource: DimensionSourceEnums.CreditAPI,
    detailSourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    type: IndicatorTypeEnums.generalItems,
    description: '企业经营商品未被海关批准入境而被退运或销毁',
  },
  [DimensionTypeEnums.ProductQualityProblem2]: {
    key: DimensionTypeEnums.ProductQualityProblem2,
    name: '产品抽查不合格',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    type: IndicatorTypeEnums.generalItems,
    description: '企业在产品抽查中存在不合格记录',
  },
  [DimensionTypeEnums.ProductQualityProblem1]: {
    key: DimensionTypeEnums.ProductQualityProblem1,
    name: '产品召回',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    type: IndicatorTypeEnums.generalItems,
    description: '企业发生过产品召回事件',
  },
  [DimensionTypeEnums.ProductQualityProblem7]: {
    key: DimensionTypeEnums.ProductQualityProblem7,
    name: '药品抽查检验不合格',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publishdate',
          order: 'DESC',
          fieldSnapshot: 'PublishDate',
        },
      ]),
    ],
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    source: DimensionSourceEnums.CreditES,
    sourcePath: '/api/search/search-credit',
    detailSource: DimensionSourceEnums.CreditAPI,
    detailSourcePath: '/api/search/search-credit',
    status: DataStatusEnums.Enabled,
    type: IndicatorTypeEnums.generalItems,
    description: '企业药品抽查中存在检验不合格记录',
  },
  [DimensionTypeEnums.ProductQualityProblem9]: {
    key: DimensionTypeEnums.ProductQualityProblem9,
    name: '食品安全检查不合格',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'batch',
          order: 'DESC',
          fieldSnapshot: 'LianDate',
        },
      ]),
    ],
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/FoodSafety',
    status: DataStatusEnums.Enabled,
    type: IndicatorTypeEnums.generalItems,
    description: '企业食品安全抽查中存在检验不合格记录',
  },
  [DimensionTypeEnums.MainInfoUpdateScope]: {
    key: DimensionTypeEnums.MainInfoUpdateScope,
    name: '近期变更经营范围',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.hitCount, 0, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 2, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/History/GetCoyHistoryInfo',
    status: DataStatusEnums.Disabled,
    // 匹配到目标主体 近1年经营范围变更 XX记录
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期在工商登记的“经营范围”有变更',
  },
  [DimensionTypeEnums.MainInfoUpdateAddress]: {
    key: DimensionTypeEnums.MainInfoUpdateAddress,
    name: '近期变更注册地址',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.hitCount, 0, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 2, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/History/GetCoyHistoryInfo',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期在工商登记的“经营地址”有变更',
  },
  [DimensionTypeEnums.MainInfoUpdateName]: {
    key: DimensionTypeEnums.MainInfoUpdateName,
    name: '近期变更企业名称',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.hitCount, 0, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 2, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/History/GetCoyHistoryInfo',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期在工商登记的“企业名称”有变更',
  },
  [DimensionTypeEnums.MainInfoUpdateLegalPerson]: {
    key: DimensionTypeEnums.MainInfoUpdateLegalPerson,
    name: '近期变更法定代表人',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.hitCount, 0, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 2, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/History/GetCoyHistoryInfo',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期在工商登记的“法定代表人”有变更',
  },
  [DimensionTypeEnums.MainInfoUpdateHolder]: {
    key: DimensionTypeEnums.MainInfoUpdateHolder,
    name: '近期变更大股东',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.hitCount, 0, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '/api/proxy/risk/dynamic/getDynamicSelfGroupList',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期大股东有变更',
  },
  [DimensionTypeEnums.MainInfoUpdatePerson]: {
    key: DimensionTypeEnums.MainInfoUpdatePerson,
    name: '近期变更实际控制人',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.hitCount, 0, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '/api/proxy/risk/dynamic/getDynamicSelfGroupList',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业近期实际控制人有变更',
  },
  [DimensionTypeEnums.MainInfoUpdateBeneficiary]: {
    key: DimensionTypeEnums.MainInfoUpdateBeneficiary,
    name: '近期变更受益所有人',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.hitCount, 0, [0]),
      GetFields(DimensionFieldKeyEnums.cycle, 0, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'ChangeDate',
          order: 'DESC',
          fieldSnapshot: 'ChangeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '/api/proxy/risk/dynamic/getDynamicSelfGroupList',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业受益所有人（持股>=25%）有变更',
  },
  [DimensionTypeEnums.Liquidation]: {
    key: DimensionTypeEnums.Liquidation,
    name: '清算信息',
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/ECILocal/GetLiquidationDetail',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">#cycle#【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业依法解散后清理公司债权债务的行为',
  },
  [DimensionTypeEnums.StaffWorkingOutsideForeignInvestment]: {
    key: DimensionTypeEnums.StaffWorkingOutsideForeignInvestment,
    name: '潜在利益冲突',
    dimensionFields: [
      {
        fieldName: '关联类型',
        fieldKey: DimensionFieldKeyEnums.types,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '关联类型 包含',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [
          { key: DetailsParamEnums.Invest, keyName: '自然人股东', status: 1 },
          { key: DetailsParamEnums.LegalAndEmploy, keyName: '董监高/法人', status: 1 },
          {
            key: DetailsParamEnums.ActualController,
            keyName: '实际控制人',
            status: DataStatusEnums.Enabled,
          },
          { key: DetailsParamEnums.HisInvest, keyName: '自然人股东（历史）', status: 1 },
          { key: DetailsParamEnums.HisLegalAndEmploy, keyName: '董监高/法人（历史）', status: 1 },
          { key: DetailsParamEnums.MainInfoUpdateBeneficiary, keyName: '受益所有人', status: 1 },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '数据范围（人员列表）',
        fieldKey: DimensionFieldKeyEnums.dataRange,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '人员列表范围过滤条件',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
        defaultValue: [
          {
            key: DetailsParamEnums.Groups,
            keyName: '人员分组',
            value: [],
            nameList: [], //分组名称
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
    ],
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业股东、董监高等与内部员工存在相同',
  },
  [DimensionTypeEnums.SuspectedInterestConflict]: {
    key: DimensionTypeEnums.SuspectedInterestConflict,
    name: '疑似潜在利益冲突',
    dimensionFields: [
      {
        fieldName: '关联类型',
        fieldKey: DimensionFieldKeyEnums.types,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '关联类型 包含',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [
          {
            key: DetailsParamEnums.SameName,
            keyName: '相同姓名',
            status: DataStatusEnums.Enabled,
            child: [
              { key: DetailsParamEnums.Invest, keyName: '自然人股东', status: 1 },
              { key: DetailsParamEnums.LegalAndEmploy, keyName: '董监高/法人', status: 1 },
              {
                key: DetailsParamEnums.ActualController,
                keyName: '实际控制人',
                status: DataStatusEnums.Enabled,
              },
              { key: DetailsParamEnums.HisInvest, keyName: '自然人股东（历史）', status: 1 },
              { key: DetailsParamEnums.HisLegalAndEmploy, keyName: '董监高/法人（历史）', status: 1 },
              { key: DetailsParamEnums.MainInfoUpdateBeneficiary, keyName: '受益所有人', status: 1 },
            ],
          },
          { key: DetailsParamEnums.SameContact, keyName: '相同联系方式（电话、邮箱）', status: 1 },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Checkbox,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
      {
        fieldName: '数据范围（人员列表）',
        fieldKey: DimensionFieldKeyEnums.dataRange,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '人员列表范围过滤条件',
        defaultCompareType: DimensionFieldCompareTypeEnums.Between,
        defaultValue: [
          {
            key: DetailsParamEnums.Groups,
            keyName: '人员分组',
            value: [],
            nameList: [], //分组名称
          },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 2,
      },
    ],
    source: DimensionSourceEnums.Rover,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业股东、董监高等与内部员工存在疑似同名/相同联系方式',
  },
  [DimensionTypeEnums.CapitalReduction]: {
    key: DimensionTypeEnums.CapitalReduction,
    name: '减资公告',
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体<em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业公布减资公告',
  },
  [DimensionTypeEnums.HitOuterBlackList]: {
    key: DimensionTypeEnums.HitOuterBlackList,
    name: '被列入外部黑名单',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      GetFields(DimensionFieldKeyEnums.isValid, 1, [1]),
      GetFields(DimensionFieldKeyEnums.hitCount, 2, [0]),
      GetFields(DimensionFieldKeyEnums.sortField, 3, [
        {
          field: 'decisiondate',
          order: 'DESC',
          fieldSnapshot: 'Publishdate',
        },
      ]),
      {
        fieldName: '黑名单类型',
        fieldKey: DimensionFieldKeyEnums.blackType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '该参数为使用外部黑名单必设字段, 用于框定外部黑名单的种类',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['LaborGuarantee'], // 劳动保障违法
        options: BlackTypeItems,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 4,
      },
    ],
    source: DimensionSourceEnums.OuterBlacklist,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【被列入外部黑名单】 #count#条记录</em>',
    sort: 1,
    type: IndicatorTypeEnums.generalItems,
  },
  [DimensionTypeEnums.FinancialHealth]: {
    key: DimensionTypeEnums.FinancialHealth,
    name: '财务健康度',
    dimensionFields: [
      {
        fieldName: '资产负债率',
        fieldKey: DimensionFieldKeyEnums.assetLiabilityRatio,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '欠缴金额 大于等于 70 %',
        defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        defaultValue: [70],
        options: [{ unit: '%', min: 0, max: 100 }],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 0,
      },
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/Dynamic/GetNewFinancingInfo',
    status: DataStatusEnums.Disabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '根据企业对外公布的最新一期财报数据，评估企业的财务健康度',
  },
  [DimensionTypeEnums.StockPledge]: {
    key: DimensionTypeEnums.StockPledge,
    name: '股权质押',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      {
        fieldName: '质押状态',
        fieldKey: DimensionFieldKeyEnums.pledgeStatus,
        dataType: DimensionFieldTypeEnums.String,
        comment: '股权质押状态',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [],
        options: {},
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '排查对象',
        fieldKey: DimensionFieldKeyEnums.targetInvestigation,
        dataType: DimensionFieldTypeEnums.String,
        comment: '排查对象属性，多选:主要人员、分支机构、企业本身',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [TargetInvestigationEnums.Self],
        options: PersonCaseTargetInvestigation,
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
      {
        fieldName: '股权质押角色',
        fieldKey: DimensionFieldKeyEnums.equityPledgeRole,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '股权质押角色，多选:质押人参股企业、质押人',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [1, 2],
        options: [
          { label: '质押人参股企业', value: 1 },
          { label: '质押人', value: 2 },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        fieldOrder: 2,
      },
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publicdate',
          order: 'DESC',
          fieldSnapshot: 'NoticeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.EnterpriseLib,
    sourcePath: '/api/QccSearch/List/StockPledgeV2',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业存在股权质押信息',
  },
  [DimensionTypeEnums.MainInfoUpdateCapitalChange]: {
    key: DimensionTypeEnums.MainInfoUpdateCapitalChange,
    name: '注册资本变更',
    dimensionFields: [
      GetFields(DimensionFieldKeyEnums.cycle, 0, [3]),
      {
        fieldName: '风险动态类型',
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '风险动态类型 37-注册资本',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: [37],
        options: [{ value: 37, label: '注册资本变更' }],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.MultiSelect,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
      {
        fieldName: '周期内注册资本变更',
        fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
        dataType: DimensionFieldTypeEnums.Object,
        comment: '1个自然年度内拟减少注册资本超过其原注册资本5%',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [
          {
            valuePeriodTrend: 1,
            valuePeriodThreShold: 5,
            valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
            valuePeriodBaseLine: 1,
          },
        ],
        options: [
          {
            valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
            valuePeriodThreShold: { label: '占比', value: { unit: '%', min: 0, max: 100 } },
            valuePeriodThreSholdCompareType: {
              label: '占比比较(大于/小于)',
              value: DimensionFieldCompareTypeEnums.GreaterThan,
            },
            valuePeriodBaseLine: {
              label: '时间基准',
              value: BaseLineDateSelect,
            },
          },
        ],
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Text,
        status: DataStatusEnums.Enabled,
        fieldOrder: 14,
      },
      GetFields(DimensionFieldKeyEnums.naturalCycle, 104, [3]),
      GetFields(DimensionFieldKeyEnums.isShowTip, 103, [1]),
      GetFields(DimensionFieldKeyEnums.sortField, 10, [
        {
          field: 'publicdate',
          order: 'DESC',
          fieldSnapshot: 'NoticeDate',
        },
      ]),
    ],
    source: DimensionSourceEnums.RiskChange,
    sourcePath: '',
    status: DataStatusEnums.Enabled,
    template: '匹配到目标主体 <em class="#level#">【#name#】 #count#条记录</em>',
    type: IndicatorTypeEnums.generalItems,
    description: '企业周期内注册资本变更',
  },
  [DimensionTypeEnums.TaxpayerCertificationChange]: {
    key: DimensionTypeEnums.TaxpayerCertificationChange,
    name: '纳税人资质变更',
    source: DimensionSourceEnums.CompanyDetail,
    status: DataStatusEnums.Enabled,
    template: templateString,
    type: IndicatorTypeEnums.generalItems,
    description: '纳税人资质变更',
  },
  [DimensionTypeEnums.AdditionalShareListingDate]: {
    key: DimensionTypeEnums.AdditionalShareListingDate,
    name: '增发新股上市日',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '上市板块',
        fieldKey: DimensionFieldKeyEnums.secType,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '1 A股 3 新三板 8 港股',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: [
          { value: 1, label: 'A股' },
          { value: 3, label: '新三板' },
          { value: 8, label: '港股' },
        ],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
    ],
    status: DataStatusEnums.Enabled,
    template: templateString,
    type: IndicatorTypeEnums.generalItems,
    description: '增发新股上市日',
    sourcePath: '/api/financing/addissues',
  },
  [DimensionTypeEnums.ConvertibleBondListingDate]: {
    key: DimensionTypeEnums.ConvertibleBondListingDate,
    name: '可转债上市日',
    source: DimensionSourceEnums.EnterpriseLib,
    dimensionFields: [
      {
        fieldName: '债券类型',
        fieldKey: DimensionFieldKeyEnums.bondType,
        dataType: DimensionFieldTypeEnums.String,
        comment: '14 可转债',
        defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
        defaultValue: ['14'],
        options: [{ value: '14', label: '可转债' }],
        isArray: 1,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 1,
      },
    ],
    status: DataStatusEnums.Enabled,
    template: templateString,
    type: IndicatorTypeEnums.generalItems,
    description: '可转债上市日',
    sourcePath: '/api/financialinfo/bond/bondlist',
  },
};
