/**
 * 行政处罚事由类型
 */
export const punishReasonTypeMap = [
  { label: '围串标', value: '201' },
  { label: '分包/转包/挂靠', value: '202' },
  { label: '虚假材料', value: '203' },
  { label: '商业贿赂', value: '301' },
  { label: '其他', value: '0' },
];

/**
 * 行政处罚处理机构一级
 */
export const ProcessingAgencyLevelOneMap = [
  { label: '人民银行', value: '1' },
  { label: '证监会', value: '2' },
  { label: '金融监管局', value: '3' },
  { label: '交易所', value: '4' },
  { label: '税务局', value: '5' },
  { label: '市场监督局', value: '6' },
  { label: '发改委', value: '7' },
  { label: '工信部', value: '8' },
  { label: '财政部', value: '9' },
  { label: '网信办', value: '10' },
  { label: '生态环境部', value: '12' },
  { label: '海关', value: '13' },
  { label: '法院', value: '14' },
  { label: '协会', value: '15' },
  { label: '其他机构', value: '99' },
];

/**
 * 行政处罚处理机构二级
 */
export const ProcessingAgencyLevelTwoMap = [
  { label: '中国人民银行', value: '101' },
  { label: '国家外汇管理局', value: '102' },
  { label: '金融监管局', value: '301' },
  { label: '银保监会（原）', value: '302' },
  { label: '上交所', value: '401' },
  { label: '深交所', value: '402' },
  { label: '北交所', value: '403' },
  { label: '股转系统', value: '404' },
  { label: '上期所', value: '405' },
  { label: '郑商所', value: '406' },
  { label: '中金所', value: '407' },
  { label: '联交所', value: '408' },
  { label: '大商所', value: '409' },
  { label: '股权交易中心', value: '410' },
  { label: '中央网信办', value: '1001' },
  { label: '地方网信', value: '1002' },
  { label: '农业农村部', value: '9907' },
];

/**
 * 行政处罚类型
 */
export const PenaltiesType = [
  { label: '警告', value: '0901', esCode: 'A001' },
  { label: '通报批评', value: '0902', esCode: 'A002' },
  { label: '罚款', value: '0903', esCode: 'A003' },
  { label: '没收违法所得', value: '0904', esCode: 'A004' },
  { label: '没收非法财物', value: '0905', esCode: 'A005' },
  { label: '暂扣许可证件', value: '0906', esCode: 'A006' },
  { label: '降低资质等级', value: '0907', esCode: 'A007' },
  { label: '吊销许可证/执照', value: '0908', esCode: 'A008' },
  { label: '限制开展生产经营活动', value: '0909', esCode: 'A009' },
  { label: '责令停产停业', value: '0910', esCode: 'A010' },
  { label: '责令关闭', value: '0911', esCode: 'A011' },
  //   { label: '限制申请行政许可', value: '0912', esCode: 'A012' },
  { label: '限制从业', value: '0913', esCode: 'A013' },
  { label: '行政拘留', value: '0914', esCode: 'A014' },
  { label: '移送司法机关', value: '0915', esCode: 'A015' },
  { label: '不予处罚', value: '0916', esCode: 'A016' },
  { label: '其他行政处罚', value: '0999', esCode: 'A099' },

  // 以下是未知状态处罚类型,不作为行政处罚
  // { label: '其他行政处罚', value: '0999', esCode: 'A101' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A102' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A103' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A104' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A105' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A106' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A108' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A109' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A110' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A111' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A112' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A113' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A114' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A115' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A116' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A117' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A118' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A119' },
  // { label: '其他行政处罚', value: '0999', esCode: 'A199' },
];

/**
 * 金融监管类型
 * （1）暂停相关业务A112；暂停或限制交易权限A111；加入惩戒名单A118
 * （2）严重警告A110；注销基金管理人登记A119；
 * （3）责令更正；公开谴责A101；监管关注A102；监管警示A104；.监管函A103；诫勉谈话A105；警告A117；警告函A106；内部批评A107；书面警示A109；通报批评A116；认定不适当人选A108；责令致歉A114；自律管理A115
 * （4）其他处理措施
 */
export const FinancialSupervisionType = [
  { label: '公开谴责', value: '101', esCode: 'A101' },
  { label: '监管关注', value: '102', esCode: 'A102' },
  { label: '监管函', value: '103', esCode: 'A103' },
  { label: '监管警示', value: '104', esCode: 'A104' },
  { label: '诫勉谈话', value: '105', esCode: 'A105' },
  { label: '警示函', value: '106', esCode: 'A106' },
  { label: '内部批评', value: '107', esCode: 'A107' },
  { label: '认定不适当人选', value: '108', esCode: 'A108' },
  { label: '书面警示', value: '109', esCode: 'A109' },
  { label: '严重警告', value: '110', esCode: 'A110' },
  { label: '暂停或者限制交易权限', value: '111', esCode: 'A111' },
  { label: '暂停相关业务', value: '112', esCode: 'A112' },
  { label: '责令改正', value: '113', esCode: 'A113' },
  { label: '责令致歉', value: '114', esCode: 'A114' },
  { label: '自律管理', value: '115', esCode: 'A115' },
  { label: '通报批评', value: '116', esCode: 'A116' },
  { label: '警告', value: '117', esCode: 'A117' },
  { label: '其他处理措施', value: '199', esCode: 'A199' },
  { label: '加入黑名单', value: '118', esCode: 'A118' },
  { label: '注销基金管理人登记', value: '119', esCode: 'A119' },
];

/**
 * 经营异常-异常类型
 */
export const BusinessAbnormalType = [
  { value: '0803', esCode: '3', label: '公示信息隐瞒真实情况/弄虚作假' },
  { value: '0801', esCode: '1', label: '登记的住所/经营场所无法联系企业' },
  { value: '0805', esCode: '5', label: '未在规定期限公示年度报告' },
  { value: '0802', esCode: '2', label: '未按规定公示企业信息' },
  { value: '0804', esCode: '4', label: '未在登记所从事经营活动' },
  { value: '0806', esCode: '6', label: '商事主体名称不适宜' },
  { value: '0807', esCode: '7', label: '其他原因' },
];

/**
 * 行政处罚处罚机构
 */
const ProcessingAgency = [
  {
    label: '人民银行',
    value: 1,
    children: [
      {
        label: '中国人民银行',
        value: 101,
      },
      {
        label: '国家外汇管理局',
        value: 102,
      },
    ],
  },

  {
    label: '证监会',
    value: 2,
  },

  {
    label: '金融监管局',
    value: 3,
    children: [
      {
        label: '金融监管局',
        value: 301,
      },
      {
        label: '银保监会（原）',
        value: 302,
      },
    ],
  },

  {
    label: '交易所',
    value: 4,
    children: [
      {
        label: '上交所',
        value: 401,
      },
      {
        label: '深交所',
        value: 402,
      },
      {
        label: '北交所',
        value: 403,
      },
      {
        label: '股转系统',
        value: 404,
      },
      {
        label: '上期所',
        value: 405,
      },
      {
        label: '郑商所',
        value: 406,
      },
      {
        label: '中金所',
        value: 407,
      },
      {
        label: '联交所',
        value: 408,
      },
      {
        label: '大商所',
        value: 409,
      },
      {
        label: '股权交易中心',
        value: 410,
      },
    ],
  },

  {
    label: '税务局',
    value: 5,
  },

  {
    label: '市场监督局',
    value: 6,
    children: [
      {
        label: '市场监督局',
        value: 601,
      },
      {
        label: '药品监督局',
        value: 602,
      },
      {
        label: '知识产权局',
        value: 603,
      },
    ],
  },

  {
    label: '发改委',
    value: 7,
  },

  {
    label: '工信部',
    value: 8,
    children: [
      {
        label: '工业和信息化局',
        value: 801,
      },
      {
        label: '通信管理局',
        value: 802,
      },
    ],
  },

  {
    label: '财政部',
    value: 9,
  },

  {
    label: '网信办',
    value: 10,
    children: [
      {
        label: '中央网信办',
        value: 1001,
      },
      {
        label: '地方网信',
        value: 1002,
      },
    ],
  },

  {
    label: '生态环境部',
    value: 12,
  },

  {
    label: '海关',
    value: 13,
  },

  {
    label: '法院',
    value: 14,
  },

  {
    label: '协会',
    value: 15,
    children: [
      {
        label: '证券业协会',
        value: 1501,
      },
      {
        label: '基金业协会',
        value: 1502,
      },
      {
        label: '期货业协会',
        value: 1503,
      },
      {
        label: '交易商协会',
        value: 1504,
      },
      {
        label: '资产评估协会',
        value: 1505,
      },
      {
        label: '会计师协会',
        value: 1506,
      },
      {
        label: '律师协会',
        value: 1507,
      },
      {
        label: '消费者协会',
        value: 1508,
      },
    ],
  },

  {
    label: '其他机构',
    value: 99,
    children: [
      {
        label: '交通运输部',
        value: 9901,
      },
      {
        label: '应急管理部',
        value: 9902,
      },
      {
        label: '住建部',
        value: 9903,
      },
      {
        label: '文化和旅游部',
        value: 9904,
      },
      {
        label: '人力资源和社会保障部',
        value: 9905,
      },
      {
        label: '自然资源部',
        value: 9906,
      },
      {
        label: '农业农村部',
        value: 9907,
      },
      {
        label: '教育部',
        value: 9908,
      },
      {
        label: '公安部',
        value: 9909,
      },
      {
        label: '科技部',
        value: 9910,
      },
      {
        label: '司法部',
        value: 9911,
      },
      {
        label: '民政部',
        value: 9912,
      },
      {
        label: '商务部',
        value: 9913,
      },
      {
        label: '水利部',
        value: 9914,
      },
      {
        label: '军委后勤部',
        value: 9915,
      },
      {
        label: '国家林业和草原局',
        value: 9916,
      },
      {
        label: '烟草专卖局',
        value: 9917,
      },
      {
        label: '国家能源局',
        value: 9918,
      },
      {
        label: '医疗保障局',
        value: 9919,
      },
      {
        label: '卫健委',
        value: 9920,
      },
      {
        label: '城市管理监督局',
        value: 9921,
      },
      {
        label: '统计局',
        value: 9922,
      },
      {
        label: '气象局',
        value: 9923,
      },
      {
        label: '信访局',
        value: 9924,
      },
      {
        label: '国家事业单位管理局',
        value: 9925,
      },
      {
        label: '审计署',
        value: 9926,
      },
      {
        label: '检察院',
        value: 9927,
      },
      {
        label: '纪委监委',
        value: 9929,
      },
      {
        label: '地方政府',
        value: 9928,
      },
    ],
  },
];
