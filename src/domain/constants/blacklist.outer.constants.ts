// import { IndicatorTypeEnums } from 'libs/model/settings/IndicatorTypeEnums';
// import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
// import { DimensionRiskLevelEnum } from 'libs/enums/diligence/DimensionRiskLevelEnum';
// import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';

import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';

export const CompOvsSanctionsFieldNameMapping = {
  nationcode: '国家代码',
  sanctionscode: '制裁代码',
  sanctionstypecode: '制裁种类代码',
  sanctionstype: '制裁种类',
  name: '名称',
  sanctionsregulations: '制裁条例/名单',
  sanctionsmeasure: '制裁措施/项目',
  sanctionsreason: '制裁原因',
  designateddate: '指定日期',
  remark: '备注',
  identifications: '认证信息',
  alias: '别名信息',
  address: '地址信息',
  keynolist: '企业匹配信息',
  federalregisternotice: '联邦公报通知',
  expirationdate: '截止日期',
  startdate: '开始日期',
  lastupdatedate: '最后更新日期',
};

export const ForeignExportControlsCodeTranslation = {
  '1': {
    name: '美国财政部办公室OFAC制裁名单',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'remark',
      'alias',
      'address',
    ],
  },
  '2': {
    name: '英国外交、联邦与发展办公室制裁局FCDO制裁名单',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'sanctionsreason',
      'designateddate',
      'lastupdatedate',
      'alias',
      'address',
    ],
  },
  '3': {
    name: '美国国土安全部UFLPA实体清单',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'sanctionsreason', 'designateddate'],
  },
  '4': {
    name: '美国国防部CMCC中国军事公司名单',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'designateddate', 'keynolist'],
  },
  '5': {
    name: '美国国务院国防贸易管制局禁止名单',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'designateddate', 'federalregisternotice'],
  },
  '6': {
    name: '美国商业部工业安全局BIS实体清单',
    fields: [
      'nationcode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'sanctionsreason',
      'alias',
      'address',
      'keynolist',
      'federalregisternotice',
    ],
  },
  '7': {
    name: '美国商业部工业安全局BIS被拒绝人员名单',
    fields: ['nationcode', 'sanctionstypecode', 'name', 'sanctionsregulations', 'designateddate', 'address', 'expirationdate', 'federalregisternotice'],
  },
  '8': {
    name: '美国商业部工业安全局BIS未经验证的列表',
    fields: ['nationcode', 'sanctionstypecode', 'name', 'sanctionsregulations', 'address', 'keynolist', 'federalregisternotice'],
  },
  '9': {
    name: '美国商业部工业安全局BIS军事最终用户列表',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'sanctionsregulations', 'address', 'keynolist', 'federalregisternotice'],
  },
  '12': {
    name: '亚投行禁止名单',
    fields: ['nationcode', 'sanctionstypecode', 'name', 'sanctionsregulations', 'sanctionsreason', 'designateddate', 'address', 'expirationdate', 'startdate'],
  },
  '13': {
    name: '加拿大自治综合制裁名单',
    fields: ['nationcode', 'sanctionstypecode', 'sanctionstype', 'name', 'address'],
  },
  '14': {
    name: '联合国安理会综合清单',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'designateddate',
      'lastupdatedate',
      'remark',
      'alias',
      'address',
    ],
  },
  '15': {
    name: '联合国制裁委员会制裁名单',
    fields: [
      'nationcode',
      'sanctionscode',
      'sanctionstypecode',
      'sanctionstype',
      'name',
      'sanctionsregulations',
      'sanctionsmeasure',
      'designateddate',
      'lastupdatedate',
      'remark',
      'alias',
      'address',
    ],
  },
};

/**
 * 黑名单类型
 */
export const BlackTypeItems = [
  { value: 'all', label: '全部', esCode: [] },
  { value: 'GovernmentPurchaseIllegal', label: '政府采购严重违法失信行为记录名单', esCode: ['25'] },
  { value: 'GovProcurementIllegal', label: '国央企采购黑名单', esCode: ['74', '77', '78'] },
  { value: 'SafetyProductionEnterprise', label: '安全生产领域失信生产经营单位', esCode: ['2'] },
  { value: 'CustomsList', label: '海关失信企业名单', esCode: ['7'] },
  { value: 'IntellectualPropertyIllegal', label: '知识产权（专利）领域严重失信联合戒对象名单', esCode: ['43'] },
  { value: 'EnvironmentalProtection', label: '环保失信黑名单', esCode: ['13'] },
  { value: 'LaborGuarantee', label: '劳动保障违法', esCode: ['6'] },
  { value: 'ListedCompanyIllegal', label: '违法失信上市公司', esCode: ['18'] },
  { value: 'FgwBlackList', label: '发改委黑名单', esCode: ['57'] },
  // 切换到单独的索引
  //{ value: 'ForeignExportControls', label: '出口管制合规风险企业清单', esCode: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '12', '13', '14', '15'] },
  {
    value: 'SupervisionOfKeyIndustry',
    label: '重点行业领域监管黑名单',
    esCode: ['40', '41', '68', '79', '39', '4', '38', '14', '5', '54', '3', '24', '28', '33', '34'],
  },
  {
    value: 'MedicalMedicineIllegal',
    label: '医疗医药领域黑名单',
    esCode: ['40', '41', '68', '79', '39'],
  },
  { value: 'InspectionAuthority', label: '出入境检验检疫信用管理严重失信企业名单', esCode: ['4'] },
  { value: 'PriceBreakPromise', label: '价格失信黑名单', esCode: ['38'] },
  { value: 'QualityCredit', label: '严重质量失信黑名单', esCode: ['14'] },
  { value: 'MigrantWorkers', label: '拖欠农民工工资黑名单', esCode: ['5'] },
  { value: 'ElectricityIndustryIllegal', label: '电力行业严重违法失信主体', esCode: ['54'] },
  { value: 'Statistics', label: '统计领域严重失信企业及其有关人员', esCode: ['3'] },
  { value: 'EcBlacklist', label: '电子商务领域黑名单', esCode: ['24'] },
  { value: 'LogisticsIndustryIllegal', label: '运输物流行业严重失信黑名单', esCode: ['28'] },
  { value: 'ConstructionEngineeringBlacklist', label: '建筑工程领域黑名单', esCode: ['33', '34'] },
  { value: 'ArmyProcurementIllegal', label: '军队采购失信名单', esCode: ['75', '76'] },
];
