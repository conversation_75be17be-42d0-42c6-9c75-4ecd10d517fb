import { DetailsParamEnums } from '@domain/enums/diligence/DetailsParamEnums';
import { MetricTypeEnums } from '@domain/enums/metric/MetricTypeEnums';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';

/**
 * 合同违约-DimensionTypeEnums.ContractBreach  违约等级
 */
export const ContractBreachDegree = {
  L5: 'L5/极高风险',
  L4: 'L4/高风险',
  L3: 'L3/中风险',
};

/**
 * 指标分类
 */
export const MetricTypeContant = {
  [MetricTypeEnums.Simple]: '尽调指标',
  [MetricTypeEnums.MonitorMetric]: '监控指标',
  [MetricTypeEnums.MonitorSupervisionMetric]: '监管类指标',
  [MetricTypeEnums.MonitorBusinessMetric]: '业务类指标',
};

/**
 * 维度风险
 */
export const DimensionRiskLevelContant = {
  [DimensionRiskLevelEnum.Alert]: '低风险',
  [DimensionRiskLevelEnum.Medium]: '中风险',
  [DimensionRiskLevelEnum.High]: '高风险',
};

/**
 * 关系类型描述
 */
export const RelationTypeConst = {
  [DetailsParamEnums.ForeignInvestment]: '对外投资',
  [DetailsParamEnums.Shareholder]: '参股股东',
  [DetailsParamEnums.EmploymentRelationship]: '董监高/法人关联',
  [DetailsParamEnums.HisForeignInvestment]: '对外投资（历史）',
  [DetailsParamEnums.HisShareholder]: '参股股东（历史）',
  [DetailsParamEnums.ShareholdingRelationship]: '持股关联',
  [DetailsParamEnums.InvestorsRelationship]: '投资关联',
  [DetailsParamEnums.HisInvestorsRelationship]: '投资关联（历史）',
  [DetailsParamEnums.HisShareholdingRelationship]: '持股关联（历史）',
  [DetailsParamEnums.HisLegalAndEmploy]: '董监高/法人（历史）',
  [DetailsParamEnums.ActualController]: '相同实际控制人',
  [DetailsParamEnums.MainInfoUpdateBeneficiary]: '相同受益所有人',
  [DetailsParamEnums.ShareholdingRatio]: '持股/投资股权比例（含历史）',
  [DetailsParamEnums.Branch]: '分支机构',
  [DetailsParamEnums.HitInnerBlackList]: '被列入内部黑名单',
  [DetailsParamEnums.Invest]: '对外投资企业被列入内部黑名单',
  [DetailsParamEnums.Employ]: '董监高/法人关联',
  [DetailsParamEnums.HisEmploy]: '历史董监高/法人关联',
  [DetailsParamEnums.HisInvest]: '历史对外投资',
  [DetailsParamEnums.Guarantee]: '相互担保关联',
  [DetailsParamEnums.EquityPledgeRelation]: '股权出质关联',
  [DetailsParamEnums.HasPhone]: '相同电话号码',
  [DetailsParamEnums.WebsiteRelation]: '相同域名信息',
  [DetailsParamEnums.HasAddress]: '相同经营地址',
  [DetailsParamEnums.HasEmail]: '相同邮箱',
  [DetailsParamEnums.Hold]: '控制关系',
};

export const AssociationTypeConst = {
  [DetailsParamEnums.LegalRepresentative]: '法定代表人',
  [DetailsParamEnums.ActualController]: '实际控制人',
  [DetailsParamEnums.MajorShareholder]: '大股东',
  [DetailsParamEnums.Shareholder]: '股东',
  [DetailsParamEnums.ShareHolderInvest]: '股东类型为投资机构',
};
