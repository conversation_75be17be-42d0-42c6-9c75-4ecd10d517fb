import { DimensionFieldKeyEnums } from '@domain/enums/dimension/dimension.filter.params';
import { DimensionFieldTypeEnums } from '@domain/enums/dimension/DimensionFiledDataTypeEnums';
import { DimensionFieldCompareTypeEnums } from '@domain/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFieldInputTypeEnums } from '@domain/enums/dimension/DimensionFieldInputTypeEnums';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
// import { RelatedPartyTypeMap } from './judgement.constants';
import { cloneDeep } from 'lodash';
import { CompanyDetailDimensionFields } from './dimension/company.detail.dimension.fields';
import { RelatedRiskChangeDimensionFields } from './dimension/related.risk.change.dimension.fields';
import { RiskChangeDimensionFields } from './dimension/risk.change.dimension.fields';

export const BaseDimensionFields = {
  ...CompanyDetailDimensionFields,
  ...RelatedRiskChangeDimensionFields,
  ...RiskChangeDimensionFields,
  [DimensionFieldKeyEnums.duration]: {
    fieldName: '成立时长',
    fieldKey: DimensionFieldKeyEnums.duration,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '成立时长小于等于 12 个月',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
    defaultValue: [12],
    options: [{ unit: '月', min: 0, max: 9999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 0,
  },
  [DimensionFieldKeyEnums.registrationAmount]: {
    fieldName: '注册金额',
    fieldKey: DimensionFieldKeyEnums.registrationAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '注册资本小于等于100万',
    defaultValue: [100],
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
    options: [{ unit: '万元', min: 0, max: 999999999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 0,
  },
  [DimensionFieldKeyEnums.cycle]: {
    fieldName: '统计周期',
    fieldKey: DimensionFieldKeyEnums.cycle,
    dataType: DimensionFieldTypeEnums.Number,
    comment:
      '统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
    defaultValue: [-1],
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    options: [-1, 1, 2, 3, 4, 5],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Radio,
    status: DataStatusEnums.Enabled,
    fieldOrder: 0,
  },
  [DimensionFieldKeyEnums.naturalCycle]: {
    fieldName: '统计自然周期',
    fieldKey: DimensionFieldKeyEnums.naturalCycle,
    dataType: DimensionFieldTypeEnums.Number,
    comment:
      '自然统计周期 不可以传0 -1 表示不限 compareType = Equal; 当 compareType = GreaterThan 表示 近x年 发生时间 > 通过cycle取到的时间； 当 compareType = LessThanOrEqual 表示 x年前 发生时间 ＜= 通过cycle取到的时间；',
    defaultValue: [-1],
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    options: [-1, 1, 2, 3, 4, 5],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Radio,
    status: DataStatusEnums.Enabled,
    fieldOrder: 0,
  },
  [DimensionFieldKeyEnums.range]: {
    fieldName: '时间范围',
    fieldKey: DimensionFieldKeyEnums.range,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '时间范围区间: min-10位时间戳，max-10位时间戳',
    defaultCompareType: DimensionFieldCompareTypeEnums.Between,
    defaultValue: [{ min: 1735546427, max: 1735546427 }],
    options: [{ min: 1735546427, max: 1735546427 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.NumberRange,
    status: DataStatusEnums.Enabled,
    fieldOrder: 0,
  },
  [DimensionFieldKeyEnums.isValid]: {
    fieldName: '数据范围',
    fieldKey: DimensionFieldKeyEnums.isValid,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '数据范围 1:当前有效，0:历史, -1:不限',
    defaultValue: [1],
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    options: [
      { label: '当前有效', value: 1 },
      { label: '历史', value: 0 },
      { label: '不限', value: -1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Radio,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  [DimensionFieldKeyEnums.hitCount]: {
    fieldName: '记录条数',
    fieldKey: DimensionFieldKeyEnums.hitCount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '命中记录条数 > 0',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [0],
    options: [{ unit: '条', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  [DimensionFieldKeyEnums.isShowTip]: {
    fieldName: '不命中是否提示',
    fieldKey: DimensionFieldKeyEnums.isShowTip,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '不命中是否提示 1-提示，2:不提示',
    defaultValue: [1],
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    options: [
      { label: '提示', value: 1 },
      { label: '不提示', value: 2 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Radio,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },

  [DimensionFieldKeyEnums.sortField]: {
    fieldName: '排序',
    fieldKey: DimensionFieldKeyEnums.sortField,
    dataType: DimensionFieldTypeEnums.Object,
    comment: '排序字段',
    defaultValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
    options: [],
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    inputType: DimensionFieldInputTypeEnums.Text,
    isArray: 0,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  // [DimensionFieldKeyEnums.relatedRoleType]: {
  //   fieldName: '关联关系',
  //   fieldKey: DimensionFieldKeyEnums.relatedRoleType,
  //   dataType: DimensionFieldTypeEnums.Object,
  //   comment:
  //     '关联方角色类型：-1:不限 1 主要人员控制企业 2 法定代表人控制企业 3 实际控制人控制企业 4 受益人控制企业 5 分支机构 6 母公司（股东信息(股比>50%)）7 母公司控制企业 8 子公司（对外投资(>50%的企业)）',
  //   defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
  //   defaultValue: [{ label: '不限', value: -1 }],
  //   options: RelatedPartyTypeMap,
  //   isArray: 1,
  //   inputType: DimensionFieldInputTypeEnums.MultiSelect,
  //   status: DataStatusEnums.Enabled,
  //   fieldOrder: 0,
  // },
};

/**
 * 获取基础类型设置并设置默认值
 * @param fieldKey
 * @param fieldOrder
 * @param setValue
 * @param compareType
 */
export const GetFields = (fieldKey: DimensionFieldKeyEnums, fieldOrder: number, setValue?: any, compareType?: DimensionFieldCompareTypeEnums) => {
  const dimensionField = cloneDeep(BaseDimensionFields[fieldKey]);
  dimensionField.fieldOrder = fieldOrder;
  if (setValue) {
    dimensionField.defaultValue = setValue;
  }

  if (compareType) {
    dimensionField.defaultCompareType = compareType;
  }

  return dimensionField;
};
