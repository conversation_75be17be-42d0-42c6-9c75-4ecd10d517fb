import { ApiProperty } from '@nestjs/swagger';

/** 尽调结果等级 */
export enum DueDiligenceResult {
  /** 高风险 */
  highRisk = 2,
  /** 中高风险 */
  mediumRisk = 1,
  /** 中风险 */
  Warning = 0,
  /** 中低风险 */
  tips = -1,
  /** 良好 */
  pass = -2,
}

/**
 * 尽调类型
 */
export enum DueDiligenceType {
  /**风险尽调*/
  RiskInsight = 0,
  /**风险监控*/
  RiskMonitor = 1,
}

export class DiligenceResultPO {
  @ApiProperty({ description: '尽调结果等级 ', enum: DueDiligenceResult, type: Number })
  level: DueDiligenceResult;
  @ApiProperty({ description: '等级分数区间下限 ', type: Number })
  mimScore: number;
  @ApiProperty({ description: '等级分数区间上限， null-无上限 ', type: Number })
  maxScore: number | null;
  @ApiProperty({ description: '等级名称 ', type: String })
  name: string;
}

/** 尽调结果等级默认设置 */
export const DiligenceResultDefaultSetting: DiligenceResultPO[] = [
  { level: DueDiligenceResult.highRisk, mimScore: 75, maxScore: null, name: '高风险' },
  { level: DueDiligenceResult.mediumRisk, mimScore: 60, maxScore: 74, name: '中高风险' },
  { level: DueDiligenceResult.Warning, mimScore: 40, maxScore: 59, name: '中风险' },
  { level: DueDiligenceResult.tips, mimScore: 20, maxScore: 39, name: '中低风险' },
  { level: DueDiligenceResult.pass, mimScore: null, maxScore: 19, name: '良好' },
];
