import { TargetInvestigationEnums, TargetScopeEnums } from '@domain/enums/dimension/FieldValueEnums';

export const JudgeResultMap = new Map([
  ['1', '支持'],
  ['2', '撤诉'],
  ['3', '诉讼中止'],
  ['4', '同意发回重审'],
  ['5', '达成调解'],
  ['6', '驳回'],
  ['7', '同意人身保护令'],
  ['8', '同意管辖权异议'],
  ['11', '撤回申请'],
  ['12', '不支持'],
  ['13', '部分支持'],
  ['14', '撤回上诉'],
  ['15', '驳回上诉'],
  ['16', '撤销原判'],
  ['17', '同意追加被执行人'],
  ['18', '财产保全'],
  ['19', '解除财产保全'],
  ['20', '对方撤诉'],
  ['21', '不支持'],
  ['22', '对方被驳回'],
  ['23', '对方撤回申请'],
  ['24', '不承担责任'],
  ['25', '执行完毕'],
  ['26', '执行中止'],
  ['27', '执行法院变更'],
  ['28', '终结本次执行'],
  ['29', '申请人被驳回'],
  ['30', '原告撤诉'],
]);

export const DocTypeMap = new Map([
  ['ver', '判决书'],
  ['adj', '裁定书'],
  ['med', '调解书'],
  ['dec', '决定书'],
  ['not', '通知书'],
  ['rep', '批复'],
  ['ans', '答复'],
  ['let', '函'],
  ['mak', '令'],
  ['other', '其他'],
]);

/**
 * 裁判文书案由列表
 * 文档链接 https://thoughts.teambition.com/workspaces/6321a5f1cb218e00459c527d/docs/6686034262ca100001ac5edf
 */
export const CaseReasonTypeMap = [
  { label: '洗钱罪', value: ['A030437'] },
  { label: '交通肇事罪', value: ['A0248'] },
  { label: '危险驾驶罪', value: ['A0249'] },
  { label: '买卖合同纠纷', value: ['B040109', 'B04010905', 'B040110', 'C02040109', 'C02040110'] },
];

export const CaseTypes = [
  { label: '民事案件', value: 'ms', caseReasonValue: 'B' }, //民事案件案由码值以B开头
  { label: '刑事案件', value: 'xs', caseReasonValue: 'A' }, //刑事案件案由码值以A开头
  { label: '行政案件', value: 'xz', caseReasonValue: 'D' }, //行政案件案由码值以D开头
  { label: '管辖案件', value: 'gx' },
  { label: '保全案件', value: 'bq' },
  { label: '执行案件', value: 'zx', caseReasonValue: 'C' }, //执行案件案由码值以C开头
  // { label: '', value: 'pc' },
  // { label: '', value: 'zc' },
  // { label: '', value: 'qsx' },
  // { label: '', value: 'gsx' },
  { label: '其他', value: 'other', caseReasonValue: 'E' }, //其他案件案由码值以E开头
];

/**
 * 案件身份
 */
export const CaseRoleMap = [
  { label: '被告', value: 'defendant' },
  { label: '原告', value: 'prosecutor' },
  { label: '第三人', value: 'thirdpartyrole' },
];

export const CaseTargetInvestigation = [
  { label: '主要人员', value: TargetInvestigationEnums.MainStaff },
  { label: '分支机构', value: TargetInvestigationEnums.Branch },
  { label: '企业本身', value: TargetInvestigationEnums.Self },
  { label: '历史主要人员', value: TargetInvestigationEnums.HisMainStaff },
  { label: '实际控制人', value: TargetInvestigationEnums.ActualController },
];

export const PersonCaseTargetInvestigation = [
  { label: '当前法人', value: TargetInvestigationEnums.Legal },
  { label: '历史法人', value: TargetInvestigationEnums.HisLegal },
  { label: '企业本身', value: TargetInvestigationEnums.Self },
  { label: '实际控制人', value: TargetInvestigationEnums.ActualController },
  { label: '第一大股东', value: TargetInvestigationEnums.LargestShareholder },
  { label: 'sf关联方企业', value: TargetInvestigationEnums.IcbcSFRelated },
  { label: 'sit关联方企业', value: TargetInvestigationEnums.SitRelated },
];

export const CreditTargetScope = [
  { label: '失信被执行人', value: TargetScopeEnums.ShiXinCount },
  { label: '被执行人', value: TargetScopeEnums.ZhiXingCount },
  { label: '限制高消费', value: TargetScopeEnums.SumptuaryObjCount },
];
