/* eslint-disable @typescript-eslint/no-use-before-define */
import { EntityManager, In } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { GroupEntity } from '@domain/entities/GroupEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';
import { DimensionHitStrategyFieldsEntity } from '@domain/entities/DimensionHitStrategyFieldsEntity';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { flattenDeep, toPlainObject } from 'lodash';
import * as Bluebird from 'bluebird';
import { RoverExceptions } from '@commons/exceptions/exceptionConstants';
import { DiligenceResultDefaultSetting } from '@domain/constants/diligence.constants';
import { DimensionFieldKeyEnums } from '@domain/enums/dimension/dimension.filter.params';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { ResourceNotFoundException } from '@commons/exceptions/ResourceNotFoundException';

export class PublishResourceParams {
  @ApiProperty({ description: '组织id,不可以为空， 小于等于0可以认为是系统模型' })
  orgId: number;
  @ApiProperty({ description: '操作者id' })
  userId: number;
}

/**
 *
 * @param riskModelId
 * @param entityManager
 * @param testGroupId 只测试指定模型的指定分组下的指标
 * @param testMetricId 只测试制定模型的指定指标
 * @returns
 */
export const getFullRiskModel = async (riskModelId: number, entityManager: EntityManager, batchId?: number) => {
  // 1. 只查询基础字段，不加载关联
  let riskModel = await entityManager.findOne(RiskModelEntity, {
    where: { modelId: riskModelId },
    cache: false,
    select: ['modelId', 'publishedContent', 'branchCode', 'branchCount', 'branchTier', 'resultSetting', 'modelName'],
  });
  if (!riskModel) {
    throw new ResourceNotFoundException(RoverExceptions.ResourceAccess.RiskModelNotFound);
  }
  if (!riskModel?.publishedContent) {
    riskModel = await entityManager.findOne(RiskModelEntity, {
      where: { modelId: riskModelId },
      relations: [
        'groups',
        'groups.groupMetrics',
        'groups.groupMetrics.metricEntity',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.dimensionDef',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.strategyFields',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.strategyFields.dimensionField',
      ],
      cache: false,
    });
  } else {
    const publishedContent = riskModel.publishedContent as unknown as RiskModelEntity;
    // 以下三个字段永远取数据记录的值，他们可能会有变化
    publishedContent.branchCode = riskModel.branchCode; // 有的模型是增加了 branchCode 之前发布的，所以publishedContent中可能没有值
    publishedContent.branchCount = riskModel.branchCount; // 基于模型在同分组下复制模型的话， 这个值会增加
    publishedContent.branchTier = riskModel.branchTier; // 有的模型是增加了 branchTier 之前发布的，所以publishedContent中可能没有值
    riskModel = publishedContent;
  }

  /**
   * 不返回dimensionField中的options
   */
  // riskModel?.groups?.forEach((group: GroupEntity) => {
  //   group?.groupMetrics?.forEach((metrics) => {
  //     metrics?.metricEntity?.dimensionHitStrategies?.forEach((dimHitStrategy) => {
  //       dimHitStrategy?.dimensionHitStrategyEntity?.strategyFields?.forEach((sf) => {
  //         delete sf?.dimensionField?.options;
  //       });
  //     });
  //   });
  // });
  // 3. 优化 groups 处理
  if (riskModel?.groups?.length) {
    // 使用 map 而不是 forEach 来提高性能
    riskModel.groups = riskModel.groups.map((group) => {
      if (group?.groupMetrics?.length) {
        group.groupMetrics = group.groupMetrics.map((metrics) => {
          if (metrics?.metricEntity?.dimensionHitStrategies?.length) {
            metrics.metricEntity.dimensionHitStrategies = metrics.metricEntity.dimensionHitStrategies.map((dimHitStrategy) => {
              if (dimHitStrategy?.dimensionHitStrategyEntity?.strategyFields?.length) {
                dimHitStrategy.dimensionHitStrategyEntity.strategyFields = dimHitStrategy.dimensionHitStrategyEntity.strategyFields.map((sf) => {
                  if (sf?.dimensionField) {
                    delete sf.dimensionField.options;
                  }
                  return sf;
                });
              }
              return dimHitStrategy;
            });
          }
          return metrics;
        });
      }
      return group;
    });
    // 4. 优化排序
    riskModel.groups.sort((a, b) => a.order - b.order);
    riskModel.groups.forEach((group) => {
      if (group.groupMetrics?.length) {
        group.groupMetrics.sort((a, b) => a.order - b.order);
      }
    });
  }
  //5. 处理 resultSetting
  if (!riskModel.resultSetting?.length) {
    // 如果没有配置结果规则，使用系统默认结果规则
    riskModel.resultSetting = DiligenceResultDefaultSetting;
  }
  // if (param?.testGroupId || param?.testMetricId) {
  //   if (param?.testGroupId) {
  //     riskModel.groups = riskModel.groups.filter((t) => t.groupId === param?.testGroupId);
  //   }
  //   if (param?.testMetricId) {
  //     riskModel.groups = riskModel.groups
  //       .map((g) => {
  //         g.groupMetrics = g.groupMetrics.filter((t) => t.metricsId === param?.testMetricId);
  //         return g;
  //       })
  //       .filter((t) => t.groupMetrics.length);
  //   }
  //   // let groups = riskModel.groups.filter((t) => t.groupId === testGroupId);
  //   // groups = groups.map((t) => {
  //   //   t.groupMetrics = t.groupMetrics.filter((t) => t.metricsId === testMetricId && t.groupId === testGroupId);
  //   //   return t;
  //   // });
  //   // riskModel = Object.assign(riskModel, { groups });
  // }
  // riskModel.groups.sort((a, b) => a.order - b.order);
  // riskModel.groups.forEach((group) => {
  //   group.groupMetrics.sort((a, b) => a.order - b.order);
  // });
  // // 如果是监控模型的情况下在模型的维度为RiskChange，添加一个动态的时间范围属性
  // if (param?.batchId && riskModel.modelType === RiskModelTypeEnums.MonitorModel) {
  //   riskModel = await assembleMonitorTimeRangeFields(param?.batchId, riskModel, entityManager);
  // }
  return riskModel;
};

/**
 * 根据执行的维度strategyIds，返回具体的命中规则
 * @param strategyIds
 * @param entityManager
 * @returns
 */
export const getDimensionHitStrategies = async (strategyIds: number[], entityManager: EntityManager) => {
  const strategies = await entityManager.find(DimensionHitStrategyEntity, {
    where: { strategyId: In(strategyIds) },
    relations: ['dimensionDef', 'strategyFields', 'strategyFields.dimensionField'],
    cache: false,
  });

  return strategies;
};

// /** 已废弃
//  * 1，如果是监控动态的模型，在batch上添加batch任务开始和结束时间
//  * 2，模型判断是RiskChange维度的数据，在策略中绑定一个DimensionFieldKeyEnums.range，属性数据
//  * 3，注意: 该属性不落库，适用于全局的监控RiskChange维度，
//  *         在动态展示时需要注意，在查询详情数据时也要带上该属性。
//  *
//  * @param batchId
//  * @param riskModel
//  * @param entityManager
//  */
// export const assembleMonitorTimeRangeFields = async (batchId: number, riskModel: RiskModelEntity, entityManager: EntityManager) => {
//   const batchEntity = await entityManager.findOne(BatchEntity, { batchId });
//   const { batchInfo } = batchEntity;
//   const { batchStartTime, batchEndTime } = batchInfo;
//   riskModel?.groups?.forEach((group: GroupEntity) => {
//     group?.groupMetrics?.forEach((metrics) => {
//       metrics?.metricEntity?.dimensionHitStrategies?.forEach((dimHitStrategy) => {
//         if (dimHitStrategy?.dimensionHitStrategyEntity?.dimensionDef?.source === DimensionSourceEnums?.RiskChange) {
//           // 根据batchStartTime, batchEndTime动态拼接时间范围的限制
//           // 1，插入 strategyFields: DimensionHitStrategyFieldsEntity[];
//           const baseField = BaseDimensionFields[DimensionFieldKeyEnums.range];
//           const dimensionFieldsEntity = Object.assign(new DimensionFieldsEntity(), {
//             ...pick(baseField, ['fieldName', 'fieldKey', 'inputType', 'dataType', 'isArray', 'fieldOrder', 'comment', 'options']),
//             dimensionId: dimHitStrategy.dimensionHitStrategyEntity.dimensionId,
//             fieldId: uuid(),
//           });
//           // 2, 插入 fieldHitStrategy: QueryHitStrategyPO;
//           const strategyField = Object.assign(new DimensionHitStrategyFieldsEntity(), {
//             id: uuid(),
//             comment: baseField.comment,
//             strategyId: dimHitStrategy.dimensionHitStrategyEntity.strategyId,
//             dimensionId: dimHitStrategy.dimensionHitStrategyEntity.dimensionId,
//             dimensionFieldId: dimensionFieldsEntity.fieldId,
//             dimensionFieldKey: dimensionFieldsEntity.fieldKey,
//             dimensionFieldName: dimensionFieldsEntity.fieldName,
//             options: baseField.options,
//             accessScope: 2,
//             createBy: 1,
//             fieldValue: [{ min: batchStartTime, max: batchEndTime }],
//             compareType: DimensionFieldCompareTypeEnums.Between,
//             searchType: DimensionFieldSearchTypeEnums.General,
//             status: DataStatusEnums.Enabled,
//             orgId: riskModel.orgId,
//           });
//           if (!dimHitStrategy?.dimensionHitStrategyEntity?.strategyFields) {
//             dimHitStrategy.dimensionHitStrategyEntity.strategyFields = [];
//           }
//           dimHitStrategy.dimensionHitStrategyEntity.strategyFields.push(strategyField);
//           if (!dimHitStrategy?.dimensionHitStrategyEntity?.fieldHitStrategy) {
//             dimHitStrategy.dimensionHitStrategyEntity.fieldHitStrategy = { must: [], minimum_should_match: 1 };
//           }
//           dimHitStrategy.dimensionHitStrategyEntity.fieldHitStrategy.must.push(strategyField.id);
//         }
//       });
//     });
//   });
//   return riskModel;
// };

export const getMetricDimensionStrategyField = async (
  entityManager: EntityManager,
  riskModelId: number,
  metricName: string,
  dimensionKey: DimensionTypeEnums,
  dimensionFieldKey: DimensionFieldKeyEnums,
) => {
  let strategyFields: DimensionHitStrategyFieldsEntity;

  const fullModel = await getFullRiskModel(riskModelId, entityManager);
  fullModel?.groups?.forEach((group: GroupEntity) => {
    group?.groupMetrics?.forEach((metrics) => {
      if (metrics?.metricEntity?.name == metricName) {
        metrics?.metricEntity?.dimensionHitStrategies?.forEach((dimHitStrategy) => {
          if (dimHitStrategy?.dimensionHitStrategyEntity?.dimensionDef?.key === dimensionKey) {
            strategyFields = dimHitStrategy?.dimensionHitStrategyEntity?.strategyFields.find((sf) => sf.dimensionFieldKey == dimensionFieldKey);
          }
        });
      }
    });
  });
  return strategyFields;
};

export const publishRiskModel = async (
  riskModels: RiskModelEntity[],
  params: PublishResourceParams,
  entityManager: EntityManager,
  skipSubResource?: boolean,
) => {
  const { userId, orgId } = params;
  // 找出关联的并且是未发布状态的资源ID
  return Bluebird.map(riskModels, async (riskModel) => {
    if (riskModel.status === DataStatusEnums.Developing) {
      // await publishGroup(riskModel.groups, params, entityManager);
      riskModel = await getFullRiskModel(riskModel.modelId, entityManager);

      //默认需要同时发布下面的子资源
      if (!skipSubResource) {
        const developingMetrics: MetricsEntity[] = riskModel.groups
          .flatMap((groupMetrics) => groupMetrics.groupMetrics)
          .map((metric) => metric.metricEntity)
          .filter((metricEntity) => metricEntity?.status === DataStatusEnums.Developing);
        if (developingMetrics?.length > 0) {
          await publishMetric(developingMetrics, params, entityManager);
        }
      }
      // 子资源发布完毕，更新模型状态为已发布，并更新发布
      riskModel = await getFullRiskModel(riskModel.modelId, entityManager);
      riskModel.orgId = orgId;
      riskModel.updateBy = userId;
      riskModel.publishBy = userId;
      riskModel.updateDate = new Date();
      riskModel.status = DataStatusEnums.Enabled;
      riskModel.publishedDate = new Date();

      await entityManager.update(RiskModelEntity, riskModel.modelId, {
        orgId,
        updateBy: userId,
        publishBy: userId,
        publishedDate: riskModel.publishedDate,
        publishedContent: toPlainObject(riskModel),
        status: DataStatusEnums.Enabled,
      });

      riskModel.publishedContent = toPlainObject(riskModel);
      return riskModel.publishedContent;
    }
  });
};

/**
 *
 * 分组只会归属到一个模型，状态跟着模型走，不需要发布分组，只需要发布分组下面的指标即可
 *
 * @param groupEntities
 * @param params
 * @param entityManager
 * @deprecated
 */
export const publishGroup = async (groupEntities: GroupEntity[], params: PublishResourceParams, entityManager: EntityManager) => {
  const groupIds: number[] = [];
  groupEntities.forEach((t) => {
    if (t.status == DataStatusEnums.Developing) {
      groupIds.push(t.groupId);
    }
  });
  if (groupIds.length > 0) {
    await publishMetric(flattenDeep(groupEntities.map((g) => g.groupMetrics.map((gm) => gm.metricEntity))), params, entityManager);
    const partialUpdate: any = {
      status: DataStatusEnums.Enabled,
      updateBy: params.userId,
      publishedDate: new Date(),
      publishBy: params.userId,
    };
    if (params.orgId) {
      partialUpdate.orgId = params.orgId;
    }
    await entityManager.update(GroupEntity, groupIds, partialUpdate);
  }
};

export const publishMetric = async (metricEntities: MetricsEntity[], params: PublishResourceParams, entityManager: EntityManager) => {
  const metricsIds: number[] = [];
  metricEntities.forEach((metric) => {
    if (metric.status === DataStatusEnums.Developing) {
      metricsIds.push(metric.metricsId);
    }
  });
  if (metricsIds.length > 0) {
    const strategies = flattenDeep(metricEntities.map((metric) => metric.dimensionHitStrategies.map((t) => t.dimensionHitStrategyEntity))).filter(
      (d) => d.status === DataStatusEnums.Developing,
    );
    if (strategies.length) {
      await publishStrategy(strategies, params, entityManager);
    }
    const partialUpdate: any = {
      status: DataStatusEnums.Enabled,
      updateBy: params.userId,
      publishedDate: new Date(),
      publishBy: params.userId,
    };
    if (params.orgId) {
      partialUpdate.orgId = params.orgId;
    }
    await entityManager.update(MetricsEntity, metricsIds, partialUpdate);
  }
};

export const publishStrategy = async (strategyEntities: DimensionHitStrategyEntity[], params: PublishResourceParams, entityManager: EntityManager) => {
  const strategyIds: number[] = [];
  strategyEntities.forEach((s) => {
    if (s.status === DataStatusEnums.Developing) {
      strategyIds.push(s.strategyId);
    }
  });
  if (strategyIds.length > 0) {
    await publishStrategyFields(flattenDeep(strategyEntities.map((s) => s.strategyFields)), params, entityManager);
    const partialUpdate: any = {
      status: DataStatusEnums.Enabled,
      updateBy: params.userId,
      publishedDate: new Date(),
      publishBy: params.userId,
    };
    if (params.orgId) {
      partialUpdate.orgId = params.orgId;
    }
    await entityManager.update(DimensionHitStrategyEntity, strategyIds, partialUpdate);
  }
};

export const publishStrategyFields = async (
  strategyFieldEntities: DimensionHitStrategyFieldsEntity[],
  params: PublishResourceParams,
  entityManager: EntityManager,
) => {
  const strategyFieldsIds: number[] = [];
  strategyFieldEntities.forEach((s) => {
    if (s.status === DataStatusEnums.Developing) {
      strategyFieldsIds.push(s.id);
    }
  });
  if (strategyFieldsIds.length > 0) {
    const partialUpdate: any = {
      status: DataStatusEnums.Enabled,
      updateBy: params.userId,
      publishedDate: new Date(),
      publishBy: params.userId,
    };
    if (params.orgId) {
      partialUpdate.orgId = params.orgId;
    }
    await entityManager.update(DimensionHitStrategyFieldsEntity, strategyFieldsIds, partialUpdate);
  }
};
