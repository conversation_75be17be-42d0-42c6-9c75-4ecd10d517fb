import { Entity<PERSON>anager, In } from 'typeorm';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { ResourceEditForbiddenException } from '@commons/exceptions/ResourceEditForbiddenException';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { GroupEntity } from '@domain/entities/GroupEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';
import { Redis } from 'ioredis';
import { v4 } from 'uuid';
import { ResourceEditableCheckRequest } from '@domain/model/riskModel/ResourceEditableCheckRequest';
import { PlatformUser } from '@domain/model/common';
import { DimensionHitStrategyFieldsEntity } from '@domain/entities/DimensionHitStrategyFieldsEntity';
import { BadRequestException } from '@nestjs/common';
import { RoverExceptions } from '@commons/exceptions/exceptionConstants';
import { DistributedResourceTypeEnums } from '@domain/enums/DistributedResourceTypeEnums';
import { DistributedSystemResourceEntity } from '@domain/entities/DistributedSystemResourceEntity';
import { DistributeResourceStatusEnums } from '@domain/enums/DistributeResourceStatusEnums';
import * as _ from 'lodash';

const editTokenPrefix = 'edit_token';
export const generateEditToken = async (redisClient: Redis, resource: ResourceEditableCheckRequest, roverUser: PlatformUser) => {
  const token = v4();
  const value = `${resource.riskModelId}_${resource.metricsId}_${roverUser.userId}_${roverUser.currentOrg}_${roverUser.currentProduct}`;
  await redisClient.set(`${editTokenPrefix}_${token}`, value, 'EX', 60 * 10);
  return token;
};

export const validateEditToken = async (redisClient: Redis, token: string, resource: ResourceEditableCheckRequest, roverUser: PlatformUser) => {
  const v = await redisClient.get(`${editTokenPrefix}_${token}`);
  const value = `${resource.riskModelId}_${resource.metricsId}_${roverUser.userId}_${roverUser.currentOrg}_${roverUser.currentProduct}`;
  if (v == value) {
    return true;
  }
  return false;
};

/**
 * 模型名称重复验证
 * 同一个组织下的，同一个modelType下的，状态为开发中和已启用的，模型名称不可重复
 * @param modelName
 * @param entityManager
 * @param riskModelId
 * @param noThrowError
 */
export const riskModelNameCheck = async (
  modelName: string,
  modelType: number,
  currentUser: PlatformUser,
  entityManager: EntityManager,
  riskModelId?: number,
) => {
  const resourceEntityList = await entityManager.find(DistributedSystemResourceEntity, {
    where: {
      resourceType: In([DistributedResourceTypeEnums.RiskModel, DistributedResourceTypeEnums.MonitorRiskModel]),
      orgId: currentUser.currentOrg,
      product: currentUser.currentProduct,
      distributeStatus: In([DistributeResourceStatusEnums.Trial, DistributeResourceStatusEnums.Enable]),
    },
  });
  if (resourceEntityList?.length) {
    const resourceIds = resourceEntityList.map((item) => item.resourceId);
    const whereCondition: any = {
      modelName,
      modelType,
      status: In([DataStatusEnums.Developing, DataStatusEnums.Enabled]),
    };
    if (riskModelId) {
      whereCondition.modelId = In(_.difference(resourceIds, [riskModelId]));
    } else {
      whereCondition.modelId = In(resourceIds);
    }
    const duplicateModelNameEntity = await entityManager.findOne(RiskModelEntity, { where: whereCondition });
    if (duplicateModelNameEntity) {
      throw new BadRequestException(RoverExceptions.ResourceAccess.RiskModelNameDuplicate);
    }
  }
};

export const riskModelStatusCheck = async (riskModelId: number, entityManager: EntityManager, noThrowError?: boolean) => {
  const entity = await entityManager.findOne(RiskModelEntity, { where: { modelId: riskModelId } });
  if (!entity) {
    throw new BadRequestException(RoverExceptions.ResourceAccess.RiskModelNotFound);
  }
  if (entity.status !== DataStatusEnums.Developing) {
    if (noThrowError) {
      return false;
    }
    throw new ResourceEditForbiddenException();
  }
  return true;
};

export const groupStatusCheck = async (groupId: number, entityManager: EntityManager, noThrowError?: boolean) => {
  const entity = await entityManager.findOne(GroupEntity, { where: { groupId } });
  if (!entity) {
    throw new BadRequestException(RoverExceptions.ResourceAccess.RiskModelNotFound);
  }
  if (entity.status !== DataStatusEnums.Developing) {
    if (noThrowError) {
      return false;
    }
    throw new ResourceEditForbiddenException();
  }
  return true;
};

export const metricStatusCheck = async (metricsId: number, entityManager: EntityManager, noThrowError?: boolean) => {
  const entity = await entityManager.findOne(MetricsEntity, { where: { metricsId } });
  if (!entity) {
    throw new BadRequestException(RoverExceptions.ResourceAccess.MetricNotFound);
  }
  if (entity.status !== DataStatusEnums.Developing) {
    if (noThrowError) {
      return false;
    }
    throw new ResourceEditForbiddenException();
  }
  return true;
};

export const dimensionHitStrategyCheck = async (dimensionHitStrategyId: number, entityManager: EntityManager, noThrowError?: boolean) => {
  const entity = await entityManager.findOne(DimensionHitStrategyEntity, { where: { strategyId: dimensionHitStrategyId } });
  if (!entity) {
    throw new BadRequestException(RoverExceptions.ResourceAccess.HitStrategyNotFound);
  }
  if (entity.status !== DataStatusEnums.Developing) {
    if (noThrowError) {
      return false;
    }
    throw new ResourceEditForbiddenException();
  }
  return true;
};

export const dimensionHitStrategyFieldCheck = async (hitStrategyFieldId: number, entityManager: EntityManager, noThrowError?: boolean) => {
  const entity = await entityManager.findOne(DimensionHitStrategyFieldsEntity, { where: { id: hitStrategyFieldId } });
  if (!entity) {
    throw new BadRequestException(RoverExceptions.ResourceAccess.HitStrategyFieldNotFound);
  }
  if (entity.status !== DataStatusEnums.Developing) {
    if (noThrowError) {
      return false;
    }
    throw new ResourceEditForbiddenException();
  }
  return true;
};
