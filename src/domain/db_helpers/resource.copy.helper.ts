import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { EntityManager, In } from 'typeorm';
import { GroupEntity } from '@domain/entities/GroupEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';
import { flattenDeep, pick } from 'lodash';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import * as Bluebird from 'bluebird';
import { GroupMetricRelationEntity } from '@domain/entities/GroupMetricRelationEntity';
import { DataCategoryEnums } from '@domain/enums/DataCategoryEnums';
import { DimensionHitStrategyFieldsEntity } from '@domain/entities/DimensionHitStrategyFieldsEntity';
import { MetricDimensionRelationEntity } from '@domain/entities/MetricDimensionRelationEntity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { getFullRiskModel } from './resource.publish.helper';
import { QueryHitStrategyPO } from '@domain/model/QueryHitStrategyPO';
import { SystemDefaultOrgId } from '@commons/constants/common';
import { IsIn, IsNotEmpty, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { v4 } from 'uuid';

export class CopyResourceParams {
  @ApiProperty({ description: '模型ID' })
  @IsNotEmpty()
  riskModelId: number;

  @ApiPropertyOptional({ description: '组织id,可以为空，为空就用原来的值' })
  @IsOptional()
  orgId?: number;

  @ApiProperty({ description: '操作者id' })
  userId: number;

  @ApiPropertyOptional({ description: '指标id,可以为空，为空就是复制所有指标，否则只复制指定指标' })
  metricsIds?: number[];

  @ApiPropertyOptional({ description: '策略id,可以为空，为空就是复制所有策略，否则只复制指定策略' })
  strategyIds?: number[];

  @ApiPropertyOptional({ description: '策略属性id,可以为空，为空就是复制所有字段，否则只复制指定字段' })
  strategyFieldIds?: number[];

  @ApiPropertyOptional({ description: '复制类型，0 复制为当前模型的新版本，1 复制为新的模型' })
  @IsOptional()
  @IsIn([0, 1])
  @Type(() => Number)
  copyType?: number;

  @ApiProperty({
    description: '是否需要替换原设置：0 不需要，1 需要',
    enum: [0, 1],
  })
  @IsIn([0, 1])
  @Type(() => Number)
  needReplace?: number = 0;
}

export class CopiedResourcePO {
  oldId: number;
  newId: number;
  copied: boolean;
}

export class StrategyFieldCopyResult extends CopiedResourcePO {
  // newField: DimensionHitStrategyFieldsEntity;
}

export class StrategyCopyResult extends CopiedResourcePO {
  fieldResults: StrategyFieldCopyResult[];
  // newStrategy: DimensionHitStrategyEntity;
}

export class MetricCopyResult extends CopiedResourcePO {
  strategyResults: StrategyCopyResult[];
  newMetric: MetricsEntity;
}

export class GroupCopyResult extends CopiedResourcePO {
  metricResults: MetricCopyResult[];
}

export class RiskModelCopyResultPO extends CopiedResourcePO {
  groupResults: GroupCopyResult[];
}

const getResourceCategory = (orgId: number) => {
  return !orgId || orgId == SystemDefaultOrgId ? DataCategoryEnums.System : DataCategoryEnums.Business;
};

/**
 * 用map的方式，将原来的id替换成新的id
 * @param po
 * @param mappings
 */
export const queryHitStrategyReplace = (po: QueryHitStrategyPO, mappings: Map<number, number>) => {
  const must = [];
  const should = [];
  const must_not = [];
  po.must?.forEach((m) => {
    if (mappings[m]) {
      must.push(mappings[m]);
    } else {
      must.push(m);
    }
  });
  po.should?.forEach((m) => {
    if (mappings[m]) {
      should.push(mappings[m]);
    } else {
      should.push(m);
    }
  });

  po.must_not?.forEach((m) => {
    if (mappings[m]) {
      must_not.push(mappings[m]);
    } else {
      must_not.push(m);
    }
  });
  po.must = must;
  po.must_not = must_not;
  po.should = should;
  return po;
};

const findCopyFrom = {
  dimensionHitStrategy: (newItems: DimensionHitStrategyEntity[], originalId: number) => {
    return newItems.find((s) => s?.extendFrom?.split('.')?.at(-1) === originalId + '');
  },
  metric: (newItems: MetricsEntity[], originalId: number) => {
    return newItems.find((s) => s?.extendFrom?.split('.')?.at(-1) === originalId + '');
  },
  group: (newItems: GroupEntity[], originalId: number) => {
    return newItems.find((s) => s?.extendFrom?.split('.')?.at(-1) === originalId + '');
  },
  hitStrategyFields: (newFields: DimensionHitStrategyFieldsEntity[], originalFieldId: number) => {
    return newFields.find((f) => f?.extendFrom?.split('.')?.at(-1) === originalFieldId + '');
  },
};

export const copyDimensionHitStrategyField = async (
  params: CopyResourceParams,
  needCopyStrategyList: DimensionHitStrategyEntity[],
  transactionManager: EntityManager,
): Promise<StrategyFieldCopyResult[]> => {
  const copyResults: StrategyFieldCopyResult[] = [];
  const { userId, orgId, strategyFieldIds, needReplace } = params;

  const newFields: DimensionHitStrategyFieldsEntity[] = flattenDeep(
    needCopyStrategyList.map((strategyEntity) => {
      let toCopyStrategyFields;
      if (strategyFieldIds?.length > 0) {
        toCopyStrategyFields = strategyEntity.strategyFields.filter((f) => strategyFieldIds?.includes(f.id));
      } else {
        toCopyStrategyFields = strategyEntity.strategyFields;
      }
      if (toCopyStrategyFields?.length > 0) {
        return toCopyStrategyFields.map((f) => {
          const toSave = Object.assign(
            new DimensionHitStrategyFieldsEntity(),
            pick(f, [
              'dimensionFieldId',
              'fieldValue',
              'searchType',
              'compareType',
              'dimensionId',
              'comment',
              'dimensionFieldKey',
              'dimensionFieldName',
              'accessScope',
              'options',
            ]),
          );
          toSave.orgId = orgId || toSave.orgId;
          toSave.status = DataStatusEnums.Developing;
          toSave.category = getResourceCategory(orgId);
          toSave.createBy = userId;
          toSave.extendFrom = f.extendFrom ? `${f.strategyId}.${f.extendFrom}` : f.id + '';
          toSave.strategyId = strategyEntity.strategyId;
          return toSave;
        });
      }
    }),
  ).filter((t) => t);
  await transactionManager.insert(DimensionHitStrategyFieldsEntity, newFields);

  // 修改  DimensionHitStrategyEntity.fieldHitStrategy
  // 构造新老 DimensionHitStrategyFieldsEntity 的mappings
  const mappings: Map<number, number> = new Map();
  newFields.forEach((newField) => {
    const oldFieldId = newField.extendFrom?.split('.')?.at(-1);
    mappings[oldFieldId] = newField.id;
    const copyResult = new StrategyFieldCopyResult();
    copyResult.oldId = Number(oldFieldId);
    copyResult.newId = newField.id;
    // copyResult.newField = newField;
    copyResults.push(copyResult);
  });
  await Bluebird.map(needCopyStrategyList, (s) => {
    const newFieldHitStrategy = queryHitStrategyReplace(s.fieldHitStrategy, mappings);
    return transactionManager.update(DimensionHitStrategyEntity, s.strategyId, {
      fieldHitStrategy: newFieldHitStrategy,
    });
  });
  return copyResults;
};

/**
 * 复制 DimensionHitStrategyEntity[]
 *
 * DimensionHitStrategyEntity 和 DimensionHitStrategyEntity.strategyFields 是 oneToMany 的关系, 所以一旦 DimensionHitStrategyEntity 被复制了
 * 就会把这个DimensionHitStrategyEntity.strategyFields 也复制一份
 *
 * dimensionHitStrategies 和  params中指定的 strategyIds 的交集才会进行复制
 *
 * @param params
 * @param dimensionHitStrategies 需要复制的  MetricDimensionRelationEntity
 * @param transactionManager
 * @param bindToMetricId 复制DimensionHitStrategyEntity之后，绑定给指定的metricsId ，如果不指定，则只复制hitStrategy 不绑定它和metric的关系
 */
export const copyDimensionHitStrategy = async (
  params: CopyResourceParams,
  dimensionHitStrategies: MetricDimensionRelationEntity[],
  transactionManager: EntityManager,
  bindToMetricId?: number,
): Promise<MetricCopyResult> => {
  const metricCopyResult = new MetricCopyResult();
  const copyResults: StrategyCopyResult[] = [];
  const { userId, orgId, needReplace } = params;
  const toCopyRelations = dimensionHitStrategies.filter((d) => params.strategyIds?.some((s) => s === d.dimensionStrategyId));
  let copiedHitStrategies: DimensionHitStrategyEntity[] = [];
  const mappings: Map<number, number> = new Map();
  if (toCopyRelations) {
    // 复制 DimensionHitStrategyEntity[]
    copiedHitStrategies = await transactionManager.save(
      DimensionHitStrategyEntity,
      toCopyRelations.map((metricDimensionHitStrategy) => {
        const h = metricDimensionHitStrategy.dimensionHitStrategyEntity;
        const toSave = Object.assign(
          new DimensionHitStrategyEntity(),
          pick(h, ['dimensionId', 'strategyName', 'template', 'comment', 'template', 'category', 'fieldHitStrategy', 'strategyRole']),
        );
        toSave.orgId = orgId || h.orgId;
        toSave.status = DataStatusEnums.Developing;
        toSave.category = getResourceCategory(toSave.orgId);
        toSave.createBy = userId;
        toSave.extendFrom = h.extendFrom ? `${h.extendFrom}.${h.strategyId}` : h.strategyId + '';
        return toSave;
      }),
    );
    for (const hitStrategy of toCopyRelations) {
      const oldStrategy = hitStrategy.dimensionHitStrategyEntity;
      const newStrategy = findCopyFrom.dimensionHitStrategy(copiedHitStrategies, oldStrategy.strategyId);
      newStrategy.strategyFields = oldStrategy.strategyFields;
      // const new
      const fieldsCopyResults = await copyDimensionHitStrategyField(params, [newStrategy], transactionManager);
      const copyResult = new StrategyCopyResult();
      copyResult.oldId = hitStrategy.dimensionStrategyId;
      copyResult.newId = newStrategy.strategyId;
      mappings[copyResult.oldId] = copyResult.newId;
      copyResult.fieldResults = fieldsCopyResults;
      copyResult.copied = true;
      copyResults.push(copyResult);
    }
  } else {
    return metricCopyResult;
  }
  if (bindToMetricId) {
    //重新绑定关系 新的 metricsId -  新的 strategyId or 老的 strategyId
    const toSaveArray: MetricDimensionRelationEntity[] = [];
    for (let i = 0; i < dimensionHitStrategies.length; i++) {
      const originalRel = dimensionHitStrategies[i];
      // const newHitStrategy = copiedHitStrategies.find((s) => s.extendFrom?.split('.')?.at(-1) === originalRel.dimensionStrategyId + '');
      const newHitStrategy = findCopyFrom.dimensionHitStrategy(copiedHitStrategies, originalRel.dimensionStrategyId);
      if (newHitStrategy || bindToMetricId !== originalRel.metricsId) {
        //当 hitStrategy 被复制了 或者 新绑定的metricID 跟原来的不是同一个的时候，才新建或者复制关系
        const toSave = Object.assign(new MetricDimensionRelationEntity(), pick(originalRel, ['priority', 'order', 'isPreCondition', 'template']));
        toSave.dimensionStrategyId = newHitStrategy?.strategyId || originalRel.dimensionStrategyId;
        toSave.metricsId = bindToMetricId;
        toSaveArray.push(toSave);
      }
    }
    if (toSaveArray.length) await transactionManager.save(MetricDimensionRelationEntity, toSaveArray);
    // 重新绑定 指标的命中规则
    const newMetric = await transactionManager.findOne(MetricsEntity, { where: { metricsId: bindToMetricId } });
    const newFieldHitStrategy = newMetric.hitStrategy.map((t) => {
      return queryHitStrategyReplace(t, mappings);
    });
    await transactionManager.update(MetricsEntity, newMetric.metricsId, {
      hitStrategy: newFieldHitStrategy,
    });
    metricCopyResult.newMetric = newMetric;
  }
  if (needReplace === 1) {
    // 删除老的关联关系
    await transactionManager.delete(MetricDimensionRelationEntity, {
      metricsId: bindToMetricId,
      dimensionStrategyId: In(params.strategyIds),
    });
  }
  metricCopyResult.strategyResults = copyResults;
  return metricCopyResult;
};

/**
 * 复制指标和指标组之间的绑定
 *
 * groupMetrics 和 params.metricsIds 的交集才会复制，否则不会复制metric，只会复制 和 newGroupId的绑定关系
 *
 * @param params
 * @param groupMetrics
 * @param transactionManager
 * @param bindToGroupId 如果有值，会复制指标的同时，把指标绑定到新的指标组，如果没有值，则把新复制的指标但是不绑定到group中
 * @param needCopyDimensionHitStrategy
 */
export const copyMetricsAndRelations = async (
  params: CopyResourceParams,
  groupMetrics: GroupMetricRelationEntity[],
  transactionManager: EntityManager,
  bindToGroupId?: number,
  needCopyDimensionHitStrategy?: boolean,
): Promise<MetricCopyResult[]> => {
  const { userId, orgId, needReplace } = params;
  const metricCopyResults: MetricCopyResult[] = [];
  const needCopyGroupMetrics: GroupMetricRelationEntity[] = groupMetrics.filter((g) => params.metricsIds?.some((m) => m === g.metricsId));
  let copiedMetrics: MetricsEntity[] = [];
  if (needCopyGroupMetrics.length) {
    copiedMetrics = await transactionManager.save(
      MetricsEntity,
      needCopyGroupMetrics.map((groupMetric) => {
        const metric = groupMetric.metricEntity;
        const toSave = Object.assign(
          new MetricsEntity(),
          pick(metric, ['name', 'riskLevel', 'isVeto', 'metricType', 'score', 'productCode', 'category', 'comment', 'hitStrategy', 'detailsJson']),
        );
        toSave.orgId = orgId || metric.orgId;
        toSave.extendFrom = metric.extendFrom ? `${metric.extendFrom}.${metric.metricsId}` : metric.metricsId + '';
        toSave.createBy = userId;
        toSave.status = DataStatusEnums.Developing;
        toSave.category = getResourceCategory(toSave.orgId);
        return toSave;
      }),
    );

    await Bluebird.map(needCopyGroupMetrics, async (groupMetric) => {
      const metricCopyResult: MetricCopyResult = new MetricCopyResult();
      const newMetric = findCopyFrom.metric(copiedMetrics, groupMetric.metricsId);
      metricCopyResult.newId = newMetric.metricsId;
      metricCopyResult.oldId = groupMetric.metricsId;
      if (params.strategyIds?.length && needCopyDimensionHitStrategy) {
        // 目前 needCopyDimensionHitStrategy 都是 false，所以这里不需要判断
        // 需要同时复制 指标的命中规则
        const toCopyStrategies = groupMetric.metricEntity.dimensionHitStrategies.filter((d) => params.strategyIds.includes(d.dimensionStrategyId));
        if (toCopyStrategies) {
          const hitStrategyCopyResult: MetricCopyResult = await copyDimensionHitStrategy(params, toCopyStrategies, transactionManager, newMetric.metricsId);
          metricCopyResult.strategyResults = hitStrategyCopyResult.strategyResults;
          metricCopyResult.newMetric = hitStrategyCopyResult.newMetric;
        }
      } else {
        //不需要复制命中规则，还是用老的规则，只需要把 规则-新metric的关系重新绑定一下即可
        await transactionManager.save(
          MetricDimensionRelationEntity,
          flattenDeep(
            needCopyGroupMetrics.map((o) => {
              const newMetric = findCopyFrom.metric(copiedMetrics, o.metricsId);
              return o.metricEntity.dimensionHitStrategies.map((o) => {
                const toSave = Object.assign(
                  new MetricDimensionRelationEntity(),
                  pick(o, ['priority', 'order', 'isPreCondition', 'template', 'dimensionStrategyId']),
                );
                toSave.metricsId = newMetric.metricsId;
                return toSave;
              });
            }),
          ),
        );
        metricCopyResult.newMetric = newMetric;
      }
      metricCopyResults.push(metricCopyResult);
    });
  }

  if (bindToGroupId) {
    // 重新绑定关系 指定的groupID， 新的metricsId or  老的metricsId
    const toSaveArray: GroupMetricRelationEntity[] = [];
    for (let i = 0; i < groupMetrics.length; i++) {
      const originalRel = groupMetrics[i];
      const newMetric = copiedMetrics.find((s) => s.extendFrom?.split('.')?.at(-1) === originalRel.metricEntity.metricsId + '');

      if (newMetric || bindToGroupId !== originalRel.groupId) {
        const toSave = Object.assign(new GroupMetricRelationEntity(), pick(originalRel, ['order']));
        toSave.metricsId = newMetric?.metricsId || originalRel.metricsId;
        toSave.groupId = bindToGroupId;
        toSaveArray.push(toSave);
      }
    }
    await transactionManager.save(GroupMetricRelationEntity, toSaveArray);
    if (needReplace === 1) {
      // 删除老的关联关系
      const oldMetricsIds = groupMetrics.map((oldGroupMetric) => oldGroupMetric.metricsId);
      await transactionManager.delete(GroupMetricRelationEntity, {
        metricsId: In(oldMetricsIds),
        groupId: bindToGroupId,
      });
    }
  }

  return metricCopyResults;
};

/**
 * 复制之前要先梳理 父子关系，确保复制后的父子关系也是正确的
 *
 * group只会归属为一个riskModel，所以复制riskModel一定需要把group全部复制一份
 *
 * @param newRiskModelId
 * @param params
 * @param groupEntities 需要有关联的子对象
 * @param transactionManager
 * @param needCopyMetric 是否复制指标，默认共用之前的指标，只是重新创建关系即可。 如果为true，会先复制指标，然后把复制的指标绑定到新的分组
 * @param needCopyHitStrategy 是否复制命中规则，默认共用之前的命中规则，只是重新创建关系即可。 如果为true，会先复制命中规则，然后把复制的命中规则绑定到新的指标上
 */
const copyGroupsToModel = async (
  newRiskModelId: number,
  params: CopyResourceParams,
  groupEntities: GroupEntity[],
  transactionManager: EntityManager,
  needCopyMetric = false,
  needCopyHitStrategy = false,
): Promise<GroupCopyResult[]> => {
  const { userId, orgId } = params;

  //先找出 所有的父级group和普通的没有层级关系的分组，先复制这些group， 然后复制子级group的时候，要同时修改子级group的parentGroup
  const parentGroups = groupEntities.filter((group) => group.parentGroupId === null);
  const childrenGroups = groupEntities
    .filter((group) => group.parentGroupId !== null && groupEntities.some((g) => g.parentGroupId == group.groupId))
    .sort((a, b) => a.parentGroupId - b.parentGroupId);
  const copiedGroupIdMapping = {};
  if (parentGroups.length) {
    const copiedParentGroups = await transactionManager.save(
      GroupEntity,
      parentGroups.map((group) => {
        const toSave = Object.assign(new GroupEntity(), {
          ...pick(group, ['isVirtual', 'groupName', 'category', 'comment', 'detailsJson', 'riskLevel', 'productCode', 'order']),
          riskModelId: newRiskModelId,
          createBy: userId,
        });
        toSave.orgId = orgId || group.orgId;
        toSave.modelId = newRiskModelId;
        toSave.category = getResourceCategory(toSave.orgId);
        toSave.extendFrom = group.extendFrom ? `${group.extendFrom}.${group.groupId}` : group.groupId + '';
        toSave.status = DataStatusEnums.Developing;
        toSave.parentGroupId = null;
        toSave.createBy = userId;
        return toSave;
      }),
    );
    for (let i = 0; i < copiedParentGroups.length; i++) {
      copiedGroupIdMapping[parentGroups[i].groupId] = copiedParentGroups[i].groupId;
    }
  }

  if (childrenGroups.length) {
    //字分组需要按照顺序(parentGroupId从小到大)一个一个复制  ，确保当它自己本身也是别人的父级分组的时候不会错乱
    for (let i = 0; i < childrenGroups.length; i++) {
      const group = childrenGroups[i];
      const toSave = Object.assign(new GroupEntity(), {
        ...pick(group, ['isVirtual', 'groupName', 'category', 'comment', 'detailsJson', 'riskLevel', 'productCode', 'order']),
        riskModelId: newRiskModelId,
        createBy: userId,
      });
      toSave.extendFrom = group.extendFrom ? `${group.extendFrom}.${group.groupId}` : group.groupId + '';
      toSave.status = DataStatusEnums.Developing;
      toSave.parentGroupId = copiedGroupIdMapping[group.parentGroupId];
      toSave.createBy = userId;
      toSave.orgId = orgId || group.orgId;
      const dbEntity = await transactionManager.save(GroupEntity, toSave);
      copiedGroupIdMapping[group.groupId] = dbEntity.groupId;
    }
  }

  const copyResults: GroupCopyResult[] = [];
  // copy metrics and save new binding relation
  await Bluebird.map(groupEntities, async (group) => {
    const groupCopyResult: GroupCopyResult = new GroupCopyResult();
    groupCopyResult.oldId = group.groupId;
    groupCopyResult.newId = copiedGroupIdMapping[group.groupId];
    const groupMetrics = group.groupMetrics;
    const needCopyRelations = needCopyMetric ? groupMetrics.filter((g) => params.metricsIds.some((m) => m === g.metricsId)) : [];
    const toRefreshRelations = groupMetrics.filter((g) => !needCopyRelations.some((n) => n.metricsId === g.metricsId));

    if (needCopyRelations.length) {
      //如果需要同时复制指标，那么先复制指标并绑定关系
      groupCopyResult.metricResults = await copyMetricsAndRelations(
        params,
        needCopyRelations,
        transactionManager,
        copiedGroupIdMapping[group.groupId],
        needCopyHitStrategy,
      );
    }
    copyResults.push(groupCopyResult);
    if (toRefreshRelations.length) {
      //对于不需要复制指标，那么直接绑定新的关系即可
      await transactionManager.save(
        GroupMetricRelationEntity,
        toRefreshRelations.map((g) => {
          return {
            groupId: copiedGroupIdMapping[group.groupId],
            metricsId: g.metricsId,
            status: g.status || DataStatusEnums.Enabled,
            order: g.order,
            createBy: userId,
          };
        }),
      );
    }
  });
  return copyResults;
};

/**
 * 如果模型是 Developing 状态，则不需要复制模型及分组，只需要复制指定的metric，dimensionHitStrategy 等并刷新关系绑定即可
 * 如果模型是 非 Developing 状态， 则要完整的复制模型及分组， 如果有指定metric，dimensionHitStrategy 则复制他们并刷新关系， 如果没有指定，则只把老的 group-metric 关系复制即可
 *
 * @param params
 * @param entityManager
 * @param needCopyMetric
 * @param needCopyHitStrategy
 * @params needCopyHitStrategy
 */
export const copyRiskModel = async (
  params: CopyResourceParams,
  entityManager: EntityManager,
  needCopyMetric?: boolean,
  needCopyHitStrategy?: boolean,
): Promise<RiskModelCopyResultPO> => {
  const { userId, orgId } = params;
  if (!params.riskModelId) {
    throw new Error('riskModelId is required');
  }
  const riskModel = await getFullRiskModel(params.riskModelId, entityManager);
  riskModel.branchCount = riskModel.branchCount || 1;
  riskModel.branchTier = riskModel.branchTier || 0;
  const copiedResult: RiskModelCopyResultPO = new RiskModelCopyResultPO();
  copiedResult.oldId = riskModel.modelId;
  await entityManager.transaction(async (transactionManager) => {
    const pickProperties = pick(riskModel, ['modelType', 'category', 'orgId', 'product', 'comment', 'resultSetting']);
    const toSave: RiskModelEntity = Object.assign(new RiskModelEntity(), pickProperties);
    toSave.modelName = `${riskModel.modelName}-副本-${riskModel.branchCount}`;
    toSave.orgId = orgId || riskModel.orgId;
    toSave.category = getResourceCategory(toSave.orgId);
    toSave.createBy = userId;
    if (params.copyType == 1) {
      //生成新的branch_code, 确保是生成新的基础模型
      toSave.branchCode = v4();
    } else {
      toSave.branchCode = riskModel.branchCode;
    }
    toSave.branchTier = (riskModel.branchTier || 0) + 1;
    toSave.branchCount = 1;
    toSave.status = DataStatusEnums.Developing;
    toSave.extendFrom = riskModel.extendFrom ? `${riskModel.extendFrom}.${riskModel.modelId}` : riskModel.modelId + '';
    const newRiskModel = await transactionManager.save(RiskModelEntity, toSave);

    //1. 父级模型以及同一个分组的根模型的 branchCount 都要 + 1
    const toUpdateId = [riskModel.modelId];
    const rootRiskModelId = riskModel.extendFrom?.split('.')?.at(0);
    if (rootRiskModelId && parseInt(rootRiskModelId) !== riskModel.modelId) {
      toUpdateId.push(parseInt(rootRiskModelId));
    }
    await transactionManager.update(RiskModelEntity, toUpdateId, { branchCount: () => 'branch_count + 1' });

    copiedResult.newId = newRiskModel.modelId;
    copiedResult.groupResults = await copyGroupsToModel(
      newRiskModel.modelId,
      params,
      riskModel.groups,
      transactionManager,
      needCopyMetric,
      needCopyHitStrategy,
    );
    copiedResult.copied = true;
  });

  return copiedResult;
};
