import { CreditEsMappingModel, creditSearchSourceMappings } from '@domain/constants/credit.analyze.constants';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { isArray } from 'lodash';

export const getCreditMappingByDimension = (dimensionType: DimensionTypeEnums): CreditEsMappingModel => {
  if (null == dimensionType) {
    return null;
  }
  const l = creditSearchSourceMappings.filter((t) =>
    isArray(t.dimensionType) ? t.dimensionType.some((t) => t == dimensionType) : t.dimensionType == dimensionType,
  );
  return l[0];
};

// export const getCreditMappingByType = (type: string): CreditEsMappingModel => {
//   return creditSearchSourceMappings.filter((t) => t.type == type)[0];
// };

// /**
//  * 检查model中是否存在某个field，避免拼装es中不存在的field到query中，造成查询失败
//  * @param model
//  * @param fieldKey
//  */
// export const checkCreditMappingField = (model: CreditEsMappingModel, fieldKey: string): boolean => {
//   return model?.fieldList.some((t) => t.sourceName == fieldKey);
// };

// export const getPublishDate = (num: number) => {
//   return [
//     Object.assign(new DateRangeRelative(), {
//       currently: true,
//       flag: 1,
//       number: num * 365,
//       unit: 'day',
//     }),
//   ];
// };

// export const getAmountConditions = (fieldOperator: OperatorEnums, fieldVal: any) => {
//   switch (fieldOperator) {
//     // case OperatorEnums.gt: //大于
//     //   return [{ min: fieldVal }];
//     case OperatorEnums.ge: //大于等于
//       return [{ min: fieldVal }, { min: fieldVal, max: fieldVal }];
//     // case OperatorEnums.eq: //等于
//     //   return [{ min: fieldVal, max: fieldVal }];
//     // case OperatorEnums.lt: //小于
//     //   return [{ max: fieldVal }];
//     case OperatorEnums.le: //小于等于
//       return [{ max: fieldVal }, { min: fieldVal, max: fieldVal }];
//     case OperatorEnums.contain: //包含
//       break;
//     case OperatorEnums.notContain: //不包含
//       break;
//     default:
//       break;
//   }
// };
