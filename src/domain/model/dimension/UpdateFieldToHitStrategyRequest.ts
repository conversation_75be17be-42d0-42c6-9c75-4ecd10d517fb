import { IntersectionType, PartialType, PickType } from '@nestjs/swagger';
import { DimensionHitStrategyFieldsEntity } from '@domain/entities/DimensionHitStrategyFieldsEntity';

export class UpdateFieldToHitStrategyRequest extends IntersectionType(
  PickType(DimensionHitStrategyFieldsEntity, ['id', 'strategyId']),
  PartialType(
    PickType(DimensionHitStrategyFieldsEntity, ['searchType', 'dimensionFieldName', 'accessScope', 'fieldValue', 'compareType', 'options', 'comment']),
  ),
) {}
