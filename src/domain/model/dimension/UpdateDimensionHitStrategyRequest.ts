import { IntersectionType, PartialType, PickType } from '@nestjs/swagger';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';

export class UpdateDimensionHitStrategyRequest extends IntersectionType(
  PickType(DimensionHitStrategyEntity, ['strategyId']),
  PartialType(PickType(DimensionHitStrategyEntity, ['strategyName', 'comment', 'fieldHitStrategy', 'template'])),
) {}
