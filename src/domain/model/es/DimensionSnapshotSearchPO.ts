import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { DateRangeRelative, NumberRange } from '../data/source/CreditSearchFilter';

export class DimensionSnapshotSearchPO {
  @ApiPropertyOptional({ description: '当事人角色(裁判文书,开庭公告)' })
  @IsOptional()
  principalRole?: string[];

  @ApiPropertyOptional({ description: '案由(裁判文书,开庭公告)' })
  @IsOptional()
  reason?: string[];

  @ApiPropertyOptional({ description: '案件状态(裁判文书)' })
  @IsOptional()
  dimensionStatus?: string[];

  @ApiPropertyOptional({ description: '案件类型(裁判文书)' })
  @IsOptional()
  caseType?: string[];

  @ApiPropertyOptional({ description: '文书类型(裁判文书)' })
  @IsOptional()
  judgmentType?: string[];

  @ApiPropertyOptional({ description: '维度数据发生地域' })
  @IsOptional()
  area?: string[];

  // @ApiPropertyOptional({ description: '金额' })
  // @IsOptional()
  // amount: number[];

  @ApiPropertyOptional({ description: '执行法院级别(裁判文书)' })
  @IsOptional()
  courtLevel?: string[];

  @ApiPropertyOptional({ description: '执行法院(裁判文书,开庭公告,行政处罚-处罚单位)' })
  @IsOptional()
  courtName?: string[];

  @ApiPropertyOptional({ description: '审理程序(裁判文书)' })
  @IsOptional()
  trialRound?: string[];

  @ApiPropertyOptional({ description: '发生年份(裁判文书-发布年份,行政处罚-触发年份)' })
  @IsOptional()
  year?: string[];

  @ApiPropertyOptional({ description: '新闻来源名称(负面舆情-媒体类型)' })
  @IsOptional()
  sourceName?: string[];

  @ApiPropertyOptional({ description: '标签(负面舆情-主题类型)' })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({ description: '发布时间(负面舆情-发布时间)' })
  @IsOptional()
  publishTime?: DateRangeRelative[];

  @ApiPropertyOptional({ description: '金额范围(裁判文书-案件金额，行政处罚-罚款金额)' })
  @IsOptional()
  amount?: NumberRange[];

  @ApiPropertyOptional({ description: '企业状态(对外投资-企业状态)' })
  @IsOptional()
  status?: string[];

  @ApiPropertyOptional({ description: '持股比例(对外投资-持股比例)' })
  @IsOptional()
  totalpercent?: NumberRange[];

  @ApiPropertyOptional({ description: '企业名称(关联方)' })
  @IsOptional()
  companyName?: string;
}
