import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PrincipalsEsDoc {
  @ApiProperty({ description: '角色' })
  role: string;

  @ApiProperty({ description: '名称' })
  name: string;

  @ApiProperty({ description: '公司或者人员的ID' })
  keyNO?: string;
}

export class DimensionContentSearchEsPO {
  @ApiPropertyOptional({ description: '当事人(裁判文书,开庭公告)' })
  principals: PrincipalsEsDoc[];

  @ApiPropertyOptional({ description: '案由(裁判文书,开庭公告)' })
  reason: string;

  @ApiPropertyOptional({ description: '案由类型(裁判文书,开庭公告)' })
  reasonType: string;

  @ApiPropertyOptional({ description: '案件状态(裁判文书-案件状态, 对外投资-企业状态)' })
  dimensionStatus: string;

  @ApiPropertyOptional({ description: '案件类型(裁判文书)' })
  caseType: string;

  @ApiPropertyOptional({ description: '文书类型(裁判文书)' })
  judgmentType: string;

  @ApiPropertyOptional({ description: '维度数据发生地域' })
  area: string;

  @ApiPropertyOptional({ description: '金额(裁判文书-案件金额,行政处罚-处罚金额, 对外投资-持股比例)' })
  amount: number;

  @ApiPropertyOptional({ description: '执行法院级别(裁判文书)' })
  courtLevel: string;

  @ApiPropertyOptional({ description: '执行法院(裁判文书,开庭公告,行政处罚-处罚单位)' })
  courtName: string;

  @ApiPropertyOptional({ description: '审理程序(裁判文书)' })
  trialRound: string;

  @ApiPropertyOptional({ description: '发生年份(裁判文书-发布年份,行政处罚-触发年份)' })
  year: string;

  @ApiPropertyOptional({ description: '新闻来源名称(负面舆情-媒体类型)' })
  sourceName: string;

  @ApiPropertyOptional({ description: '标签(负面舆情-主题类型)' })
  tags: string[];

  @ApiPropertyOptional({ description: '发布时间(负面舆情-发布时间)、风险动态的ChangeDate' })
  publishTime: number;

  @ApiPropertyOptional({ description: '企业名称(关联方)' })
  companyName: string;
}

export class DimensionHitStrategyEsDoc {
  id: string;
  batchId: number;
  companyId: string;
  diligenceId: number;
  orgId: number;
  snapshotId: string;
  strategyId: number;
}

export class DimensionSnapshotEsDoc {
  id: string;

  dimensionId: string;

  dimensionKey: DimensionTypeEnums;

  @ApiProperty({ description: '维度内容' })
  dimensionContent: string;

  @ApiProperty({ description: '维度内容(索引，可搜索字段)' })
  dimensionContentSearch: DimensionContentSearchEsPO;
}
