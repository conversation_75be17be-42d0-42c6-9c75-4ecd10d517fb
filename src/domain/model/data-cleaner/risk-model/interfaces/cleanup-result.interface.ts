/**
 * branchCode 级别清理结果接口
 *
 * 用于返回基于组织分配关系的 branchCode 清理分析结果
 */
export interface BranchCodeCleanupResult {
  /**
   * 是否可以清理
   *
   * 基于组织分配关系的安全检查结果
   */
  canClean: boolean;

  /**
   * 是否可以完全清理
   *
   * 考虑跨 branchCode 分享情况后的清理能力
   */
  canFullClean: boolean;

  /**
   * 清理结果原因说明
   *
   * 详细说明为什么可以或不可以清理
   */
  reason: string;

  /**
   * 跨 branchCode 分享的指标ID列表
   *
   * 这些指标被其他 branchCode 引用，需要特殊处理
   */
  sharedMetrics: number[];

  /**
   * 跨 branchCode 分享的策略ID列表
   *
   * 这些策略被其他 branchCode 引用，需要特殊处理
   */
  sharedStrategies: number[];
}

/**
 * 指标清理结果接口
 *
 * 用于返回指标级别的清理分析结果
 */
export interface MetricCleanupResult {
  /**
   * 是否可以删除指标
   *
   * 基于跨模型引用和清理触发方式的综合判断
   */
  canDeleteMetric: boolean;

  /**
   * 清理结果原因说明
   */
  reason: string;

  /**
   * 跨 branchCode 引用的模型信息
   *
   * 列出所有引用该指标的其他 branchCode 模型
   */
  crossBranchReferences: Array<{
    modelId: number;
    branchCode: string;
    modelName: string;
  }>;
}

/**
 * 策略清理结果接口
 *
 * 用于返回策略级别的清理分析结果
 */
export interface StrategyCleanupResult {
  /**
   * 是否可以删除策略
   *
   * 基于多级依赖链和清理触发方式的综合判断
   */
  canDeleteStrategy: boolean;

  /**
   * 清理结果原因说明
   */
  reason: string;

  /**
   * 通过指标引用该策略的模型信息
   *
   * 策略 → 指标 → 分组 → 模型的完整依赖链
   */
  referencingModels: Array<{
    modelId: number;
    branchCode: string;
    modelName: string;
    viaMetricIds: number[]; // 通过哪些指标引用
  }>;
}

/**
 * 快速批量回退结果接口
 *
 * 用于返回批量回退操作的执行结果
 */
export interface QuickRollbackResult {
  /**
   * 成功回退的记录数量
   */
  affectedRecords: number;

  /**
   * 详细的回退结果信息
   */
  rollbackDetails: Array<{
    /**
     * 暂存记录ID
     */
    stagingId: number;

    /**
     * 目标类型（branchCode 或 riskModelId）
     */
    targetType: string;

    /**
     * 目标值
     */
    targetValue: string;

    /**
     * 回退状态
     */
    status: 'success' | 'error';

    /**
     * 错误信息（仅在 status 为 error 时存在）
     */
    error?: string;
  }>;
}

/**
 * 清理摘要结果接口
 *
 * 用于返回清理任务的整体执行摘要
 */
export interface CleanupSummary {
  /**
   * 成功处理的记录数
   */
  successCount: number;

  /**
   * 失败处理的记录数
   */
  errorCount: number;

  /**
   * 处理开始时间
   */
  startTime: Date;

  /**
   * 处理结束时间
   */
  endTime: Date;

  /**
   * 错误详情列表
   */
  errors: Array<{
    stagingId: number;
    error: string;
    timestamp: Date;
  }>;
}
