import { DataStatusEnums } from '@domain/enums/DataStatusEnums';

/**
 * 清理分析结果接口
 *
 * 用于保存清理分析的完整结果，包括影响评估、可清理实体、状态备份等信息
 */
export interface CleanupAnalysisResult {
  /**
   * 影响评估
   *
   * 统计本次清理操作将影响的各类实体数量
   */
  impactAssessment: {
    /**
     * 受影响的分组数量
     */
    affectedGroups: number;

    /**
     * 受影响的指标数量
     */
    affectedMetrics: number;

    /**
     * 受影响的策略数量
     */
    affectedStrategies: number;

    /**
     * 受影响的关联关系数量
     */
    affectedRelations: number;
  };

  /**
   * 可清理的实体列表
   *
   * 列出所有经过安全检查后确认可以清理的实体ID
   */
  cleanableEntities: {
    /**
     * 可清理的分组ID列表
     */
    groupIds: number[];

    /**
     * 可清理的指标ID列表
     */
    metricIds: number[];

    /**
     * 可清理的策略ID列表
     */
    strategyIds: number[];

    /**
     * 可清理的关联关系ID列表
     */
    relationIds: {
      /**
       * 分组-指标关联关系ID列表
       */
      groupMetricRelations: number[];

      /**
       * 指标-策略关联关系ID列表
       */
      metricDimensionRelations: number[];

      /**
       * 策略-字段关联关系ID列表
       */
      strategyFieldsRelations: number[];
    };
  };

  /**
   * 实体原始状态备份
   *
   * 用于撤回操作，保存实体在清理前的原始状态
   */
  originalStatusBackup: {
    /**
     * 分组原始状态备份
     */
    groups: Array<{
      id: number;
      originalStatus: DataStatusEnums;
    }>;

    /**
     * 指标原始状态备份
     */
    metrics: Array<{
      id: number;
      originalStatus: DataStatusEnums;
    }>;

    /**
     * 策略原始状态备份
     */
    strategies: Array<{
      id: number;
      originalStatus: DataStatusEnums;
    }>;

    /**
     * 模型原始状态备份
     */
    models: Array<{
      id: number;
      originalStatus: DataStatusEnums;
    }>;
  };

  /**
   * 共享数据分析
   *
   * 区分独占和共享的指标、策略，用于指导清理策略
   */
  sharedDataAnalysis: {
    /**
     * 独占指标ID列表
     *
     * 只被当前清理目标使用的指标，可以完全删除
     */
    exclusiveMetrics: number[];

    /**
     * 共享指标ID列表
     *
     * 被多个 branchCode 或模型共享的指标，需要特殊处理
     */
    sharedMetrics: number[];

    /**
     * 独占策略ID列表
     *
     * 只被当前清理目标使用的策略，可以完全删除
     */
    exclusiveStrategies: number[];

    /**
     * 共享策略ID列表
     *
     * 被多个 branchCode 或模型共享的策略，需要特殊处理
     */
    sharedStrategies: number[];
  };

  /**
   * 风险等级评估
   *
   * 基于清理范围和影响程度的风险评估
   */
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';

  /**
   * 风险因素列表
   *
   * 详细说明导致风险等级的具体因素
   */
  riskFactors: string[];
}

/**
 * 依赖关系分析结果接口
 *
 * 用于保存实体间依赖关系的分析结果
 */
export interface DependencyAnalysisResult {
  /**
   * 实体ID
   */
  entityId: number;

  /**
   * 实体类型
   */
  entityType: 'group' | 'metric' | 'strategy';

  /**
   * 直接依赖的实体列表
   */
  directDependencies: Array<{
    entityId: number;
    entityType: string;
    relationshipType: string;
  }>;

  /**
   * 被依赖的实体列表
   */
  dependentEntities: Array<{
    entityId: number;
    entityType: string;
    relationshipType: string;
  }>;

  /**
   * 跨 branchCode 引用标记
   */
  hasCrossBranchReferences: boolean;

  /**
   * 引用的 branchCode 列表
   */
  referencedBranchCodes: string[];
}
