import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { CleanupTriggerType } from '../../../../enums/data-cleaner/cleanup-trigger-type.enum';

/**
 * 清理配置接口
 */
export interface DataCleanupConfig {
  /** 清理触发方式 */
  triggerType: CleanupTriggerType;

  /** 组织ID */
  organizationId?: number;

  /** 商业产品代码 */
  branchCode?: string;

  /** 风险模型ID */
  riskModelId?: number;

  /** 操作用户ID */
  userId: number;
}

/**
 * 清理分析结果接口
 */
export interface CleanupAnalysisResult {
  /** 暂存记录ID */
  stagingId: number;

  /** 受影响的表名列表 */
  affectedTables: string[];

  /** 受影响的记录总数 */
  affectedRecords: number;

  /** 预估数据大小（KB） */
  estimatedSize: number;

  /** 安全警告信息 */
  safetyWarnings: string[];
}

/**
 * 清理操作结果接口
 */
export interface DataCleanupResult {
  /** 操作是否成功 */
  success: boolean;

  /** 受影响的记录数 */
  affectedRecords: number;

  /** 暂存记录ID */
  stagingId: string;

  /** 操作结果消息 */
  message: string;

  /** 错误信息（如果失败） */
  error?: string;
}

/**
 * 影响评估接口
 */
export interface ImpactAssessment {
  /** 受影响的分组数量 */
  affectedGroups: number;

  /** 受影响的指标数量 */
  affectedMetrics: number;

  /** 受影响的策略数量 */
  affectedStrategies: number;

  /** 受影响的关系数量 */
  affectedRelations: number;
}

/**
 * 可清理实体列表接口
 */
export interface CleanableEntities {
  /** 可清理的分组ID列表 */
  groupIds: number[];

  /** 可清理的指标ID列表 */
  metricIds: number[];

  /** 可清理的策略ID列表 */
  strategyIds: number[];

  /** 可清理的关系ID映射 */
  relationIds: {
    groupMetricRelations: number[];
    metricDimensionRelations: number[];
    strategyFieldsRelations: number[];
  };
}

/**
 * 原始状态备份接口
 */
export interface OriginalStatusBackup {
  /** 分组状态备份 */
  groups: Array<{ id: number; originalStatus: DataStatusEnums }>;

  /** 指标状态备份 */
  metrics: Array<{ id: number; originalStatus: DataStatusEnums }>;

  /** 策略状态备份 */
  strategies: Array<{ id: number; originalStatus: DataStatusEnums }>;

  /** 模型状态备份 */
  models: Array<{ id: number; originalStatus: DataStatusEnums }>;
}

/**
 * 共享数据分析结果接口
 */
export interface SharedDataAnalysis {
  /** 独占指标ID列表（只被当前目标使用） */
  exclusiveMetrics: number[];

  /** 共享指标ID列表（被多个目标共享） */
  sharedMetrics: number[];

  /** 独占策略ID列表（只被当前目标使用） */
  exclusiveStrategies: number[];

  /** 共享策略ID列表（被多个目标共享） */
  sharedStrategies: number[];
}

/**
 * 详细分析结果接口
 */
export interface DetailedCleanupAnalysisResult {
  /** 影响评估 */
  impactAssessment: ImpactAssessment;

  /** 可清理的实体列表 */
  cleanableEntities: CleanableEntities;

  /** 实体原始状态备份 */
  originalStatusBackup: OriginalStatusBackup;

  /** 共享数据分析 */
  sharedDataAnalysis: SharedDataAnalysis;

  /** 风险等级 */
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';

  /** 风险因素列表 */
  riskFactors: string[];
}

/**
 * 安全检查结果接口
 */
export interface SafetyCheckResult {
  /** 是否安全 */
  isSafe: boolean;

  /** 安全检查失败原因 */
  reason?: string;

  /** 警告信息列表 */
  warnings?: string[];

  /** 可以清理 */
  canClean: boolean;

  /** 可以完全清理 */
  canFullClean: boolean;

  /** 跨branchCode分享的指标 */
  sharedMetrics?: number[];

  /** 跨branchCode分享的策略 */
  sharedStrategies?: number[];
}

/**
 * 快速批量回退选项接口
 */
export interface QuickRollbackOptions {
  /** 目标类型过滤 */
  targetType?: CleanupTriggerType;

  /** branchCode 过滤 */
  branchCode?: string;

  /** 创建时间后过滤 */
  createdAfter?: Date;

  /** 操作人员ID */
  operatorId: number;

  /** 是否只模拟运行 */
  dryRun?: boolean;
}

/**
 * 回退详细结果接口
 */
export interface RollbackDetail {
  /** 暂存记录ID */
  stagingId: number;

  /** 目标类型 */
  targetType: string;

  /** 目标值 */
  targetValue: string;

  /** 操作状态 */
  status: 'success' | 'error';

  /** 错误信息（如果失败） */
  error?: string;
}

/**
 * 快速批量回退结果接口
 */
export interface QuickRollbackResult {
  /** 受影响的记录数 */
  affectedRecords: number;

  /** 回退详细结果列表 */
  rollbackDetails: RollbackDetail[];
}

/**
 * 清理摘要接口
 */
export interface CleanupSummary {
  /** 成功清理的记录数 */
  successCount: number;

  /** 失败的记录数 */
  errorCount: number;

  /** 开始时间 */
  startTime?: Date;

  /** 结束时间 */
  endTime?: Date;

  /** 错误详情列表 */
  errors?: Array<{ stagingId: number; error: string }>;
}
