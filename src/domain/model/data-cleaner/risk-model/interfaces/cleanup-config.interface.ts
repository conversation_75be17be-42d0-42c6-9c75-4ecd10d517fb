/**
 * 清理环境配置接口
 *
 * 用于区分测试环境和生产环境的清理策略，确保生产数据安全
 */
export interface CleanupEnvironmentConfig {
  /**
   * 环境类型
   * - test: 测试环境，允许更激进的清理策略
   * - production: 生产环境，严格保护商业数据
   */
  environment: 'test' | 'production';

  /**
   * 允许清理的测试组织ID列表
   *
   * 生产环境下，只有这些测试组织的数据可以被清理
   * 测试环境下，此配置可以为空（允许清理所有组织）
   */
  testOrganizations: number[];

  /**
   * 是否启用安全清理模式
   *
   * 启用时会进行更严格的安全检查
   */
  safeCleanupEnabled: boolean;

  /**
   * 批量操作大小
   *
   * 用于控制单次批量删除的记录数量，避免对数据库性能造成影响
   */
  batchSize: number;

  /**
   * 最大撤回次数
   *
   * 限制单个暂存记录的最大撤回操作次数
   */
  maxRollbackCount: number;
}

/**
 * 快速批量回退选项接口
 *
 * 用于紧急情况下的批量回退操作配置
 */
export interface QuickRollbackOptions {
  /**
   * 目标类型过滤
   *
   * 可选，只回退指定类型的记录
   */
  targetType?: 'branchCode' | 'riskModelId';

  /**
   * branchCode 过滤
   *
   * 可选，只回退指定 branchCode 的记录
   */
  branchCode?: string;

  /**
   * 创建时间过滤
   *
   * 可选，只回退指定时间后创建的记录
   */
  createdAfter?: Date;

  /**
   * 操作人员ID
   *
   * 必需，用于审计和权限验证
   */
  operatorId: number;

  /**
   * 是否只模拟运行
   *
   * 为 true 时不执行实际回退，只返回会被回退的记录信息
   */
  dryRun?: boolean;
}
