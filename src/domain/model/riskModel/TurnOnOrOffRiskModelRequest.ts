import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsNotEmpty } from 'class-validator';

export class TurnOnOrOffRiskModelRequest {
  @ApiProperty({ description: '模型id' })
  @IsNotEmpty()
  modelId: number;

  @ApiProperty({ description: '状态 0 禁用 ， 1 开启' })
  @IsNotEmpty()
  @IsIn([0, 1])
  status: number;

  @ApiProperty({ description: '最大开启数量' })
  @IsNotEmpty()
  maxActiveCount: number;
}
