import { ApiPropertyOptional } from '@nestjs/swagger';
import { ProductCodeAllowValues } from '@domain/enums/ProductCodeEnums';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsIn, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { PaginationParams } from '../common';
import { RiskModelTypeEnums, RiskModelTypes } from '@domain/enums/RiskModelTypeEnums';
import { Type } from 'class-transformer';

export class SearchRiskModelRequest extends PaginationParams {
  @ApiPropertyOptional({ description: '是否查看指定组织相关的模型，如果不指定则查看所有(客户端搜索的时候不需要指定，会自动用当前用户的值覆盖)' })
  @IsOptional()
  orgId?: number;

  @ApiPropertyOptional({
    description: '产品编码(客户端搜索的时候不需要指定，会自动用当前用户的值覆盖)',
    enum: ProductCodeAllowValues,
  })
  @IsIn(ProductCodeAllowValues)
  @IsOptional()
  product?: string;

  // @ApiPropertyOptional({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: DataStatusAllowedStatus,
  // })
  // @IsIn(DataStatusAllowedStatus)
  // @IsOptional()
  // status?: DataStatusEnums;

  @ApiPropertyOptional({ description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃', type: Number, isArray: true })
  @IsArray()
  @IsIn(DataStatusAllowedStatus, { each: true })
  @Type(() => Number)
  @IsOptional()
  statuses?: DataStatusEnums[];

  @ApiPropertyOptional({ description: '模型名称' })
  @IsOptional()
  modelName?: string;

  @ApiPropertyOptional({ description: '模型的分支编码(客户端搜索的时候会忽略这个值)' })
  @IsOptional()
  branchCode?: string;

  @ApiPropertyOptional({ description: '排除的模型ID列表' })
  @IsOptional()
  excludedModelsIds?: number[];

  @ApiPropertyOptional({ description: '模型类型', enum: RiskModelTypes })
  @IsOptional()
  @IsIn(RiskModelTypes)
  modelType?: RiskModelTypeEnums;

  @ApiPropertyOptional({ description: '是否只展示根目录级别模型：true-只展示根目录级别模型，false-展示所有级别模型' })
  @IsOptional()
  onlyTier?: boolean = false;
}

export class GetDimensionHitStrategiesRequest {
  @ApiPropertyOptional({ description: '模型id' })
  @IsNotEmpty()
  modelId: number;

  @ApiPropertyOptional({ description: '维度策略ids' })
  @IsArray()
  @IsNumber({}, { each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  strategyIds: number[];
}
