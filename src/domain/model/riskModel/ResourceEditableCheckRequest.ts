import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class ResourceEditableCheckRequest {
  @ApiPropertyOptional({ description: '风险模型id' })
  @IsOptional()
  riskModelId?: number;

  @ApiPropertyOptional({ description: '指标id' })
  @IsOptional()
  metricsId?: number;

  @ApiPropertyOptional({ description: '命中规则的ID' })
  @IsOptional()
  hitStrategyId?: number;

  @ApiPropertyOptional({ description: '命中规则的属性ID' })
  @IsOptional()
  hitStrategyFieldId?: number;
}
