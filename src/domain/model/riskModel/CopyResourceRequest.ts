import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { CopyResourceParams } from '@domain/db_helpers/resource.copy.helper';
import { IsIn, IsNotEmpty, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class CopyResourceRequest extends OmitType(CopyResourceParams, ['orgId', 'userId']) {
  @ApiProperty({
    description: '需要复制的资源类型(当前操作的资源)：1 riskModel,2 metric, 3 hitStrategy, 4 hitStrategyField',
    enum: [1, 2, 3, 4],
  })
  @IsIn([1, 2, 3, 4])
  @Type(() => Number)
  @IsNotEmpty()
  targetResource: number;

  @ApiPropertyOptional({
    description: '复制metric之后绑定到指定的group',
  })
  @IsOptional()
  bindMetricToGroupId?: number;

  @ApiPropertyOptional({
    description: '复制命中规则之后绑定到指定的metric',
  })
  @IsOptional()
  bindStrategyToMetricId?: number;

  @ApiPropertyOptional({
    description: '复制命中规则字段之后绑定到指定的strategy',
  })
  @IsOptional()
  bindStrategyFieldToStrategyId?: number;
}

// export class CopyRiskModelRequest extends PickType(CopyResourceParams, ['riskModelId']) {}
