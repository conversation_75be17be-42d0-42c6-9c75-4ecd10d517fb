import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsNotEmpty, IsOptional } from 'class-validator';
import { PaginationParams } from '../common';
import { ProductCodeAllowValues } from '@domain/enums/ProductCodeEnums';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';

export class SearchRiskModelBranchRequest extends PaginationParams {
  @ApiProperty({ description: '模型id' })
  @IsNotEmpty()
  riskModelId: number;

  @ApiPropertyOptional({ description: '组织id' })
  @IsOptional()
  orgId?: number;

  @ApiPropertyOptional({
    description: '产品编码(客户端搜索的时候不需要指定，会自动用当前用户的值覆盖)',
    enum: ProductCodeAllowValues,
  })
  @IsIn(ProductCodeAllowValues)
  @IsOptional()
  product?: string;

  @ApiPropertyOptional({
    description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  @IsOptional()
  status?: DataStatusEnums;

  @ApiPropertyOptional({ description: '模型的分支编码(客户端搜索的时候会忽略这个值)', deprecated: true })
  @IsOptional()
  //@deprecated
  branchCode?: string;

  @ApiPropertyOptional({ description: '排除的模型ID列表' })
  @IsOptional()
  excludedModelsIds?: number[];
}
