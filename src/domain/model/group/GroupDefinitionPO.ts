import { PickType } from '@nestjs/swagger';
import { GroupEntity } from '@domain/entities/GroupEntity';

export const GROUP_DEFINITION_PO_KEYS = ['groupName', 'groupId', 'parentGroupId', 'isVirtual', 'detailsJson', 'comment', 'riskLevel'];

export class GroupDefinitionPO extends PickType(GroupEntity, [
  'groupName',
  'groupId',
  'parentGroupId',
  'isVirtual',
  'detailsJson',
  'comment',
  'riskLevel',
] as const) {}
