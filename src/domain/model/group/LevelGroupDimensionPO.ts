import { GroupMetricScorePO } from './GroupMetricScorePO';
import { ApiProperty } from '@nestjs/swagger';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';

//import { DimensionRiskLevelEnum } from 'libs/enums/diligence/DimensionRiskLevelEnum';

export class LevelGroupDimensionPO {
  @ApiProperty({ description: '高风险项', type: GroupMetricScorePO, isArray: true })
  [DimensionRiskLevelEnum.High]: GroupMetricScorePO[];
  @ApiProperty({ description: '中风险项', type: GroupMetricScorePO, isArray: true })
  [DimensionRiskLevelEnum.Medium]: GroupMetricScorePO[];
  @ApiProperty({ description: '低风险项', type: GroupMetricScorePO, isArray: true })
  [DimensionRiskLevelEnum.Alert]: GroupMetricScorePO[];
}
