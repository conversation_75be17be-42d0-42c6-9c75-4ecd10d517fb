import { ApiProperty } from '@nestjs/swagger';
import { values } from 'lodash';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';
import { MetricScorePO } from '../metric/MetricScorePO';
import { GroupDefinitionPO } from './GroupDefinitionPO';

export class GroupMetricScorePO {
  @ApiProperty({ description: '一组维度的总分' })
  score: number;

  @ApiProperty({ description: '分组的定义', type: GroupDefinitionPO })
  groupDefinition: GroupDefinitionPO;

  @ApiProperty({
    description: '命中维度中最高风险等级',
    enum: values(DimensionRiskLevelEnum),
  })
  level: DimensionRiskLevelEnum;

  @ApiProperty({ description: '一组维度的分数详情', type: MetricScorePO, isArray: true })
  scoreDetails?: MetricScorePO[];

  @ApiProperty({ description: '风险扫描时候以及维度下面所有命中的记录的总数' })
  totalHits?: number;

  @ApiProperty({ description: '一组维度的排序' })
  sort?: number;

  constructor() {
    this.score = 0;
    this.scoreDetails = [];
    this.totalHits = 0;
    this.level = DimensionRiskLevelEnum.Alert;
  }
}
