import { ApiProperty, PickType } from '@nestjs/swagger';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';
import { DimensionSourceEnums } from '@domain/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { DimensionFilterParams } from './DimensionStrategyPO';

/**
 * 维度命中策略
 */
export class DimensionHitResultPO extends PickType(DimensionHitStrategyEntity, ['strategyId', 'strategyName', 'strategyRole', 'status'] as const) {
  @ApiProperty({ description: '维度 唯一标识' })
  dimensionKey: DimensionTypeEnums;

  @ApiProperty({ description: '维度 名称' })
  dimensionName: string;

  @ApiProperty({
    required: false,
    description: '数据结果的来源',
    enum: Object.values(DimensionSourceEnums),
  })
  source: DimensionSourceEnums;

  @ApiProperty({ description: '该数据维度命中的条数' })
  totalHits?: number;

  @ApiProperty({ description: '维度命中结果说明' })
  description: string;

  @ApiProperty({ description: '独立的维度过滤条件' })
  dimensionFilter?: DimensionFilterParams;
}
