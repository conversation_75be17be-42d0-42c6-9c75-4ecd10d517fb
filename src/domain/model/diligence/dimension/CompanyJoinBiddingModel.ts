import { ApiProperty } from '@nestjs/swagger';

export class CompanyJoinBiddingModel {
  @ApiProperty({ description: '公司名称' })
  companyName: string;

  @ApiProperty({ description: '公司keyNo' })
  companyId: string;

  @ApiProperty({ description: '关联公司名称' })
  competitionCompanyName: string;

  @ApiProperty({ description: '关联公司keyNo' })
  competitionCompanyId: string;

  @ApiProperty({ description: '共同投标项目数' })
  jointTenderCount: number;

  @ApiProperty({ description: '企业中标项目数' })
  tenderCount: number;

  @ApiProperty({ description: '关联企业中标项目数' })
  competitionTenderCount: number;

  @ApiProperty({ description: '企业中标率' })
  tenderPercent: string;

  @ApiProperty({ description: '关联企业中标率' })
  competitionTenderPercent: string;

  @ApiProperty({ description: '中标率异常标签' })
  tag: string;

  @ApiProperty({ description: '中标率异常标签' })
  competitionTag: string;
}
