import { ApiPropertyOptional } from '@nestjs/swagger';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';

/**
 * 独立的维度过滤条件，并未放到模型中配置，
 * 例如监控的风险动态维度查询时间范围会根据每次监控执行周期计算，存到batch的batchInof中，每次尽调传到具体数据源的实现逻辑里
 */
export class DimensionFilterParams {
  /**
   * 监控的尽调，rishchange 数据源的时间过滤范围开始时间
   */
  @ApiPropertyOptional({ description: '数据范围-开始时间' })
  startTime?: number;

  /**
   * 监控的尽调，rishchange 数据源的时间过滤范围结束时间
   */
  @ApiPropertyOptional({ description: '数据范围-结束时间' })
  endTime?: number;

  @ApiPropertyOptional({ description: '监控groupId' })
  monitorGroupId?: number;

  @ApiPropertyOptional({ description: '数据Id' })
  id?: string;

  @ApiPropertyOptional({ description: '是否为关联方企业' })
  isRelated?: boolean;
}

export const RiskLevelConst = {
  //只需要标注警示和关注
  [DimensionRiskLevelEnum.High]: '警示风险',
  [DimensionRiskLevelEnum.Medium]: '关注风险',
  [DimensionRiskLevelEnum.Alert]: '提示风险',
};

//在导出显示风险等级时使用
export const RiskLevelDescription = {
  [DimensionRiskLevelEnum.High]: '高风险',
  [DimensionRiskLevelEnum.Medium]: '中风险',
  [DimensionRiskLevelEnum.Alert]: '低风险',
};
