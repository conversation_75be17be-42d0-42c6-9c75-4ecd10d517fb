import { ApiProperty, IntersectionType } from '@nestjs/swagger';
import { IsString, MaxLength, MinLength } from 'class-validator';
import { SingleCompanyDiligenceRequest } from './SingleCompanyDiligenceRequest';
import { PaginationParams } from '../../common';

export class ListCompanyDiligenceHistoryRequest extends IntersectionType(SingleCompanyDiligenceRequest, PaginationParams) {
  @ApiProperty({ description: '公司ID' })
  @IsString()
  @MaxLength(32)
  @MinLength(32)
  companyId: string;
}
