import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';

export class DiligenceResponse extends DiligenceHistoryEntity {
  @ApiProperty({ description: '是否是缓存的尽调' })
  cached: boolean;

  @ApiProperty({ description: 'diligenceId和companyId不匹配' })
  notMatch?: boolean;

  @ApiPropertyOptional({ description: '模型名称' })
  riskModelName?: string;
}

export class PaidCheckDetail {
  // { companyId, orgModelId, riskModelName: riskModel.modelName, paid }
  @ApiProperty({ description: '公司ID' })
  companyId: string;

  @ApiProperty({ description: '模型ID' })
  orgModelId: number;

  @ApiProperty({ description: '模型名称' })
  riskModelName: string;

  @ApiProperty({ description: '是否已缴费' })
  paid: number;
}

export class PaidCheckResponse {
  @ApiProperty({ description: '需要扣除额度的数量' })
  paidCount: number;
  @ApiProperty({ description: '校验详情' })
  list: PaidCheckDetail[];
}
