import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { DimensionFilterParams } from '../dimension/DimensionStrategyPO';

export class GetHitDetailsParamBase {
  orgId: number;
  key: DimensionTypeEnums;
  @ApiPropertyOptional({ description: '关联的策略id' })
  strategyId?: number;
  @ApiPropertyOptional({ description: '维度的额外过滤条件' })
  dimensionFilter?: DimensionFilterParams;

  constructor(orgId: number, key: DimensionTypeEnums, strategyId?: number) {
    this.orgId = orgId;
    this.key = key;
    this.strategyId = strategyId;
  }
}
