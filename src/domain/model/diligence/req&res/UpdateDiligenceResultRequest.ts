import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateDiligenceResultItem {
  @ApiProperty({ description: '原来的值' })
  wasValue: number;

  @ApiProperty({ description: '修改后的值' })
  nowValue: number;

  @ApiProperty({ description: '1: 电话核实, 2: 实地核实, 3: 网络核实, 4: 其他' })
  verifyType?: number;

  @ApiPropertyOptional({ description: '备注信息' })
  comment?: string;
}

// export class UpdateDiligenceResultDimensionItem extends UpdateDiligenceResultItem {
//   @ApiProperty({ description: '数据维度' })
//   @IsIn([...Object.values(DimensionLevel1Enums), ...Object.values(DimensionTypeEnums), ...Object.values(DimensionTypeEnums)])
//   dimensionKey: DimensionTypeEnums;
// }

export class UpdateDiligenceResultRequest {
  @ApiPropertyOptional({ description: '尽调结果修改' })
  result: UpdateDiligenceResultItem;

  // @ApiPropertyOptional({ description: '数据维度修改' })
  // dimension?: UpdateDiligenceResultDimensionItem[];
}
