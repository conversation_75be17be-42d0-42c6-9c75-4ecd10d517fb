import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsNotEmpty, IsNumber, IsOptional, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class DiligenceRequestBase {
  @ApiProperty({ description: '选择的模型的ID列表，默认情况下如果 公司-> 模型 映射关系 超过5条，会自动转成批量' })
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => Number)
  @IsNotEmpty()
  @ArrayMaxSize(10)
  orgModelIds: number[];

  @ApiProperty({ description: '单元测试单独验证某一个分组，某一个维度的模型配置是否正确' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @Max(24)
  testGroupId?: number;

  @ApiProperty({ description: '单元测试单独验证某一个分组，某一个维度的模型配置是否正确' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @Max(24)
  testMetricId?: number;

  @ApiPropertyOptional({ description: '缓存时间，单位小时,默认是24小时之内执行过的排查，不会执行' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @Max(24)
  cacheHours? = 24;

  @ApiProperty({ description: 'batchId' })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  @Max(24)
  batchId?: number;
}
