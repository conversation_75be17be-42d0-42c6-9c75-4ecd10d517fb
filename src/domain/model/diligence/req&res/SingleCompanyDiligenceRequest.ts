import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';
import { DiligenceRequestBase } from './DiligenceRequestBase';

export class SingleCompanyDiligenceRequest extends DiligenceRequestBase {
  @ApiProperty({ description: '公司ID' })
  @IsNotEmpty()
  @IsString()
  @MaxLength(32)
  @MinLength(32)
  companyId: string;

  @ApiPropertyOptional({ description: '公司名称(不需要，会用公司ID获取到的信息覆盖)' })
  @IsOptional()
  @MaxLength(500)
  companyName: string;

  @ApiPropertyOptional({ description: '是否为关联方企业' })
  isRelated?: boolean = false;
}
