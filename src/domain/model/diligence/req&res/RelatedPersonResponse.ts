import { ApiProperty } from '@nestjs/swagger';
import { PersonData } from '../../data/source/PersonData';

export class RelatedPersonResponse extends PersonData {
  @ApiProperty({ description: '潜在利冲人员编号' })
  personNo?: string;

  @ApiProperty({ description: '潜在利冲人员分组' })
  group?: string;

  @ApiProperty({ description: 'person表中人员id' })
  personId?: number;

  @ApiProperty({ description: '0: 无关系（确认非本人）, 1: 有关系（确认本人）' })
  status?: number = 0;

  @ApiProperty({ description: '联系人号码' })
  phones?: any;

  @ApiProperty({ description: '联系人姓名' })
  contacts?: string;

  @ApiProperty({ description: '邮箱' })
  emails?: any;

  @ApiProperty({
    type: String,
    description: '关系类型  spouse-配偶,father-父亲, mother-母亲,children-子女, sibling-兄弟姐妹, other-其他 ',
  })
  relationship?: string | null;

  @ApiProperty({ type: Number, description: '关联人员Id, 员工本人为-1' })
  relationPersonId?: number;

  @ApiProperty({ description: '关联人员名称' })
  relationPersonName?: string | null;

  @ApiProperty({ description: '关联人员keyNo' })
  relationPersonKeyNo?: string | null;
}
