import { ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { SingleCompanyDiligenceRequest } from './SingleCompanyDiligenceRequest';

export class GetDiligenceResultRequest extends OmitType(SingleCompanyDiligenceRequest, ['orgModelIds']) {
  @ApiPropertyOptional({ description: '如果该字段不为空，会获取该id对应的尽调结果 优先级最高' })
  @IsOptional()
  diligenceId?: number;
}
