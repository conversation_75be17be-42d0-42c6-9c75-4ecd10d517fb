import { ApiProperty } from '@nestjs/swagger';

export enum SnapshotStatus {
  PROCESSING = 0, // 生成中
  SUCCESS = 1, // 成功
  FAIL = 2, // 失败
  /** 快照写入中,已发送到es写入队列排队 */
  WRITING = 3,
}

export class SnapshotDetail {
  @ApiProperty({ description: '快照状态  0-生成中，1-成功， 2-失败' })
  status: SnapshotStatus;
  @ApiProperty({ description: '' })
  successHits: string[];
  // hitDimensions?: string[]; // 命中维度(需要生成快照的数据维度)
}
