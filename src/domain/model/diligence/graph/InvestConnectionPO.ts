import { ApiProperty } from '@nestjs/swagger';
import { GraphConnectionBasePO } from './GraphConnectionBasePO';

export class InvestConnectionPO extends GraphConnectionBasePO {
  @ApiProperty({ description: '尽调的公司的名字' })
  companyNameDD: string;

  @ApiProperty({ description: '尽调的公司的keyno' })
  companyKeynoDD: string;

  @ApiProperty({ description: '关联到的公司的名字(客商列表中/黑名单中的公司)' })
  companyNameRelated: string;

  @ApiProperty({ description: '关联到的公司的keyno(客商列表中/黑名单中的公司)' })
  companyKeynoRelated: string;

  @ApiProperty({ description: '尽调公司到关联到的客户公司的关系的方向(客商列表中/黑名单中的公司),正数表示是正向，负数表示反向' })
  direction: number;

  @ApiProperty({ description: '企业之间的关系' })
  role: string;

  @ApiProperty({ description: '持有的股份' })
  stockpercent: number;

  @ApiProperty({ description: '认缴金额' })
  shouldcapi: number;

  @ApiProperty({ description: '是否历史数据' })
  history: boolean;
}
