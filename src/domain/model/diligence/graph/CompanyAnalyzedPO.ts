import { ApiProperty } from '@nestjs/swagger';

export class CompanyAnalyzedPO {
  @ApiProperty({ description: '开始点公司名' })
  startCompanyName: string;
  @ApiProperty({ description: '开始点公司id' })
  startCompanyKeyno: string;
  @ApiProperty({ description: '终点公司名' })
  endCompanyName: string;
  @ApiProperty({ description: '终点公司id' })
  endCompanyKeyno: string;
  @ApiProperty({ description: '步数' })
  steps: number;
  @ApiProperty({ description: '关系' })
  relations: any[];
  @ApiProperty({ description: '是否历史' })
  isHistory = false;
}
