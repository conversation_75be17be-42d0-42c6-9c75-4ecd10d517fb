/**
 * 处理 graph 中 intersect 节点的 POJO
 */
export class HandleIntersectPO {
  usefulDimensionTypesKeys: string[];
  shareholdingRatioValue: number;
  historyRelation: string[];
  intersectTypes: string[];
  relationshipEnum: string;
  historyRelationshipEnum: string;
  dimensionEnum: string;

  constructor(
    usefulDimensionTypesKeys: string[],
    shareholdingRatioValue: number,
    historyRelation: string[],
    intersectTypes: string[],
    relationshipEnum: string,
    historyRelationshipEnum: string,
    dimensionEnum: string,
  ) {
    this.usefulDimensionTypesKeys = usefulDimensionTypesKeys;
    this.shareholdingRatioValue = shareholdingRatioValue;
    this.historyRelation = historyRelation;
    this.intersectTypes = intersectTypes;
    this.relationshipEnum = relationshipEnum;
    this.historyRelationshipEnum = historyRelationshipEnum;
    this.dimensionEnum = dimensionEnum;
  }
}
