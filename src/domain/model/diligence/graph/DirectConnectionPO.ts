import { ApiProperty } from '@nestjs/swagger';
import { GraphConnectionBasePO } from './GraphConnectionBasePO';

export class DirectConnectionPO extends GraphConnectionBasePO {
  @ApiProperty({ description: '尽调的公司的名字' })
  companyNameDD: string;

  @ApiProperty({ description: '尽调的公司的keyno' })
  companyKeynoDD: string;

  @ApiProperty({ description: '关联到的公司的名字(客商列表中/黑名单中的公司)' })
  companyNameRelated: string;

  @ApiProperty({ description: '关联到的公司的keyno(客商列表中/黑名单中的公司)' })
  companyKeynoRelated: string;
}
