import { ApiProperty, PickType } from '@nestjs/swagger';
import { CompanyAnalyzedBasePO } from './CompanyAnalyzedBasePO';

export class RiskType {
  riskType: string;
  riskTypeDesc: string;
  vids: string[];

  constructor(riskType: string, riskTypeDesc: string, vid: string) {
    this.riskType = riskType;
    this.riskTypeDesc = riskTypeDesc;
    this.vids = [vid];
  }
}

export class RelatedPartyGroupPO extends PickType(CompanyAnalyzedBasePO, [
  'startCompanyKeyno',
  'startCompanyName',
  'companyNameRelated',
  'companyKeynoRelated',
  'shortStatus',
  'businessStartTime',
  'paidCapi',
]) {
  @ApiProperty({ description: '风险类型的ids' })
  vids: string[];
  @ApiProperty({ description: '风险类型' })
  riskTypes: string[];
  @ApiProperty({ description: '风险类型描述' })
  riskTypeDescList: string[];
  @ApiProperty({ description: '关联方类型' })
  relatedTypes: string[];
  @ApiProperty({ description: '关联方类型描述' })
  relatedTypeDescList: string[];
  @ApiProperty({ description: '联系方式' })
  contactList?: string[];
  //冗余字段用于维度命中描述
  //@deprecated 使用 template，不需要再使用 dimensionDesc了
  dimensionDesc?: string;
  @ApiProperty({ description: '风险类型以及vids映射' })
  riskTypeInfos: RiskType[];
}
