import { ApiProperty } from '@nestjs/swagger';

export class CompanyAnalyzedBasePO {
  @ApiProperty({ description: '开始点公司名' })
  startCompanyName: string;
  @ApiProperty({ description: '开始点公司id' })
  startCompanyKeyno: string;
  @ApiProperty({ description: '关联公司名' })
  companyNameRelated: string;
  @ApiProperty({ description: '关联公司id' })
  companyKeynoRelated: string;
  @ApiProperty({ description: '关联公司名' })
  endCompanyName?: string;
  @ApiProperty({ description: '关联公司id' })
  endCompanyKeyno?: string;
  @ApiProperty({ description: '关联公司登记状态' })
  shortStatus?: string;
  @ApiProperty({ description: '关联公司成立时间' })
  businessStartTime?: Date;
  @ApiProperty({ description: '关联公司实缴资本，实缴资本可能会带单位，类似（万元人民币）、（万元港币）、（万元美元）、（万元）' })
  paidCapi?: string;
}
