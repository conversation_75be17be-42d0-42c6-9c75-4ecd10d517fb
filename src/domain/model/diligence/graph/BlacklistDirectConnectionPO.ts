import { ApiProperty } from '@nestjs/swagger';
import { DirectConnectionPO } from './DirectConnectionPO';

export class BlacklistDirectConnectionPO extends DirectConnectionPO {
  @ApiProperty({ description: '加入黑名单的时间' })
  joinDate: Date;

  @ApiProperty({ description: '加入黑名单的原因' })
  reason: string;

  @ApiProperty({ description: '黑名单有效期' })
  duration: number;

  @ApiProperty({ description: '黑名单的ID' })
  blacklistId: number;
}
