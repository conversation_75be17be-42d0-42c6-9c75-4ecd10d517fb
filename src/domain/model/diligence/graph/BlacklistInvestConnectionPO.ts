import { InvestConnectionPO } from './InvestConnectionPO';
import { ApiProperty } from '@nestjs/swagger';

export class BlacklistInvestConnectionPO extends InvestConnectionPO {
  @ApiProperty({ description: '加入黑名单的时间' })
  joinDate: Date;

  @ApiProperty({ description: '加入黑名单的原因' })
  reason: string;

  @ApiProperty({ description: '黑名单有效期' })
  duration: number;

  @ApiProperty({ description: '黑名单的ID' })
  blacklistId: number;
}
