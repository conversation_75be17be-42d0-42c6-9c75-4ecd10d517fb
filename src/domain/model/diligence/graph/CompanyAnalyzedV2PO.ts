import { ApiProperty } from '@nestjs/swagger';
import { RelationPath } from './GraphRelation';
import { CompanyAnalyzedBasePO } from './CompanyAnalyzedBasePO';

export class CompanyAnalyzedV2PO extends CompanyAnalyzedBasePO {
  @ApiProperty({ description: '关系' })
  relations: any[][];
  @ApiProperty({ description: '关系' })
  shortestPath?: any[];
  @ApiProperty({ description: '关联路径（二维数组）' })
  relationPaths: RelationPath[][];
  @ApiProperty({ description: '关联类型（edges 数组）' })
  relationTypes: string[];
  @ApiProperty({ description: '数据版本v2,因为加了穿透关系，原本的结构不再能满足需求' })
  version = 'V2';
}
