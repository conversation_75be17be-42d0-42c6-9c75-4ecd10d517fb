import { ApiProperty } from '@nestjs/swagger';
import { InvestConnectionPO } from './InvestConnectionPO';
import { PersonConnectionPO } from './PersonConnectionPO';

export class PartnerConnectionDDResponse {
  @ApiProperty({ description: '人员关联' })
  personConnections: PersonConnectionPO[];

  @ApiProperty({ description: '持股关联' })
  investConnections: InvestConnectionPO[];

  @ApiProperty({ description: '分支机构关联' })
  branchConnections: InvestConnectionPO[];
}
