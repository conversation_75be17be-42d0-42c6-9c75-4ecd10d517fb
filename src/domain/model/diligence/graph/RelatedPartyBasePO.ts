import { CompanyAnalyzedBasePO } from './CompanyAnalyzedBasePO';
import { ApiProperty, PickType } from '@nestjs/swagger';

export class RelatedPartyBasePO extends PickType(CompanyAnalyzedBasePO, [
  'startCompanyKeyno',
  'startCompanyName',
  'companyNameRelated',
  'companyKeynoRelated',
  'shortStatus',
  'businessStartTime',
  'paidCapi',
]) {
  @ApiProperty({ description: '风险类型的id' })
  vid: string;
  @ApiProperty({ description: '风险类型' })
  riskType: string;
  @ApiProperty({ description: '风险类型描述' })
  riskTypeDesc: string;
  @ApiProperty({ description: '关联方类型' })
  relatedType: string;
  @ApiProperty({ description: '关联方类型描述' })
  relatedTypeDesc: string;
  @ApiProperty({ description: '联系方式' })
  contact?: string;
}
