import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { InvestConnectionPO } from './InvestConnectionPO';
import { BlacklistDirectConnectionPO } from './BlacklistDirectConnectionPO';
import { PersonConnectionPO } from './PersonConnectionPO';
import { BlacklistInvestConnectionPO } from './BlacklistInvestConnectionPO';
import { BlacklistPersonConnectionPO } from './BlacklistPersonConnectionPO';
import { CompanyModel } from '../../company/CompanyModel';
import { InvestigationConnectionPO } from './InvestigationConnectionPO';
import { CompanyAnalyzedV2PO } from './CompanyAnalyzedV2PO';

export class RoverGraphParsedPO {
  [DimensionTypeEnums.InvestorsRelationship]: InvestConnectionPO[] = [];
  [DimensionTypeEnums.ShareholdingRelationship]: InvestConnectionPO[] = [];
  [DimensionTypeEnums.ServeRelationship]: PersonConnectionPO[] = [];
  [DimensionTypeEnums.Shareholder]: BlacklistInvestConnectionPO[] = [];
  [DimensionTypeEnums.ForeignInvestment]: BlacklistInvestConnectionPO[] = [];
  [DimensionTypeEnums.EmploymentRelationship]: BlacklistPersonConnectionPO[] = [];
  [DimensionTypeEnums.HitInnerBlackList]: BlacklistDirectConnectionPO[] = [];
  [DimensionTypeEnums.BlacklistSameSuspectedActualController]?: CompanyModel[] = [];
  [DimensionTypeEnums.CustomerPartnerInvestigation]?: InvestigationConnectionPO[] | CompanyAnalyzedV2PO[] = [];
  [DimensionTypeEnums.BlacklistPartnerInvestigation]?: InvestigationConnectionPO[] | CompanyAnalyzedV2PO[] = [];
}
