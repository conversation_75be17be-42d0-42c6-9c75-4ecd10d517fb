import { ApiProperty } from '@nestjs/swagger';
import { GraphConnectionBasePO } from './GraphConnectionBasePO';

export class PersonConnectionPO extends GraphConnectionBasePO {
  @ApiProperty({ description: '关联人的姓名' })
  personName: string;

  @ApiProperty({ description: '关联人的 keyno' })
  personKeyno: string;

  @ApiProperty({ description: '尽调的公司的名字' })
  companyNameDD: string;

  @ApiProperty({ description: '尽调的公司的keyno' })
  companyKeynoDD: string;

  @ApiProperty({ description: '人员和尽调的公司关系类型', enum: ['INVEST', 'LEGAL', 'EMPLOY'] })
  typeDD: string;

  @ApiProperty({ description: '人员在尽调公司中持有的股份,只有当类型是INVEST时候才有值', required: false })
  stockDD: number;

  @ApiProperty({ description: '人员在尽调公司中的认缴金额,只有当类型是INVEST时候才有值', required: false })
  capiDD: number;

  @ApiProperty({ description: '关联到的公司的名字(客商列表中/黑名单中的公司)' })
  companyNameRelated: string;

  @ApiProperty({ description: '关联到的公司的keyno(客商列表中/黑名单中的公司)' })
  companyKeynoRelated: string;

  @ApiProperty({
    description: '人员和关联到的公司关系类型(客商列表中/黑名单中的公司)',
    enum: ['INVEST', 'LEGAL', 'EMPLOY'],
  })
  typeRelated: string;

  @ApiProperty({
    description: '人员在关联到的的公司中持有的股份(客商列表中/黑名单中的公司),只有当类型是INVEST时候才有值',
    required: false,
  })
  stockRelated: number;

  @ApiProperty({
    description: '人员在关联到的公司中的认缴金额(客商列表中/黑名单中的公司),只有当类型是INVEST时候才有值',
    required: false,
  })
  capiRelated: number;

  @ApiProperty({ description: '人员在尽调公司中的角色,自然人股东，董事，法人等' })
  roleDD: string;

  @ApiProperty({ description: '人员在关联到的公司中的角色(客商列表中/黑名单中的公司),自然人股东，董事，法人等' })
  roleRelated: string;

  @ApiProperty({ description: '是否历史数据' })
  history: boolean;
}
