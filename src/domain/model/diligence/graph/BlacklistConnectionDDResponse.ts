import { ApiProperty } from '@nestjs/swagger';
import { BlacklistInvestConnectionPO } from './BlacklistInvestConnectionPO';
import { BlacklistPersonConnectionPO } from './BlacklistPersonConnectionPO';
import { BlacklistDirectConnectionPO } from './BlacklistDirectConnectionPO';
import { ActualControllerConnectionPO } from './ActualControllerConnectionPO';

export class BlacklistConnectionDDResponse {
  @ApiProperty({ description: '直接关联' })
  directConnection: BlacklistDirectConnectionPO[];

  @ApiProperty({ description: '人员关联' })
  personConnections: BlacklistPersonConnectionPO[];

  @ApiProperty({ description: '持股关联' })
  shareholderConnections: BlacklistInvestConnectionPO[];

  @ApiProperty({ description: '实际控制人关联' })
  actualControllerConnections?: ActualControllerConnectionPO[];

  @ApiProperty({ description: '分支机构关联' })
  branchConnections?: BlacklistInvestConnectionPO[];
}
