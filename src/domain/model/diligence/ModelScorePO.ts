import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { GroupMetricScorePO } from '../group/GroupMetricScorePO';
import { LevelGroupDimensionPO } from '@domain/enums/dimension/LevelGroupDimensionPO';
import { CreditRateResult } from '@domain/enums/dimension/CreditRateResult';
import { MetricScorePO } from '../metric/MetricScorePO';
import { DueDiligenceResult } from '@domain/constants/diligence.constants';
import { RiskModelTypeEnums } from '@domain/enums/RiskModelTypeEnums';
import { DiligenceTimeCostPO } from './dimension/DiligenceTimeCostPO';

export class ModelScorePO {
  @ApiProperty({ description: '风险评价分数(不根据用户设置而改变)' })
  totalScore: number;

  @ApiPropertyOptional({ description: '风险模型类型', enum: RiskModelTypeEnums })
  modelType?: RiskModelTypeEnums = RiskModelTypeEnums.GeneralRiskModel;

  @ApiProperty({ description: '尽调结果等级 ', enum: DueDiligenceResult })
  result: DueDiligenceResult;

  // @ApiProperty({ description: '尽调结果等级描述 ' })
  // resultDes: string;

  @ApiProperty({ description: '维度的分数', type: GroupMetricScorePO, isArray: true })
  groupMetricScores: GroupMetricScorePO[];

  @ApiPropertyOptional({ description: '按照风险级别和一级维度的分组', isArray: false })
  levelGroup?: LevelGroupDimensionPO;

  @ApiProperty({ description: '命中的维度的详情' })
  originalHits: MetricScorePO[];

  @ApiPropertyOptional({ description: '具有一票否决权的指标(红色通道)' })
  vetoMetrics?: MetricScorePO[];

  @ApiPropertyOptional({ description: '命中的维度的类型' })
  dimensionHits?: DimensionTypeEnums[];

  @ApiPropertyOptional({ description: '同一个维度在一次尽调中可能会被多次命中，所以这里使用 DimensionTypeEnum + strategyId做去重，便于后面快照时候区分' })
  dimensionHitsUnique?: string[];

  @ApiPropertyOptional({ description: '风险扫描时候所有命中的记录的总数' })
  totalHits? = 0;

  @ApiPropertyOptional({ description: '启用查查信用分时存在' })
  creditRateResult?: CreditRateResult;

  @ApiPropertyOptional({ description: '是否计费' })
  paid?: boolean;

  @ApiProperty({ description: '尽调记录的ID' })
  diligenceHistoryId: number;

  @ApiProperty({ description: '尽调发生的时间' })
  diligenceAt?: Date;

  @ApiPropertyOptional({ description: '时间消耗, 数组第一个是总时间，后面是分段的时间' })
  cost?: DiligenceTimeCostPO;
}
