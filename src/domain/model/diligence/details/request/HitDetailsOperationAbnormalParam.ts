import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { HitDetailsBaseQueryPaginationParams } from './HitDetailsBaseQueryParams';

export class HitDetailsOperationAbnormalParam extends HitDetailsBaseQueryPaginationParams {
  @ApiProperty({ required: false, description: '严重违法的Ids' })
  ids: string[];
  @ApiProperty({ required: false, type: Number, default: 0, example: 0, description: '正常或历史，默认为1（非历史），0（历史） -1（全部） 默认1' })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  isValid = 1;
  @ApiProperty({ required: false, description: '标准化的案由编码' })
  stdReason: number;
  @ApiProperty({ required: false, description: '字符串的案由' })
  reason: string;
  @ApiProperty({ required: false, description: '是否来自风险扫描' })
  isRiskScan: boolean;
}
