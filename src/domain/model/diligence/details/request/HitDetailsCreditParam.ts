import { IntersectionType, PickType } from '@nestjs/swagger';
import { CreditSearchFilter } from '../../../data/source/CreditSearchFilter';
import { HitDetailsBaseQueryPaginationParams } from './HitDetailsBaseQueryParams';

/**
 * 查信用入参
 * 可用于：被列入失信被执行人
 * 可用于：限制高消费
 * 可用于：限制出境
 * 可用于：破产重整
 * 可用于：合同违约
 * 可用于：债券违约
 * 可用于：黑名单
 */
export class HitDetailsCreditParam extends HitDetailsBaseQueryPaginationParams {}

/**
 * 股权冻结/被执行人
 */
export class HitFreezeEquityParam extends IntersectionType(HitDetailsBaseQueryPaginationParams, PickType(CreditSearchFilter, ['amount'])) {}

/**
 * 被执行人
 */
export class HitPersonExecutionParam extends IntersectionType(HitDetailsBaseQueryPaginationParams, PickType(CreditSearchFilter, ['amount'])) {}

/**
 * 环保处罚/行政处罚/税收公告
 */
export class HitPenaltiesParam extends IntersectionType(HitDetailsBaseQueryPaginationParams, PickType(CreditSearchFilter, ['publishdate', 'amount'])) {}
