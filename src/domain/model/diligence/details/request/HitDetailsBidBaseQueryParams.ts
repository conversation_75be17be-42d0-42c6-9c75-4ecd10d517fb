import { ApiProperty } from '@nestjs/swagger';
import { PaginationParams } from '@domain/model/common';
import { JointBiddingAnalysisModel } from '../../../tender/JointBiddingAnalysisModel';
import { IsOptional } from 'class-validator';

export class HitDetailsBidBaseQueryParams extends PaginationParams {
  @ApiProperty({ required: false, description: '公司ids' })
  keyNos?: string[];

  @ApiProperty({ required: true, description: '' })
  keyNoAndNames?: JointBiddingAnalysisModel[];

  @ApiProperty({ required: false, description: '排序字段，默认为空' })
  @IsOptional()
  sortField?: string;

  @ApiProperty({ required: false, default: false, description: '是否升序(默认false' })
  isSortAsc?: boolean = false;

  orgId?: number;
  diligenceId?: number;
  status?: number[];

  /**
   * 招标排查创建时间
   */
  createDate?: Date;

  constructor() {
    super();
    this.keyNos = this.keyNoAndNames?.map((e) => e.companyId);
  }
}
