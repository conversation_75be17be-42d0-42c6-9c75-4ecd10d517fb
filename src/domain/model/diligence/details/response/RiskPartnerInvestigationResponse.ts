//交叉重叠关系排查相关维度详情
import { ApiProperty } from '@nestjs/swagger';
import { HitDetailsBaseResponse } from './HitDetailsBaseResponse';
import { PartnerConnectionDDResponse } from '../../graph/PartnerConnectionDDResponse';
import { PersonConnectionPO } from '../../graph/PersonConnectionPO';
import { InvestConnectionPO } from '../../graph/InvestConnectionPO';

/**
 * 与第三方列表企业存在投资任职关联
 */
export class DimensionHitDetailsCustomerPartnerInvestigation {
  @ApiProperty({ type: String, description: '关联企业名称' })
  companyNameRelated: string;
  @ApiProperty({ type: String, description: '关联企业名称id' })
  companyKeynoRelated: string;
  @ApiProperty({ description: '关联信息', type: PartnerConnectionDDResponse })
  relationTypes: PersonConnectionPO[] | InvestConnectionPO[];
}

export class HitDetailsResponseCustomerPartnerInvestigation extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsCustomerPartnerInvestigation, isArray: true })
  Result: DimensionHitDetailsCustomerPartnerInvestigation[];
}
