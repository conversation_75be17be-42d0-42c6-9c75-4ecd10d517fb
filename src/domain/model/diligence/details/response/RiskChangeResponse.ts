import { ApiProperty } from '@nestjs/swagger';
import { HitDetailsBaseResponse } from './HitDetailsBaseResponse';
import { RiskChangeCategoryEnum } from '@domain/enums/riskchange/RiskChangeCategoryEnum';

/**
 * 风险变更响应类型定义
 *
 * 本文件定义了风险变更数据的完整类型结构，基于以下两个处理文件的分析：
 * - dimension-hit-detail.processor.ts: 处理维度命中详情
 * - related-dimension-hit-detail.processor.ts: 处理关联方维度命中详情
 *
 * 风险变更数据的处理流程：
 * 1. 从 ES 查询获得原始数据（itemRaw）
 * 2. 使用 cloneDeep(itemRaw) 创建 newItem
 * 3. 对 newItem 中的 JSON 字段进行预处理：
 *    - Extend1: 解析为 JSON 对象
 *    - ChangeExtend: 解析为 JSON 对象
 * 4. 根据 Category 进行不同的业务逻辑处理
 * 5. 返回处理后的数据
 *
 * 主要的扩展信息类型按 Category 分类：
 * - 股权变更类 (72, 44, 68, 204): ShareChangeExtendInfo
 * - 司法案件类 (4, 49, 18, 7, 27, 90): JudicialCaseExtendInfo
 * - 行政处罚类 (107, 22, 31, 117, 121): AdministrativePenaltyExtendInfo
 * - 股权出质/质押类 (12, 50): EquityPledgeExtendInfo
 * - 抵押担保类 (15, 30, 53, 101): MortgageGuaranteeExtendInfo
 * - 企业公告类 (65, 113): AnnouncementExtendInfo
 * - 受益所有人变更类 (114): BeneficiaryChangeExtendInfo
 * - 基础信息变更类 (39, 60, 40, 41, 37, 38等): BasicInfoChangeExtendInfo
 */

// 人员/企业信息基础结构
export class PersonOrCompanyInfo {
  @ApiProperty({ description: '关键编号', required: false })
  KeyNo?: string;

  @ApiProperty({ description: '名称', required: false })
  Name?: string;

  @ApiProperty({ description: '组织类型 -2:未知人员, -1:加密人员, 0:企业, 2:自然人', required: false })
  Org?: number;

  @ApiProperty({ description: '显示名称', required: false })
  ShowName?: string;

  @ApiProperty({ description: '持股比例', required: false })
  PercentTotal?: string;
}

// 股权变更相关扩展信息
export class ShareChangeExtendInfo {
  @ApiProperty({ description: '股比下降count', required: false })
  A?: number;

  @ApiProperty({ description: '退出count', required: false })
  B?: number;

  @ApiProperty({ description: '新增count', required: false })
  C?: number;

  @ApiProperty({ description: '股比下降列表', required: false })
  D?: Array<{
    K?: string; // KeyNo
    B?: string; // 变更前比例
    C?: string; // 变更后比例
  }>;

  @ApiProperty({ description: '退出列表', type: [PersonOrCompanyInfo], required: false })
  E?: Array<PersonOrCompanyInfo>;

  @ApiProperty({ description: '新增列表', type: [PersonOrCompanyInfo], required: false })
  F?: Array<PersonOrCompanyInfo>;

  @ApiProperty({ description: '上升count', required: false })
  G?: number;

  @ApiProperty({ description: '上升列表', required: false })
  H?: Array<{
    K?: string; // KeyNo
    B?: string; // 变更前比例
    C?: string; // 变更后比例
  }>;

  @ApiProperty({ description: '是否有大股东变更 1有 0无', required: false })
  IsBP?: string | number;

  @ApiProperty({ description: '大股东变更列表', type: [PersonOrCompanyInfo], required: false })
  BP?: Array<PersonOrCompanyInfo>;

  @ApiProperty({ description: '参与人KeyNo（持股比例变更等场景）', required: false })
  K?: string;

  @ApiProperty({ description: '变更类型标识', required: false })
  T?: number;
}

// 司法案件相关扩展信息
export class JudicialCaseExtendInfo {
  @ApiProperty({ description: '案号', required: false })
  A?: string;

  @ApiProperty({ description: '法院', required: false })
  B?: string;

  @ApiProperty({ description: '当事人列表', type: [PersonOrCompanyInfo], required: false })
  C?: Array<PersonOrCompanyInfo>;

  @ApiProperty({ description: '案由', required: false })
  D?: string;

  @ApiProperty({ description: '案件类型', required: false })
  E?: string;

  @ApiProperty({ description: '案由', required: false })
  F?: string;

  @ApiProperty({ description: '审理程序', required: false })
  G?: string;

  @ApiProperty({ description: '其他信息', required: false })
  H?: string;

  @ApiProperty({ description: '案件金额', required: false })
  I?: string | number;

  @ApiProperty({ description: '当事人角色信息', required: false })
  K?: Array<{
    keyno?: string;
    role?: string;
    org?: number;
    roletype?: number;
    showname?: string;
    roletag?: number;
    source?: number;
    name?: string;
  }>;

  @ApiProperty({ description: '裁判日期（时间戳）', required: false })
  M?: number;

  @ApiProperty({ description: '风险分类代码', required: false })
  rc?: string;

  @ApiProperty({ description: '重要性级别', required: false })
  im?: number;
}

// 行政处罚相关扩展信息
export class AdministrativePenaltyExtendInfo {
  @ApiProperty({ description: '处罚事由', required: false })
  A?: string;

  @ApiProperty({ description: '处罚类型', required: false })
  B?: string;

  @ApiProperty({ description: '处罚决定书文号', required: false })
  C?: string;

  @ApiProperty({ description: '处罚决定日期（时间戳）', required: false })
  D?: number;

  @ApiProperty({ description: '处罚金额', required: false })
  E?: number;

  @ApiProperty({ description: '处罚机关', required: false })
  F?: string;

  @ApiProperty({ description: '公示日期（时间戳）', required: false })
  G?: number;
}

// 股权出质/质押相关扩展信息
export class EquityPledgeExtendInfo {
  @ApiProperty({ description: '状态', required: false })
  status?: string;

  @ApiProperty({ description: '出质人信息', type: PersonOrCompanyInfo, required: false })
  pledgorinfo?: PersonOrCompanyInfo;

  @ApiProperty({ description: '质权人信息', type: PersonOrCompanyInfo, required: false })
  pledgeeinfo?: PersonOrCompanyInfo;

  @ApiProperty({ description: '出质股权数额', required: false })
  pledgedamount?: string;

  @ApiProperty({ description: '出质比例', required: false })
  percent?: string;

  @ApiProperty({ description: '登记日期（时间戳）', required: false })
  regdate?: number;

  @ApiProperty({ description: '企业ID', required: false })
  companyid?: string;

  @ApiProperty({ description: '企业名称', required: false })
  companyname?: string;

  @ApiProperty({ description: '类型标识', required: false })
  t?: number;

  @ApiProperty({ description: '二级类型标识', required: false })
  t2?: number;

  @ApiProperty({ description: '出质人姓名', required: false })
  name?: string;

  @ApiProperty({ description: '出质人KeyNo', required: false })
  keyno?: string;

  @ApiProperty({ description: '组织类型', required: false })
  org?: number;
}

// 抵押担保相关扩展信息
export class MortgageGuaranteeExtendInfo {
  @ApiProperty({ description: '抵押金额', required: false })
  A?: number;

  @ApiProperty({ description: '担保金额', required: false })
  B?: number;

  @ApiProperty({ description: '开始日期', required: false })
  C?: string;

  @ApiProperty({ description: '结束日期', required: false })
  D?: string;

  @ApiProperty({ description: '抵押权人/债权人', type: PersonOrCompanyInfo, required: false })
  E?: PersonOrCompanyInfo;

  @ApiProperty({ description: '抵押人/债务人', type: PersonOrCompanyInfo, required: false })
  F?: PersonOrCompanyInfo;

  @ApiProperty({ description: '登记编号', required: false })
  G?: string;

  @ApiProperty({ description: '类型标识', required: false })
  T?: number;
}

// 企业公告相关扩展信息
export class AnnouncementExtendInfo {
  @ApiProperty({ description: '公告标题', required: false })
  Title?: string;

  @ApiProperty({ description: '公告类型', required: false })
  TS?: string;

  @ApiProperty({ description: '发布时间（时间戳）', required: false })
  Publishtime?: number;

  @ApiProperty({ description: '公告URL', required: false })
  url?: string;
}

// 受益所有人变更扩展信息
export class BeneficiaryChangeExtendInfo {
  @ApiProperty({ description: '变更前内容', required: false })
  beforecontent?: string;

  @ApiProperty({ description: '变更后内容', required: false })
  aftercontent?: string;

  @ApiProperty({ description: '变更扩展信息', required: false })
  changeextend?: string;

  @ApiProperty({ description: '对象ID', required: false })
  objectid?: string;

  @ApiProperty({ description: '变更状态', required: false })
  changestatus?: number;

  @ApiProperty({ description: '扩展字段1', required: false })
  extend1?: string;
}

// 基础信息变更扩展信息（如企业名称、地址等）
export class BasicInfoChangeExtendInfo {
  @ApiProperty({ description: '变更前内容', required: false })
  a?: string;

  @ApiProperty({ description: '变更后内容', required: false })
  b?: string;

  @ApiProperty({ description: '变更前高亮内容', required: false })
  c?: string;

  @ApiProperty({ description: '变更后高亮内容', required: false })
  d?: string;
}

/**
 * 风险变更数据项基础结构
 */
export class RiskChangeItem {
  /** 记录唯一标识 */
  @ApiProperty({ description: '记录唯一标识' })
  Id: string;

  /** 企业/人员标识 */
  @ApiProperty({ description: '企业/人员标识' })
  KeyNo: string;

  /** 企业/人员名称 */
  @ApiProperty({ description: '企业/人员名称' })
  Name: string;

  /** 风险变更分类 */
  @ApiProperty({ description: '风险变更分类', enum: RiskChangeCategoryEnum })
  Category: RiskChangeCategoryEnum;

  /** 对象标识 */
  @ApiProperty({ description: '对象标识' })
  ObjectId: string;

  /** 变更状态 */
  @ApiProperty({ description: '变更状态' })
  ChangeStatus: number;

  /** 变更日期（时间戳） */
  @ApiProperty({ description: '变更日期（时间戳）' })
  ChangeDate: number;

  /** 创建日期（时间戳） */
  @ApiProperty({ description: '创建日期（时间戳）' })
  CreateDate: number;

  /** 更新日期（时间戳） */
  @ApiProperty({ description: '更新日期（时间戳）' })
  UpdateDate: number;

  /** 数据类型 1:公司 2:人员 */
  @ApiProperty({ description: '数据类型 1:公司 2:人员' })
  DataType: number;

  /** 是否有效 */
  @ApiProperty({ description: '是否有效' })
  IsValid: number;

  /** 重要性标记 */
  @ApiProperty({ description: '重要性标记' })
  ImportanceFlag: number;

  /** 是否为风险 */
  @ApiProperty({ description: '是否为风险' })
  IsRisk: number;

  /** 风险等级 */
  @ApiProperty({ description: '风险等级' })
  RiskLevel: number;

  /** 变更前内容 */
  @ApiProperty({ description: '变更前内容', required: false })
  BeforeContent?: string | any;

  /** 变更后内容 */
  @ApiProperty({ description: '变更后内容', required: false })
  AfterContent?: string | any;

  /** 变更扩展信息（JSON字符串或解析后的对象） */
  @ApiProperty({
    description: '变更扩展信息',
    required: false,
    oneOf: [
      { type: 'string' },
      { $ref: '#/components/schemas/ShareChangeExtendInfo' },
      { $ref: '#/components/schemas/JudicialCaseExtendInfo' },
      { $ref: '#/components/schemas/AdministrativePenaltyExtendInfo' },
      { $ref: '#/components/schemas/EquityPledgeExtendInfo' },
      { $ref: '#/components/schemas/MortgageGuaranteeExtendInfo' },
      { $ref: '#/components/schemas/AnnouncementExtendInfo' },
      { $ref: '#/components/schemas/BeneficiaryChangeExtendInfo' },
      { $ref: '#/components/schemas/BasicInfoChangeExtendInfo' },
      { type: 'object' },
    ],
  })
  ChangeExtend?:
    | string
    | ShareChangeExtendInfo
    | JudicialCaseExtendInfo
    | AdministrativePenaltyExtendInfo
    | EquityPledgeExtendInfo
    | MortgageGuaranteeExtendInfo
    | AnnouncementExtendInfo
    | BeneficiaryChangeExtendInfo
    | BasicInfoChangeExtendInfo
    | any;

  /** 扩展字段1（JSON字符串或解析后的对象） */
  @ApiProperty({ description: '扩展字段1', required: false })
  Extend1?: string | any;

  /** 扩展字段2 */
  @ApiProperty({ description: '扩展字段2', required: false })
  Extend2?: string;

  /** 扩展字段3 */
  @ApiProperty({ description: '扩展字段3', required: false })
  Extend3?: string;

  /** 扩展字段4 */
  @ApiProperty({ description: '扩展字段4', required: false })
  Extend4?: string;

  /** 扩展字段5 */
  @ApiProperty({ description: '扩展字段5', required: false })
  Extend5?: string;

  /** 发布时间（用于部分类型的数据） */
  @ApiProperty({ description: '发布时间（时间戳）', required: false })
  PublishTime?: number;

  // 以下字段是在处理过程中可能被添加或修改的

  /** 处理后的变更扩展信息中的 IsBP 字段 */
  @ApiProperty({ description: '是否为控股股东/实际控制人', required: false })
  IsBP?: number;

  /** 处理后从 ChangeExtend 中提取的变更前内容 */
  @ApiProperty({ description: '处理后的变更前内容', required: false })
  BeforeChange?: any;

  /** 处理后从 ChangeExtend 中提取的变更后内容 */
  @ApiProperty({ description: '处理后的变更后内容', required: false })
  AfterChange?: any;

  /** URL链接（如企业公告等数据类型） */
  @ApiProperty({ description: 'URL链接', required: false })
  URL?: string;

  /** 描述信息结构 */
  @ApiProperty({ description: '描述信息', required: false })
  Description?: {
    Title?: string;
    Subtitle?: string;
    Content?: string;
    ContentArray?: Array<{ k: string; v: string; isLink?: boolean }>;
    Highlight?: Array<{ Id: string; Name: string; Org: number }>;
    OtherHighlight?: Array<{ Id: string; Name: string; Type: string }>;
    SubtitleHighlight?: string;
    RelateChange?: string;
    BigEventDesc?: string;
  };

  /** 股权变更比例 */
  @ApiProperty({ description: '股权变更比例', required: false })
  ShareChangeRate?: number;

  /** 持股人员信息 */
  @ApiProperty({ description: '持股人员信息', type: PersonOrCompanyInfo, required: false })
  PartInfo?: PersonOrCompanyInfo;
}

export class RiskChangeResponse extends HitDetailsBaseResponse {
  @ApiProperty({ description: '结果列表', type: RiskChangeItem, isArray: true })
  Result: RiskChangeItem[];
}

/**
 * 使用示例：
 *
 * 1. 在 Controller 中使用：
 * ```typescript
 * @Get('risk-change')
 * @ApiResponse({ type: RiskChangeResponse })
 * async getRiskChange(): Promise<RiskChangeResponse> {
 *   // ...
 * }
 * ```
 *
 * 2. 类型安全的数据处理：
 * ```typescript
 * // 处理股权变更数据
 * const item = response.Result[0];
 * if (item.Category === RiskChangeCategoryEnum.category72) {
 *   const changeExtend = item.ChangeExtend as ShareChangeExtendInfo;
 *   console.log('股比下降数量:', changeExtend.A);
 * }
 *
 * // 处理司法案件数据
 * if (item.Category === RiskChangeCategoryEnum.category4) {
 *   const changeExtend = item.ChangeExtend as JudicialCaseExtendInfo;
 *   console.log('案号:', changeExtend.A);
 *   console.log('案件金额:', changeExtend.I);
 * }
 * ```
 *
 * 3. 在处理文件中使用：
 * ```typescript
 * const newItem: RiskChangeItem = cloneDeep(itemRaw);
 * // 进行类型安全的处理
 * ```
 */
