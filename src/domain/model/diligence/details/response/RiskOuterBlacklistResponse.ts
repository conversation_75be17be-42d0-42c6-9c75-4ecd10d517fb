//外部黑名单相关维度详情
import { ApiProperty } from '@nestjs/swagger';
import { HitDetailsBaseResponse } from './HitDetailsBaseResponse';

/**
 * 命中外部黑名单
 */
export class DimensionHitDetailsHitOuterBlackList {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '黑名单企业名称' })
  Name: string;
  @ApiProperty({ type: String, description: '公司 keyNo' })
  KeyNo: string;
  @ApiProperty({ type: String, description: '命中黑名单类型' })
  CaseReasonType: string;
  @ApiProperty({ type: String, description: '风险等级' })
  level: string;
  @ApiProperty({ type: String, description: '列入原因' })
  CaseReason: string;
  @ApiProperty({ type: String, description: '列入机关' })
  Court: string;
  @ApiProperty({ type: Number, description: '列入日期' })
  Publishdate: number;
}

export class HitDetailsResponseHitOuterBlackList extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsHitOuterBlackList, isArray: true })
  Result: DimensionHitDetailsHitOuterBlackList[];
}
