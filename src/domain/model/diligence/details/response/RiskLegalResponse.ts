//法律风险相关维度详情
import { ApiProperty } from '@nestjs/swagger';
import { HitDetailsBaseResponse } from './HitDetailsBaseResponse';

/**
 * 被列入失信被执行人
 */
export class DimensionHitDetailsPersonCredit {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '案号' })
  CaseNo: number;
  @ApiProperty({ type: String, description: '执行法院' })
  ExecuteGov: string;
  @ApiProperty({ type: String, description: '涉案金额' })
  Amount: number;
  @ApiProperty({ type: String, description: '履行情况' })
  Executestatus: string;
  @ApiProperty({ type: String, description: '失信行为' })
  Actionremark: string;
  @ApiProperty({ type: String, description: '立案日期' })
  LiAnDate: number;
  @ApiProperty({ type: String, description: '发布日期' })
  PublicDate: string;
}

export class HitDetailsResponsePersonCredit extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsPersonCredit, isArray: true })
  Result: DimensionHitDetailsPersonCredit[];
}

/**
 * 限制高消费
 */
export class DimensionHitDetailsRestrictedConsumption {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '案号' })
  CaseNo: number;
  @ApiProperty({ type: String, description: '限消令对象' })
  Name: string;
  @ApiProperty({ type: String, description: '关联对象' })
  PledgorInfo: string;
  @ApiProperty({ type: String, description: '申请人' })
  ApplicantInfo: number;
  @ApiProperty({ type: String, description: '立案日期' })
  LianDate: number;
  @ApiProperty({ type: String, description: '发布日期' })
  PublishDate: number;
  @ApiProperty({ type: String, description: '原文' })
  FileUrl: string;
}

export class HitDetailsResponseRestrictedConsumption extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsRestrictedConsumption, isArray: true })
  Result: DimensionHitDetailsRestrictedConsumption[];
}

/**
 * 主要负责人被列入失信被执行人
 */
export class DimensionHitDetailsMainMembersPersonCredit {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '案号' })
  CaseNo: number;
  @ApiProperty({ type: String, description: '疑似申请执行人' })
  SqrInfo: string;
  @ApiProperty({ type: String, description: '执行法院' })
  Court: string;
  @ApiProperty({ type: String, description: '执行依据文号' })
  OrgNo: string;
  @ApiProperty({ type: String, description: '失信被执行人' })
  SubjectInfo: string;
  @ApiProperty({ type: String, description: '涉案金额（元）' })
  Amount: number;
  @ApiProperty({ type: String, description: '履行情况' })
  ExecuteStatus: number;
  @ApiProperty({ type: String, description: '失信行为' })
  ActionRemark: string;
  @ApiProperty({ type: String, description: '立案日期' })
  LianDate: number;
  @ApiProperty({ type: String, description: '发布日期' })
  PublishDate: number;
}

export class HitDetailsResponseMainMembersPersonCredit extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsMainMembersPersonCredit, isArray: true })
  Result: DimensionHitDetailsMainMembersPersonCredit[];
}

/**
 * 主要人员限制高消费
 */
export class HitDetailsResponseMainMemberRestrictedConsumption extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsRestrictedConsumption, isArray: true })
  Result: DimensionHitDetailsRestrictedConsumption[];
}

/**
 * 主要人员限制出境
 */
export class DimensionHitDetailsMainMembersRestrictedOutbound {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '案号' })
  CaseNo: string;
  @ApiProperty({ type: String, description: '限制出境对象' })
  SubjectInfo: string;
  @ApiProperty({ type: String, description: '被执行人' })
  PledgorInfo: string;
  @ApiProperty({ type: String, description: '被执行人地址' })
  Address: string;
  @ApiProperty({ type: String, description: '执行依据文号' })
  OrgNo: string;
  @ApiProperty({ type: String, description: '申请执行人' })
  ApplicantInfo: string;
  @ApiProperty({ type: String, description: '执行标的金额(元)' })
  Amount: string;
  @ApiProperty({ type: String, description: '承办法院' })
  Court: string;
  @ApiProperty({ type: String, description: '发布日期' })
  PublishDate: number;
}

export class HitDetailsResponseMainMembersRestrictedOutbound extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsMainMembersRestrictedOutbound, isArray: true })
  Result: DimensionHitDetailsMainMembersRestrictedOutbound[];
}

/**
 * 公司及主要人员涉刑事犯罪
 */
export class DimensionHitDetailsCompanyOrMainMembersCriminalOffence {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '案件名称' })
  CaseName: string;
  @ApiProperty({ type: String, description: '当事人' })
  RoleAmt: string;
  @ApiProperty({ type: String, description: '案号' })
  AnNoList: string;
  @ApiProperty({ type: String, description: '最新案件进程' })
  LastCaseProgress: string;
  @ApiProperty({ type: String, description: '法院' })
  CourtList: string;
}

export class HitDetailsResponseMainMembersCriminalOffence extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsCompanyOrMainMembersCriminalOffence, isArray: true })
  Result: DimensionHitDetailsCompanyOrMainMembersCriminalOffence[];
}

/**
 * 涉贪污受贿裁判相关提及方
 */
export class DimensionHitDetailsCriminalInvolve {
  @ApiProperty({ type: String, description: '文书标题' })
  casename: string;
  @ApiProperty({ type: String, description: '案由' })
  casereason: string;
  @ApiProperty({ type: String, description: '案号' })
  caseno: string;
  @ApiProperty({ type: String, description: '当事人' })
  caserolegroupbyrolename: string;
  @ApiProperty({ type: String, description: '案件金额(元)' })
  amountinvolved: string;
  @ApiProperty({ type: String, description: '裁判结果' })
  judgeresult: string;
  @ApiProperty({ type: String, description: '裁判日期' })
  judgedate: string;
  @ApiProperty({ type: String, description: '发布日期' })
  courtdate: string;
}

export class HitDetailsResponseCriminalInvolve extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsCriminalInvolve, isArray: true })
  Result: DimensionHitDetailsCriminalInvolve[];
}

/**
 * 税收违法
 */
export class DimensionHitDetailsTaxationOffences {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '发布日期' })
  IllegalTime: number;
  @ApiProperty({ type: String, description: '所属税务机关' })
  TaxGov: string;
  @ApiProperty({ type: String, description: '案件性质' })
  CaseNature: string;
  @ApiProperty({ type: String, description: '主要违法事实' })
  IllegalContent: string;
  @ApiProperty({ type: String, description: '法律依据及处理处罚情况' })
  PunishContent: number;
}

export class HitDetailsResponseTaxationOffences extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsTaxationOffences, isArray: true })
  Result: DimensionHitDetailsTaxationOffences[];
}

/**
 * 破产重整
 */
export class DimensionHitDetailsBankruptcy {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '案号' })
  CaseNo: string;
  @ApiProperty({ type: String, description: '破产类型' })
  CaseReasonType: string;
  @ApiProperty({ type: String, description: '被申请人' })
  respondentName: string;
  @ApiProperty({ type: String, description: '申请人' })
  applicant: string;
  @ApiProperty({ type: String, description: '经办法院' })
  Court: string;
  @ApiProperty({ type: String, description: '公开日期' })
  PublishDate: number;
}

export class HitDetailsResponseBankruptcy extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBankruptcy, isArray: true })
  Result: DimensionHitDetailsBankruptcy[];
}

/**
 * 股权冻结
 */
export class DimensionHitDetailsFreezeEquity {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '执行通知书文号' })
  CaseSearchId: string;
  @ApiProperty({ type: String, description: '被执行人' })
  pledgName: string;
  @ApiProperty({ type: String, description: '冻结股权标的企业' })
  pledgPledgor: string;
  @ApiProperty({ type: String, description: '股权数额' })
  AmountDesc: string;
  @ApiProperty({ type: String, description: '执行法院' })
  Court: string;
  @ApiProperty({ type: String, description: '类型' })
  ExecuteStatus: string;
  @ApiProperty({ type: String, description: '状态' })
  TypeDesc: string;
}

export class HitDetailsResponseFreezeEquity extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsFreezeEquity, isArray: true })
  Result: DimensionHitDetailsFreezeEquity[];
}

/**
 * 被执行人
 */
export class DimensionHitDetailsPersonExecution {
  @ApiProperty({ type: String, description: '案号' })
  CaseNo: string;
  @ApiProperty({ type: String, description: '被执行人证件号/组织机构代码' })
  Partycardnum: string;
  @ApiProperty({ type: String, description: '被执行人证件号/组织机构代码' })
  OrgNo: string;
  @ApiProperty({ type: String, description: '立案日期' })
  LiAnDate: number;
  @ApiProperty({ type: String, description: '执行法院' })
  ExecuteGov: string;
  @ApiProperty({ type: String, description: '执行标的(元)' })
  BiaoDi: number;
}

export class HitDetailsResponsePersonExecution extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsPersonExecution, isArray: true })
  Result: DimensionHitDetailsPersonExecution[];
}

/**
 * 司法拍卖
 */
export class DimensionHitDetailsJudicialAuction {
  @ApiProperty({ type: String, description: '详情Id' })
  id: string;
  @ApiProperty({ type: String, description: '标题' })
  biaoti: string;
  @ApiProperty({ type: String, description: '案号' })
  Caseno: string;
  @ApiProperty({ type: String, description: '起拍价(元)' })
  yiwu: number;
  @ApiProperty({ type: String, description: '评估价(元)' })
  EvaluationPrice: number;
  @ApiProperty({ type: String, description: '拍卖时间' })
  actionremark: string;
  @ApiProperty({ type: String, description: '处置单位' })
  executegov: string;
}

export class HitDetailsResponseJudicialAuction extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsJudicialAuction, isArray: true })
  Result: DimensionHitDetailsJudicialAuction[];
}

/**
 * 合同违约
 */
export class DimensionHitDetailsContractBreach {
  @ApiProperty({ type: String, description: '详情Id' })
  id: string;
  @ApiProperty({ type: String, description: '违约次数' })
  CaseCount: number;
  @ApiProperty({ type: String, description: '违约等级' })
  Revel: string;
  @ApiProperty({ type: String, description: '违约指数' })
  ScoreLog: number;
  @ApiProperty({ type: String, description: '违约次数：' })
  TotalNum: number;
  @ApiProperty({ type: String, description: '涉案金额' })
  TotalAmt: string;
}

export class HitDetailsResponseContractBreach extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsContractBreach, isArray: true })
  Result: DimensionHitDetailsContractBreach[];
}

/**
 * 买卖合同纠纷
 */
export class HitDetailsResponseSalesContractDispute extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsCriminalInvolve, isArray: true })
  Result: DimensionHitDetailsCriminalInvolve[];
}

export class DimensionHitDetailsLaborContractDispute {
  @ApiProperty({ type: String, description: '详情Id' })
  Id: string;
  @ApiProperty({ type: String, description: '案件名称' })
  CaseName: string;
  @ApiProperty({ type: String, description: '案件身份' })
  CaseRoleIdentity: string;
  @ApiProperty({ type: String, description: '案号，可能存在多个案号' })
  AnNoList: string[];
  @ApiProperty({ type: String, description: '案件金额' })
  Amt: string;
  @ApiProperty({ type: String, description: '最新案件进程(LastestDate+LatestTrialRound)' })
  LatestDateTrialRound: string;
  @ApiProperty({ type: String, description: '法院' })
  CourtList: string[];
}

/**
 * 劳动纠纷
 */
export class HitDetailsResponseLaborContractDispute extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsLaborContractDispute, isArray: true })
  Result: DimensionHitDetailsLaborContractDispute[];
}

/**
 * 终本案件
 */
export class DimensionHitDetailsEndExecutionCase {
  @ApiProperty({ type: String, description: '案号' })
  endExecutionCaseDetail: string;
  @ApiProperty({ type: String, description: '被执行人' })
  NameAndKeyNo: string;
  @ApiProperty({ type: String, description: '疑似申请执行人' })
  SqrInfo: string;
  @ApiProperty({ type: String, description: '未履行金额(元)' })
  FailureAct: number;
  @ApiProperty({ type: String, description: '执行标的(元)' })
  ExecuteObject: number;
  @ApiProperty({ type: String, description: '执行法院' })
  Court: string;
  @ApiProperty({ type: String, description: '立案日期' })
  judgedate: number;
  @ApiProperty({ type: String, description: '终本日期' })
  EndDate: number;
}

export class HitDetailsResponseSalesEndExecutionCase extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsEndExecutionCase, isArray: true })
  Result: DimensionHitDetailsEndExecutionCase[];
}
