//行政监管风险相关维度详情
import { ApiProperty } from '@nestjs/swagger';
import { HitDetailsBaseResponse } from './HitDetailsBaseResponse';

/**
 * 被列入严重违法失信企业名录
 */
export class DimensionHitDetailsCompanyCredit {
  @ApiProperty({ type: Number, description: '列入日期' })
  AddDate: number;
  @ApiProperty({ type: String, description: '作出决定机关(列入)' })
  AddOffice: string;
  @ApiProperty({ type: String, description: '列入严重违法失信企业名单原因' })
  AddReason: string;
}

export class HitDetailsResponseCompanyCredit extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsCompanyCredit, isArray: true })
  Result: DimensionHitDetailsCompanyCredit[];
}

/**
 * 被列入非正常户
 */
export class DimensionHitDetailsBusinessAbnormal4 {
  @ApiProperty({ type: Number, description: '列名' })
  label: number;
  @ApiProperty({ type: String, description: '列值' })
  value: string;
}

export class HitDetailsResponseBusinessAbnormal4 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBusinessAbnormal4, isArray: true })
  Result: DimensionHitDetailsBusinessAbnormal4[];
}

/**
 * 被列入经营异常名录
 */
export class DimensionHitDetailsBusinessAbnormal3 {
  @ApiProperty({ type: Number, description: '列入日期' })
  CurrenceDate: number;
  @ApiProperty({ type: String, description: '作出决定机关（列入）' })
  Court: string;
  @ApiProperty({ type: String, description: '列入经营异常名录原因' })
  CaseReason: string;
}

export class HitDetailsResponseBusinessAbnormal3 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBusinessAbnormal3, isArray: true })
  Result: DimensionHitDetailsBusinessAbnormal3[];
}

/**
 * 多次被列入经营异常名录【当下未列入】
 */
export class DimensionHitDetailsOperationAbnormal {
  @ApiProperty({ type: Number, description: '移出日期' })
  LianDate: number;
  @ApiProperty({ type: String, description: '作出决定机关(移出)' })
  ActionRemark: string;
  @ApiProperty({ type: String, description: '移出经营异常名录原因' })
  RemoveReason: string;
  @ApiProperty({ type: Number, description: '列入日期' })
  PublishDate: number;
  @ApiProperty({ type: String, description: '作出决定机关(列入)' })
  Court: string;
  @ApiProperty({ type: String, description: '列入经营异常名录原因' })
  CaseReason: string;
}

export class HitDetailsResponseOperationAbnormal extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsOperationAbnormal, isArray: true })
  Result: DimensionHitDetailsOperationAbnormal[];
}

/**
 * 欠税公告
 */
export class DimensionHitDetailsTaxArrearsNotice {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '欠税税种' })
  Title: string;
  @ApiProperty({ type: Number, description: '欠税余额(元)' })
  Amount: number;
  @ApiProperty({ type: Number, description: '当前新发生的欠税金额(元)' })
  NewAmount: number;
  @ApiProperty({ type: Number, description: '发布单位' })
  IssuedBy: number;
  @ApiProperty({ type: Number, description: '发布日期' })
  PublishDate: number;
}

export class HitDetailsResponseTaxArrearsNotice extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsTaxArrearsNotice, isArray: true })
  Result: DimensionHitDetailsTaxArrearsNotice[];
}

/**
 * 存在产品质量问题-未准入境
 */
export class DimensionHitDetailsProductQualityProblem6 {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '产品名称' })
  Title: string;
  @ApiProperty({ type: String, description: '产品类型' })
  CaseReasonType: string;
  @ApiProperty({ type: String, description: '数/重量' })
  AmountDesc: string;
  @ApiProperty({ type: String, description: '原因' })
  ActionRemark: string;
  @ApiProperty({ type: Number, description: '报送时间' })
  LianDate: number;
  @ApiProperty({ type: Number, description: '发布日期' })
  PublishDate: number;
  @ApiProperty({ type: String, description: '原文' })
  originalSource: string;
}

export class HitDetailsResponseProductQualityProblem6 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsProductQualityProblem6, isArray: true })
  Result: DimensionHitDetailsProductQualityProblem6[];
}

/**
 * 存在产品质量问题-产品抽查检查不合格
 */
export class DimensionHitDetailsProductQualityProblem2 {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '产品名称' })
  CaseReason: string;
  @ApiProperty({ type: String, description: '产品类别' })
  CaseReasonType: string;
  @ApiProperty({ type: String, description: '规格型号' })
  OrgNo: string;
  @ApiProperty({ type: String, description: '生产单位' })
  name: string;
  @ApiProperty({ type: String, description: '主要不合格项目' })
  ActionRemark: string;
  @ApiProperty({ type: Number, description: '抽查/公告时间' })
  PublishDate: number;
}

export class HitDetailsResponseProductQualityProblem2 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsProductQualityProblem2, isArray: true })
  Result: DimensionHitDetailsProductQualityProblem2[];
}

/**
 * 存在产品质量问题-产品召回
 */
export class DimensionHitDetailsProductQualityProblem1 {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '召回产品' })
  Title: string;
  @ApiProperty({ type: String, description: '召回企业' })
  NameAndKeyNo: string;
  @ApiProperty({ type: Number, description: '发布时间' })
  PublishDate: number;
}

export class HitDetailsResponseProductQualityProblem1 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsProductQualityProblem1, isArray: true })
  Result: DimensionHitDetailsProductQualityProblem1[];
}

/**
 * 存在产品质量问题-药品抽查【检验不合格】
 */
export class DimensionHitDetailsProductQualityProblem7 {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '药品品名' })
  Specs: string;
  @ApiProperty({ type: String, description: '检查实施机关' })
  Court: string;
  @ApiProperty({ type: String, description: '类型' })
  CaseReasonType: string;
  @ApiProperty({ type: String, description: '检测结果' })
  ActionRemark: string;
}

export class HitDetailsResponseProductQualityProblem7 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsProductQualityProblem7, isArray: true })
  Result: DimensionHitDetailsProductQualityProblem7[];
}

/**
 * 存在产品质量问题-食品安全【检查不合格】
 */
export class DimensionHitDetailsProductQualityProblem9 {
  @ApiProperty({ type: String, description: '食品名称' })
  Title: string;
  @ApiProperty({ type: String, description: '抽检次数' })
  Amount2: string;
  @ApiProperty({ type: String, description: '被抽检企业' })
  NameAndKeyNo: string;
  @ApiProperty({ type: String, description: '标称生产企业' })
  applicant: string;
  @ApiProperty({ type: String, description: '标称生产企业地址' })
  Address: string;
  @ApiProperty({ type: String, description: '商标' })
  ActionRemark: string;
  @ApiProperty({ type: String, description: '规格型号' })
  Specs: string;
  @ApiProperty({ type: Number, description: '生产日期/批号' })
  lianDate: number;
  @ApiProperty({ type: String, description: '抽检结果' })
  CheckResult: string;
}

export class HitDetailsResponseProductQualityProblem9 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsProductQualityProblem9, isArray: true })
  Result: DimensionHitDetailsProductQualityProblem9[];
}

/**
 * 抽查检查-不合格
 */
export class DimensionHitDetailsSpotCheck {
  @ApiProperty({ type: String, description: '检查实施机关' })
  court: string;
  @ApiProperty({ type: String, description: '类型' })
  casereasontype: string;
  @ApiProperty({ type: Number, description: '日期' })
  publishdate: number;
  @ApiProperty({ type: String, description: '结果' })
  SpotCheck: string;
}

export class HitDetailsResponseSpotCheck extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsSpotCheck, isArray: true })
  Result: DimensionHitDetailsSpotCheck[];
}

/**
 * 行政处罚2,3
 */
export class DimensionHitDetailsAdministrativePenalties2 {
  @ApiProperty({ type: String, description: '详情id' })
  id: string;
  @ApiProperty({ type: String, description: '决定文书号' })
  caseno: string;
  @ApiProperty({ type: String, description: '违法事实' })
  casereason: string;
  @ApiProperty({ type: String, description: '处罚结果' })
  title: string;
  @ApiProperty({ type: String, description: '处罚金额(元)' })
  amount: string;
  @ApiProperty({ type: String, description: '处罚单位' })
  court: string;
  @ApiProperty({ type: Number, description: '处罚日期' })
  publishdate: number;
  @ApiProperty({ type: String, description: '原文' })
  originalSource: string;
}

/**
 * 行政处罚（涉及商业贿赂、垄断行为或政府采购活动违法行为）
 */
export class HitDetailsResponseAdministrativePenalties2 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsAdministrativePenalties2, isArray: true })
  Result: DimensionHitDetailsAdministrativePenalties2[];
}

/**
 * 行政处罚，环抱处罚
 */
export class DimensionHitDetailsAdministrativePenalties {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '决定文书号' })
  CaseNo: string;
  @ApiProperty({ type: String, description: '违法事实' })
  CaseReason: string;
  @ApiProperty({ type: String, description: '处罚结果' })
  Title: string;
  @ApiProperty({ type: String, description: '处罚金额(元)' })
  Amount: string;
  @ApiProperty({ type: String, description: '处罚单位' })
  Court: string;
  @ApiProperty({ type: Number, description: '处罚日期' })
  PunishDate: number;
  @ApiProperty({ type: String, description: '原文' })
  originalSource: string;
}

export class HitDetailsResponseAdministrativePenalties extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsAdministrativePenalties, isArray: true })
  Result: DimensionHitDetailsAdministrativePenalties[];
}

/**
 * 税务催缴公告
 */
export class DimensionHitDetailsTaxCallNotice {
  @ApiProperty({ type: String, description: '详情id' })
  id: string;
  @ApiProperty({ type: String, description: '标题' })
  title: string;
  @ApiProperty({ type: String, description: '发布机构' })
  courtname: string;
  @ApiProperty({ type: Number, description: '发布日期' })
  publicdate: number;
  @ApiProperty({ type: String, description: '公告类型' })
  noticetype: string;
}

export class HitDetailsResponseTaxCallNotice extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsTaxCallNotice, isArray: true })
  Result: DimensionHitDetailsTaxCallNotice[];
}

/**
 * 公安通告
 */
export class DimensionHitDetailsSecurityNotice {
  @ApiProperty({ type: String, description: '详情id' })
  id: string;
  @ApiProperty({ type: String, description: '标题' })
  title: string;
  @ApiProperty({ type: String, description: '发布机构' })
  courtname: string;
  @ApiProperty({ type: Number, description: '发布日期' })
  publicdate: number;
  @ApiProperty({ type: String, description: '公告类型' })
  noticetype: string;
}

export class HitDetailsResponseSecurityNotice extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsSecurityNotice, isArray: true })
  Result: DimensionHitDetailsSecurityNotice[];
}

/**
 * 监管处罚
 */
export class DimensionHitDetailsRegulateFinance {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '决定书文号' })
  caseno: string;
  @ApiProperty({ type: String, description: '违规事实' })
  PunishReason: string;
  @ApiProperty({ type: String, description: '处理结果' })
  PunishResult: string;
  @ApiProperty({ type: String, description: '处理单位' })
  PunishOffice: string;
  @ApiProperty({ type: Number, description: '处理日期' })
  PunishDate: number;
}

export class HitDetailsResponseRegulateFinance extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsRegulateFinance, isArray: true })
  Result: DimensionHitDetailsRegulateFinance[];
}
