// 基础资质自维度相关详情
import { ApiProperty } from '@nestjs/swagger';
import { DimensionHitDetailsDescription, HitDetailsBaseResponse } from './HitDetailsBaseResponse';

/**
 * 注销备案
 */
export class DimensionHitDetailsCancellationOfFiling {
  @ApiProperty({ type: String, description: '注销原因-Detail.LiqBAInfo.CancelReason' })
  CancelReason: string;
  @ApiProperty({ type: String, description: '公告内容-Detail.CreditorNoticeInfo.NoticeContent' })
  NoticeContent: string;
  @ApiProperty({ type: String, description: '公告期-Detail.CreditorNoticeInfo.NoticeDate' })
  NoticeDate: string;
  @ApiProperty({ type: String, description: '债权申报联系人-Detail.CreditorNoticeInfo.ClaimsDeclarationMember' })
  ClaimsDeclarationMember: string;
  @ApiProperty({ type: String, description: '债权申报联系电话-Detail.CreditorNoticeInfo.ClaimsDeclarationTelNo' })
  ClaimsDeclarationTelNo: string;
  @ApiProperty({ type: String, description: '债权申报地址-Detail.CreditorNoticeInfo.ClaimsDeclarationAddress' })
  ClaimsDeclarationAddress: string;
  @ApiProperty({ type: String, description: '登记机关-Detail.LiqBAInfo.BelongOrg' })
  BelongOrg: string;
}

export class HitDetailsResponseCancellationOfFiling extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsCancellationOfFiling, isArray: true })
  Result: DimensionHitDetailsCancellationOfFiling[];
}

/**
 * 简易注销
 */
export class DimensionHitDetailsBusinessAbnormal2 {
  @ApiProperty({ type: String, description: '主键id' })
  id: string;
  @ApiProperty({ type: String, description: '公告名称' })
  annoName: string;
  @ApiProperty({ type: String, description: '公告开始日期-结束日期' })
  publishDate: string;
  @ApiProperty({ type: String, description: '简易注销结果' })
  resultContent: string;
}

export class HitDetailsResponseBusinessAbnormal2 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBusinessAbnormal2, isArray: true })
  Result: DimensionHitDetailsBusinessAbnormal2[];
}

/**
 * 经营状态非存续
 */
export class DimensionHitDetailsBusinessAbnormal1 {
  @ApiProperty({ type: String, description: '登记状态' })
  label: string;
  @ApiProperty({ type: String, description: '经营状态' })
  value: string;
  @ApiProperty({ type: String, description: '经营状态' })
  ShortStatus: string;
}

export class HitDetailsResponseBusinessAbnormal1 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBusinessAbnormal1, isArray: true })
  Result: DimensionHitDetailsBusinessAbnormal1[];
}

export class HitDetailsResponseWithDescription extends HitDetailsBaseResponse {
  @ApiProperty({ description: '详情描述' })
  description: string;
}

/**
 * 疑似停业歇业停产或被吊销证照
 */
export class DimensionHitDetailsBusinessAbnormal5 {
  @ApiProperty({ type: String, description: '决定书文号' })
  CaseNo: string;
  @ApiProperty({ type: String, description: '违法事实' })
  CaseReason: string;
  @ApiProperty({ type: String, description: '处罚结果' })
  Title: string;
  @ApiProperty({ type: String, description: '处罚单位' })
  Court: string;
  @ApiProperty({ type: Number, description: '出发日期' })
  PunishDate: number;
}

export class HitDetailsResponseBusinessAbnormal5 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBusinessAbnormal5, isArray: true })
  Result: DimensionHitDetailsBusinessAbnormal5[];
}

/**
 * 无统一社会信用代码
 */
export class HitDetailsResponseBusinessAbnormal7 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsDescription, isArray: true })
  Result: DimensionHitDetailsDescription[];
}

/**
 * 假冒国企
 */
export class HitDetailsResponseFakeSOES extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsDescription, isArray: true })
  Result: DimensionHitDetailsDescription[];
}

/**
 * 久无实缴
 */
export class DimensionHitDetailsNoCapital {
  @ApiProperty({ type: Number, description: '列名' })
  label: number;
  @ApiProperty({ type: String, description: '列值' })
  value: string;
}

export class HitDetailsResponseNoCapital extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsNoCapital, isArray: true })
  Result: DimensionHitDetailsNoCapital[];
}

/**
 * 注册资本过低
 */
export class DimensionHitDetailsLowCapital extends DimensionHitDetailsNoCapital {
  @ApiProperty({ type: String, description: '注册资本' })
  RegistCapi: string;
}

export class HitDetailsResponseLowCapital extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsLowCapital, isArray: true })
  Result: DimensionHitDetailsLowCapital[];
}

/**
 * 疑似空壳企业
 */
export class DimensionHitDetailsCompanyShell extends DimensionHitDetailsDescription {
  @ApiProperty({ type: String, description: '详情id' })
  detailId: string;
  @ApiProperty({ type: String, description: '是否有详情' })
  hasDetail: string;
  @ApiProperty({ type: String, description: '标题' })
  title: string;
}

export class HitDetailsResponseCompanyShell extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsCompanyShell, isArray: true })
  Result: DimensionHitDetailsCompanyShell[];
}

/**
 * 经营期限已过有效期
 */
export class DimensionHitDetailsBusinessAbnormal6 {
  @ApiProperty({ type: Number, description: '列名' })
  label: number;
  @ApiProperty({ type: String, description: '列值' })
  value: string;
  @ApiProperty({ type: Number, description: '起始时间' })
  TermStart: number;
  @ApiProperty({ type: String, description: '结束时间' })
  TeamEnd: number;
}

export class HitDetailsResponseBusinessAbnormal6 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBusinessAbnormal6, isArray: true })
  Result: DimensionHitDetailsBusinessAbnormal6[];
}

export class DimensionHitDetailsFinancialHealth {
  @ApiProperty({ type: Number, description: '列名' })
  label: number;
  @ApiProperty({ type: String, description: '列值' })
  value: string;
}

export class HitDetailsResponseFinancialHealth extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsFinancialHealth, isArray: true })
  Result: DimensionHitDetailsFinancialHealth[];
}

/**
 * 临近经营期限
 */
export class HitDetailsResponseBusinessAbnormal8 extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBusinessAbnormal6, isArray: true })
  Result: DimensionHitDetailsBusinessAbnormal6[];
}

/**
 * 涉诈高风险名单
 */
export class HitDetailsResponseFraudList extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsDescription, isArray: true })
  Result: DimensionHitDetailsDescription[];
}

/**
 * 新成立企业
 */
export class DimensionHitDetailsEstablishedTime extends DimensionHitDetailsNoCapital {
  @ApiProperty({ type: Number, description: '成立日期' })
  StartDate: number;
}

export class HitDetailsResponseEstablishedTime extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsEstablishedTime, isArray: true })
  Result: DimensionHitDetailsEstablishedTime[];
}

/**
 * 无有效资质证书
 */
export class HitDetailsResponseNoCertification extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsDescription, isArray: true })
  Result: DimensionHitDetailsDescription[];
}

export class CertificationDetailResponse {
  @ApiProperty({ type: Number, description: '' })
  index: number;

  @ApiProperty({ type: String, description: '资质名称' })
  name: string;

  @ApiProperty({ type: Date, description: '开始时间' })
  startDate?: Date;

  @ApiProperty({ type: Date, description: '结束时间' })
  endDate?: Date;

  @ApiProperty({ type: String, description: '状态描述' })
  expirationDesc: string;
}

/**
 * 资质筛查
 */
export class HitDetailsResponseCertification extends HitDetailsBaseResponse {
  @ApiProperty({ type: CertificationDetailResponse, isArray: true })
  Result: CertificationDetailResponse[];
}
