//经营稳定性风险相关维度详情
import { ApiProperty } from '@nestjs/swagger';
import { DimensionHitDetailsDescription, HitDetailsBaseResponse } from './HitDetailsBaseResponse';

/**
 * 债券违约
 */
export class DimensionHitDetailsBondDefaults {
  @ApiProperty({ type: String, description: '债券简称' })
  BondShortName: string;
  @ApiProperty({ type: String, description: '债券类型' })
  BondTypeName: string;
  @ApiProperty({ type: String, description: '违约状态' })
  DefaultStatusDesc: string;
  @ApiProperty({ type: String, description: '首次违约日期' })
  FirstDefaultDate: string;
  @ApiProperty({ type: String, description: '累计违约本金(亿元)' })
  AccuOverdueCapital: string;
  @ApiProperty({ type: String, description: '累计违约利息(亿元)' })
  AccuOverdueInterest: string;
  @ApiProperty({ type: String, description: '到期日期' })
  MaturityDate: string;
}

export class HitDetailsResponseBondDefaults extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBondDefaults, isArray: true })
  Result: DimensionHitDetailsBondDefaults[];
}

/**
 * 票据违约
 */
export class DimensionHitDetailsBillDefaults {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '承兑人' })
  companyNamePascal: string;
  @ApiProperty({ type: String, description: '承兑人开户行' })
  BankName: string;
  @ApiProperty({ type: String, description: '截止日期' })
  EndDate: string;
  @ApiProperty({ type: String, description: '披露日期' })
  PublishDate: string;
  @ApiProperty({ type: String, description: '逾期金额(元)' })
  OverdueBalance: string;
}

export class HitDetailsResponseBillDefaults extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBillDefaults, isArray: true })
  Result: DimensionHitDetailsBillDefaults[];
}

/**
 * 动产抵押
 */
export class DimensionHitDetailsChattelMortgage {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '承兑人' })
  companyNamePascal: string;
  @ApiProperty({ type: String, description: '承兑人开户行' })
  BankName: string;
  @ApiProperty({ type: Number, description: '截止日期' })
  EndDate: number;
  @ApiProperty({ type: Number, description: '披露日期' })
  PublishDate: number;
  @ApiProperty({ type: Number, description: '逾期金额(元)' })
  OverdueBalance: number;
}

export class HitDetailsResponseChattelMortgage extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsChattelMortgage, isArray: true })
  Result: DimensionHitDetailsChattelMortgage[];
}

/**
 * 股权出质
 */
export class DimensionHitDetailsEquityPledge {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '登记编号' })
  RegistNo: string;
  @ApiProperty({ type: String, description: '出质人' })
  pledgorInfo: string;
  @ApiProperty({ type: String, description: '出质股权标的企业' })
  relatedCompanyInfo: string;
  @ApiProperty({ type: String, description: '质权人' })
  pledgeeInfo: string;
  @ApiProperty({ type: String, description: '出质股权数额' })
  PledgedAmount: string;
  @ApiProperty({ type: Number, description: '登记日期' })
  RegDate: number;
  @ApiProperty({ type: Number, description: '状态' })
  Status: number;
}

export class HitDetailsResponseEquityPledge extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsEquityPledge, isArray: true })
  Result: DimensionHitDetailsEquityPledge[];
}

/**
 * 土地抵押
 */
export class DimensionHitDetailsLandMortgage {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '登记编号' })
  RegistNo: string;
  @ApiProperty({ type: String, description: '出质人' })
  pledgorInfo: string;
  @ApiProperty({ type: String, description: '出质股权标的企业' })
  relatedCompanyInfo: string;
  @ApiProperty({ type: String, description: '质权人' })
  pledgeeInfo: string;
  @ApiProperty({ type: String, description: '出质股权数额' })
  PledgedAmount: string;
  @ApiProperty({ type: Number, description: '登记日期' })
  RegDate: number;
  @ApiProperty({ type: Number, description: '状态' })
  Status: number;
}

export class HitDetailsResponseLandMortgage extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsLandMortgage, isArray: true })
  Result: DimensionHitDetailsLandMortgage[];
}

/**
 * 担保风险
 */
export class DimensionHitDetailsGuaranteeRisk {
  @ApiProperty({ type: String, description: '详情id' })
  Id: string;
  @ApiProperty({ type: String, description: '保证类型' })
  GuaranteeType: string;
  @ApiProperty({ type: String, description: '被担保方' })
  VoucheeInfo: string;
  @ApiProperty({ type: String, description: '担保方' })
  GuaranteeInfo: string;
  @ApiProperty({ type: String, description: '债权人' })
  Creditor: string;
  @ApiProperty({ type: Number, description: '被保证债权本金(万元)' })
  GuaranteeMoney: number;
  @ApiProperty({ type: Number, description: '裁判日期' })
  Judgedate: number;
  @ApiProperty({ type: Number, description: '发布日期' })
  PublicDate: number;
}

export class HitDetailsResponseGuaranteeRisk extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsGuaranteeRisk, isArray: true })
  Result: DimensionHitDetailsGuaranteeRisk[];
}

/**
 * 无中标项目
 */
export class HitDetailsResponseNoTender extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsDescription, isArray: true })
  Result: DimensionHitDetailsDescription[];
}

/**
 * 经营范围变更
 */
export class DimensionHitDetailsMainInfoUpdateScope {
  @ApiProperty({ type: Number, description: '变更日期' })
  ChangeDate: number;
  @ApiProperty({ type: String, description: '变更前' })
  beforeScope: string;
  @ApiProperty({ type: String, description: '变更后' })
  afterScope: string;
}

export class HitDetailsResponseMainInfoUpdateScope extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsMainInfoUpdateScope, isArray: true })
  Result: DimensionHitDetailsMainInfoUpdateScope[];
}

/**
 * 经营范围变更
 */
export class DimensionHitDetailsMainInfoUpdateAddress {
  @ApiProperty({ type: Number, description: '变更日期' })
  ChangeDate: number;
  @ApiProperty({ type: String, description: '变更前' })
  beforeAddress: string;
  @ApiProperty({ type: String, description: '变更后' })
  afterAddress: string;
}

export class HitDetailsResponseMainInfoUpdateAddress extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsMainInfoUpdateAddress, isArray: true })
  Result: DimensionHitDetailsMainInfoUpdateAddress[];
}

/**
 * 企业名称变更
 */
export class DimensionHitDetailsMainInfoUpdateName {
  @ApiProperty({ type: Number, description: '变更日期' })
  ChangeDate: number;
  @ApiProperty({ type: String, description: '变更前' })
  CompanyName: string;
  @ApiProperty({ type: String, description: '变更后' })
  after: any;
}

export class HitDetailsResponseMainInfoUpdateName extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsMainInfoUpdateName, isArray: true })
  Result: DimensionHitDetailsMainInfoUpdateName[];
}

/**
 * 法定代表人变更
 */
export class DimensionHitDetailsMainInfoUpdateLegalPerson {
  @ApiProperty({ type: Number, description: '变更日期' })
  ChangeDate: number;
  @ApiProperty({ type: String, description: '变更前' })
  CompanyName: string;
  @ApiProperty({ type: String, description: '变更后' })
  after: any;
}

export class HitDetailsResponseMainInfoUpdateLegalPerson extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsMainInfoUpdateLegalPerson, isArray: true })
  Result: DimensionHitDetailsMainInfoUpdateLegalPerson[];
}

/**
 * 大股东变更
 */
export class DimensionHitDetailsMainInfoUpdateHolder {
  @ApiProperty({ type: Number, description: '变更日期' })
  ChangeDate: number;
  @ApiProperty({ type: String, description: '变更前' })
  BeforeContent: any;
  @ApiProperty({ type: String, description: '变更后' })
  AfterContent: any;
}

export class HitDetailsResponseMainInfoUpdateHolder extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsMainInfoUpdateHolder, isArray: true })
  Result: DimensionHitDetailsMainInfoUpdateHolder[];
}

/**
 * 实际控制人变更
 */
export class DimensionHitDetailsMainInfoUpdatePerson {
  @ApiProperty({ type: Number, description: '变更日期' })
  ChangeDate: number;
  @ApiProperty({ type: String, description: '变更前' })
  BeforeContent: any;
  @ApiProperty({ type: String, description: '变更后' })
  AfterContent: any;
}

export class HitDetailsResponseMainInfoUpdatePerson extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsMainInfoUpdatePerson, isArray: true })
  Result: DimensionHitDetailsMainInfoUpdatePerson[];
}

/**
 * 受益所有人变更
 */
export class DimensionHitDetailsMainInfoUpdateBeneficiary {
  @ApiProperty({ type: Number, description: '变更日期' })
  ChangeDate: number;
  @ApiProperty({ type: String, description: '变更前' })
  BeforeChange: any;
  @ApiProperty({ type: String, description: '变更后' })
  AfterChange: any;
}

export class HitDetailsResponseMainInfoUpdateBeneficiary extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsMainInfoUpdateBeneficiary, isArray: true })
  Result: DimensionHitDetailsMainInfoUpdateBeneficiary[];
}

/**
 * 清算信息
 */
export class DimensionHitDetailsLiquidation {
  @ApiProperty({ type: Number, description: '清算组负责人' })
  Leader: number;
  @ApiProperty({ type: String, description: '清算组成员' })
  Member: any;
}

export class HitDetailsResponseLiquidation extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsLiquidation, isArray: true })
  Result: DimensionHitDetailsLiquidation[];
}
