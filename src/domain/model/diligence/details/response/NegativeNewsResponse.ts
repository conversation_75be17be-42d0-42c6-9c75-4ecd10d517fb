//负面新闻相关维度详情
import { ApiProperty } from '@nestjs/swagger';
import { HitDetailsBaseResponse } from './HitDetailsBaseResponse';

/**
 * 负面新闻
 */
export class DimensionHitDetailsNegativeNews {
  @ApiProperty({ type: String, description: '标题' })
  title: string;
  @ApiProperty({ type: String, description: 'tag' })
  tagsnew: string[];
  @ApiProperty({ type: String, description: '标签信息' })
  codedesc: string[];
  @ApiProperty({ type: String, description: '关联企业' })
  companyname: string;
  @ApiProperty({ type: String, description: '发布时间' })
  publishtime: string;
  @ApiProperty({ type: String, description: '来源' })
  source: string;
}

export class HitDetailsResponseNegativeNews extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsNegativeNews, isArray: true })
  Result: DimensionHitDetailsNegativeNews[];
}
