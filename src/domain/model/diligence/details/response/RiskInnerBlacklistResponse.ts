//内部黑名单相关维度详情
import { ApiProperty } from '@nestjs/swagger';
import { HitDetailsBaseResponse } from './HitDetailsBaseResponse';
import { DurationEnums } from '@domain/enums/blacklist/DurationEnums';
import { BlacklistDirectConnectionPO } from '../../graph/BlacklistDirectConnectionPO';
import { BlacklistPersonConnectionPO } from '../../graph/BlacklistPersonConnectionPO';
import { BlacklistInvestConnectionPO } from '../../graph/BlacklistInvestConnectionPO';

/**
 * 命中内部黑名单
 */
export class DimensionHitDetailsHitInnerBlackList {
  @ApiProperty({ description: '尽调的公司的名字' })
  companyNameDD: string;

  @ApiProperty({ description: '尽调的公司的keyno' })
  companyKeynoDD: string;

  @ApiProperty({ description: '关联到的公司的名字(客商列表中/黑名单中的公司)' })
  companyNameRelated: string;

  @ApiProperty({ description: '关联到的公司的keyno(客商列表中/黑名单中的公司)' })
  companyKeynoRelated: string;
  @ApiProperty({ description: '加入黑名单的时间' })
  joinDate: Date;

  @ApiProperty({ description: '加入黑名单的原因' })
  reason: string;

  @ApiProperty({ description: '黑名单的ID' })
  blacklistId: number;
  @ApiProperty({ enum: DurationEnums, description: '有效期' })
  duration: DurationEnums;
}

export class HitDetailsResponseHitInnerBlackList extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsHitInnerBlackList, isArray: true })
  Result: DimensionHitDetailsHitInnerBlackList[];
}

/**
 * 与内部黑名单企业存在关联关系
 */
export class DimensionHitDetailsBlacklistPartnerInvestigation {
  @ApiProperty({ type: String, description: '关联企业名称' })
  companyNameRelated: string;
  @ApiProperty({ type: String, description: '关联企业名称id' })
  companyKeynoRelated: string;
  @ApiProperty({ description: '关联信息' })
  relationTypes: BlacklistDirectConnectionPO[] | BlacklistPersonConnectionPO[] | BlacklistInvestConnectionPO[];
}

export class HitDetailsResponseBlacklistPartnerInvestigation extends HitDetailsBaseResponse {
  @ApiProperty({ type: DimensionHitDetailsBlacklistPartnerInvestigation, isArray: true })
  Result: DimensionHitDetailsBlacklistPartnerInvestigation[];
}
