//潜在利益冲突相关维度
import { ApiProperty } from '@nestjs/swagger';
import { HitDetailsBaseResponse } from './HitDetailsBaseResponse';
import { RelatedPersonResponse } from '../../req&res/RelatedPersonResponse';

/**
 * 潜在利益冲突-股权/任职关联
 */
export class HitDetailsResponseStaffWorkingOutsideForeignInvestment extends HitDetailsBaseResponse {
  @ApiProperty({ type: RelatedPersonResponse, isArray: true })
  Result: RelatedPersonResponse[];
}
