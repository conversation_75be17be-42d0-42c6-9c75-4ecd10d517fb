export class Path {
  KeyNo: string;
  Name: string;
  Percent: string;
  PercentTotal: string;
  Level: string;
  PathCount: number;
  Org: number;
  HasImage: boolean;
  Paths: Path[];
  IsAddPath: boolean;
  DataType?: number;
}

export class BenefitTypeInfo {
  Type: string;
  TypeDesc: string;
  JobType?: string;
  Job?: string;
}

export class Benefit {
  KeyNo: string;
  Name: string;
  Percent: string;
  PercentTotal: string;
  Level: string;
  PathCount: number;
  Org: number;
  HasImage: boolean;
  Paths: Path[][];
  IsBenefit: number;
  IsAddPath: boolean;
  SameBenefiterCoyCount: number;
  PercentTotalValue: number;
  RelatedCount: number;
  ImageUrl: string;
  BenefitTypeInfo: BenefitTypeInfo[];
  BenefitTypeFirst: number;
  JobType: number;
}
