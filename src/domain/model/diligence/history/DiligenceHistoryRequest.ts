import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { PaginationParams } from '../../common';

export class DiligenceHistoryRequest extends PaginationParams {
  @ApiProperty({ description: '公司名称关键字', type: String, required: true })
  @IsNotEmpty()
  @Type(() => String)
  @IsOptional()
  searchKey: string;

  @ApiPropertyOptional({
    description: '创建时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  createDate?: DateRangeRelative[];

  @ApiProperty({ description: '0 通过\n1 风险较高\n2 慎重考虑' })
  result?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '排查记录的ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  ids?: number[];

  @ApiPropertyOptional({ description: 'all-全部记录，bundle-消费记录，默认查询全部记录' })
  mode?: string = 'all';

  @ApiProperty({ required: false, description: '排序字段，默认为空' })
  @IsOptional()
  sortField?: string;

  @ApiProperty({ required: false, default: false, description: '是否升序（默认false，即默认倒序）' })
  isSortAsc?: boolean = false;

  @ApiProperty({ description: '操作人用户id' })
  operators?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '排查记录的depIds' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  depIds?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '排查记录的模型ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  modelIds?: number[];
}
