import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsIn, IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationParams } from '../../common';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';

export class DiligenceBundleSearchRequest extends PaginationParams {
  @ApiProperty({ required: false, description: '排序字段，默认为空' })
  @IsOptional()
  sortField?: string;

  @ApiProperty({ required: false, default: false, description: '是否升序（默认false，即默认倒序）' })
  isSortAsc?: boolean = false;

  @ApiProperty({ description: '操作人用户id' })
  operators?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '排查记录的depIds' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  depIds?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '排查记录的模型ids' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  modelIds?: number[];

  @ApiProperty({ required: true, description: '产品编号' })
  @IsIn(Object.values(ProductCodeEnums))
  product: string;
}
