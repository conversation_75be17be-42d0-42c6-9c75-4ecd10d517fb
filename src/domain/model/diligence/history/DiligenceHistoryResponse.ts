import { ApiProperty } from '@nestjs/swagger';
import { ChildDimensionHit } from '../../batch/request/ChildDimensionHit';
import { UserEntity } from '@domain/entities/UserEntity';
import { PaginationResponse } from '../../common';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';

export class DiligenceHistoryResponse extends PaginationResponse {
  @ApiProperty({ type: DiligenceHistoryEntity, isArray: true })
  data: DiligenceHistoryEntity[];

  @ApiProperty({ type: ChildDimensionHit, isArray: true, description: '公司命中子维度数量（统计的公司数）' })
  childDimensionHits?: ChildDimensionHit[];

  @ApiProperty({ type: UserEntity, description: '操作人' })
  editor?: UserEntity;

  @ApiProperty({ type: Date, description: '操作时间' })
  createDate?: Date | null;
}
