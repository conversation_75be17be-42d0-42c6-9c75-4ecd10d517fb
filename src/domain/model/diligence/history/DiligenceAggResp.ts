import { ApiProperty } from '@nestjs/swagger';
import { DiligenceRiskLevelAggItem, DiligenceRiskModelAgg } from '@modules/company-management/company/model/DiligenceRiskModelAgg';

export class DiligenceAggResponse {
  @ApiProperty({ type: DiligenceRiskModelAgg, isArray: true, description: '风控模型查询项(聚合)' })
  riskModelData: DiligenceRiskModelAgg[];
  @ApiProperty({ type: DiligenceRiskLevelAggItem, isArray: true, description: '风控等级查询项(聚合)' })
  riskLevelData: DiligenceRiskLevelAggItem[];
}
