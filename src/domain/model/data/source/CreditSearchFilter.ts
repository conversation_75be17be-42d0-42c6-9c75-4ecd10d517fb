import { ApiProperty } from '@nestjs/swagger';
import { ArrayNotEmpty, IsIn, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PaginationQueryParams } from '../../common';

export class DateRangeRelative {
  number: number;
  unit: string;
  currently: boolean;
  flag: number;
  min: number | string;
  max: number | string;
  year?: number;
}

export const CreditTypeQueryToEsMapping = {
  '01': { esCode: '100', desc: '破产重整' },
  '02': { esCode: '2', desc: '失信被执行人' },
  '03': { esCode: '3', desc: '限制高消费' },
  '04': { esCode: '4', desc: '限制出境' },
  '05': { esCode: '5', desc: '被执行人' },
  '06': { esCode: '6', desc: '终本案件' },
  '07': { esCode: '7', desc: '股权冻结' },
  '08': { esCode: '8', desc: '经营异常' },
  '09': { esCode: '9', desc: '行政处罚' },
  '0901': { esCode: 'A001', desc: '警告' },
  '0902': { esCode: 'A002', desc: '通报批评' },
  '0903': { esCode: 'A003', desc: '罚款' },
  '0904': { esCode: 'A004', desc: '没收违法所得' },
  '0905': { esCode: 'A005', desc: '没收非法财物' },
  '0906': { esCode: 'A006', desc: '暂扣许可证件' },
  '0907': { esCode: 'A007', desc: '降低资质等级' },
  '0908': { esCode: 'A008', desc: '吊销许可证/执照' },
  '0909': { esCode: 'A009', desc: '限制开展生产经营活动' },
  '0910': { esCode: 'A010', desc: '责令停产停业' },
  '0911': { esCode: 'A011', desc: '责令关闭' },
  '0912': { esCode: 'A012', desc: '限制申请行政许可' },
  '0913': { esCode: 'A013', desc: '限制从业' },
  '0914': { esCode: 'A014', desc: '行政拘留' },
  '0915': { esCode: 'A015', desc: '移送司法机关' },
  '0916': { esCode: 'A016', desc: '不予处罚' },
  '0999': { esCode: 'A099', desc: '其他行政处罚' },
  '12': { esCode: '12', desc: '环保处罚' },
  '13': { esCode: '13', desc: '欠税公告' },
  '14': { esCode: '14', desc: '严重违法' },
  '15': { esCode: '15', desc: '黑名单' },
  '16': { esCode: '16', desc: '产品召回' },
  '17': { esCode: '17', desc: '食品安全' },
  // '18':{esCode:'18',desc:'违规处理'},
  '19': { esCode: '19', desc: '税收违法' },
  '20': { esCode: '201', desc: '抽查检查' },
  '21': { esCode: '21', desc: '双随机抽查' },
  '22': { esCode: '22', desc: '未准入境' },
  '23': { esCode: '231', desc: '简易注销' },
  '24': { esCode: '24', desc: '注销备案' },
  '25': { esCode: '25', desc: '软件违规' },
  '26': { esCode: '26', desc: '政府约谈' },
  '2701': { esCode: '2701', desc: '信用评价' },
  '27': { esCode: '27', desc: '税务评级' },
  '28': { esCode: '28', desc: '环保信用' },
  '29': { esCode: '29', desc: '其他评级' },
  '30': { esCode: '30', desc: '债券违约' },
};

export class NumberRange {
  @ApiProperty({
    description: '最小值',
    type: Number,
    isArray: false,
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  @Type(() => Number)
  min?: number;

  @ApiProperty({
    description: '最大值',
    type: Number,
    isArray: false,
    required: false,
  })
  @IsNotEmpty()
  @IsOptional()
  @Type(() => Number)
  max?: number;
}

export class CreditType {
  @ApiProperty({
    description: '信用大类code',
    type: String,
    isArray: false,
    required: true,
  })
  @IsOptional()
  @IsIn(Object.keys(CreditTypeQueryToEsMapping))
  @IsNotEmpty()
  category: string;

  @ApiProperty({
    description: '信用小类code',
    type: String,
    isArray: true,
    required: false,
  })
  @IsOptional()
  child?: string[];
}

export class CreditSearchFilter extends PaginationQueryParams {
  @ApiProperty({ required: true, description: '公司id' })
  keyNo: string;

  @ApiProperty({
    description: '发布时间',
    isArray: true,
    required: false,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @IsOptional()
  publishdate?: DateRangeRelative[];

  @ApiProperty({
    description: '发生时间',
    isArray: true,
    required: false,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @IsOptional()
  currencedate?: DateRangeRelative[];

  @ApiProperty({
    description: '生产日期',
    isArray: true,
    required: false,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @IsOptional()
  lianDate?: DateRangeRelative[];

  @ApiProperty({
    description: '信用类型',
    type: CreditType,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreditType)
  creditType?: CreditType[];

  @ApiProperty({
    description: '数据状态,0-历史，1-当前有效',
    isArray: false,
    type: String,
    required: false,
    enum: ['0', '1'],
  })
  @Type(() => String)
  @IsOptional()
  @IsIn(['0', '1'])
  isValid?: string;

  @ApiProperty({
    description: '金额',
    type: NumberRange,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => NumberRange)
  amount?: NumberRange[];
}
