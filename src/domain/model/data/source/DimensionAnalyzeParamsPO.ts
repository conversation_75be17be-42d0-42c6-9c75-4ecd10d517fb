import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsNumber } from 'class-validator';

export class DimensionAnalyzeParamsPO {
  isScanRisk?: boolean = false;

  keyNo: string;

  companyName?: string;

  orgId?: number;

  @ApiPropertyOptional({ description: '需要手动忽略掉的dimension id' })
  excludeDimensionIds?: string[];

  monitorGroupId?: number;

  lastIndustryCode?: string; // 末级国标行业code
}

export class ConditionValPO {
  @ApiProperty({ description: '键', required: false })
  key?: string;

  @ApiProperty({ description: '名称', required: false })
  keyName?: string;

  @ApiProperty({ description: '是否启用', required: false })
  @IsIn([0, 1])
  @IsNumber()
  status?: number = 1;

  //目前仅使用在持股/投资股权比例（含历史）的值
  @ApiProperty({ description: '设置项值', required: false })
  @IsNumber()
  value?: number;

  child?: ConditionValPO[];
}
