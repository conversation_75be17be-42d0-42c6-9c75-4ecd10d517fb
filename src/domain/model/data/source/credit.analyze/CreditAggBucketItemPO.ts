import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';

export class AggBucketItemPO {
  dimensionType: string;
  hitCount: number;
}

export class CreditAggBucketItemPO extends AggBucketItemPO {
  amount?: number;
  amount2?: number;
  level?: number;
}

export class JudgementAggBucketItemPO extends AggBucketItemPO {
  amount?: number;
}

export class RiskChangeAggBucketItemPO extends AggBucketItemPO {
  hitsInfo?: RiskChangeAggHitsInfo[];
}

export class RiskChangeAggHitsInfo {
  Id: string;
  ChangeExtend: JSON;
}
