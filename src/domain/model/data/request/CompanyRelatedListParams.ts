import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber } from 'class-validator';
import { PaginationParams } from '../../common';

export class CompanyRelatedListParams extends PaginationParams {
  // @ApiPropertyOptional({ required: true, description: '监控模型id' })
  // @IsNumber()
  // monitorModelId: number;

  @ApiPropertyOptional({ required: true, description: '主体公司keyno' })
  //@IsArray()
  @IsNotEmpty({ each: true })
  companyId: string;

  @ApiPropertyOptional({ description: '关联方名称关键词' })
  //@IsString()
  keyword?: string;

  @ApiPropertyOptional({ description: '排序参数' })
  sortField?: string;

  @ApiPropertyOptional({ description: '排序规则', enum: ['ASC', 'DESC'] })
  sortOrder?: 'ASC' | 'DESC';

  @ApiPropertyOptional({ description: '分组id' })
  @IsNumber()
  monitorGroupId: number;
}

export class CompanyRelatedMonitorAllRequest extends CompanyRelatedListParams {
  // @IsNumber()
  // @Type(() => Number)
  // @Min(1)
  // @ApiPropertyOptional({ description: '监控总数' })
  // count: number;
  key: string;
}
