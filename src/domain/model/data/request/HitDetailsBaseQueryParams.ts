import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';
import { PaginationParams } from '../../common';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';

export class DateDetailsQueryParams extends PaginationParams {
  @ApiProperty({ required: true, description: '公司id' })
  @IsString()
  @IsNotEmpty()
  keyNo: string;

  @ApiProperty({ required: true, description: '记录参数id' })
  @IsArray()
  @IsNotEmpty({ each: true })
  ids: string[];

  @ApiPropertyOptional({ description: '排序参数' })
  sortField?: string;

  @ApiPropertyOptional({ description: '排序规则', enum: ['ASC', 'DESC'] })
  sortOrder?: 'ASC' | 'DESC';
}

export class HitDetailsQueryParams extends DateDetailsQueryParams {
  @ApiProperty({ required: true, description: '维度key' })
  key: DimensionTypeEnums;
}
