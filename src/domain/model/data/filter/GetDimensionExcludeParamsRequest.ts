//import { PaginationParams } from 'libs/model/common';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { ApiProperty, IntersectionType } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { PaginationParams } from '../../common';

export class BaseDimensionExcludeParamsPO {
  @ApiProperty({ description: '公司ID' })
  companyId: string;
}

export class GetDimensionExcludeParamsRequest extends IntersectionType(PaginationParams, BaseDimensionExcludeParamsPO) {
  @ApiProperty({ description: '维度类型' })
  @IsOptional()
  dimensionKey?: DimensionTypeEnums;
}
