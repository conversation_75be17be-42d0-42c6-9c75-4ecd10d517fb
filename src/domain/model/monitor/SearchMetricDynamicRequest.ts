import { PaginationParams } from '../common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { Type } from 'class-transformer';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsIn, IsNumber, IsOptional, Max, Min, MinLength, ValidateNested } from 'class-validator';
import { MetricDynamicStatusAllowedStatus, MetricDynamicStatusEnums } from '@domain/enums/metric/MetricDynamicStatusEnums';

export class SearchMetricDynamicRequest extends PaginationParams {
  @ApiPropertyOptional({ description: '监控组id', isArray: true })
  @IsOptional()
  @ArrayMinSize(0)
  @ArrayMaxSize(20)
  @IsNumber({}, { each: true })
  @Type(() => Number)
  groupId?: number[];

  @ApiPropertyOptional({ description: '重点监控的监控组id' })
  @IsOptional()
  favoriteGroupId?: number;

  @ApiPropertyOptional({ type: Number, isArray: true, description: '风控等级集合' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  riskLevels?: number[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: 'metricsIds' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  metricsIds?: number[];

  @ApiProperty({
    description: 'uniqueHashkey 唯一的key',
    type: String,
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  uniqueHashkeys?: string[];

  @ApiPropertyOptional({
    description: '创建时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  createDate?: DateRangeRelative[];

  @ApiPropertyOptional({
    description: '数据状态, 0 待处理 1 已处理 2 已废弃, 默认展示 待处理 的数据',
    isArray: true,
    enum: MetricDynamicStatusAllowedStatus,
  })
  @IsOptional()
  @IsIn(MetricDynamicStatusAllowedStatus, { each: true })
  dataStatus?: MetricDynamicStatusEnums[];

  @ApiPropertyOptional({ type: Number, isArray: true, description: '动态类型: 1.监管类动态, 2.业务类动态, 3.监控监管类指标, 4.监控业务类指标' })
  @IsOptional()
  @IsArray()
  @IsIn([1, 2, 3, 4], { each: true })
  @Type(() => Number)
  metricTypes?: number[];

  @ApiPropertyOptional({ description: '是否包含关联方, 0 不包含 1 包含，2 只返回关联方 默认0' })
  @IsOptional()
  @IsIn([0, 1, 2])
  includeRelatedParty?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @ArrayMinSize(0)
  @ArrayMaxSize(20)
  companyIds?: string[];

  @ApiPropertyOptional({ required: false, default: false, description: '是否升序（默认false，即默认倒序）' })
  @IsOptional()
  isSortAsc?: boolean = false;

  @ApiPropertyOptional({
    required: false,
    description: '聚合的对象， 1 今日动态， 2 监控企业， 3 动态分布 , 4 搜索项聚合, 5 关联方图表',
  })
  @IsOptional()
  @ArrayMinSize(0)
  @ArrayMaxSize(3)
  @IsIn([1, 2, 3, 4, 5], { each: true })
  @Type(() => Number)
  aggsField?: number[];

  @ApiPropertyOptional({ description: '聚合TopN的动态' })
  @IsOptional()
  @Min(1)
  @Max(20)
  aggsTopN?: number;

  @ApiPropertyOptional({ description: '模型id' })
  @IsOptional()
  riskModelId?: number;

  @ApiPropertyOptional({ description: '模型ids' })
  @IsOptional()
  riskModelIds?: number[];

  @ApiPropertyOptional({ description: '所有授权的分组Id' })
  @IsOptional()
  authGroupIds?: number[];

  @ApiPropertyOptional({ description: '模型的分支编码' })
  @IsOptional()
  modelBranchCode?: string;

  @ApiProperty({
    description: '排序字段',
    type: String,
    isArray: false,
    required: false,
  })
  @IsOptional()
  sortField?: string;

  @ApiProperty({
    description: '排序类型 ASC or DESC',
    type: String,
    isArray: false,
    required: false,
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  @IsIn(['ASC', 'DESC'])
  sortOrder?: string;

  @ApiPropertyOptional({ description: '搜索关键字' })
  @MinLength(1)
  @IsOptional()
  keywords?: string;
}
