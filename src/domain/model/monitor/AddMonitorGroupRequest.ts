import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsIn, IsNotEmpty, IsOptional, IsString, MaxLength, <PERSON><PERSON>ength, ValidateNested } from 'class-validator';
import { PushRuleJsonPo } from '../push/PushRuleJsonPo';
import { Type } from 'class-transformer';
import { PushEnableEnum } from '@domain/enums/push/PushEnableEnum';
import { MonitorStatusEnums } from '@domain/enums/monitor/MonitorStatusEnums';

export class AddMonitorPushRule {
  @ApiPropertyOptional({ description: '推送的规则', type: PushRuleJsonPo })
  @Type(() => PushRuleJsonPo)
  @ValidateNested()
  ruleJson?: PushRuleJsonPo;
}

export class AddMonitorGroupRequest {
  @ApiProperty({ description: '分组名称' })
  @IsString()
  @MaxLength(20)
  @MinLength(3)
  groupName: string;

  @ApiPropertyOptional({ description: '监控模型id(如果不指定，默认不开启该分组的监控)' })
  @IsOptional()
  monitorModelId?: number;

  @ApiProperty({
    description: '分组状态： 0-暂停，1-启用, 2-禁用',
    type: Number,
    isArray: false,
    required: false,
    enum: MonitorStatusEnums,
  })
  @IsIn(Object.values(MonitorStatusEnums))
  @IsOptional()
  monitorStatus?: MonitorStatusEnums;

  @ApiProperty({
    description: '是否开启消息推送： 0-不开启，1-开启',
    type: Number,
    isArray: false,
    required: false,
    enum: PushEnableEnum,
  })
  @IsIn(Object.values(PushEnableEnum))
  @IsOptional()
  pushEnable?: PushEnableEnum = PushEnableEnum.Disable;

  @ApiPropertyOptional({ description: '推送规则添加', type: AddMonitorPushRule })
  @IsOptional()
  monitorPushRules?: AddMonitorPushRule[];

  @ApiPropertyOptional({ description: '查看权限，拥有该用户的userIds, -1 为全部可见', type: Number, isArray: true, required: true })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(100)
  @IsNotEmpty()
  userIds: number[];
}
