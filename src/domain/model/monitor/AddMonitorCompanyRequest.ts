import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  <PERSON>ength,
  <PERSON>,
  <PERSON><PERSON>ength,
  ValidateNested,
} from 'class-validator';

export class AddMonitorCompanyItemPO {
  @ApiProperty({ description: '公司ID' })
  @IsString()
  @MaxLength(32)
  @MinLength(32)
  companyId: string;

  @ApiProperty({ description: '公司名称' })
  @MaxLength(500)
  companyName: string;

  // @ApiPropertyOptional({ description: '主关联企业ID' })
  // @IsOptional()
  // relatedCompanyId?: string;
}

export class BatchAddCompanyByNameRequest {
  @ApiProperty({ description: '监控分组id' })
  @IsNumber()
  @Min(1)
  monitorGroupId: number;

  @ApiPropertyOptional({ description: '企业名称', type: Array, isArray: true })
  @IsNotEmpty()
  @IsArray()
  companyNames: string[];
}

export class AddMonitorCompanyRequest {
  @ApiProperty({ description: '监控分组id' })
  @IsNumber()
  @Min(1)
  monitorGroupId: number;

  @ApiProperty({ description: '需要添加的公司' })
  @IsNotEmpty()
  @ArrayMinSize(1)
  @ArrayMaxSize(100)
  @ValidateNested({ each: true })
  items: AddMonitorCompanyItemPO[];

  @ApiPropertyOptional({ description: '批次id' })
  @IsNumber()
  @IsOptional()
  batchId?: number;
}
