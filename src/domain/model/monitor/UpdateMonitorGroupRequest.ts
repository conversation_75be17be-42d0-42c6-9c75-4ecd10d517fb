import { ApiProperty, ApiPropertyOptional, IntersectionType, PartialType, PickType } from '@nestjs/swagger';
import { MonitorGroupEntity } from '@domain/entities/MonitorGroupEntity';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { AddMonitorPushRule } from './AddMonitorGroupRequest';
import { Type } from 'class-transformer';

export class UpdateMonitorPushRule extends AddMonitorPushRule {
  @ApiPropertyOptional({ description: '推送规则id', type: Number })
  @IsOptional()
  pushRuleId?: number;
}

export class UpdateMonitorGroupRequest extends IntersectionType(
  PickType(MonitorGroupEntity, ['monitorGroupId']),
  PartialType(PickType(MonitorGroupEntity, ['name', 'monitorModelId', 'comment', 'pushEnable', 'monitorStatus'])),
) {
  @ApiPropertyOptional({ description: '更新推送规则添加', type: UpdateMonitorPushRule })
  @IsOptional()
  monitorPushRules: UpdateMonitorPushRule[];

  @ApiPropertyOptional({ description: '查看权限，拥有该用户的userIds, -1 为全部可见', type: Number, isArray: true, required: true })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(100)
  @IsNotEmpty()
  userIds?: number[];

  @ApiProperty({ description: '页码，从1开始计数', type: Number, isArray: false, required: false })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  transferUserId?: number;
}

//export class EnableMonitorGroupRequest extends IntersectionType(PickType(MonitorGroupEntity, ['monitorGroupId', 'monitorStatus'])) {}
