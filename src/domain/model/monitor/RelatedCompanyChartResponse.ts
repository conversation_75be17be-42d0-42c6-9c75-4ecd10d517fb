import { ApiProperty } from '@nestjs/swagger';

export class RelatedCompanyMetricItemPO {
  @ApiProperty({ description: '指标ID' })
  metricsId?: string;

  @ApiProperty({ description: '指标名称' })
  metricsName?: string;

  @ApiProperty({ description: '指标命中count' })
  metricsCount?: number;

  @ApiProperty({ description: '风险等级' })
  riskLevel?: number;
  // @ApiProperty({ description: '指标的动态Id' })
  // metricDynamics: MonitorMetricDynamicEsDoc[];
}

export class RelatedCompanyItemPO {
  @ApiProperty({ description: '关联方企业ID' })
  companyId?: string;

  @ApiProperty({ description: '关联方企业名称' })
  companyName?: string;

  @ApiProperty({ description: '关联方企业的指标信息' })
  companyMetrics?: RelatedCompanyMetricItemPO[];
}

export class RelatedCompanyChartItemPO {
  @ApiProperty({ description: '关联方企业类型' })
  relatedType?: string;

  @ApiProperty({ description: '关联方企业类型名称' })
  relatedName?: string;

  @ApiProperty({ description: '关联方企业信息' })
  relatedCompanys?: RelatedCompanyItemPO[];
}

export class RelatedCompanyChartResponse {
  @ApiProperty({ description: '关联方企业信息' })
  relatedCompanyChartItems?: RelatedCompanyChartItemPO[];
}
