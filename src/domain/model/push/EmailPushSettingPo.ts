import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PushScopePo } from './PushScopePo';
import { PushTimePo } from './PushTimePo';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 邮件推送设置信息
 */
export class EmailPushSettingPo {
  @ApiProperty({
    description: '邮箱地址列表（最多绑定 10 个邮箱）',
    type: String,
    isArray: true,
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(10)
  @IsOptional()
  emailAddress?: string[];

  @ApiPropertyOptional({ description: '推送数据范围（风险尽调为：riskLevel）', type: PushScopePo, isArray: false })
  @Type(() => PushScopePo)
  pushScope?: PushScopePo;

  @ApiPropertyOptional({ description: '推送时间', type: PushTimePo, isArray: false })
  @Type(() => PushTimePo)
  pushTime?: PushTimePo;
}
