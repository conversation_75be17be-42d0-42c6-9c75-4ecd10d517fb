import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { PushFrequencyTypeEnum } from '@domain/enums/push/PushFrequencyTypeEnum';

/**
 * 推送时间
 */
export class PushTimePo {
  @ApiProperty({
    description: '推送类型：REAL（实时）  DAILY（每日），WEEKLY（每周），MONTHLY（每月）',
    isArray: false,
    type: String,
    required: true,
  })
  @IsNotEmpty()
  scheduleType: PushFrequencyTypeEnum = PushFrequencyTypeEnum.DAILY;

  @ApiPropertyOptional({ description: '推送时间点', isArray: false })
  hour?: number;

  @ApiPropertyOptional({
    description: '是否仅工作日推送（0：否，1：是，仅适用于 DAILY 类型）',
    isArray: false,
    required: false,
    type: Number,
  })
  isWorkdayOnly?: number;

  @ApiPropertyOptional({
    description: '每周推送的星期几（1-7，对应周一到周日，仅适用于 WEEKLY 类型）',
    isArray: false,
    required: false,
    type: Number,
  })
  weekDays?: number;

  @ApiPropertyOptional({
    description: '每月推送的日期（1-31，对应每月几号，仅适用于 MONTHLY 类型）',
    isArray: false,
    required: false,
    type: Number,
  })
  monthDays?: number;
}
