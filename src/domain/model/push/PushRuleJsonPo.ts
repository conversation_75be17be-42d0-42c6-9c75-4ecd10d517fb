import { PushScopePo } from './PushScopePo';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PushTimePo } from './PushTimePo';
import { IsArray, IsIn, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { MessageTypeEnum } from '@domain/enums/message/MessageTypeEnum';

/**
 * 推送规则设置信息
 */
export class PushRuleJsonPo {
  @ApiProperty({
    description: '消息类型：1-短信，2-邮件',
    required: true,
    type: Number,
    isArray: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @IsIn(Object.values(MessageTypeEnum))
  @IsNotEmpty({ each: true })
  methods: MessageTypeEnum[];

  @ApiProperty({
    description: '手机号码',
    required: true,
    type: String,
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  phones: string[];

  @ApiProperty({
    description: '邮箱',
    required: true,
    type: String,
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  emails: string[];

  @ApiPropertyOptional({ description: '推送数据范围（风险尽调为：riskLevel）', type: PushScopePo, isArray: false })
  @Type(() => PushScopePo)
  pushScope?: PushScopePo;

  @ApiPropertyOptional({ description: '推送时间', type: PushTimePo, isArray: false })
  @Type(() => PushTimePo)
  pushTime?: PushTimePo;
}
