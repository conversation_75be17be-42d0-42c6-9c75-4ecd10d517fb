import { ApiPropertyOptional } from '@nestjs/swagger';

export class VerificationCompanyResult {
  @ApiPropertyOptional({ description: '核验结果，取值说明：默认空，1-MATCH(匹配)、2-UNMATCH(不匹配)' })
  verificationResult: number;
  @ApiPropertyOptional({ description: '人员角色，多角色用逗号隔开，lp ：法人 ；tm ：高管 ；sh：股东' })
  relationship: string;
  @ApiPropertyOptional({ description: '公司名称' })
  companyName: string;
  @ApiPropertyOptional({ description: '统—社会信⽤代码' })
  companyCreditCode: string;
  @ApiPropertyOptional({ description: '企业经营状态' })
  companyStatus: string;
}

export class VerificationRelatedCompanyItem {
  @ApiPropertyOptional({ description: '核验结果，取值说明：默认空，1-MATCH(匹配)、2-UNMATCH(不匹配)' })
  verificationResult: number;
  @ApiPropertyOptional({ description: '人员角色，多角色用逗号隔开，lp ：法人 ；tm ：高管 ；sh：股东' })
  relationship: string;
  @ApiPropertyOptional({ description: '公司名称' })
  companyName: string;
  @ApiPropertyOptional({ description: '统—社会信⽤代码' })
  companyCreditCode: string;
  @ApiPropertyOptional({ description: '企业经营状态' })
  companyStatus: string;
  @ApiPropertyOptional({ description: '关键路径' })
  relationPath: string;
}

export class VerificationResultItem {
  @ApiPropertyOptional({ description: '核验记录ID' })
  recordId: number;
  @ApiPropertyOptional({ description: '核验记录结果' })
  recordResult: number;
  @ApiPropertyOptional({ description: '接口调用是否成功' })
  isSuccess: boolean;
  @ApiPropertyOptional({ description: '公司名称' })
  companyName: string;
  @ApiPropertyOptional({ description: '身份证脱敏' })
  idCardMasked: string;
  @ApiPropertyOptional({ description: '目标人姓名' })
  personName: string;
  // @ApiPropertyOptional({ description: '目标企业核验结果', type: VerificationCompanyResult })
  // verificationCompany: VerificationCompanyResult;
  // @ApiPropertyOptional({ description: '关联方企业核验结果', type: VerificationRelatedCompanyItem })
  // verificationRelatedCompanyList: VerificationRelatedCompanyItem[];
}

export class VerificationResponse {
  @ApiPropertyOptional({ description: '核验批次Id' })
  batchId: number;
  @ApiPropertyOptional({ description: '成功条数' })
  successCount: number;
  @ApiPropertyOptional({ description: '失败条数' })
  failCount: number;
  @ApiPropertyOptional({ description: '核验结果', type: VerificationResultItem })
  verificationResultList: VerificationResultItem[];
}
