import { ApiProperty } from '@nestjs/swagger';

export class CompanyDetailModel {
  @ApiProperty({ description: '详情描述' })
  detailDesc: string;
  @ApiProperty({ description: '类型' })
  type: string;
  @ApiProperty({ description: '类型值' })
  typeValue: string;
  @ApiProperty({ description: '关联路径' })
  path: any[];
  @ApiProperty({ description: '占比' })
  percentTotal: string;
}

/**
 * TODO 先临时定义对象占位， 后续根据实际情况需要修改
 */
export class CompanyModel {
  @ApiProperty({ description: '类型' })
  typeDesc: string;
  @ApiProperty({ description: '详情' })
  details: CompanyDetailModel;

  @ApiProperty({ description: '被排查企业keyNo' })
  sourceCompanyId?: string;
  @ApiProperty({ description: '被排查企业名' })
  sourceCompanyName?: string;

  @ApiProperty({ description: '排序（内部黑名单的列入时间顺序，第三方列表添加的顺序）' })
  sort?: number;

  @ApiProperty({ description: '内部黑名单ID' })
  blacklistId?: number;

  @ApiProperty({ description: '黑名单列入原因' })
  reason?: string;

  @ApiProperty({ description: '' })
  companyName?: string;

  @ApiProperty({ description: '' })
  companyId?: string;

  @ApiProperty({ description: '人员姓名' })
  name?: string;

  @ApiProperty({ description: '人员keyno' })
  keyNo?: string;
}
