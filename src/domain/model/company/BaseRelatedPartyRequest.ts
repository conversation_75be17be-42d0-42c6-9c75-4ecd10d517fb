import { IsIn, IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { NebulaRelatedEdgeEnums } from '@domain/enums/dimension/NebulaRelatedEdgeEnums';
import { NebulaRiskTagEnums } from '@domain/enums/dimension/NebulaRiskTagEnums';
import { LogicOperatorEnums } from '@domain/enums/dimension/LogicOperatorEnums';

export class BaseRelatedPartyRequest {
  @IsNotEmpty()
  @IsString()
  companyIds: string[];

  @ApiProperty({ description: '数据状态', example: 1 })
  dataStatus?: number;

  @ApiProperty({ description: '统计周期', example: -1 })
  cycle?: number;

  @ApiProperty({ description: '关联方类型', example: [NebulaRelatedEdgeEnums.ActualController] })
  @IsIn(Object.values(NebulaRelatedEdgeEnums))
  relatedTypes: string[];

  @ApiProperty({ description: '关联风险类型', example: [NebulaRiskTagEnums.Exception] })
  @IsIn(Object.values(NebulaRiskTagEnums))
  riskTypes: string[];

  @ApiProperty({ description: '逻辑运算符', example: LogicOperatorEnums.AND })
  @IsIn(Object.values(LogicOperatorEnums))
  logicalOperator = LogicOperatorEnums.AND.toString(); // 这是一个可选属性，可能是空字符串

  @ApiProperty({ description: '持股比例', example: 0.5 })
  percentage = 50;

  @ApiProperty({ description: '是否过滤投资机构，默认 true', default: true })
  filterInvestmentAgency = true;

  @ApiProperty({ description: '关联方成员公司关联类型', example: [NebulaRelatedEdgeEnums.Hold] })
  @IsIn(Object.values(NebulaRelatedEdgeEnums))
  associateCompanyRelatedTypes?: string[];

  // constructor(
  //   companyIds: string[],
  //   dataStatus: number,
  //   cycle: number,
  //   relatedTypes: string[],
  //   riskTypes: string[],
  //   logicalOperator?: string,
  //   percentage?: number,
  // ) {
  //   this.companyIds = companyIds;
  //   this.dataStatus = dataStatus;
  //   this.cycle = cycle;
  //   this.relatedTypes = relatedTypes;
  //   this.riskTypes = riskTypes;
  //   this.logicalOperator = logicalOperator;
  //   this.percentage = percentage;
  // }
}
