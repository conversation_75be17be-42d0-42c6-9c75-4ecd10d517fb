import { BaseRelatedPartyRequest } from './BaseRelatedPartyRequest';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsIn, IsOptional, IsString } from 'class-validator';
import { LogicOperatorEnums } from '@domain/enums/dimension/LogicOperatorEnums';

export class RelatedPartRequest extends BaseRelatedPartyRequest {
  @ApiProperty({ type: String, isArray: true, description: '包含的案由类型' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  reasonTypes?: string[];

  @ApiProperty({ type: String, isArray: true, description: '不包含的案由类型' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludedReasonTypes?: string[];

  @ApiProperty({ type: String, isArray: true, description: '案件类型' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  caseTypes: string[];

  @ApiProperty({ description: '开庭公告身份，0-原告,1-被告,2-第三方,3-其他', example: 0 })
  @IsIn(Object.values(LogicOperatorEnums))
  @IsOptional()
  announcementRole?: number;

  @ApiProperty({ description: '裁判文书身份，0-原告,1-被告,2-第三方,3-其他', example: 1 })
  @IsIn(Object.values(LogicOperatorEnums))
  @IsOptional()
  judgementRole?: number;

  @ApiProperty({ description: '登记状态', example: ['注销'] })
  @IsIn(['注销', '吊销'])
  @IsOptional()
  shortStatusList?: string[];
}
