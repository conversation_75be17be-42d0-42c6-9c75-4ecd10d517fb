import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, ArrayNotEmpty, IsArray, IsIn, IsO<PERSON>al, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { JointBiddingAnalysisModel } from '../tender/JointBiddingAnalysisModel';
import { ExcludeNodeTypeEnums } from '@domain/enums/diligence/ExcludeNodeTypeEnums';

export class GroupCompanyRelationsRequest {
  @ApiProperty({ description: '公司keyno', type: String, required: false })
  companyIds?: string[];

  @ApiProperty({ description: '公司keyno', type: String, required: false })
  keyNos?: string[];

  @ApiProperty({ description: '组织id', type: Number })
  orgId: number;

  @Min(0.01)
  @Max(100.0)
  @ApiProperty({ description: '投资/持股 比例', type: Number })
  percentage: number;

  @ApiProperty({ description: '层级深度，最小1，最大6', type: Number })
  @Min(1)
  @Max(6)
  depth: number;

  @ApiProperty({ description: '关联类型', type: String })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsIn(
    [
      'Legal',
      'Employ',
      'HisEmploy',
      'Invest',
      'HisInvest',
      'HisLegal',
      'ContactNumber',
      'Website',
      'Address',
      'Mail',
      'Patent',
      'IntPatent',
      'SoftwareCopyright',
      'Case',
      'ActualController',
    ],
    {
      each: true,
    },
  )
  types: string[];

  @ApiProperty({ required: true, description: '' })
  @ArrayMinSize(2)
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => JointBiddingAnalysisModel)
  keyNoAndNames: JointBiddingAnalysisModel[];

  @ApiProperty({ description: '节点排除类型', enum: Object.values(ExcludeNodeTypeEnums) })
  @IsArray()
  excludedTypes?: ExcludeNodeTypeEnums[];
}
