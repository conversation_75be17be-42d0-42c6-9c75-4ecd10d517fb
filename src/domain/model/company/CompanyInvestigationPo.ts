export class CompanyInvestigationPo {
  /**
   * 关联机构id
   */
  orgId: number;
  /**
   * 公司id
   */
  companyId: string;
  /**
   * 关联层级
   */
  depth: number;
  /**
   * 持股比例（Legal/HisLegal/Employ/HisEmploy/Invest/HisInvest）其中任意一个时生效 ）
   */
  percentage?: number;
  /**
   * 关联类型[ Legal, HisLegal,Employ, HisEmploy, Invest, HisInvest, ActualController,FinalBenefit, Branch]
   */
  types?: string[];

  /**
   * 关联公司范围（根据维度设置的范围获取到的公司 ids）
   */
  rangeCompanyIds?: string[];

  constructor(orgId?: number, companyId?: string, depth?: number, percentage?: number, types?: string[], rangeCompanyIds?: string[]) {
    this.orgId = orgId;
    this.companyId = companyId;
    this.depth = depth;
    this.percentage = percentage;
    this.types = types;
    this.rangeCompanyIds = rangeCompanyIds;
  }
}
