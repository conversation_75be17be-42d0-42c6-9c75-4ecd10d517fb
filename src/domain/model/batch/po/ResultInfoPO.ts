import { RecordBasePO } from './parse/ParsedRecordBase';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class ResultInfoPO extends RecordBasePO {
  @ApiPropertyOptional({ description: '如果该字段不为空，会获取该id对应的尽调结果 优先级最高' })
  @IsOptional()
  diligenceId?: number;

  @ApiPropertyOptional({ description: '人企核验，核验记录Id' })
  @IsOptional()
  verificationRecordId?: number;
}
