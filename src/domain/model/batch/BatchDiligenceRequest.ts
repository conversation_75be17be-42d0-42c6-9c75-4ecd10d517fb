import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsIn, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

import { BatchDiligenceRequestItemPO } from './BatchDiligenceRequestItemPO';
import { DiligenceRequestBase } from '../diligence/req&res/DiligenceRequestBase';

export class BatchDiligenceRequest extends DiligenceRequestBase {
  @ApiProperty({
    description: '批量排查的数据，默认情况下，如果超过5条，会自动转成批量排查',
    isArray: true,
    type: BatchDiligenceRequestItemPO,
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => BatchDiligenceRequestItemPO)
  data: BatchDiligenceRequestItemPO[];

  @ApiPropertyOptional({ description: '如果是批量任务，需要关联的批次id，如果不指定，需要的时候会使用系统自己生成的ID' })
  refBatchId?: string;

  @ApiPropertyOptional({
    description: '是否异步执行，0：同步执行，1：异步执行 , 如果是1，则batch任务不会自动启动，需要手动启动，启动之前，可以给batch继续添加任务',
  })
  @IsOptional()
  @IsIn([0, 1])
  asyncBatch?: number;
}
