import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, MaxLength, MinLength } from 'class-validator';
import { ParsedRecordBase } from './po/parse/ParsedRecordBase';

export class BatchDiligenceRequestItemPO {
  @ApiPropertyOptional({ description: '公司名称' })
  @IsOptional()
  @MaxLength(200)
  @MinLength(1)
  companyName: string;

  @ApiProperty({ description: '公司keyNo' })
  @IsNotEmpty()
  @MaxLength(40)
  @MinLength(20)
  companyId: string;
}
