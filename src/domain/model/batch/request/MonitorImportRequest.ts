import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';
import { PaginationParams } from '../../common';

// 验证常量
export const MONITOR_CONSTANTS = {
  CREDIT_CODE_PATTERN: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
  ID_CARD_PATTERN: /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,
  MAX_NAME_LENGTH: 50,
  ID_CARD_LENGTH: 18, // 身份证固定长度为18位
  CREDIT_CODE_LENGTH: 18, // 统一社会信用代码固定长度为18位
};

export class SearchMonitorBatchImportRequest extends PaginationParams {
  @ApiProperty({ description: '批量导入批次id' })
  @Type(() => Number)
  @IsNotEmpty()
  batchId: number;

  @ApiPropertyOptional({ description: '导入items项目数据' })
  @IsArray()
  @IsOptional()
  itemIds?: number[];
}

export class UpdateMonitorImportRequest {
  @ApiProperty({ description: 'batchId' })
  @IsNumber()
  batchId: number;

  @ApiProperty({ description: '记录Id', type: Number })
  @IsNotEmpty()
  itemId: number;

  @ApiProperty({ description: '企业Id', type: String })
  @IsNotEmpty()
  companyId: string;

  @ApiProperty({ description: '企业名称', type: String })
  @IsNotEmpty()
  companyName: string;

  @ApiProperty({ description: '统一社会信用代码', type: String })
  compCreditCode?: string;
}

export class DeleteMonitorImportRequest {
  @ApiPropertyOptional({ description: 'itemIds', type: Number, isArray: true, required: false })
  @IsArray()
  @IsOptional()
  itemIds?: number[];

  @ApiProperty({ description: 'flag 传 true 移除识别失败的人企核验数据' })
  flag = false;

  @ApiProperty({ description: '批量任务 Id' })
  batchId?: number;
}

export class ExecuteMonitorImportRequest {
  @ApiProperty({ description: '批量导入任务 Id' })
  @Type(() => Number)
  @IsNumber()
  @IsNotEmpty()
  monitorImportId: number;

  @ApiPropertyOptional({ description: '导入items项目数据', type: Number, isArray: true, required: false })
  @IsArray()
  @IsOptional()
  itemIds?: number[];

  @ApiProperty({ description: '监控分组' })
  @Type(() => Number)
  @IsNumber()
  @IsNotEmpty()
  monitorGroupId: number;

  @ApiProperty({ description: '是否重试 0否 1是', required: false })
  @IsOptional()
  isRetry?: number;
}

export class SearchMonitorBatchImportResultRequest extends PaginationParams {
  @ApiProperty({ description: '批量导入批次id' })
  @Type(() => Number)
  @IsNotEmpty()
  batchId: number;

  @ApiProperty({ description: '是否成功', required: false })
  @IsOptional()
  isSuccess?: number;

  @ApiPropertyOptional({ description: '是否成功 0未执行 1成功 2重复 3失败' })
  @IsArray()
  @IsOptional()
  isSuccessList?: number[];
}

export class RetryAllErrorMonitorBatchImportRequest {
  @ApiProperty({ description: '批量导入批次id' })
  @Type(() => Number)
  @IsNotEmpty()
  batchId: number;

  @ApiProperty({ description: '是否成功', required: false })
  @IsOptional()
  isSuccess?: number;

  @ApiProperty({ description: '监控分组' })
  @Type(() => Number)
  @IsNumber()
  @IsNotEmpty()
  monitorGroupId: number;
}

export class RetryMonitorImportItemRequest {
  @ApiProperty({ description: '批量导入itemId' })
  @Type(() => Number)
  @IsNotEmpty()
  itemId: number;
  @ApiProperty({ description: '监控分组' })
  @Type(() => Number)
  @IsNumber()
  @IsNotEmpty()
  monitorGroupId: number;
}
