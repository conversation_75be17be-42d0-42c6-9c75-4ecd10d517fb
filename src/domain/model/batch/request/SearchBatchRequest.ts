import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsIn, IsNotEmpty, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { values } from 'lodash';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { PaginationParams } from '../../common';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';

export class SearchBatchRequest extends PaginationParams {
  @ApiProperty({ description: '0: 导入， 1: 导出', required: false })
  // @IsOptional()
  // // @IsNumber()
  // @Type(() => Number)
  // @IsIn([0, 1])
  batchType?: number;

  @ApiProperty({ description: '0: 导入， 1: 导出, 99:同步的batch记录', required: false })
  // @IsOptional()
  // @IsArray()
  // @IsIn([0, 1, 99], { each: true })
  // @Type(() => Number)
  batchTypes?: number[];

  @ApiProperty({
    description:
      '10: 批量排查(文件导入),' +
      '11: 批量排查(公司ID),' +
      '12: 批量排查(合作伙伴选择), ' +
      '13: 批量年检, ' +
      '20: 合作伙伴导入(文件导入),' +
      '21: 合作伙伴导入(公司ID)，' +
      '25: 合作监控导入， ' +
      '37: 人企核验-常规核验-核验结果-导出，  ' +
      '38: 人企核验-深度核验-核验结果-导出，' +
      '39: 人企核验-核验记录-导出，' +
      '40: 人企核验-常规核验-批量导入核验，  ' +
      '41: 人企核验-深度核验-批量导入核验',
    enum: values(BatchBusinessTypeEnums),
    required: false,
  })
  @IsArray()
  @Type(() => Number)
  @IsIn(values(BatchBusinessTypeEnums), { each: true })
  @IsOptional()
  businessType?: number[];

  @ApiProperty({ description: ' 任务状态 0: 待处理, 1: 处理中， 2: 处理完成， 3: 处理失败', required: false })
  @IsArray()
  @Type(() => Number)
  @IsIn([0, 1, 2, 3], { each: true })
  @IsOptional()
  status?: number[];

  @ApiPropertyOptional({
    description: '提交时间',
    isArray: true,
    type: DateRangeRelative,
    required: false,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  createDate?: DateRangeRelative[];

  @ApiProperty({ description: '搜索关键字', type: String, required: false })
  @IsNotEmpty()
  @Type(() => String)
  @IsOptional()
  searchKey?: string;

  @ApiProperty({ description: '排序参数', required: false })
  field?: string;
  @ApiProperty({ description: '排序规则', enum: ['ASC', 'DESC'], required: false })
  order?: 'ASC' | 'DESC';

  @ApiProperty({ description: '提交人', required: false })
  createUsers?: number[];

  @ApiProperty({ description: '提交人归属部门', required: false })
  depIds?: number[];
}
