import { Is<PERSON>rra<PERSON>, <PERSON>NotEmpt<PERSON>, <PERSON><PERSON><PERSON>ber, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationParams } from '../../common';
import { Type } from 'class-transformer';

// 验证常量
export const VALIDATION_CONSTANTS = {
  CREDIT_CODE_PATTERN: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
  ID_CARD_PATTERN: /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,
  MAX_NAME_LENGTH: 50,
  ID_CARD_LENGTH: 18, // 身份证固定长度为18位
  CREDIT_CODE_LENGTH: 18, // 统一社会信用代码固定长度为18位
};

export class SearchVerificationImportRequest extends PaginationParams {
  @ApiProperty({ description: '批量导入批' })
  @Type(() => Number)
  @IsNotEmpty()
  batchId: number;

  @ApiPropertyOptional({ description: '导入items项目数据' })
  @IsArray()
  @IsOptional()
  itemIds?: number[];
}

export class UpateVerificationImportRequest {
  @ApiProperty({ description: 'batchId' })
  @IsNumber()
  batchId: number;

  @ApiProperty({ description: '记录Id', type: Number })
  @IsNotEmpty()
  itemId: number;

  @ApiProperty({ description: '企业Id', type: String })
  @IsNotEmpty()
  companyId: string;

  @ApiProperty({ description: '企业名称', type: String })
  @IsNotEmpty()
  companyName: string;

  @ApiProperty({ description: '统一社会信用代码', type: String })
  compCreditCode?: string;

  @ApiProperty({ description: '核验人员姓名', type: String })
  @IsNotEmpty()
  personName: string;

  @ApiProperty({ description: '核验人员身份证信息', type: String })
  @IsNotEmpty()
  personIdcard: string;
}

export class DeleteVerificationImportRequest {
  @ApiPropertyOptional({ description: 'itemIds', type: Number, isArray: true, required: false })
  @IsArray()
  @IsOptional()
  itemIds?: number[];

  @ApiProperty({ description: 'flag 传 true 移除识别失败的人企核验数据' })
  flag = false;

  @ApiProperty({ description: '批量任务 Id' })
  batchId?: number;
}

export class ExecuteVerificationImportRequest {
  @ApiProperty({ description: '批量导入任务 Id' })
  @IsNumber()
  @IsNotEmpty()
  verificationImportId: number;

  @ApiPropertyOptional({ description: '导入items项目数据', type: Number, isArray: true, required: false })
  @IsArray()
  @IsOptional()
  itemIds?: number[];

  @ApiProperty({ description: '核验类型，取值说明：1-REGULAR(常规核验)、2-DEEP(深度核验)' })
  @IsNumber()
  @IsNotEmpty()
  verificationType: number;
}
