import { PaginationQueryParams } from '@domain/model/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { DimensionLevel1Enums } from '@domain/enums/diligence/DimensionLevel1Enums';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';

export class SearchBatchResultRequest extends PaginationQueryParams {
  @ApiProperty({ description: 'batchId', type: String, required: true })
  @IsNotEmpty()
  @Type(() => Number)
  @IsOptional()
  batchId: number;

  @ApiProperty({ description: '公司名称关键字', type: String, required: true })
  @IsNotEmpty()
  @Type(() => String)
  @IsOptional()
  searchKey: string;

  @ApiPropertyOptional({
    description: '创建时间',
    isArray: true,
    type: DateRangeRelative,
  })
  @Type(() => DateRangeRelative)
  @ValidateNested({ each: true })
  @IsArray()
  @IsOptional()
  createDate?: DateRangeRelative[];

  @ApiProperty({ description: '0 通过\n1 风险较高\n2 慎重考虑' })
  result?: number[];

  @ApiProperty({ description: '查询维度', enum: DimensionLevel1Enums })
  dimensionLevel1?: DimensionLevel1Enums;

  @ApiProperty({ description: '查询维度' })
  dimensionLevel2?: DimensionTypeEnums;

  @ApiProperty({ description: '排查详情ID：diligenceId', type: String, required: false })
  diligenceId?: number;

  @ApiProperty({ description: '排查详情ID数组', required: false })
  diligenceIds?: number[];

  @ApiProperty({ description: '第三方分组' })
  groupIds?: number[];

  @ApiProperty({ description: '第三方标签' })
  labelIds?: number[];

  @ApiProperty({ description: '第三方部门' })
  departments?: string[];

  @ApiProperty({ description: '省份' })
  province?: string[];

  @ApiProperty({ description: '排查公司是否在第三方列表中' })
  existCustomer?: boolean;

  @ApiProperty({ description: '仅显示距离上次发生变化的记录' })
  @IsOptional()
  changedOnly?: boolean;

  @ApiProperty({ description: '用来对比changed的Batch 当changedOnly为true时必传' })
  @IsOptional()
  preBatchId?: number;
}
