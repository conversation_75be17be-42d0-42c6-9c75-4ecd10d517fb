import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { PaginationResponse } from '../../common';
import { VerificationBatchImportItemEntity } from '@domain/entities/VerificationBatchImportItemEntity';

export class VerificationImportResponse extends PaginationResponse {
  @ApiProperty({ description: '列表', type: VerificationBatchImportItemEntity })
  @Type(() => VerificationBatchImportItemEntity)
  data: VerificationBatchImportItemEntity[];
}
