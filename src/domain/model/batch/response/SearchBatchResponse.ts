import { ApiProperty } from '@nestjs/swagger';
import { SearchBatchResponseItem } from './SearchBatchResponseItem';
import { Type } from 'class-transformer';
import { PaginationResponse } from '../../common';

export class SearchBatchResponse extends PaginationResponse {
  @ApiProperty({ description: '列表', type: SearchBatchResponseItem })
  @Type(() => SearchBatchResponseItem)
  data: SearchBatchResponseItem[];
}
