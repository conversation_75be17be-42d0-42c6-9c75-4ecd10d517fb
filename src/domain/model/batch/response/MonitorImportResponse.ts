import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { PaginationResponse } from '../../common';
import { VerificationBatchImportItemEntity } from '@domain/entities/VerificationBatchImportItemEntity';
import { MonitorBatchImportItemEntity } from '@domain/entities/MonitorBatchImportItemEntity';

export class MonitorImportResponse extends PaginationResponse {
  @ApiProperty({ description: '列表', type: MonitorBatchImportItemEntity })
  @Type(() => MonitorBatchImportItemEntity)
  data: MonitorBatchImportItemEntity[];
}
