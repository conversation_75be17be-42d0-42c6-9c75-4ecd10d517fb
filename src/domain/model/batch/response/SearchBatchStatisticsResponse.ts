import { ApiProperty } from '@nestjs/swagger';

export class SearchBatchStatisticsResponse {
  @ApiProperty({ description: '基础资质' })
  riskBaseInfoSum: number;
  @ApiProperty({ description: '司法诉讼风险' })
  riskLegalSum: number;
  @ApiProperty({ description: '行政监管风险' })
  riskAdministrativeSupervisionSum: number;
  @ApiProperty({ description: '经营稳定性风险' })
  riskOperateStabilitySum: number;
  @ApiProperty({ description: '负面舆情' })
  riskNegativeOpinionSum: number;
  @ApiProperty({ description: '合作交叉重叠排查' })
  partnerSum: number;
  @ApiProperty({ description: '黑名单（内部）排查' })
  innerBlacklistSum: number;
  @ApiProperty({ description: '黑名单（外部）排查' })
  outerBlacklistSum: number;
  @ApiProperty({ description: '潜在利益冲突排查' })
  interestConflictSum: number;
}
