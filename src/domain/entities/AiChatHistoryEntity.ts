import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, IsNumber, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import OpenAI from 'openai';

@Entity('ai_chat_history')
@Index('index1', ['chatId'])
export class AiChatHistoryEntity {
  @PrimaryGeneratedColumn({
    name: 'chat_history_id',
    type: 'int',
  })
  @ApiProperty({ description: '聊天历史的唯一标识符' })
  chatHistoryId: number;

  @Column({
    type: 'varchar',
    name: 'chat_id',
    length: 45,
    nullable: false,
  })
  @ApiProperty({ description: '聊天ID' })
  @IsString()
  @MaxLength(45)
  chatId: string;

  @Column({
    type: 'int',
    name: 'prompt_id',
    nullable: false,
  })
  @ApiProperty({ description: '提示ID' })
  @IsNumber()
  promptId: number;

  @Column({
    type: 'text',
    name: 'prompt_content',
    nullable: false,
  })
  @ApiProperty({ description: '提示词内容' })
  @IsNumber()
  promptContent?: number;

  @Column({
    type: 'text',
    name: 'input_content',
    nullable: false,
  })
  @ApiProperty({ description: '输入内容' })
  @IsString()
  @MaxLength(5000)
  inputContent: string;

  // @Column({
  //   type: 'varchar',
  //   length: 2000,
  //   nullable: false,
  // })
  // @ApiProperty({ description: '输出内容' })
  // @IsString()
  // @MaxLength(2000)
  // outputContent: string;

  @Column({
    type: 'json',
    nullable: true,
    name: 'api_response',
  })
  @ApiPropertyOptional({ description: 'API响应数据' })
  apiResponse: OpenAI.Chat.ChatCompletion;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;
}
