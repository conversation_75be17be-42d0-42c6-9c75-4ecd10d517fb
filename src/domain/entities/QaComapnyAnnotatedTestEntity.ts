import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { QaResultTypeEnums } from '@modules/ai-intelligence/qa/model/enums/QaResultTypeEnums';
import { QaTaskResultGroupEnums } from '@modules/ai-intelligence/qa/model/enums/QaTaskResultGroupEnums';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
@Entity('qa_company_annotated_data_test')
export class QaCompanyAnnotatedTestEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'qa_company_item_id',
  })
  companyItemId: number;

  @Column('varchar', {
    nullable: false,
    name: 'result_group',
  })
  resultGroup: QaTaskResultGroupEnums;

  @Column('varchar', {
    nullable: false,
    name: 'result_type',
  })
  resultType: QaResultTypeEnums;

  @Column('varchar', {
    nullable: false,
    name: 'field_key_l1',
  })
  fieldKeyL1: string;

  @Column('varchar', {
    nullable: false,
    name: 'field_key_l2',
  })
  fieldKeyL2?: string;

  @Column('int', {
    nullable: false,
    name: 'model_id',
  })
  modelId: number;

  @Column('int', {
    nullable: false,
    name: 'model_branch_code',
  })
  modelBranchCode: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    name: 'modify_date',
  })
  @ApiPropertyOptional({ description: '人工修改的时间' })
  modifyDate?: Date;

  @Column('varchar', {
    nullable: false,
    name: 'result_type_before',
  })
  @ApiPropertyOptional({ description: '人工修改前的结果类型' })
  resultTypeBefore?: QaResultTypeEnums;

  @Column('varchar', { name: 'annotated_result_hashkey', comment: '标注结果hashkey' })
  @ApiProperty({ description: '标注结果hashkey' })
  annotatedResultHashkey: string;

  @Column('int', {
    nullable: true,
    name: 'user_id',
  })
  createUserId?: number;
}
