import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { IsNumber } from 'class-validator';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
// import { UserEntity } from './UserEntity';
//import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';

@Entity('due_diligence_excludes')
@Index('org_company_dimension', ['orgId', 'companyId', 'dimensionKey'], { unique: false })
export class DiligenceExcludesEntity {
  @PrimaryColumn({
    type: 'varchar',
    name: 'id',
  })
  @ApiProperty({ description: '主键，手动生成  orgId+companyId+dimensionKey+recordId 做md5' })
  id: string;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'company_id',
  })
  companyId: string;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'dimension_key',
  })
  dimensionKey: DimensionTypeEnums;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'record_id',
  })
  recordId: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'dimension_id',
  })
  dimensionId: string;

  // @ManyToOne(() => UserEntity)
  // @JoinColumn({ name: 'operator' })
  // @ApiProperty({ type: UserEntity, description: '操作人' })
  // editor?: UserEntity;

  @Column('int', {
    nullable: false,
    name: 'operator',
  })
  @ApiProperty({ description: '操作人ID' })
  @IsNumber()
  @Type(() => Number)
  operator: number;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date | null;

  // 新增comment字段
  @Column('varchar', {
    nullable: true,
    length: 300,
    name: 'comment',
  })
  @ApiProperty({ description: '排除原因备注' })
  comment?: string;
}
