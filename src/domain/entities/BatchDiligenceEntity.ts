import { Column, Entity, Index, JoinColumn, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { BatchChangingDetailPO } from '@domain/model/batch/po/BatchChangingDetailPO';
import { DiligenceHistoryEntity } from './DiligenceHistoryEntity';

@Entity('batch_diligence')
@Index('batch_id', ['batchId', 'diligenceId'])
@Index('job_id', ['jobId'])
export class BatchDiligenceEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column({
    type: 'int',
    name: 'batch_id',
  })
  batchId: number;

  @Column({
    type: 'int',
    name: 'job_id',
    nullable: false,
  })
  jobId: number;

  @Column({
    type: 'int',
    name: 'diligence_id',
  })
  diligenceId: number;

  @OneToOne(() => DiligenceHistoryEntity)
  @JoinColumn({ name: 'diligence_id' })
  diligenceEntity?: DiligenceHistoryEntity;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  /**
   * 此变量用于监控距离上次排查的数据是否发生了变更
   * 如有变更，此数值在上一次的基础上自增1
   */
  @Column('int', {
    nullable: true,
    name: 'changing_version',
  })
  changingVersion?: number;

  @Column('json', {
    nullable: true,
    name: 'changing_detail',
  })
  changingDetail?: BatchChangingDetailPO;
}
