import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, JoinC<PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { UserEntity } from './UserEntity';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';

@Entity('message')
export class MessageEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number;

  @Column('varchar', { name: 'title', comment: '消息标题', length: 255 })
  @ApiProperty({ description: '消息标题', type: String })
  title: string;

  @Column('varchar', { name: 'content', comment: '消息内容', length: 255 })
  @ApiProperty({ description: '消息内容', type: String })
  content: string;

  @Column('int', {
    nullable: false,
    name: 'user_id',
    comment: '用户id',
  })
  @ApiProperty({ description: '接收人Id', type: Number })
  userId: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
    comment: '组织id',
  })
  @ApiProperty({ description: '组织id', type: Number })
  orgId: number;

  @Column('varchar', { name: 'product', comment: '归属产品', length: 32 })
  @ApiProperty({ description: '归属产品', type: String })
  product: ProductCodeEnums;

  @Column('tinyint', {
    name: 'msg_type',
    comment: '消息类型：1-任务提醒；2-下载提醒；',
  })
  @ApiProperty({ description: '消息类型：1-任务提醒；2-下载提醒；', type: Number })
  msgType: number;

  @Column('varchar', { name: 'object_id', comment: '对象id', length: 255 })
  @ApiProperty({ description: '对象id', type: String })
  objectId: string;

  @Column('varchar', { name: 'url', comment: '消息链接', length: 300, nullable: true })
  @ApiProperty({ description: '消息链接', type: String })
  url: string;

  @Column('tinyint', {
    name: 'status',
    comment: '消息状态：1-未读；2-已读；-1-删除；',
    default: () => "'1'",
    nullable: false,
  })
  @ApiProperty({ description: '消息状态：1-未读；2-已读；-1-删除；', type: Number })
  status: number;

  @Column('datetime', {
    name: 'create_date',
    comment: '创建时间',
    default: () => 'CURRENT_TIMESTAMP',
  })
  @ApiProperty({ description: '创建时间' })
  createDate: Date;

  @Column('datetime', {
    name: 'update_date',
    comment: '更新时间',
    default: () => 'CURRENT_TIMESTAMP',
  })
  @ApiProperty({ description: '更新时间' })
  updateDate: Date;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'user_id' })
  @ApiProperty({ type: UserEntity, description: '接收人' })
  receiver: UserEntity;
}
