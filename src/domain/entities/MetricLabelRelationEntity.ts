import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('metric_label_relation')
export class MetricLabelRelationEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'label_id',
  })
  labelId: number;

  @Column('int', {
    nullable: false,
    name: 'metrics_id',
  })
  metricsId: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;
}
