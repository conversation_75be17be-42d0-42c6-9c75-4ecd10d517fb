import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('group_label_relation')
export class GroupLabelRelationEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    primary: true,
    name: 'group_id',
  })
  groupId: number;

  @Column('int', {
    nullable: false,
    primary: true,
    name: 'label_id',
  })
  labelId: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;
}
