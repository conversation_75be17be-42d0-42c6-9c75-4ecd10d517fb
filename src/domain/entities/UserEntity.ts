import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user', { database: process.env.DATABASE_BUNDLE })
export class UserEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'user_id',
  })
  userId: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'name',
  })
  name: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'phone',
  })
  phone: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'email',
  })
  email: string | null;

  active: number;
}
