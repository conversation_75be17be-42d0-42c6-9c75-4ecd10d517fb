import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsNumber, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { AiReportsEntity } from './AiReportsEntity';

@Entity('ai_report_comment')
// 假设为报告 ID 和用户 ID 创建索引
@Index('index1', ['reportId', 'userId'])
export class AiReportCommentEntity {
  @PrimaryGeneratedColumn({ name: 'comment_id', type: 'int' })
  @ApiProperty({ description: '评论的唯一标识符' })
  commentId: number;

  @Column({
    type: 'int',
    name: 'report_id',
    nullable: false,
  })
  @ApiProperty({ description: '关联的报告 ID' })
  @IsNumber()
  reportId: number;

  @Column({
    type: 'int',
    name: 'org_id',
    nullable: false,
  })
  @ApiProperty({ description: '组织 ID' })
  @IsNumber()
  orgId: number;

  @Column({
    type: 'int',
    name: 'like_it',
    default: 0,
  })
  @ApiProperty({ description: '-1 表示不喜欢，1 表示喜欢, 0 表示未评价' })
  @IsNumber()
  likeIt: number;

  @Column({
    type: 'int',
    name: 'user_id',
    nullable: false,
  })
  @ApiProperty({ description: '发表评论的用户 ID' })
  @IsNumber()
  userId: number;

  @Column({
    type: 'text',
    name: 'comment',
    nullable: false,
  })
  @ApiProperty({ description: '评论内容' })
  @IsString()
  @MaxLength(2000)
  comment: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @ManyToOne(() => AiReportsEntity, (report) => report.comments)
  @JoinColumn({ name: 'report_id' })
  reportEntity: AiReportsEntity;
}
