import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsIn, IsNumber, MaxLength } from 'class-validator';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from '@domain/model/diligence/dimension/DimensionHitStrategyPO';

// import { UserEntity } from './UserEntity';

@Entity('settings_dimension')
@Index('org_key', ['orgId', 'key'], { unique: true })
export class DimensionSettingsEntity {
  @Column({
    type: 'int',
    name: 'id',
  })
  id: number;

  @PrimaryColumn('int', {
    nullable: false,
    name: 'org_id',
  })
  @ApiProperty({ description: '归属的组织的ID', type: Number })
  @Type(() => Number)
  @IsNumber()
  orgId: number;

  @PrimaryColumn('varchar', {
    nullable: false,
    length: 100,
    name: 'key',
  })
  @ApiProperty({ description: '维度的key', enum: Object.values(DimensionTypeEnums) })
  @IsIn(Object.values(DimensionTypeEnums))
  @MaxLength(100)
  key: DimensionTypeEnums;

  @Column('json', {
    nullable: false,
    name: 'content',
  })
  @ApiProperty()
  @Type(() => DimensionHitStrategyPO)
  content: DimensionHitStrategyPO;

  @Column({
    type: 'int',
    name: 'sort',
    default: () => "'0'",
  })
  @ApiProperty({ description: '排序', type: Number })
  sort?: number;

  // @ManyToOne(() => UserEntity)
  // @JoinColumn({ name: 'editor_id' })
  // @ApiProperty({ type: UserEntity, description: '添加人' })
  // editor: UserEntity;

  @Column('int', {
    nullable: false,
    name: 'editor_id',
  })
  @ApiProperty({ description: '添加人ID' })
  @IsNumber()
  @Type(() => Number)
  editorId: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;
}
