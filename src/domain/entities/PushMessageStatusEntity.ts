import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsNotEmpty } from 'class-validator';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { MessageTypeEnum } from '@domain/enums/message/MessageTypeEnum';

@Entity('push_message_status')
export class PushMessageStatusEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  @ApiProperty({ type: Number, description: 'orgId' })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'push_content_id',
  })
  @ApiProperty({ type: Number, description: '关联的推送内容Id' })
  pushContentId: number;

  @Column('tinyint', {
    name: 'method',
    nullable: false,
    comment: '开启短信通知',
    width: 1,
    default: () => "'0'",
  })
  @ApiProperty({
    description: '消息类型：1-短信，2-邮件',
    type: Number,
    isArray: false,
    required: true,
    enum: MessageTypeEnum,
  })
  @IsIn(Object.values(MessageTypeEnum))
  @IsNotEmpty()
  method: MessageTypeEnum;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'recipient',
  })
  @ApiProperty({ type: String, description: '联系方式信息（电话号码，或者 邮箱） ' })
  recipient: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'attempts',
  })
  @ApiProperty({ type: Number, description: '重试次数，最多3次' })
  attempts: number;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'status',
  })
  @ApiProperty({
    description: '发送状态：0-未发送, 1-发送',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;

  @Column('text', {
    nullable: true,
    name: 'error_message',
  })
  @ApiProperty({ description: '错误信息' })
  errorMessage: string;
}
