import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { MetricsEntity } from './MetricsEntity';
import { DimensionHitStrategyEntity } from './DimensionHitStrategyEntity';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, Min } from 'class-validator';
import { Type } from 'class-transformer';

@Entity('metric_dimension_relation')
export class MetricDimensionRelationEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'metrics_id',
  })
  @ApiProperty()
  @Type(() => Number)
  @IsNotEmpty()
  metricsId: number;

  @Column('int', {
    nullable: false,
    name: 'priority',
    default: () => "'1'",
  })
  priority: number;

  @Column('int', {
    nullable: false,
    name: 'order',
    default: () => "'1'",
  })
  order: number;

  @Column('int', {
    nullable: false,
    name: 'is_pre_condition',
    default: () => "'0'",
  })
  isPreCondition: number;

  @Column('int', {
    nullable: false,
    name: 'dimension_strategy_id',
  })
  @ApiProperty()
  @IsNotEmpty()
  @Type(() => Number)
  @Min(1)
  dimensionStrategyId: number;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'template',
    default: '匹配到目标主体 <em class="#level#">【#name#】</em>',
  })
  template? = '匹配到目标主体 <em class="#level#">【#name#】</em>';

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @ManyToOne(() => MetricsEntity, (metricEntity) => metricEntity.dimensionHitStrategies)
  @JoinColumn({ name: 'metrics_id' })
  metricEntity: MetricsEntity;

  @ManyToOne(() => DimensionHitStrategyEntity, (dimensionHitStrategyEntity) => dimensionHitStrategyEntity.dimensionHitStrategies)
  @JoinColumn({ name: 'dimension_strategy_id' })
  dimensionHitStrategyEntity: DimensionHitStrategyEntity;
}
