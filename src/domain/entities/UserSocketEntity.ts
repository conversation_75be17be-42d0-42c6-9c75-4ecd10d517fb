import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';

@Entity('user_socket')
@Index(['userId'], { unique: false })
@Index(['orgId'], { unique: false })
@Index(['sessionId'], { unique: false })
@Index(['socketId'], { unique: false })
export class UserSocketEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  @ApiProperty()
  id: number;

  @Column({ type: 'int', name: 'user_id' })
  userId: number;

  @Column({ type: 'int', name: 'org_id' })
  orgId: number;

  @Column({ type: 'varchar', length: 45, name: 'session_id' })
  sessionId: string;

  @Column({ type: 'varchar', length: 45, name: 'socket_id' })
  socketId: string;

  @Column({ type: 'datetime', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP', name: 'update_date' })
  updateDate: Date;

  @Column({ type: 'datetime', default: () => 'CURRENT_TIMESTAMP', name: 'create_date' })
  createDate: Date;

  @Column({ type: 'int', default: 1, comment: '1 在线\n2 离线', name: 'status' })
  status: number;

  @Column('varchar', { name: 'product', comment: '归属产品', length: 32, default: ProductCodeEnums.Pro })
  @ApiProperty({ description: '归属产品', type: String })
  product: ProductCodeEnums;
}
