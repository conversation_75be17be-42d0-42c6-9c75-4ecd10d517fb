import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { QaResultTypeEnums } from '@modules/ai-intelligence/qa/model/enums/QaResultTypeEnums';
import { QaTaskResultGroupEnums } from '@modules/ai-intelligence/qa/model/enums/QaTaskResultGroupEnums';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { QaCompanyEntity } from './QaCompanyEntity';

@Entity('qa_task_result')
export class QaTaskResultEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'task_id',
  })
  taskId: number;

  @Column('int', {
    nullable: false,
    name: 'task_item_id',
  })
  taskItemId: number;

  @Column('varchar', {
    nullable: false,
    name: 'result_type',
  })
  resultType: QaResultTypeEnums;

  @Column('varchar', {
    nullable: false,
    name: 'result_group',
  })
  resultGroup: QaTaskResultGroupEnums;

  @Column('varchar', {
    nullable: false,
    name: 'field_key_l1',
  })
  fieldKeyL1: string;

  @Column('varchar', {
    nullable: false,
    name: 'field_key_l2',
  })
  fieldKeyL2: string;

  @Column('varchar', { name: 'model_branch_code', comment: '模型分支编码' })
  @ApiProperty({ description: '模型分支编码' })
  modelBranchCode: string;

  @Column('varchar', { name: 'result_hashkey', comment: '结果hashkey' })
  @ApiProperty({ description: '结果hashkey' })
  resultHashkey: string;

  @Column('varchar', { name: 'annotated_result_hashkey', comment: '标注结果hashkey' })
  @ApiProperty({ description: '标注结果hashkey' })
  annotatedResultHashkey: string;

  @Column('int', { name: 'model_id', comment: '模型ID' })
  @ApiProperty({ description: '模型ID' })
  modelId: number;

  @Column('varchar', { name: 'metrics_id', comment: '指标ID' })
  @ApiProperty({ description: '指标ID' })
  metricsId?: string;

  @Column('varchar', { name: 'strategy_id', comment: '策略ID' })
  @ApiProperty({ description: '策略ID' })
  strategyId?: string;

  @Column('datetime', {
    nullable: false,
    name: 'update_date',
  })
  updateDate: Date;

  @Column('datetime', {
    nullable: false,
    name: 'create_date',
  })
  createDate: Date;

  @ManyToOne(() => QaCompanyEntity, (qaCompany) => qaCompany.qaTaskResults)
  @JoinColumn({ name: 'task_item_id' })
  qaCompany: QaCompanyEntity;
}
