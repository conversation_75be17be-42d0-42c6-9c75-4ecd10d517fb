import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { IsNumber } from 'class-validator';
// import { UserEntity } from './UserEntity';
import { UpdateDiligenceResultRequest } from '@domain/model/diligence/req&res/UpdateDiligenceResultRequest';

@Entity('due_diligence_remark')
@Index('index1', ['diligenceId'])
export class DiligenceRemarkEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'diligence_id',
  })
  diligenceId: number;

  // @ManyToOne(() => UserEntity)
  // @JoinColumn({ name: 'operator' })
  // @ApiProperty({ type: UserEntity, description: '操作人' })
  // editor: UserEntity;

  @Column('int', {
    nullable: false,
    name: 'operator',
  })
  @ApiProperty({ description: '操作人ID' })
  @IsNumber()
  @Type(() => Number)
  operator: number;

  @Column('json', {
    nullable: true,
    name: 'details',
  })
  @ApiProperty()
  @Type(() => UpdateDiligenceResultRequest) //
  details: UpdateDiligenceResultRequest;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date | null;

  // 新增remark字段
  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'due_diligence_remarkcol',
  })
  remarkcol?: string;
}
