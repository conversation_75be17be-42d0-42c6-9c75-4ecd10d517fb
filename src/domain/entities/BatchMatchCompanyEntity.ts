import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, IsNumber, MaxLength } from 'class-validator';
// import { UserEntity } from './UserEntity';
import { Type } from 'class-transformer';
import { BatchStatisticsBasePO } from '@domain/model/batch/po/BatchStatisticsBasePO';

@Entity('batch_match_company')
export class BatchMatchCompanyEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: true,
    length: 300,
    name: 'file_name',
  })
  @ApiProperty({ description: '公司名称(通过excel文件导入的时候不为空)' })
  @MaxLength(300)
  fileName: string;

  // @ManyToOne(() => UserEntity)
  // @JoinColumn({ name: 'create_by' })
  // @ApiProperty({ type: UserEntity, description: '创建人' })
  // creator: UserEntity | null;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  createBy: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('varchar', {
    nullable: true,
    name: 'origin_file',
  })
  @ApiPropertyOptional({ description: '原文件地址' })
  @MaxLength(200)
  originFile: string | null;

  @Column('json', {
    nullable: true,
    name: 'statistics_info',
  })
  @ApiProperty({ description: '识别统计' })
  statisticsInfo: BatchStatisticsBasePO;
}
