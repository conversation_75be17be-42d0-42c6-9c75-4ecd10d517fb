import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { CleanupTriggerType } from '../enums/data-cleaner/cleanup-trigger-type.enum';
import { DataCleanupStagingStatus } from '../enums/data-cleaner/data-cleanup-staging-status.enum';
import { DetailedCleanupAnalysisResult } from '../model/data-cleaner/risk-model/interfaces/cleanup.interfaces';

/**
 * 数据清理暂存表实体
 * 管理清理操作的三个阶段：分析、废弃、删除
 */
@Entity('data_cleanup_staging')
export class DataCleanupStagingEntity {
  @PrimaryGeneratedColumn()
  id: number;

  // 清理目标标识
  @Column({
    name: 'target_type',
    type: 'varchar',
    length: 50,
    comment: '清理目标类型: branchCode|riskModelId',
  })
  targetType: CleanupTriggerType;

  @Column({
    name: 'target_value',
    type: 'varchar',
    length: 200,
    comment: '清理目标值',
  })
  targetValue: string;

  @Column({
    name: 'risk_model_id',
    type: 'int',
    nullable: true,
    comment: '关联的风险模型ID',
  })
  riskModelId?: number;

  @Column({
    name: 'branch_code',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '关联的branchCode',
  })
  branchCode?: string;

  @Column({
    name: 'organization_id',
    type: 'int',
    nullable: true,
    comment: '组织ID',
  })
  organizationId?: number;

  // 分析结果
  @Column({
    name: 'analysis_result',
    type: 'json',
    comment: '清理分析结果详情',
  })
  analysisResult: DetailedCleanupAnalysisResult;

  // 状态管理
  @Column({
    name: 'staging_status',
    type: 'tinyint',
    default: DataCleanupStagingStatus.ANALYZED,
    comment: '暂存状态: 0-已分析, 1-已废弃, 2-已删除',
  })
  stagingStatus: DataCleanupStagingStatus;

  @Column({
    name: 'deprecated_date',
    type: 'datetime',
    nullable: true,
    comment: '标记废弃时间',
  })
  deprecatedDate?: Date;

  @Column({
    name: 'deleted_date',
    type: 'datetime',
    nullable: true,
    comment: '彻底删除时间',
  })
  deletedDate?: Date;

  @Column({
    name: 'rollback_date',
    type: 'datetime',
    nullable: true,
    comment: '撤回操作时间',
  })
  rollbackDate?: Date;

  @Column({
    name: 'rollback_count',
    type: 'int',
    default: 0,
    comment: '撤回操作次数',
  })
  rollbackCount: number;

  // 统计信息
  @Column({
    name: 'affected_tables',
    type: 'json',
    comment: '受影响的表名列表',
  })
  affectedTables: string[];

  @Column({
    name: 'affected_records',
    type: 'int',
    default: 0,
    comment: '受影响的记录总数',
  })
  affectedRecords: number;

  @Column({
    name: 'actual_affected_records',
    type: 'int',
    nullable: true,
    comment: '实际受影响的记录数（废弃阶段后）',
  })
  actualAffectedRecords?: number;

  @Column({
    name: 'actual_deleted_records',
    type: 'int',
    nullable: true,
    comment: '实际删除的记录数（删除阶段后）',
  })
  actualDeletedRecords?: number;

  @Column({
    name: 'estimated_size',
    type: 'bigint',
    default: 0,
    comment: '预估数据大小（字节）',
  })
  estimatedSize: number;

  // 备份数据（用于撤回）
  @Column({
    name: 'backup_data',
    type: 'longtext',
    nullable: true,
    comment: '备份数据（JSON格式，用于撤回操作）',
  })
  backupData?: string;

  // 审计字段
  @CreateDateColumn({
    name: 'create_date',
    comment: '创建时间',
  })
  createDate: Date;

  @UpdateDateColumn({
    name: 'update_date',
    comment: '更新时间',
  })
  updateDate: Date;

  @Column({
    name: 'created_by',
    type: 'int',
    comment: '创建人ID',
  })
  createdBy: number;

  @Column({
    name: 'deprecated_by',
    type: 'int',
    nullable: true,
    comment: '废弃操作人ID',
  })
  deprecatedBy?: number;

  @Column({
    name: 'deleted_by',
    type: 'int',
    nullable: true,
    comment: '删除操作人ID',
  })
  deletedBy?: number;

  @Column({
    name: 'last_operated_by',
    type: 'int',
    nullable: true,
    comment: '最后操作人ID',
  })
  lastOperatedBy?: number;

  // 其他信息
  @Column({
    name: 'risk_level',
    type: 'varchar',
    length: 20,
    default: 'MEDIUM',
    comment: '风险等级: LOW|MEDIUM|HIGH',
  })
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';

  @Column({
    name: 'safety_warnings',
    type: 'json',
    nullable: true,
    comment: '安全警告信息列表',
  })
  safetyWarnings?: string[];

  @Column({
    name: 'notes',
    type: 'text',
    nullable: true,
    comment: '备注信息',
  })
  notes?: string;
}
