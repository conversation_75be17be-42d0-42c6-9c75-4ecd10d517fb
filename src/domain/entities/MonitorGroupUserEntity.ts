import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { MonitorGroupEntity } from './MonitorGroupEntity';

@Entity('monitor_group_user')
export class MonitorGroupUserEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'monitor_group_id',
  })
  monitorGroupId: number;

  @Column('int', {
    nullable: false,
    name: 'user_id',
  })
  userId: number;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  product: string;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @ManyToOne(() => MonitorGroupEntity, (monitorGroup) => monitorGroup.monitorGroupUsers)
  @JoinColumn({ name: 'monitor_group_id' })
  monitorGroup: MonitorGroupEntity;
}
