import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserEntity } from './UserEntity';
import { IsIn, IsNumber, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { ProductCodeAllowValues, ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { MonitorGroupEntity } from './MonitorGroupEntity';
import { MonitorCompanyPrimaryObjectEnum, MonitorCompanyStatusEnums } from '@domain/enums/monitor/MonitorCompanyStatusEnums';

@Entity('monitor_company')
export class MonitorCompanyEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  monitorCompanyId: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'dep_id',
    default: () => '-1',
  })
  depId: number;

  @ApiProperty({ description: '公司ID' })
  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'company_id',
  })
  companyId: string;

  @ApiProperty({ description: '公司名称' })
  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'company_name',
  })
  @MaxLength(45)
  companyName: string;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date | null;

  @Column('int', {
    nullable: false,
    name: 'monitor_group_id',
  })
  monitorGroupId: number;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  createBy: number;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'create_by' })
  @ApiProperty({ type: UserEntity, description: '创建人' })
  creator: UserEntity | null;

  @Column({
    type: 'int',
    name: 'batch_id',
    default: () => '-1',
  })
  batchId?: number;

  @Column({
    type: 'int',
    name: 'primary_object',
    default: 1,
  })
  @ApiProperty({ description: '1: 主公司， 0: 关联方' })
  primaryObject?: MonitorCompanyPrimaryObjectEnum;

  @Column({
    type: 'int',
    name: 'related_party_count',
    default: 0,
  })
  @ApiPropertyOptional({ description: '关联方数量' })
  relatedPartyCount?: number;

  @Column({
    type: 'int',
    name: 'status',
    default: () => '2',
  })
  @ApiProperty({ description: '-1:删除，0:等待， 1: 处理中， 2: 处理完成 3：执行错误， 4：队列中排队 5 套餐失效被冻结' })
  @IsNumber()
  @Type(() => Number)
  @IsIn(Object.values(MonitorCompanyStatusEnums))
  status?: MonitorCompanyStatusEnums;

  // @ManyToOne(() => CompanyEntity)
  // @JoinColumn({ name: 'company_id', referencedColumnName: 'companyId' })
  // @ApiProperty({ type: CompanyEntity, description: '公司' })
  // companyInfo: CompanyEntity | null;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: ProductCodeEnums;

  @Column('varchar', {
    nullable: false,
    name: 'related_dynamic_hash_key',
  })
  @ApiProperty({ description: '监控企业的关联方发生变化时，记录关联方变化的动态的uniqueHashkey' })
  relatedDynamicHashKey: string;

  @Column({
    type: 'int',
    name: 'risk_level',
  })
  @ApiProperty({ description: '最新一次执行监控批次是，监控公司动态中最高的等级' })
  riskLevel: number;

  // @Column('datetime', {
  //   nullable: true,
  //   name: 'latest_dynamic_date',
  // })
  // @ApiProperty({ description: '最新一次动态发生时间' })
  // latestDynamicDate: Date | null;

  // @Column({
  //   type: 'int',
  //   name: 'follow_status',
  //   default: () => '-1',
  // })
  // @ApiProperty({ description: '-1 无动态：企业没有动态；0 待处理：企业有动态待跟进；1 跟进中：企业有跟进中的动态；2 已处理：企业所有的动态都已处理；' })
  // @IsNumber()
  // @Type(() => Number)
  // followStatus?: number;

  // @OneToMany(() => MonitorGroupCompanyRelationEntity, (relation) => relation.monitorCompany)
  // groupCompanyRelations?: MonitorGroupCompanyRelationEntity[];

  @OneToOne(() => MonitorGroupEntity)
  @JoinColumn({ name: 'monitor_group_id' })
  monitorGroupEntity?: MonitorGroupEntity;
}
