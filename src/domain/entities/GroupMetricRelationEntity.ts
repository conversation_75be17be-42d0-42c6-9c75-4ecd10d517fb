import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { GroupEntity } from './GroupEntity';
import { MetricsEntity } from './MetricsEntity';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { IsIn } from 'class-validator';

@Entity('group_metric_relation')
export class GroupMetricRelationEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'metrics_id',
  })
  @ApiProperty({ description: '指标的ID' })
  metricsId: number;

  @Column('int', {
    nullable: false,
    name: 'status',
  })
  @ApiProperty({
    description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;

  @Column('int', {
    nullable: false,
    primary: true,
    name: 'group_id',
  })
  @ApiProperty({ description: '分组的ID' })
  groupId: number;

  @Column('int', {
    nullable: true,
    name: 'order',
  })
  @ApiProperty({ description: '排序' })
  order?: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @ManyToOne(() => GroupEntity, (groupEntity) => groupEntity.groupMetrics)
  @JoinColumn({ name: 'group_id' })
  groupEntity: GroupEntity;

  @ManyToOne(() => MetricsEntity, (metricEntity) => metricEntity.groupMetrics)
  @JoinColumn({ name: 'metrics_id' })
  metricEntity: MetricsEntity;
}
