import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { QaTaskResultGroupEnums } from '@modules/ai-intelligence/qa/model/enums/QaTaskResultGroupEnums';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { QaCompanyEntity } from './QaCompanyEntity';

@Entity('qa_task_result_analyzed')
export class QaTaskResultAnalyzedEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'task_id',
  })
  taskId: number;

  @Column('varchar', {
    nullable: false,
    name: 'result_group',
  })
  resultGroup: QaTaskResultGroupEnums;

  @Column('varchar', {
    nullable: false,
    name: 'field_key_l1',
  })
  fieldKeyL1: string;

  @Column('int', { name: 'model_id', comment: '模型ID' })
  @ApiProperty({ description: '模型ID' })
  modelId: number;

  @Column('datetime', {
    nullable: false,
    name: 'update_date',
  })
  updateDate: Date;

  @Column('datetime', {
    nullable: false,
    name: 'create_date',
  })
  createDate: Date;

  @Column('int', { name: 'tp_count', comment: 'TP数量' })
  @ApiProperty({ description: 'TP数量' })
  tpCount: number;

  @Column('int', { name: 'fp_count', comment: 'FP数量' })
  @ApiProperty({ description: 'FP数量' })
  fpCount: number;

  @Column('int', { name: 'fn_count', comment: 'FN数量' })
  @ApiProperty({ description: 'FN数量' })
  fnCount: number;

  @Column('int', { name: 'tn_count', comment: 'TN数量' })
  @ApiProperty({ description: 'TN数量' })
  tnCount: number;

  @Column('float', { name: 'recall', comment: '召回率' })
  @ApiProperty({ description: '召回率' })
  recall: number;

  @Column('float', { name: 'precision', comment: '精确率' })
  @ApiProperty({ description: '精确率' })
  precision: number;

  @Column('float', { name: 'f1_score', comment: 'F1分数' })
  @ApiProperty({ description: 'F1分数' })
  f1Score: number;

  @Column('float', { name: 'accuracy', comment: '准确率' })
  @ApiProperty({ description: '准确率' })
  accuracy: number;

  @ApiPropertyOptional()
  relatedQaCompany?: QaCompanyEntity;
}
