// import { Colum<PERSON>, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
// import { ApiProperty } from '@nestjs/swagger';
// import { IsIn, IsNumber } from 'class-validator';
// import { Type } from 'class-transformer';
// // import { DatasetEntity } from './DatasetEntity';

// @Entity('dataset_items', { schema: 'qcc_scorecard_dev' })
// export class DatasetItemEntity {
//   @PrimaryGeneratedColumn({
//     type: 'int',
//     name: 'id',
//   })
//   id: number;

//   @Column('int', {
//     nullable: false,
//     name: 'dataset_id',
//   })
//   datasetId: number;

//   @Column('varchar', {
//     nullable: false,
//     length: 45,
//     name: 'item_id',
//   })
//   @ApiProperty({ description: '关联的资源的ID' })
//   itemId: string;

//   @Column('int', {
//     nullable: false,
//     name: 'status',
//   })
//   @ApiProperty({ description: '0: 公司, 1: 人员' })
//   @IsNumber()
//   @Type(() => Number)
//   @IsIn([0, 1])
//   type: number;

//   @Column('datetime', {
//     nullable: false,
//     default: () => 'CURRENT_TIMESTAMP',
//     name: 'create_date',
//   })
//   @Type(() => Date)
//   createDate: Date;

//   @Column('datetime', {
//     nullable: false,
//     default: () => 'CURRENT_TIMESTAMP',
//     name: 'update_date',
//   })
//   updateDate: Date;

//   // @ManyToOne(() => DatasetEntity, (datasetEntity) => datasetEntity.items)
//   // @JoinColumn({ name: 'dataset_id' })
//   // datasetEntity: DatasetEntity;
// }
