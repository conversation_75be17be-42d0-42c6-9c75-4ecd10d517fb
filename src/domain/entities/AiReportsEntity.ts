import { Column, Entity, Index, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsIn, IsNumber, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { AiBusinessTypeEnums } from '@modules/ai-intelligence/ai_analyzer/enums/AiBusinessTypeEnums';
import { AiReportChatEntity } from './AiReportChatEntity';
import { AiReportCommentEntity } from './AiReportCommentEntity';

@Entity('ai_reports')
@Index('index1', ['businessId', 'businessType', 'userId'])
export class AiReportsEntity {
  @PrimaryGeneratedColumn({ name: 'report_id', type: 'int' })
  @ApiProperty({ description: '报告的唯一标识符' })
  reportId: number;

  @Column({
    type: 'int',
    nullable: false,
    name: 'business_type',
    default: 0,
    comment: '0 尽调\n1 监控\n2 风险巡检',
  })
  @ApiProperty({ description: '业务类型，0: 尽调，1: 监控，2: 风险巡检' })
  @IsNumber()
  @IsIn([0, 1, 2])
  businessType: AiBusinessTypeEnums;

  @Column({
    type: 'varchar',
    length: 50,
    name: 'business_id',
    nullable: false,
    comment: 'diligence_id 或者其他唯一的字符串',
  })
  @ApiProperty({ description: '业务ID，如diligence_id或其他唯一字符串' })
  @IsString()
  @MaxLength(50)
  businessId: string;

  @Column({
    type: 'int',
    name: 'org_id',
    nullable: false,
  })
  @ApiProperty({ description: '组织ID' })
  @IsNumber()
  orgId: number;

  @Column({
    type: 'int',
    name: 'user_id',
    nullable: false,
  })
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column({
    type: 'text',
    name: 'report_content',
    nullable: false,
  })
  @ApiProperty({ description: '报告内容' })
  @IsString()
  @MaxLength(3000)
  reportContent: string;

  @OneToMany(() => AiReportChatEntity, (chat) => chat.reportEntity)
  chatList?: AiReportChatEntity[];

  @OneToMany(() => AiReportCommentEntity, (comment) => comment.reportEntity)
  comments?: AiReportCommentEntity[];
}
