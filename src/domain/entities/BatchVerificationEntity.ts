import { Column, Entity, Index, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { VerificationRecordEntity } from './VerificationRecordEntity';
import { BatchEntity } from './BatchEntity';
import { ProductCodeAllowValues, ProductCodeEnums } from '@domain/enums/ProductCodeEnums';

@Entity('batch_verification')
@Index('batch_id', ['batchId', 'verificationId'])
@Index('job_id', ['jobId'])
export class BatchVerificationEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column({
    type: 'int',
    name: 'batch_id',
  })
  batchId: number;

  @Column({
    type: 'int',
    name: 'job_id',
    nullable: false,
  })
  jobId: number;

  @Column({
    type: 'int',
    name: 'verification_id',
  })
  verificationId: number;

  @OneToOne(() => VerificationRecordEntity)
  @JoinColumn({ name: 'verification_id' })
  verificationRecord?: VerificationRecordEntity;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column({
    type: 'int',
    name: 'paid_count',
  })
  paidCount: number;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: ProductCodeEnums;

  @ManyToOne(() => BatchEntity)
  @JoinColumn({ name: 'batch_id' })
  batchEntity: BatchEntity;
}
