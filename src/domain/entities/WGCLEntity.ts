import { ApiProperty } from '@nestjs/swagger';
import { Data } from 'node-cache';
import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('ryg_passage_company_wgcl_incr')
/**上市违规处理 */
export class WGCLEntity {
  @PrimaryColumn({
    type: 'varchar',
    name: 'id',
  })
  id: string;

  @Column('varchar', { name: 'type' })
  @ApiProperty({ description: '类型' })
  type: string;

  // `markedman` varchar(2000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处罚对象',
  @Column('varchar', { name: 'markedman' })
  @ApiProperty({ description: '处罚对象' })
  markedman: string;

  // `markedmankey` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处罚对象key',
  @Column('varchar', { name: 'markedmankey' })
  @ApiProperty({ description: '处罚对象key' })
  markedmankey: string;

  // `markedmanorg` int NOT NULL DEFAULT '0',
  @Column('int', { name: 'markedmanorg' })
  @ApiProperty({ description: '' })
  markedmanorg: number;

  // `disposition` varchar(1000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处分类型',
  @Column('varchar', { name: 'disposition' })
  @ApiProperty({ description: '处分类型' })
  disposition: string;

  // `violation` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '违规行为',
  @Column('text', { name: 'violation' })
  @ApiProperty({ description: '违规行为' })
  violation: string;

  // `punishmentmeasure` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '处分措施',
  @Column('text', { name: 'punishmentmeasure' })
  @ApiProperty({ description: '处分措施' })
  punishmentmeasure: string;

  // `processman` varchar(1000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处理人',
  @Column('varchar', { name: 'processman' })
  @ApiProperty({ description: '处理人' })
  processman: string;

  // `punishmentamount` varchar(1000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处罚金额',
  @Column('varchar', { name: 'punishmentamount' })
  @ApiProperty({ description: '处罚金额' })
  punishmentamount: string;

  // `publicdate` datetime NOT NULL COMMENT '公告日期',
  @Column('datetime', { name: 'publicdate' })
  @ApiProperty({ description: '公告日期' })
  publicdate: Data;

  // `isadd` int DEFAULT NULL COMMENT '有效标识:1-新增；0-更新；-1-删除',
  @Column('int', { name: 'isadd' })
  @ApiProperty({ description: '有效标识:1-新增；0-更新；-1-删除' })
  isadd: number;

  // `updatedtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  @Column('timestamp', { name: 'updatedtime' })
  @ApiProperty({ description: '更新时间' })
  updatedtime: Data;

  // `keyno` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '企业id',
  @Column('varchar', { name: 'keyno' })
  @ApiProperty({ description: '企业id' })
  keyno: string;

  // `relatedtype` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关联类型，多个逗号分隔（1法人，2历史法人，3主要人员，4历史主要人员，5股东，6历史股东）',
  @Column('varchar', { name: 'relatedtype' })
  @ApiProperty({ description: '关联类型，多个逗号分隔（1法人，2历史法人，3主要人员，4历史主要人员，5股东，6历史股东）' })
  relatedtype: string;

  // `job` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '职务（多个逗号分隔）',
  @Column('varchar', { name: 'job' })
  @ApiProperty({ description: '职务（多个逗号分隔）' })
  job: string;
}
