import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
// import { DatasetEntity } from './DatasetEntity';
import { QaCompanyEntity } from './QaCompanyEntity';
import { QaDatasetEntity } from './QaDatasetEntity';

@Entity('qa_dataset_items')
export class QaDatasetItemEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'dataset_id',
  })
  datasetId: number;

  @Column('int', {
    nullable: false,
    name: 'item_id',
  })
  @ApiProperty({ description: '关联的资源的ID' })
  itemId: number;

  @Column('int', {
    nullable: false,
    name: 'status',
  })
  @ApiProperty({ description: '0: 禁用, 1: 可用' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1])
  status: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @ManyToOne(() => QaDatasetEntity, (datasetEntity) => datasetEntity.items)
  @JoinColumn({ name: 'dataset_id' })
  qaDatasetEntity: QaDatasetEntity;

  @OneToOne(() => QaCompanyEntity)
  @JoinColumn({ name: 'item_id' })
  qaCompanyEntity: QaCompanyEntity;
}
