import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DimensionSourceEnums } from '@domain/enums/diligence/DimensionSourceEnums';
import { IsIn, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';
import { Type } from 'class-transformer';
import { IndicatorTypeEnums } from '@domain/enums/dimension/IndicatorTypeEnums';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DimensionFieldsEntity } from './DimensionFieldsEntity';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyEntity } from './DimensionHitStrategyEntity';

@Entity('dimension_definition')
export class DimensionDefinitionEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'dimension_id',
  })
  dimensionId: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'dimension_key',
  })
  @ApiProperty({ description: '维度的唯一标识', enum: Object.values(DimensionTypeEnums) })
  @IsIn(Object.values(DimensionTypeEnums))
  key: DimensionTypeEnums;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'name',
  })
  @ApiProperty({ description: '维度的名称' })
  @IsString()
  @MinLength(1)
  @MaxLength(45)
  name: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ description: '创建时间' })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  @ApiProperty({ description: '更新时间' })
  updateDate: Date;

  @Column('datetime', {
    nullable: true,
    name: 'modified_date',
  })
  @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  @IsOptional()
  modifiedDate?: Date;

  // @Column('int', {
  //   nullable: false,
  //   name: 'product_code',
  // })
  // @ApiProperty({ description: '关联的产品' })
  // @Type(() => Number)
  // productCode: number;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty({ description: '创建者' })
  @Type(() => Number)
  createBy: number;

  @Column('int', {
    nullable: true,
    name: 'update_by',
  })
  @ApiProperty({ description: '关联的产品' })
  @Type(() => Number)
  updateBy: number;

  @Column('int', {
    nullable: false,
    default: () => "'1'",
    name: 'status',
    comment: '1: 有效 0: 无效',
  })
  @ApiProperty({
    description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;

  @Column('int', {
    nullable: true,
    name: 'extend_from',
  })
  @ApiPropertyOptional({ description: '扩展自哪个维度(如果是从其他维度复制过来的)' })
  @IsOptional()
  extendFrom?: number;

  @ApiProperty({ description: '维度的数据来源，例如 数据接口，指定es等等', enum: Object.values(DimensionSourceEnums) })
  @IsIn(Object.values(DimensionSourceEnums))
  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'source',
  })
  source: DimensionSourceEnums;

  @ApiPropertyOptional({ description: '维度数据接口地址' })
  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'source_path',
  })
  @IsOptional()
  sourcePath?: string;

  @ApiProperty({
    description: '维度详情的数据来源 ，例如 数据接口，指定es等等',
    enum: Object.values(DimensionSourceEnums),
  })
  @IsIn(Object.values(DimensionSourceEnums))
  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'detail_source',
  })
  detailSource?: DimensionSourceEnums;

  @ApiPropertyOptional({ description: '数据详情接口地址' })
  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'detail_source_path',
  })
  detailSourcePath?: string;

  @ApiPropertyOptional({ description: '专业版数据维度对应code' })
  @Column('varchar', {
    nullable: true,
    length: 30,
    name: 'type_code',
  })
  typeCode?: string;

  @ApiProperty({ description: '指标类型，一般项，关键项', enum: Object.values(IndicatorTypeEnums) })
  @IsIn(Object.values(IndicatorTypeEnums))
  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'indicator_type',
    default: IndicatorTypeEnums.generalItems,
  })
  type?: IndicatorTypeEnums = IndicatorTypeEnums.generalItems;

  @ApiPropertyOptional({ description: '描述' })
  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'description',
  })
  @IsOptional()
  description?: string;

  @Column('datetime', {
    nullable: true,
    name: 'deprecated_date',
  })
  @ApiPropertyOptional({ description: '废弃日期' })
  @IsOptional()
  deprecatedDate?: Date | null;

  @Column('datetime', {
    nullable: true,
    name: 'deprecate_start_date',
  })
  @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  @IsOptional()
  deprecateStartDate?: Date | null;

  @OneToMany(() => DimensionFieldsEntity, (dimensionFields) => dimensionFields.dimensionDef)
  dimensionFields: DimensionFieldsEntity[];

  @OneToMany(() => DimensionHitStrategyEntity, (dimensionHitStrategy) => dimensionHitStrategy.dimensionDef)
  dimensionHitStrategy: DimensionHitStrategyEntity[];
}
