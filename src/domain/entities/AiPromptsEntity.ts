import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, IsIn, IsNumber, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { AiBusinessTypeEnums } from '@modules/ai-intelligence/ai_analyzer/enums/AiBusinessTypeEnums';

@Entity('ai_prompts')
@Index('index', ['productCode', 'businessType', 'orgId'])
export class AiPromptsEntity {
  @PrimaryGeneratedColumn({ name: 'prompt_id', type: 'int' })
  @ApiProperty({ description: '提示的唯一标识符' })
  promptId: number;

  @Column({
    type: 'int',
    nullable: false,
    name: 'business_type',
    comment: '0 尽调\n1 风险巡检\n2 风险监控',
  })
  @ApiProperty({ description: '提示类别，0: 尽调，1: 风险巡检，2: 风险监控' })
  @IsNumber()
  @IsIn([0, 1, 2])
  businessType: AiBusinessTypeEnums;

  @Column({
    type: 'int',
    nullable: false,
    name: 'is_default',
    comment: '0 否\n1 是',
  })
  @ApiProperty({ description: '是否是默认的提示词: 0 否， 1 是' })
  @IsNumber()
  @IsIn([0, 1])
  isDefault: number;

  @Column({
    type: 'varchar',
    length: 45,
    name: 'product_code',
    nullable: false,
    comment: '第三方，风险洞察',
  })
  @ApiProperty({ description: '产品代码' })
  @IsString()
  @MaxLength(45)
  productCode: string;

  @Column({
    type: 'text',
    nullable: true,
    name: 'content',
  })
  @ApiPropertyOptional({ description: '提示内容' })
  @IsString()
  @MaxLength(3000)
  content: string | null;

  @Column({
    type: 'varchar',
    length: 200,
    nullable: true,
    name: 'desc',
  })
  @ApiPropertyOptional({ description: '提示描述' })
  @IsString()
  @MaxLength(200)
  desc: string | null;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column({
    type: 'int',
    nullable: true,
    name: 'org_id',
    comment: '如果没有值，就是系统级别的prompt，都可以用',
  })
  @ApiPropertyOptional({ description: '组织ID，如果为空则为系统级提示' })
  @IsNumber()
  orgId: number | null;

  @Column({
    type: 'int',
    nullable: false,
    default: 0,
    name: 'fee_level',
    comment: '0 免费版\n1 收费1\n2 收费2',
  })
  @ApiProperty({ description: '费用级别，0: 免费版，1: 收费1，2: 收费2' })
  @IsNumber()
  @IsIn([0, 1, 2])
  feeLevel: number;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: false,
    default: '报告分析员',
    name: 'prompt_name',
  })
  @ApiProperty({ description: '提示名称' })
  @IsString()
  @MaxLength(45)
  promptName: string;
}
