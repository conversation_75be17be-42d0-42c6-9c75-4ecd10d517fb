import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsIn, IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength, Min, MinLength } from 'class-validator';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DimensionFieldInputTypeEnums } from '@domain/enums/dimension/DimensionFieldInputTypeEnums';
import { DimensionFieldKeyEnums, getUniqueFieldKeys } from '@domain/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyEntity } from './DimensionHitStrategyEntity';
import { DimensionHitStrategyFieldsEntity } from './DimensionHitStrategyFieldsEntity';
import { DimensionFieldTypeEnums } from '@domain/enums/dimension/DimensionFiledDataTypeEnums';
import { DimensionDefinitionEntity } from './DimensionDefinitionEntity';
import { DimensionFieldCompareTypeEnums } from '@domain/enums/dimension/DimensionFieldCompareTypeEnums';

@Entity('dimension_fields', { schema: 'qcc_scorecard_dev' })
export class DimensionFieldsEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'field_id',
  })
  fieldId: number;

  @Column('int', {
    nullable: true,
    default: () => "'1'",
    name: 'input_type',
  })
  @ApiPropertyOptional({
    description: '输入类型：0 文本框 1 下拉框单选 2 下拉多选 3 单选框 4 复选框',
    enum: Object.values(DimensionFieldInputTypeEnums),
  })
  @IsIn(Object.values(DimensionFieldInputTypeEnums))
  inputType: DimensionFieldInputTypeEnums;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'comment',
  })
  @ApiPropertyOptional({ description: '字段描述' })
  @IsOptional()
  @MinLength(0)
  @MaxLength(200)
  comment?: string;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'field_key',
  })
  @ApiProperty({
    description: '字段key, 同一个风险维度下field_key不能重复',
    enum: getUniqueFieldKeys(),
  })
  @IsString()
  @IsIn(getUniqueFieldKeys())
  fieldKey: DimensionFieldKeyEnums;

  @Column('json', {
    nullable: true,
    name: 'default_value',
  })
  @ApiProperty({ description: ' 维度属性默认值 字段值数组，如果比较类型区间，则第一个是最小值，第二个是最大值', isArray: true })
  defaultValue: Array<any>;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'default_compare_type',
  })
  @ApiProperty({ description: '默认比较类型', enum: Object.values(DimensionFieldCompareTypeEnums) })
  @IsIn(Object.values(DimensionFieldCompareTypeEnums))
  defaultCompareType: DimensionFieldCompareTypeEnums;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'data_type',
  })
  @ApiProperty({ description: '数据类型', enum: Object.values(DimensionFieldTypeEnums) })
  @IsIn(Object.values(DimensionFieldTypeEnums))
  dataType: DimensionFieldTypeEnums;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'is_array',
  })
  @ApiProperty({ description: '是否是数组', enum: [0, 1] })
  @IsIn([0, 1])
  isArray: number;

  @Column('int', {
    nullable: true,
    default: () => "'0'",
    name: 'field_order',
  })
  @ApiPropertyOptional({ description: '排序' })
  @IsOptional()
  @IsNumber()
  fieldOrder?: number;

  @Column('int', {
    nullable: false,
    name: 'dimension_id',
  })
  @ApiProperty({ description: '维度ID' })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  dimensionId: number;

  @Column('datetime', {
    nullable: true,
    name: 'deprecated_date',
  })
  @ApiPropertyOptional({ description: '废弃日期' })
  @IsOptional()
  deprecatedDate?: Date | null;

  @Column('datetime', {
    nullable: true,
    name: 'deprecate_start_date',
  })
  @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  @IsOptional()
  deprecateStartDate?: Date | null;

  @Column('json', {
    nullable: true,
    name: 'options',
  })
  @ApiPropertyOptional({ description: '如果该字段是选择框，这里可以配置选项' })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(200)
  options?: Array<any>;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'name',
  })
  @ApiProperty({ description: '字段名称' })
  fieldName: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ description: '创建时间' })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  @ApiProperty({ description: '更新时间' })
  updateDate: Date;

  @Column('datetime', {
    nullable: true,
    name: 'modified_date',
  })
  @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  @IsOptional()
  modifiedDate?: Date;

  @Column('int', {
    nullable: false,
    default: () => "'1'",
    name: 'status',
  })
  @ApiProperty({
    description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;

  @ManyToOne(() => DimensionDefinitionEntity, (dimensionDef) => dimensionDef.dimensionFields)
  @JoinColumn({ name: 'dimension_id' })
  dimensionDef: DimensionDefinitionEntity;

  @OneToMany(() => DimensionHitStrategyFieldsEntity, (strategyField) => strategyField.dimensionField)
  strategyFieldEntity: DimensionHitStrategyEntity[];
}
