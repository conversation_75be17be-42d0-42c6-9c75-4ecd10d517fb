import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

@Entity({ name: 'access_control' })
export class AccessControlEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column({
    type: 'int',
    name: 'expire',
  })
  batchId: number;

  @Column({
    type: 'int',
    name: 'status',
  })
  status = 0;

  @Column({
    type: 'int',
    name: 'scope',
  })
  scope = 1;

  @Column('varchar', {
    nullable: true,
    length: 255,
    name: 'access_key',
  })
  accessKey: string;

  @Column('varchar', {
    nullable: true,
    length: 255,
    name: 'secret_key',
  })
  secretKey: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'product',
  })
  product: string;

  @Column('varchar', {
    nullable: true,
    name: 'comment',
  })
  @ApiPropertyOptional({ description: '描述信息，可以是普通描述及错误信息等' })
  @MaxLength(200)
  comment: string | null;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;
}
