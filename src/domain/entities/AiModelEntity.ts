import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsDate } from 'class-validator';
import { Type } from 'class-transformer';

@Entity('ai_models')
export class AiModelEntity {
  @PrimaryGeneratedColumn({ name: 'id', type: 'int' })
  @ApiProperty({ description: '模型ID' })
  modelId: number;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: false,
    name: 'model',
    comment: '模型版本',
  })
  @ApiProperty({ description: '模型版本' })
  model: string; //'DeepSeek-R1' | 'DeepSeek-V3' | 'Doubao-pro-128k'

  @Column({
    type: 'varchar',
    nullable: false,
    name: 'url',
    comment: '模型地址',
  })
  url?: string;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: false,
  })
  @ApiProperty({ description: '模型token(加密后)' })
  token: string;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: false,
    name: 'name',
    comment: '模型名称',
  })
  @ApiProperty({ description: '模型名称' })
  name: string;

  @Column({
    type: 'varchar',
    length: 200,
    nullable: false,
    name: 'description',
    comment: '模型描述',
  })
  description: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;
}
