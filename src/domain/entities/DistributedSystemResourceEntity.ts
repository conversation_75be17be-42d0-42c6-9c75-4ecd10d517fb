import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { DistributedResourceTypeEnums } from '@domain/enums/DistributedResourceTypeEnums';
import { ApiProperty } from '@nestjs/swagger';
import { IsIn } from 'class-validator';
import { ProductCodeAllowValues } from '@domain/enums/ProductCodeEnums';
import { RiskModelEntity } from './RiskModelEntity';
import { DistributeResourceStatusEnums } from '@domain/enums/DistributeResourceStatusEnums';

@Entity('distributed_system_resource')
export class DistributedSystemResourceEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'resource_type',
  })
  resourceType: DistributedResourceTypeEnums;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: string;

  @Column('varchar', {
    nullable: true,
    name: 'branch_code',
  })
  @ApiProperty({ description: '模型或者指标的编码' })
  branchCode: string;

  @Column('int', {
    nullable: false,
    name: 'resource_id',
  })
  resourceId: number;

  @Column('int', {
    nullable: false,
    name: 'distribute_status',
  })
  distributeStatus: DistributeResourceStatusEnums;

  @Column('int', {
    nullable: false,
    name: 'distributed_by',
  })
  distributedBy: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  @ApiProperty({ description: '资源归属的组织id' })
  orgId: number;

  @Column('int', {
    nullable: true,
    name: 'is_org_default',
  })
  @ApiProperty({ description: '是否是组织级别在指定类型下面的默认资源 1 是 ， 0 否' })
  isOrgDefault = 0;

  // @Column('int', {
  //   nullable: false,
  //   default: () => "'1'",
  //   name: 'status',
  //   comment: '1: 有效 0: 无效',
  // })
  // @ApiProperty({
  //   description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
  //   enum: DataStatusAllowedStatus,
  // })
  // @IsIn(DataStatusAllowedStatus)
  // status: DataStatusEnums;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: true,
    name: 'expire_date',
  })
  expireDate?: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @ManyToOne(() => RiskModelEntity, (riskModelEntity) => riskModelEntity.distributedResource)
  @JoinColumn({ name: 'resource_id' })
  riskModelEntity: RiskModelEntity;
}
