import { <PERSON>umn, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, IsIn, IsNumber, MaxLength } from 'class-validator';
import { UserEntity } from './UserEntity';
import { Type } from 'class-transformer';
import { BatchStatisticsBasePO } from '@domain/model/batch/po/BatchStatisticsBasePO';
import { ProductCodeAllowValues, ProductCodeEnums } from '@domain/enums/ProductCodeEnums';

@Entity('monitor_batch_import')
export class MonitorBatchImportEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: true,
    length: 300,
    name: 'file_name',
  })
  @ApiProperty({ description: '监控企业信息(通过excel文件导入的时候不为空)' })
  @MaxLength(300)
  fileName: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'create_by' })
  @ApiProperty({ type: UserEntity, description: '创建人' })
  creator: UserEntity | null;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  creatorId: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('varchar', {
    nullable: true,
    name: 'origin_file',
  })
  @ApiPropertyOptional({ description: '原文件地址' })
  @MaxLength(200)
  originFile: string | null;

  @Column('json', {
    nullable: true,
    name: 'statistics_info',
  })
  @ApiProperty({ description: '识别统计' })
  statisticsInfo: BatchStatisticsBasePO;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: ProductCodeEnums;

  @Column('int', {
    nullable: true,
    name: 'batch_id',
  })
  @ApiProperty({ description: '批次id' })
  batchId: number;

  @Column('int', {
    nullable: true,
    name: 'is_execute',
  })
  @ApiProperty({ description: '是否已执行' })
  isExecute: number;

  @Column('int', {
    nullable: true,
    name: 'monitor_group_id',
  })
  @ApiProperty({ description: '分组id' })
  monitorGroupId: number;
}
