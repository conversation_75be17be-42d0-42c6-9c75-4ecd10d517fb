import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProductCodeAllowValues, ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { IsIn } from 'class-validator';
import { MetricDynamicContentPO } from '@domain/model/metric/MetricDynamicContentPO';
import { MetricTypeEnums } from '@domain/enums/metric/MetricTypeEnums';

@Entity('monitor_metrics_dynamic')
export class MonitorMetricsDynamicEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'metrics_id',
  })
  metricsId: number;

  @Column('int', {
    nullable: false,
    name: 'batch_id',
  })
  batchId: number;

  @Column('int', {
    nullable: false,
    name: 'pre_batch_id',
  })
  preBatchId: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: false,
    name: 'unique_hashkey',
  })
  uniqueHashkey: string;

  @Column('varchar', {
    nullable: false,
    name: 'metrics_name',
  })
  metricsName: string;

  @Column('varchar', {
    nullable: false,
    name: 'company_metrics_hash_key',
  })
  @ApiProperty({ description: '记录的hashkey， 目前规则是  metricsId + groupId + companyId + riskModelBranchCode的md5值' })
  companyMetricsHashkey: string;

  // @Column('varchar', {
  //   nullable: false,
  //   name: 'unique_key',
  // })
  // @ApiProperty({ description: '手工生成的唯一键: 如果是占位用的，则用companyMetricsHashkey+status 组成的hash， 如果是可重复出现的key，那就用 companyMetricsHashkey+年月日时(尽量规避短时间内重复生成一样的key)' })
  // uniqueKey: string;

  @Column('varchar', {
    nullable: false,
    name: 'company_id',
  })
  companyId: string;

  @Column('varchar', {
    nullable: false,
    name: 'company_name',
  })
  companyName: string;

  @Column('varchar', {
    nullable: false,
    name: 'metric_type',
  })
  metricsType?: MetricTypeEnums;

  // @Column('varchar', {
  //   nullable: false,
  //   name: 'related_company_id',
  // })
  // relatedCompanyId?: string;

  @Column('int', {
    nullable: false,
    name: 'risk_model_id',
  })
  riskModelId: number;

  @Column('varchar', {
    nullable: false,
    name: 'risk_model_branch_code',
  })
  @ApiProperty({ description: '模型的branchCode,一个 branchCode可以包含有多个riskModelId' })
  riskModelBranchCode: string;

  @Column('int', {
    nullable: false,
    name: 'diligence_id',
  })
  diligenceId: number;

  @Column('int', {
    nullable: false,
    name: 'diligence_result',
  })
  diligenceResult: number;

  @Column('int', {
    nullable: false,
    name: 'diligence_score',
  })
  diligenceScore: number;

  @Column('int', {
    nullable: false,
    name: 'risk_level',
  })
  riskLevel: number;

  // @Column('int', {
  //   nullable: false,
  //   name: 'related_type',
  // })
  // @ApiPropertyOptional({ description: '关联方的类型， 逗号分割' })
  // relatedType?: string | string[];

  @Column('int', {
    nullable: false,
    name: 'risk_score',
  })
  riskScore: number;

  @Column('int', {
    nullable: false,
    name: 'monitor_group_id',
  })
  monitorGroupId: number;

  @Column('int', {
    nullable: false,
    name: 'status',
  })
  // -3 作废(备用状态，表示动态数据已经作废了) -2基线数据(当前没风险时候造的占位用的数据),-1 基线数据，无需处理 0 待处理 1 跟进中 2 已处理
  status: number;

  @Column('json', {
    nullable: false,
    name: 'metrics_content',
  })
  metricsContent?: MetricDynamicContentPO;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: ProductCodeEnums;

  @ApiPropertyOptional({ description: '关联的策略id' })
  relatedStrategyIds?: number[];
}
