import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsIn, IsNumber, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { AiBusinessTypeEnums } from '@modules/ai-intelligence/ai_analyzer/enums/AiBusinessTypeEnums';
import { AiReportsEntity } from './AiReportsEntity';

@Entity('ai_report_chat')
// 这里假设你需要为某些字段创建索引，可根据实际情况修改
@Index('index1', ['reportId'])
export class AiReportChatEntity {
  @PrimaryGeneratedColumn({ name: 'chat_id' })
  @ApiProperty({ description: '聊天记录的唯一标识符' })
  chatId: string;

  @Column({
    type: 'int',
    nullable: false,
    default: 0,
    name: 'business_type',
    comment: '0 尽调\n1 监控\n2 风险巡检',
  })
  @ApiProperty({ description: '业务类型，0: 尽调，1: 监控，2: 风险巡检' })
  @IsNumber()
  @IsIn([0, 1, 2])
  businessType: AiBusinessTypeEnums;

  @Column({
    type: 'varchar',
    length: 50,
    name: 'business_id',
    nullable: false,
    comment: 'diligence_id 或者其他唯一的字符串',
  })
  @ApiProperty({ description: '业务ID，如diligence_id或其他唯一字符串' })
  @IsString()
  @MaxLength(50)
  businessId: string;

  @Column({
    type: 'int',
    name: 'org_id',
    nullable: false,
  })
  @ApiProperty({ description: '组织ID' })
  @IsNumber()
  orgId: number;

  @Column({
    type: 'int',
    name: 'user_id',
    nullable: false,
  })
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @Column({
    type: 'int',
    name: 'report_id',
    nullable: false,
  })
  @ApiProperty({ description: '关联的报告 ID' })
  @IsString()
  @MaxLength(45)
  reportId: number;

  @Column({
    type: 'text',
    name: 'chat_name',
    nullable: false,
  })
  @ApiProperty({ description: '聊天内容截取' })
  @IsString()
  @MaxLength(5000)
  chatName: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @ManyToOne(() => AiReportsEntity, (report) => report.chatList)
  @JoinColumn({ name: 'report_id' })
  reportEntity: AiReportsEntity;
}
