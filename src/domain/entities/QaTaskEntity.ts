import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { Column, Entity, JoinColumn, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { RiskModelEntity } from './RiskModelEntity';
import { QaDatasetEntity } from './QaDatasetEntity';
import { QaTypeEnumAllowedValues, QaTypeEnums } from '@modules/ai-intelligence/qa/model/enums/QaTypeEnums';
import { IsIn } from 'class-validator';
import { QaTaskTypeEnumAllowedValues, QaTaskTypeEnums } from '@modules/ai-intelligence/qa/model/enums/QaTaskTypeEnums';

@Entity('qa_task')
export class QaTaskEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: false,
    name: 'task_name',
  })
  @ApiProperty({ description: '任务名称' })
  taskName: string;

  @Column('int', {
    nullable: true,
    default: () => "'0'",
    name: 'qa_type',
  })
  @ApiProperty({
    description: '测试目标的类型 监控模型/尽调模型， 根据选中的dataset自动填充',
    enum: QaTypeEnumAllowedValues,
  })
  @IsIn(QaTypeEnumAllowedValues)
  qaType: QaTypeEnums;

  @Column('int', {
    nullable: true,
    default: () => "'0'",
    name: 'task_type',
  })
  @ApiProperty({ description: '任务类型', enum: QaTaskTypeEnumAllowedValues })
  taskType: QaTaskTypeEnums;

  @Column('int', { name: 'ref_batch_id', comment: '关联的尽调批次ID' })
  @ApiProperty({ description: '关联的尽调批次ID-testModelId对应的batch' })
  //@deprecated
  refDiligenceBatchId?: number;

  @Column('int', { name: 'ref_batch_id_base', comment: '关联的尽调批次ID' })
  @ApiProperty({ description: '关联的尽调批次ID-baseModelId对应的batch' })
  refDiligenceBatchIdBase?: number;

  @Column('tinyint', { name: 'status', comment: '任务状态：0-未处理，1-执行中, 2-已执行，3-出错' })
  @ApiProperty({ description: '任务状态：0-未处理，1-执行中, 2-已执行，3-出错' })
  status: BatchStatusEnums;

  @Column('varchar', { name: 'create_username', comment: '临时使用这个字段用来存储monitor的时候interval的值的指定', length: 100 })
  @ApiPropertyOptional({ description: 'monitor的时候interval的值的指定: year:3, month:3,week:3,day:3 这种格式' })
  monitorIntervalString: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('varchar', { name: 'comment', comment: '备注', length: 45 })
  @ApiProperty({ description: '备注' })
  comment?: string;

  @Column('varchar', { name: 'error', comment: '备注', length: 45 })
  @ApiProperty({ description: '备注' })
  error?: string;

  @Column('int', { name: 'dataset_id', comment: '数据集ID' })
  @ApiProperty({ description: '数据集ID' })
  datasetId: number;

  @Column('int', { name: 'dataset_size', comment: '数据集大小' })
  @ApiProperty({ description: '数据集大小' })
  datasetSize: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: false,
    name: 'product',
  })
  product: ProductCodeEnums;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  //   @Column('int', {
  //     name: 'model_id',
  //     comment: '模型ID,需要测试的模型ID，如果有值，就会跟baseModelId一起跑，然后对比base的结果以及annotated data 里面的数据',
  //   })
  //   @ApiProperty({ description: '模型ID' })
  //   testModelId?: number;

  @Column('varchar', { name: 'base_model_id', comment: '基础模型ID' })
  @ApiPropertyOptional({ description: '基础模型ID' })
  baseModelId: number;

  @Column('int', { name: 'finished_batch_count', comment: '已完成的批次数量' })
  @ApiProperty({ description: '已完成的批次数量' })
  finishedBatchCount?: number;

  @Column('varchar', { name: 'model_branch_code', comment: '模型分支编码' })
  @ApiProperty({ description: '模型分支编码' })
  modelBranchCode: string;

  //   testRiskModelEntity: RiskModelEntity;

  baseRiskModelEntity: RiskModelEntity;

  @ManyToOne(() => QaDatasetEntity, (dataset) => dataset.qaTasks)
  @JoinColumn({ name: 'dataset_id' })
  qaDatasetEntity: QaDatasetEntity;

  @Column('int', {
    nullable: true,
    name: 'user_id',
  })
  createUserId?: number;
}
