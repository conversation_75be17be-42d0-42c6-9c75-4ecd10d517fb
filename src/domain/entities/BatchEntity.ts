import { Column, Entity, Index, JoinColumn, ManyToMany, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, IsIn, IsNumber, MaxLength } from 'class-validator';
// import { UserEntity } from './UserEntity';
import { Type } from 'class-transformer';
import { BatchStatisticsBasePO } from '@domain/model/batch/po/BatchStatisticsBasePO';
import { DiligenceHistoryEntity } from './DiligenceHistoryEntity';
import { BatchInfoPO } from '@modules/batch-processing/model/BatchInfoPO';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { UserEntity } from './UserEntity';
import { BatchVerificationEntity } from './BatchVerificationEntity';

@Entity('batch')
@Index('org_batch', ['orgId', 'depId', 'batchType', 'businessType'], { unique: false })
@Index('batch_status', ['batchId', 'status'], { unique: false })
export class BatchEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'batch_id',
  })
  batchId: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'dep_id',
    default: () => '-1',
  })
  depId: number;

  @Column('varchar', {
    nullable: true,
    length: 300,
    name: 'file_name',
  })
  @ApiProperty({ description: '公司名称(通过excel文件导入的时候不为空)' })
  @MaxLength(300)
  fileName: string;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'create_by' })
  @ApiProperty({ type: UserEntity, description: '创建人' })
  creator: UserEntity | null;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  createBy: number;

  @Column('varchar', {
    nullable: false,
    length: 64,
    name: 'product',
  })
  @ApiProperty({ description: '租户归属产品' })
  product: ProductCodeEnums;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    name: 'start_date',
  })
  @ApiProperty({ type: Date, description: '开始处理的时间' })
  @IsDate()
  @Type(() => Date)
  startDate?: Date;

  @Column('datetime', {
    nullable: false,
    name: 'end_date',
  })
  @ApiProperty({ type: Date, description: '处理完成的时间' })
  @IsDate()
  @Type(() => Date)
  endDate?: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('int', {
    nullable: true,
    default: () => '0',
    name: 'status',
  })
  @ApiProperty({ description: '0: 待处理, 1: 处理中， 2: 处理完成， 3: 处理失败' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1, 2])
  status: BatchStatusEnums;

  @Column('int', {
    nullable: false,
    name: 'batch_type',
  })
  @ApiProperty({ description: '-1:不发送异步处理message的batch(对同一个公司同时进行多模型排查的batch) ,0: 导入， 1: 导出' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1])
  batchType: number;

  @Column('int', {
    nullable: false,
    default: () => '0',
    name: 'can_retry',
  })
  @ApiProperty({ description: '0: 不可重试， 1: 可以重试' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1])
  canRetry: number;

  @Column('int', {
    nullable: true,
    default: () => '0',
    name: 'business_type',
  })
  @ApiProperty({ description: '10: 批量排查-excel上传; 11: 批量排查-文本输入; 12: 批量排查-从合作伙伴中选择; 20: 合作伙伴-excel上传; 20: 合作伙伴-文本输入;' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1])
  businessType: BatchBusinessTypeEnums;

  @Column('varchar', {
    nullable: true,
    name: 'comment',
  })
  @ApiPropertyOptional({ description: '描述信息，可以是普通描述及错误信息等' })
  @MaxLength(200)
  comment: string | null;

  @Column('varchar', {
    nullable: true,
    name: 'result_file',
  })
  @ApiPropertyOptional({ description: '结果对应的文件地址' })
  @MaxLength(200)
  resultFile: string | null;

  @Column('varchar', {
    nullable: true,
    name: 'origin_file',
  })
  @ApiPropertyOptional({ description: '原文件地址' })
  @MaxLength(200)
  originFile: string | null;

  @Column('json', {
    nullable: true,
    name: 'batch_info',
  })
  @ApiPropertyOptional({ description: '该字段暂时保留，后续用不到再删除' })
  @Type(() => BatchInfoPO)
  batchInfo?: BatchInfoPO | null;

  @Column('int', {
    nullable: true,
    name: 'record_count',
  })
  @ApiProperty({ description: '该批处理包含的数据的行数' })
  @IsNumber()
  @Type(() => Number)
  recordCount: number;

  @Column('int', {
    nullable: true,
    name: 'paid_count',
    default: () => 0,
  })
  @IsNumber()
  @Type(() => Number)
  paidCount: number;

  @Column('int', {
    nullable: true,
    name: 'error_count',
    default: () => 0,
  })
  @IsNumber()
  @Type(() => Number)
  errorCount: number;

  @Column('int', {
    nullable: true,
    name: 'success_count',
    default: () => 0,
  })
  @IsNumber()
  @Type(() => Number)
  successCount: number;

  @Column('int', {
    nullable: true,
    name: 'updated_count',
    default: () => 0,
  })
  @IsNumber()
  @Type(() => Number)
  updatedCount: number;

  @Column('int', {
    nullable: true,
    name: 'duplicated_count',
    default: () => 0,
  })
  @IsNumber()
  @Type(() => Number)
  duplicatedCount: number;

  @Column('int', {
    nullable: true,
    name: 'withholding_count',
    default: () => 0,
  })
  @IsNumber()
  @Type(() => Number)
  withholdingCount: number;

  @Column('int', {
    nullable: true,
    name: 'withholding_record_count',
    default: () => 0,
  })
  @IsNumber()
  @Type(() => Number)
  withholdingRecordCount: number;

  @Column('json', {
    nullable: true,
    name: 'statistics_info',
  })
  @ApiProperty({ description: '识别统计' })
  statisticsInfo: BatchStatisticsBasePO;

  @Column('varchar', {
    nullable: true,
    name: 'detail_file',
  })
  @ApiPropertyOptional({ description: '1.批量导出任务结果 2.排查报告导出结果' })
  @MaxLength(500)
  detailFile: string | null;

  @Column('varchar', {
    nullable: true,
    name: 'preview_url',
  })
  @ApiPropertyOptional({ description: '排查报告导出预览url' })
  @MaxLength(500)
  previewUrl: string | null;

  @ManyToMany(() => DiligenceHistoryEntity, (diligenceEntity) => diligenceEntity.batchEntities)
  diligenceEntities?: DiligenceHistoryEntity[];

  @OneToMany(() => BatchVerificationEntity, (batchVerificationEntity) => batchVerificationEntity.batchEntity)
  batchVerificationEntities: BatchVerificationEntity[];
}
