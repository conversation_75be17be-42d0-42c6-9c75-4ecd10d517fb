import { Column, Entity, Index, PrimaryColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 国标行业统计业务表
 */
@Entity('industry_statistics_sync')
@Index('idx_update_date', ['updateDate'])
@Index('idx_industry_name', ['industryName'])
@Index('idx_industry_code', ['industryCode'])
export class IndustryStatisticsSyncEntity {
  @PrimaryColumn('varchar', {
    length: 32,
    name: 'id',
    comment: '主键',
  })
  @ApiProperty({ description: '主键' })
  @MaxLength(32)
  @IsNotEmpty()
  id: string;

  @Column('int', {
    nullable: false,
    default: 1,
    name: 'data_status',
    comment: '数据状态',
  })
  @ApiProperty({ description: '数据状态', default: 1 })
  dataStatus: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
    comment: '新增时间',
  })
  @ApiProperty({ description: '新增时间' })
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    name: 'update_date',
    comment: '更新时间',
  })
  @ApiProperty({ description: '更新时间' })
  @Type(() => Date)
  updateDate: Date;

  @Column('varchar', {
    nullable: true,
    length: 32,
    default: '',
    name: 'industry_name',
    comment: '国标行业名称',
  })
  @ApiProperty({ description: '国标行业名称' })
  @MaxLength(32)
  @IsOptional()
  industryName?: string;

  @Column('varchar', {
    nullable: true,
    length: 8,
    default: '',
    name: 'industry_code',
    comment: '国标行业code',
  })
  @ApiProperty({ description: '国标行业code' })
  @MaxLength(8)
  @IsOptional()
  industryCode?: string;

  @Column('int', {
    nullable: true,
    default: 0,
    name: 'industry_level',
    comment: '国标行业等级',
  })
  @ApiProperty({ description: '国标行业等级: 1-大类, 2-中类, 3-小类, 4-细类' })
  @IsOptional()
  industryLevel?: number;

  @Column('int', {
    nullable: true,
    default: 0,
    name: 'no_patent_count',
    comment: '行业无有效发明授权专利公司数',
  })
  @ApiProperty({ description: '行业无有效发明授权专利公司数' })
  @IsOptional()
  noPatentCount?: number;

  @Column('int', {
    nullable: true,
    default: 0,
    name: 'has_patent_count',
    comment: '行业有有效发明授权专利公司数',
  })
  @ApiProperty({ description: '行业有有效发明授权专利公司数' })
  @IsOptional()
  hasPatentCount?: number;

  @Column('int', {
    nullable: true,
    default: 0,
    name: 'no_copyright_count',
    comment: '行业无软著的公司数',
  })
  @ApiProperty({ description: '行业无软著的公司数' })
  @IsOptional()
  noCopyrightCount?: number;

  @Column('int', {
    nullable: true,
    default: 0,
    name: 'has_copyright_count',
    comment: '行业有软著的公司数',
  })
  @ApiProperty({ description: '行业有软著的公司数' })
  @IsOptional()
  hasCopyrightCount?: number;
}
