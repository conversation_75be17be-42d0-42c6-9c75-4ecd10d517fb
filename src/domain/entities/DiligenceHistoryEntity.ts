import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Column, Entity, Index, JoinColumn, JoinTable, ManyToMany, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { IsNumber, MaxLength } from 'class-validator';
// import { UserEntity } from './UserEntity';
import { ModelScorePO } from '@domain/model/diligence/ModelScorePO';
import { SnapshotDetail } from '@domain/model/diligence/SnapshotDetail';
import { BatchEntity } from './BatchEntity';
import { RiskModelEntity } from './RiskModelEntity';

@Entity('due_diligence')
@Index('org_company_uniq', ['orgId', 'companyId'], { unique: false })
// @Index('dep_company_uniq', ['orgId', 'depId', 'companyId'], { unique: false })
@Index('snapshot_id', ['snapshotId'], { unique: true })
export class DiligenceHistoryEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'is_first_to_org',
  })
  @ApiPropertyOptional({ description: '是否是首次执行 - 组织级别, 如果cached为true，忽略这个值' })
  isFirstToOrg = 0;

  @Column('int', {
    nullable: false,
    name: 'is_first_to_model',
  })
  @ApiPropertyOptional({ description: '是否是首次执行 - 组织的指定模型级别，如果cached为true，忽略这个值' })
  isFirstToModel = 0;

  @ApiProperty({ description: '是否计费了, 0 false , 1 true', default: false })
  @Column('int', {
    nullable: false,
    name: 'paid',
  })
  paid = 0;

  // @Column('int', {
  //   nullable: false,
  //   name: 'dep_id',
  //   default: () => '-1',
  // })
  // depId: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'company_id',
  })
  companyId: string;

  @Column('varchar', {
    nullable: true,
    length: 500,
    name: 'name',
  })
  @MaxLength(500)
  name: string;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'score',
  })
  @ApiPropertyOptional({ description: '尽调得分' })
  score: number;

  @Column('tinyint', {
    nullable: false,
    default: () => "'0'",
    name: 'result',
  })
  @ApiProperty({ description: '-2 良好\n-1 提示\n0 警示\n1 中风险\n2 高风险' })
  result: number;

  // @ManyToOne(() => UserEntity)
  // @JoinColumn({ name: 'operator' })
  // @ApiProperty({ type: UserEntity, description: '操作人' })
  // editor: UserEntity;

  @Column('int', {
    nullable: false,
    name: 'operator',
  })
  @ApiProperty({ description: '操作人ID' })
  @IsNumber()
  @Type(() => Number)
  operator: number;

  @Column('int', {
    nullable: false,
    name: 'org_model_id',
  })
  orgModelId: number;

  @Column('varchar', {
    nullable: false,
    name: 'model_branch_code',
  })
  @ApiProperty({ description: '模型分组编码,同一个编码的模型认为是同一个模型的多个版本' })
  modelBranchCode: string;

  @Column('varchar', {
    nullable: false,
    length: 32,
    name: 'product',
  })
  @ApiProperty({ description: '租户归属产品' })
  product: string;

  @ManyToOne(() => RiskModelEntity)
  @JoinColumn({ name: 'org_model_id' })
  orgModel: RiskModelEntity;

  @Column('json', {
    nullable: true,
    name: 'details',
  })
  @ApiProperty({ type: ModelScorePO, description: '尽调详情' })
  @Type(() => ModelScorePO)
  details: ModelScorePO;

  @Column('tinyint', {
    nullable: false,
    name: 'should_update',
  })
  shouldUpdate: number;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date | null;

  @ApiPropertyOptional({ description: '生成快照的时间' })
  @Column('datetime', {
    nullable: true,
    name: 'snapshot_date',
  })
  snapshotDate?: Date | null;

  @ApiPropertyOptional({ description: '快照ID，如果生成了快照，该ID不允许为空' })
  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'snapshot_id',
  })
  snapshotId: string;

  @ApiProperty({ type: SnapshotDetail })
  @Column('json', {
    nullable: true,
    name: 'snapshot_details',
  })
  @Type(() => SnapshotDetail)
  snapshotDetails?: SnapshotDetail;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date | null;

  @Column('int', {
    nullable: true,
    name: 'credit_rate',
  })
  @ApiProperty({ description: '查查信用分 可以为空' })
  creditRate?: number;

  @Column('int', {
    nullable: false,
    name: 'type',
  })
  @ApiProperty({ description: '类别：0-风险洞察尽调，1-风险洞察监控' })
  type: number;

  @ManyToMany(() => BatchEntity, (batch) => batch.diligenceEntities)
  @JoinTable({
    name: 'batch_diligence',
    joinColumn: { name: 'diligence_id' },
    inverseJoinColumn: { name: 'batch_id' },
  })
  batchEntities?: BatchEntity[];

  creditcode?: string;
}
