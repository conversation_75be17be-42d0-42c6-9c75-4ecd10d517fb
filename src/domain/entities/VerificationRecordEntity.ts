import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsNumber } from 'class-validator';
import { VerificationDetailEntity } from './VerificationDetailEntity';
import { AnalyzedResultDimensionPO } from '@domain/model/monitor/AnalyzedResultDimensionPO';
import { ProductCodeAllowValues, ProductCodeEnums } from '@domain/enums/ProductCodeEnums';

/**
 * 核验记录实体，用于记录人企核验的相关信息
 */
@Entity('verification_record')
export class VerificationRecordEntity {
  @ApiPropertyOptional({ description: '核验记录ID' })
  @PrimaryGeneratedColumn({ type: 'int', name: 'verification_id' })
  verificationId: number;

  @ApiPropertyOptional({ description: '核验类型，取值说明：1-REGULAR(常规核验)、2-DE<PERSON>(深度核验)' })
  @Column({ name: 'verification_type', type: 'int', comment: '核验类型，取值说明：1-REGULAR(常规核验)、2-DEEP(深度核验)' })
  verificationType: number;

  @ApiPropertyOptional({ description: '企业名称' })
  @Column({ name: 'comp_name', length: 45, comment: '企业名称，用于搜索' })
  compName: string;

  @ApiPropertyOptional({ description: '企业Id' })
  @Column({ name: 'comp_id', length: 45, comment: '企业Id' })
  compId: string;

  @ApiPropertyOptional({ description: '企业统一社会信用代码' })
  @Column({ name: 'comp_credit_code', length: 32, nullable: true, comment: '企业统一社会信用代码，用于搜索' })
  compCreditCode?: string;

  @ApiPropertyOptional({ description: '人员姓名' })
  @Column({ name: 'person_name', length: 45, comment: '人员姓名' })
  personName: string;

  @ApiPropertyOptional({ description: 'MD5加密后的证件号码' })
  @Column({ name: 'person_idcard_encrypted', length: 32, comment: 'MD5加密后的证件号码' })
  personIdcardEncrypted: string;

  @ApiPropertyOptional({ description: '打码后的证件号码' })
  @Column({ name: 'person_idcard_mask', length: 32, comment: '打码后的证件号码' })
  personIdcardMask: string;

  @ApiPropertyOptional({ description: '人员角色，多角色用逗号隔开' })
  @Column({ name: 'person_role', length: 32, nullable: true, comment: '人员角色，多角色用逗号隔开' })
  personRole?: string;

  @ApiPropertyOptional({
    description:
      '核验结果，取值说明：10常规核验通过，无关联方核验;11常规核验通过，关联方核验通过;12常规核验通过，关联方核验不通过；20常规核验不通过，无关联方核验;21常规核验不通过，关联方核验通过；22常规核验不通过，关联方核验不通过；',
  })
  @Column({
    name: 'verification_result',
    type: 'tinyint',
    nullable: true,
    comment:
      '核验结果，取值说明：10常规核验通过，无关联方核验;11常规核验通过，关联方核验通过;12常规核验通过，关联方核验不通过；20常规核验不通过，无关联方核验;21常规核验不通过，关联方核验通过；22常规核验不通过，关联方核验不通过；',
  })
  verificationResult?: number;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty({ description: '创建者' })
  @IsNumber()
  createBy: number;

  @Column('int', {
    nullable: true,
    name: 'update_by',
  })
  @ApiProperty({ description: '更新者' })
  @IsNumber()
  updateBy: number;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
    comment: '创建时间',
  })
  @ApiProperty({ description: '', type: Number })
  createDate: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
    comment: '更新时间',
  })
  @ApiProperty({ description: '', type: Number })
  updateDate: Date | null;

  @Column({ name: 'status', type: 'tinyint', default: () => 1, comment: '数据状态，默认1，1-有效，0-无效' })
  status: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: true,
    name: 'person_key_no',
  })
  personKeyNo: string;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: ProductCodeEnums;

  @ApiProperty({ type: VerificationDetailEntity, isArray: true })
  @OneToMany(() => VerificationDetailEntity, (verificationDetail) => verificationDetail.verificationRecord)
  @JoinColumn({ name: 'verification_id' })
  verificationDetails: VerificationDetailEntity[];
}
