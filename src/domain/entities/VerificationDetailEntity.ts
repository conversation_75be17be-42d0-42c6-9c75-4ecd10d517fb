import { Column, Entity, Join<PERSON><PERSON>umn, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { VerificationRecordEntity } from './VerificationRecordEntity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsNumber } from 'class-validator';
import { UserEntity } from './UserEntity';
import { ProductCodeAllowValues, ProductCodeEnums } from '@domain/enums/ProductCodeEnums';

/**
 * 核验结果详情实体，存储人企核验结果的详细信息
 */
@Entity('verification_detail')
export class VerificationDetailEntity {
  @ApiPropertyOptional({ description: '企业Id' })
  @PrimaryGeneratedColumn({ type: 'int', name: 'verification_detail_id' })
  verificationDetailId: number;

  @ApiPropertyOptional({ description: '企业Id' })
  @Column('int', {
    name: 'verification_id',
    nullable: false,
    comment: '关联的核验记录ID，关联 verification_record 表的 verification_id',
  })
  verificationId: number;

  @ApiPropertyOptional({ description: '企业类型' })
  @Column('tinyint', { name: 'comp_type', nullable: false, comment: '企业类型，取值说明：1-TARGET(目标企业)、2-RELATED(关联企业)' })
  compType: number;

  @ApiPropertyOptional({ description: '企业名称' })
  @Column('varchar', { name: 'comp_name', nullable: false, length: 45, comment: '企业名称，用于搜索' })
  compName: string;

  @ApiPropertyOptional({ description: '企业Id' })
  @Column('varchar', { name: 'comp_id', nullable: false, length: 32, comment: '企业Id' })
  compId: string;

  @ApiPropertyOptional({ description: '企业统一社会信用代码' })
  @Column('varchar', { name: 'comp_credit_code', nullable: false, length: 45, comment: '企业统一社会信用代码，用于搜索' })
  compCreditCode: string;

  @ApiPropertyOptional({ description: '企业登记状态' })
  @Column('varchar', { name: 'comp_regist_status', nullable: false, length: 32, comment: '企业登记状态' })
  compRegistStatus: string;

  @ApiPropertyOptional({ description: '人员角色，逗号隔开' })
  @Column('varchar', { name: 'person_role', length: 32, nullable: true, comment: '人员角色，逗号隔开' })
  personRole?: string;

  @ApiPropertyOptional({ description: '核验结果，取值说明：默认空，1-MATCH(匹配)、2-UNMATCH(不匹配)' })
  @Column('tinyint', {
    name: 'verification_result',
    nullable: true,
    comment: '核验结果，取值说明：默认空，1-MATCH(匹配)、2-UNMATCH(不匹配)',
  })
  verificationResult?: number;

  @ApiPropertyOptional({ description: '关键路径' })
  @Column('json', {
    name: 'relation_path',
    nullable: true,
    comment: '关键路径，核验当时的关键路径快照',
  })
  relationPath?: object;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'create_by' })
  @ApiProperty({ type: UserEntity, description: '创建人' })
  creator: UserEntity | null;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty({ description: '创建者' })
  @IsNumber()
  createBy: number;

  @Column('int', {
    nullable: true,
    name: 'update_by',
  })
  @ApiProperty({ description: '更新者' })
  @IsNumber()
  updateBy: number;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
    comment: '创建时间',
  })
  @ApiProperty({ description: '', type: Number })
  createDate: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
    comment: '更新时间',
  })
  @ApiProperty({ description: '', type: Number })
  updateDate: Date | null;

  @Column('tinyint', { name: 'status', nullable: false, default: () => 1, comment: '数据状态，默认1，1-有效，0-无效' })
  status: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: ProductCodeEnums;

  @ManyToOne(() => VerificationRecordEntity, (verificationRecord) => verificationRecord.verificationDetails)
  @JoinColumn({ name: 'verification_id' })
  verificationRecord: VerificationRecordEntity;
}
