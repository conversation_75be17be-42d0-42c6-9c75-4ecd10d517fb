import { Column, Entity, <PERSON>ToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsIn, IsN<PERSON>ber, IsOptional, Max<PERSON>ength, MinLeng<PERSON>, ValidateNested } from 'class-validator';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';
import { MetricTypeEnums } from '@domain/enums/metric/MetricTypeEnums';
import { Type } from 'class-transformer';
import { MetricSettingDetailsPO } from '@domain/model/metric/MetricSettingDetailsPO';
import { MetricDimensionRelationEntity } from './MetricDimensionRelationEntity';
import { ProductCodeAllowValues } from '@domain/enums/ProductCodeEnums';
import { GroupMetricRelationEntity } from './GroupMetricRelationEntity';
import { DataCategoryEnums } from '@domain/enums/DataCategoryEnums';
import { MetricHitStrategyDefinitionPO } from '@domain/model/metric/MetricHitStrategyDefinitionPO';

@Entity('metrics')
export class MetricsEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  metricsId: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'name',
  })
  @ApiProperty({ description: '指标名称' })
  @MinLength(3)
  @MaxLength(50)
  name: string;

  @Column('tinyint', {
    nullable: false,
    name: 'risk_level',
  })
  @ApiProperty({ description: '风险等级', enum: Object.values(DimensionRiskLevelEnum) })
  @IsIn(Object.values(DimensionRiskLevelEnum))
  riskLevel: DimensionRiskLevelEnum;

  @Column('tinyint', {
    nullable: true,
    default: () => "'0'",
    name: 'is_veto',
  })
  @ApiProperty({ description: '是否为否决指标', enum: DataStatusAllowedStatus })
  @IsIn(DataStatusAllowedStatus)
  isVeto: number;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'metric_type',
  })
  @ApiProperty({ description: '指标类型', enum: Object.values(MetricTypeEnums) })
  @IsIn(Object.values(MetricTypeEnums))
  metricType: MetricTypeEnums;

  @Column('int', {
    nullable: false,
    name: 'score',
  })
  @ApiProperty({ description: '指标分数' })
  @IsNumber()
  score: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  productCode: string;

  @Column('int', {
    nullable: false,
    default: () => "'1'",
    name: 'status',
  })
  @ApiProperty({
    description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'extend_from',
  })
  @ApiPropertyOptional({ description: '完整的继承路径(如果有)' })
  extendFrom: string | null;

  @Column('int', {
    nullable: true,
    default: () => "'1'",
    name: 'category',
  })
  @ApiProperty({ description: '模型类别 1 系统模型， 2 用户模型' })
  category: DataCategoryEnums = DataCategoryEnums.System;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'comment',
  })
  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  comment?: string;

  @Column('datetime', {
    nullable: true,
    name: 'deprecated_date',
  })
  @ApiPropertyOptional({ description: '废弃日期' })
  @IsOptional()
  deprecatedDate?: Date;

  @Column('datetime', {
    nullable: true,
    name: 'deprecate_start_date',
  })
  @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  @IsOptional()
  deprecateStartDate?: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('datetime', {
    nullable: true,
    name: 'modified_date',
  })
  @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  @IsOptional()
  modifiedDate?: Date;

  @Column('int', {
    nullable: true,
    name: 'publish_by',
  })
  @ApiPropertyOptional({ description: '发布者' })
  @Type(() => Number)
  publishBy?: number;

  @Column('datetime', {
    nullable: true,
    name: 'published_date',
  })
  publishedDate?: Date;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty({ description: '创建者' })
  @Type(() => Number)
  createBy: number;

  @Column('int', {
    nullable: true,
    name: 'update_by',
  })
  @ApiProperty({ description: '更新者' })
  @Type(() => Number)
  updateBy?: number;

  @Column('json', {
    nullable: false,
    name: 'hit_strategy',
  })
  @ApiProperty({
    description: '指标命中策略,数组中任意一个命中就算命中',
    type: MetricHitStrategyDefinitionPO,
    isArray: true,
  })
  @Type(() => MetricHitStrategyDefinitionPO)
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(10)
  @ValidateNested({ each: true })
  hitStrategy: MetricHitStrategyDefinitionPO[];

  // @Column('varchar', {
  //   nullable: false,
  //   length: 45,
  //   name: 'metric_key',
  // })
  // //testDB 无该字段
  // metricKey: string;

  @Column('int', {
    nullable: true,
    default: () => "'0'",
    name: 'org_id',
  })
  @ApiPropertyOptional({ description: '指标归属的组织，如果是用户自己创建的，该字段不为空' })
  @IsOptional()
  orgId?: number;

  @Column('json', {
    nullable: true,
    name: 'details_json',
  })
  @ApiPropertyOptional({ description: '其他设置详情', type: MetricSettingDetailsPO })
  @IsOptional()
  @Type(() => MetricSettingDetailsPO)
  @ValidateNested()
  detailsJson?: MetricSettingDetailsPO;

  @OneToMany(() => MetricDimensionRelationEntity, (relation) => relation.metricEntity)
  dimensionHitStrategies: MetricDimensionRelationEntity[];

  @OneToMany(() => GroupMetricRelationEntity, (relation) => relation.metricEntity)
  groupMetrics: GroupMetricRelationEntity[];
}
