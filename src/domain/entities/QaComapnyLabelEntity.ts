import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { QaCompanyEntity } from './QaCompanyEntity';
import { QaLabelEntity } from './QaLabelEntity';

@Entity('qa_company_label')
export class QaCompanyLabelEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'qa_company_item_id',
  })
  companyItemId: number;

  @Column('int', {
    nullable: false,
    name: 'qa_label_id',
  })
  labelId: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @ManyToOne(() => QaCompanyEntity, (company) => company.companyLabels)
  @JoinColumn({ name: 'qa_company_item_id' })
  qaCompanyEntity: QaCompanyEntity;

  @ManyToOne(() => QaLabelEntity, (label) => label.companyLabels)
  @JoinColumn({ name: 'qa_label_id' })
  qaLabelEntity: QaLabelEntity;
}
