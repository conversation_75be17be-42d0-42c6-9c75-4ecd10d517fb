import { Column, Entity, Jo<PERSON><PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsNotEmpty, IsNumber, ValidateNested } from 'class-validator';
import { PushBusinessTypeEnum } from '@domain/enums/push/PushBusinessTypeEnum';
import { PushRuleJsonPo } from '@domain/model/push/PushRuleJsonPo';
import { Type } from 'class-transformer';
import { MonitorGroupEntity } from './MonitorGroupEntity';

@Entity('push_rule')
export class PushRuleEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  pushRuleId: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'type',
  })
  @ApiProperty({ description: '业务类型：1-风险尽调监控类消息', enum: Object.values(PushBusinessTypeEnum) })
  @IsIn(Object.values(PushBusinessTypeEnum))
  type: PushBusinessTypeEnum = PushBusinessTypeEnum.RiskMonitor;

  @Column('int', {
    nullable: false,
    name: 'business_id',
  })
  @ApiProperty({
    description: '关联业务Id，目前支持分组Id',
    isArray: false,
    type: Number,
    required: false,
  })
  @IsNumber()
  @IsNotEmpty()
  businessId: number;

  @Column('json', {
    nullable: true,
    name: 'rule_Json',
  })
  @ApiPropertyOptional({ description: '推送的规则', type: PushRuleJsonPo })
  @Type(() => PushRuleJsonPo)
  @ValidateNested()
  ruleJson?: PushRuleJsonPo;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty({ description: '创建者' })
  @IsNumber()
  createBy: number;

  @Column('int', {
    nullable: true,
    name: 'update_by',
  })
  @ApiProperty({ description: '更新者' })
  @IsNumber()
  updateBy?: number;

  @ManyToOne(() => MonitorGroupEntity, (monitorGroup) => monitorGroup.pushRules)
  @JoinColumn({ name: 'business_id' })
  monitorGroup: MonitorGroupEntity;
}
