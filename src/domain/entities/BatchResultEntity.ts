import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, IsIn, IsNumber, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { ResultInfoPO } from '@domain/model/batch/po/ResultInfoPO';
import { BatchJobResultTypeEnums } from '@domain/enums/batch/BatchJobResultTypeEnums';

@Entity('batch_result')
@Index('batch_id_type', ['batchId', 'resultType'], { unique: false })
@Index('job_id', ['jobId'], { unique: false })
export class BatchResultEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'result_id',
  })
  resultId: number;

  @Column({
    type: 'int',
    name: 'batch_id',
  })
  batchId: number;

  @Column({
    type: 'int',
    name: 'job_id',
  })
  jobId: number;

  @Column({
    type: 'int',
    name: 'result_type',
  })
  @ApiProperty({
    description: '10 执行成功-付费\\n11 执行成功-未付费\\n12 执行成功-数据重复\\n20 执行失败 （代码执行过程中失败）\\n21 执行失败- 数据不合规\\n',
  })
  @IsNumber()
  @Type(() => Number)
  @IsIn([10, 11, 12, 20, 21])
  resultType: BatchJobResultTypeEnums;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('json', {
    nullable: false,
    name: 'info',
  })
  resultInfo: ResultInfoPO;

  @Column('json', {
    nullable: true,
    name: 'result',
  })
  result: object;

  @Column('varchar', {
    nullable: true,
    name: 'comment',
    length: 500,
  })
  @ApiPropertyOptional({ description: '描述信息，可以是普通描述及错误信息等' })
  @MaxLength(500)
  comment: string | null;

  /**
   * 手动生成的唯一key，暂时不需要存数据库
   */
  @Column('varchar', {
    nullable: true,
    name: 'result_hashkey',
  })
  @ApiPropertyOptional({ description: '唯一键' })
  @MaxLength(45)
  resultHashkey: string;
}
