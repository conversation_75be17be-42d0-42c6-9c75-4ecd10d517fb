# 数据库实体模块 (Database Entities Module)

## 📝 概述

数据库实体模块定义所有数据库表对应的实体类，包含业务规则和数据映射关系。

## 🎯 职责

- 数据库表结构定义
- 实体关系映射 (ORM)
- 业务规则封装
- 数据验证约束
- 生命周期钩子

## 📁 目录结构

```
entities/
├── README.md                    # 本说明文件
├── diligence/                   # 尽调相关实体
├── risk-model/                  # 风险模型实体
├── monitor/                     # 监控相关实体
├── user/                        # 用户相关实体
├── company/                     # 企业相关实体
├── batch/                       # 批处理实体
├── ai-analyzer/                 # AI分析实体
├── group/                       # 分组相关实体
├── message/                     # 消息相关实体
└── schedule/                    # 调度相关实体
```

## 🏗️ 实体设计原则

- **单一职责**: 每个实体代表一个明确的业务概念
- **领域驱动**: 实体包含业务规则和行为
- **数据完整性**: 通过约束确保数据一致性
- **性能优化**: 合理设计索引和关系

## 💡 实体示例

```typescript
@Entity('users')
export class User {
  @PrimaryKey()
  @Property({ type: 'uuid', default: () => 'uuid_generate_v4()' })
  id: string;

  @Property({ unique: true })
  @Index()
  email: string;

  @Property()
  name: string;

  @Property({ hidden: true })
  passwordHash: string;

  @Enum(() => UserRole)
  role: UserRole;

  @Property({ type: 'boolean', default: true })
  isActive: boolean;

  @Property({ type: 'timestamptz' })
  createdAt: Date = new Date();

  @Property({ type: 'timestamptz', onUpdate: () => new Date() })
  updatedAt: Date = new Date();

  // 业务方法
  public isAdmin(): boolean {
    return this.role === UserRole.ADMIN;
  }

  public activate(): void {
    this.isActive = true;
  }

  public deactivate(): void {
    this.isActive = false;
  }
}
```

## 🔗 实体关系

```typescript
// 一对多关系
@OneToMany(() => Company, company => company.owner)
companies = new Collection<Company>(this);

// 多对一关系
@ManyToOne(() => User, { eager: false })
owner: User;

// 多对多关系
@ManyToMany(() => Role, role => role.users, { owner: true })
roles = new Collection<Role>(this);
```

## 📊 实体分类

### 核心业务实体

- **User**: 用户实体
- **Company**: 企业实体
- **Diligence**: 尽调项目实体

### 配置实体

- **RiskModel**: 风险模型配置
- **Monitor**: 监控配置
- **Schedule**: 调度任务配置

### 日志实体

- **OperationLog**: 操作日志
- **AuditLog**: 审计日志
- **Message**: 消息记录

## 🔗 相关模块

- 被使用：@modules/\* (业务服务)
- 依赖：@domain/enums, @domain/value-objects
- 集成：MikroORM、数据库
