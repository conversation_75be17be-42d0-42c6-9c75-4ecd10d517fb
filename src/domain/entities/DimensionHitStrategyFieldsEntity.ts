import { <PERSON>umn, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayMinSize, IsA<PERSON>y, IsIn, IsOptional, IsString } from 'class-validator';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DimensionFieldCompareTypeEnums } from '@domain/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFieldSearchTypeEnums } from '@domain/enums/dimension/DimensionFieldSearchTypeEnums';
import { Type } from 'class-transformer';
import { DimensionHitStrategyEntity } from './DimensionHitStrategyEntity';
import { DimensionFieldsEntity } from './DimensionFieldsEntity';
import { DataCategoryEnums } from '@domain/enums/DataCategoryEnums';
import { DimensionFieldKeyEnums, getUniqueFieldKeys } from '@domain/enums/dimension/dimension.filter.params';

@Entity('dimension_hit_strategy_fields_relation')
export class DimensionHitStrategyFieldsEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  @ApiProperty({ description: '主键' })
  id: number;

  @Column({
    nullable: false,
    type: 'int',
    name: 'strategy_id',
  })
  strategyId: number;

  @Column('int', {
    nullable: true,
    name: 'org_id',
  })
  @ApiProperty({ description: '命中规则的值 归属的组织，如果是用户创建的，该字段不为空' })
  orgId?: number;

  @Column('int', {
    nullable: false,
    name: 'dimension_id',
  })
  @ApiProperty({ description: '维度ID' })
  dimensionId: number;

  @Column('int', {
    nullable: true,
    default: () => "'1'",
    name: 'category',
  })
  @ApiProperty({ description: '模型类别 1 系统模型， 2 用户模型' })
  category: DataCategoryEnums = DataCategoryEnums.System;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'extend_from',
  })
  @ApiPropertyOptional({ description: '完整的继承路径(如果有)' })
  extendFrom: string | null;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'comment',
  })
  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  comment?: string;

  @Column('int', {
    nullable: false,
    name: 'dimension_field_id',
  })
  @ApiProperty({ description: '维度字段ID' })
  dimensionFieldId: number;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'dimension_field_key',
  })
  @ApiProperty({
    description: '字段key, 同一个风险维度下field_key不能重复',
  })
  @IsString()
  dimensionFieldKey: DimensionFieldKeyEnums;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'dimension_field_name',
  })
  @ApiProperty({ description: '字段name' })
  @IsString()
  dimensionFieldName: string;

  @Column('json', {
    nullable: true,
    name: 'options',
  })
  @ApiPropertyOptional({ description: '这里可以配置选项、文本框的约束' })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(200)
  options: Array<any>;

  @Column('int', {
    nullable: false,
    name: 'search_type',
  })
  @ApiProperty({
    description: '0 常规查询\n' + '1 xx 关联关系查询\n' + '2 yy 关联关系查询',
    enum: Object.values(DimensionFieldSearchTypeEnums),
  })
  @IsIn(Object.values(DimensionFieldSearchTypeEnums))
  searchType: DimensionFieldSearchTypeEnums;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ description: '创建时间' })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  @ApiProperty({ description: '更新时间' })
  updateDate: Date;

  @Column('int', {
    nullable: false,
    default: () => "'1'",
    name: 'status',
  })
  @ApiProperty({
    description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'access_scope',
  })
  @ApiProperty({
    description:
      '用户端对filed访问权限：0-完整权限; 1-用户不可见不可修改; 2-用户可见不可修改; 3-仅用于展示策略名称描述; 4-后端该属性不参与结果返回(companyDetail数据源)',
  })
  @IsIn([0, 1, 2, 3, 4])
  accessScope: number;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty({ description: '创建者' })
  @Type(() => Number)
  createBy: number;

  @Column('int', {
    nullable: true,
    name: 'update_by',
  })
  @ApiProperty({ description: '更新者' })
  @Type(() => Number)
  updateBy?: number;

  @Column('datetime', {
    nullable: true,
    name: 'deprecated_date',
  })
  @ApiPropertyOptional({ description: '废弃日期' })
  @IsOptional()
  deprecatedDate?: Date;

  @Column('int', {
    nullable: true,
    name: 'publish_by',
  })
  @ApiPropertyOptional({ description: '发布者' })
  @Type(() => Number)
  publishBy?: number;

  @Column('datetime', {
    nullable: true,
    name: 'published_date',
  })
  publishedDate?: Date;

  @Column('datetime', {
    nullable: true,
    name: 'deprecate_start_date',
  })
  @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  @IsOptional()
  deprecateStartDate?: Date;

  @Column('datetime', {
    nullable: true,
    name: 'modified_date',
  })
  @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  @IsOptional()
  modifiedDate?: Date;

  @Column('json', {
    nullable: false,
    name: 'field_value',
  })
  @ApiProperty({ description: '字段值数组，如果比较类型区间，则第一个是最小值，第二个是最大值', isArray: true })
  fieldValue: Array<any>;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'compare_type',
  })
  @ApiProperty({ description: '比较类型', enum: Object.values(DimensionFieldCompareTypeEnums) })
  @IsIn(Object.values(DimensionFieldCompareTypeEnums))
  compareType: DimensionFieldCompareTypeEnums;

  @ManyToOne(() => DimensionHitStrategyEntity, (dimensionHitStrategyEntity) => dimensionHitStrategyEntity.strategyFields)
  @JoinColumn({ name: 'strategy_id' })
  dimensionHitStrategyEntity: DimensionHitStrategyEntity;

  @ApiProperty({ description: '维度字段基本信息' })
  @ManyToOne(() => DimensionFieldsEntity, (dimensionFieldsEntity) => dimensionFieldsEntity.strategyFieldEntity)
  @JoinColumn({ name: 'dimension_field_id' })
  dimensionField: DimensionFieldsEntity;
}
