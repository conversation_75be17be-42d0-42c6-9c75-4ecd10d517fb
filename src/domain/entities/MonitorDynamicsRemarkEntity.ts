import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { UserEntity } from './UserEntity';
import { ArrayMaxSize, IsArray, IsIn, IsNotEmpty, IsOptional, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { RemarkAttachment } from '@domain/model/monitor/RemarkAttachment';
import { MonitorDynamicHandleWay } from '@domain/enums/monitor/MonitorRiskEnums';

@Entity('monitor_dynamic_remark')
@Index('org_dynamic_index', ['orgId', 'dynamicId'])
export class MonitorDynamicsRemarkEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'dynamic_id',
  })
  @ApiProperty({ description: '风险动态记录uuid', type: String })
  dynamicId: string;

  @Column('tinyint', {
    name: 'grade',
    default: () => '0',
    comment: '跟进等级 0一般, 1-重要, 2 非常重要',
  })
  @ApiProperty({ description: '跟进等级 0一般, 1-重要, 2 非常重要', type: Number })
  @IsIn([0, 1, 2])
  @IsOptional()
  @IsNotEmpty()
  grade: number;

  @Column('tinyint', {
    name: 'way',
    default: () => '1',
    comment: '核实方式： 1-无需核实, 2-电话/短信核实, 3-实地核实, 4-网络核实, 5-其他方式',
  })
  @ApiProperty({
    description: '核实方式： 1-无需核实, 2-电话/短信核实, 3-实地核实, 4-网络核实, 5-其他方式',
    enum: MonitorDynamicHandleWay,
    type: Number,
  })
  @IsIn(Object.values(MonitorDynamicHandleWay))
  @IsNotEmpty()
  @IsOptional()
  way: number;

  @Column('varchar', {
    nullable: false,
    length: 200,
    name: 'comment',
  })
  @ApiProperty({ description: '处理结果', maxLength: 200, type: String, required: false })
  @MaxLength(200)
  @IsOptional()
  comment: string;

  @Column('json', {
    nullable: true,
    name: 'attachments',
  })
  @ApiProperty({ isArray: true, type: RemarkAttachment })
  @Type(() => RemarkAttachment)
  @IsOptional()
  @ArrayMaxSize(10)
  @IsArray()
  attachments: RemarkAttachment[];

  @Column('int', {
    nullable: false,
    name: 'update_by',
    comment: '处理人id',
  })
  @ApiProperty({ description: '处理人Id', type: Number })
  updateBy: number;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  @ApiProperty({ description: '', type: Number })
  updateDate: Date | null;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ description: '', type: Number })
  createDate: Date | null;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'update_by' })
  @ApiProperty({ type: UserEntity, description: '处理人' })
  operator: UserEntity;
}

/**
 * 表结构
 *
 create table monitor_dynamic_remark
 (
 id          int auto_increment
 primary key,
 org_id      int                                    not null,
 dynamic_id  varchar(45)                            not null comment 'monitor_metrics_dynamic.uuid',
 grade       tinyint      default 0                 not null comment '跟进等级 0一般, 1-重要, 2 非常重要',
 way         tinyint      default 1                 not null comment '核实方式：1-无需核实, 2-电话/短信核实, 3-实地核实, 4-网络核实, 5-其他方式',
 comment     varchar(200) default ''                null comment '处理结果',
 attachments json                                   null comment '附件信息',
 update_by   int                                    not null,
 create_date datetime     default CURRENT_TIMESTAMP null,
 update_date datetime     default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP
 )
 comment '合作监控企业动态跟进';

 create index org_dynamic_index on monitor_dynamic_remark (org_id, dynamic_id);
 *
 */
