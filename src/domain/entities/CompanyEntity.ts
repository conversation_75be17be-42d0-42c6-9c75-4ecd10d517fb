import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

@Entity('company')
@Index('company_id', ['companyId'], { unique: true })
export class CompanyEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  companyIntId: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'company_id',
  })
  @ApiProperty({ description: '公司唯一标识' })
  @MaxLength(45)
  companyId: string;

  @Column('varchar', {
    nullable: false,
    length: 500,
    name: 'name',
  })
  @ApiProperty({ description: '公司名称' })
  @MaxLength(500)
  @IsNotEmpty()
  name: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'econkind',
  })
  @ApiProperty({ type: String, description: '企业类型code' })
  @IsOptional()
  econkind: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'econkind_desc',
  })
  @ApiProperty({ type: String, description: '企业类型中文描述' })
  @IsOptional()
  econkindDesc: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'econ_type',
    default: () => "'0'",
  })
  @ApiProperty({ type: String, description: '企业性质' })
  @IsOptional()
  econType: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'enterprise_type',
    default: () => "'0'",
  })
  @ApiProperty({ type: String, description: '机构类型' })
  @IsOptional()
  enterpriseType: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'province',
  })
  @ApiProperty({ type: String, description: '省' })
  @IsOptional()
  province: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'city',
  })
  @ApiProperty({ type: String, description: '市' })
  @IsOptional()
  city: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'district',
  })
  @ApiProperty({ type: String, description: '区' })
  @IsOptional()
  district: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'industry1',
  })
  @ApiProperty({ type: String, description: '行业1' })
  @IsOptional()
  industry1: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'industry2',
  })
  @ApiProperty({ type: String, description: '行业2' })
  @IsOptional()
  industry2: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'industry3',
  })
  @ApiProperty({ type: String, description: '行业3' })
  @IsOptional()
  industry3: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'industry4',
  })
  @ApiProperty({ type: String, description: '行业4' })
  @IsOptional()
  industry4: string | null;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'registcapi',
  })
  @ApiProperty({ type: String, description: '注册资本' })
  @IsOptional()
  registcapi?: string;

  @Column('int', {
    nullable: true,
    name: 'registcapi_amount',
  })
  @ApiProperty({ type: String, description: '注册资本数值' })
  @IsOptional()
  registcapiAmount?: number;

  @Column('datetime', {
    nullable: true,
    name: 'start_date_code',
  })
  @ApiProperty({ type: Date, description: '成立时间' })
  @Type(() => Date)
  @IsOptional()
  startDateCode?: Date;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'status_code',
    default: () => '0',
  })
  @ApiProperty({ type: String, description: '登记状态' })
  @IsOptional()
  statusCode?: string;

  @ApiProperty({ description: '查查信用分' })
  @Column('int', {
    nullable: true,
    name: 'credit_rate',
  })
  creditRate?: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('int', {
    nullable: true,
    name: 'list_status',
    default: () => 2,
  })
  @ApiProperty({ type: String, description: '上市状态:1-已上市,2-未上市' })
  listStatus: number;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'reccap',
  })
  @ApiProperty({ type: String, description: '实缴资本' })
  @IsOptional()
  reccap?: string;

  @Column('int', {
    nullable: true,
    name: 'reccapamount',
  })
  @ApiProperty({ type: String, description: '实缴资本金额数字(万元)' })
  @IsOptional()
  reccapamount?: number;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'scale',
  })
  @ApiProperty({ type: String, description: '企业规模' })
  @IsOptional()
  scale?: string;
}
