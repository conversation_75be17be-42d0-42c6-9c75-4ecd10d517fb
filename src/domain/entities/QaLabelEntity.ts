import { Type } from 'class-transformer';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { QaCompanyLabelEntity } from './QaComapnyLabelEntity';

@Entity('qa_label')
export class QaLabelEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  labelId: number;

  @Column('varchar', {
    nullable: false,
    name: 'label_name',
  })
  labelName: string;

  @Column('varchar', {
    nullable: true,
    name: 'description',
  })
  description?: string;

  @Column('varchar', {
    nullable: false,
    name: 'org_id',
  })
  orgId: string;

  @Column('varchar', {
    nullable: false,
    name: 'product',
  })
  product: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @Type(() => Date)
  createDate: Date;

  @OneToMany(() => QaCompanyLabelEntity, (companyLabel) => companyLabel.qaLabelEntity, { onDelete: 'CASCADE' })
  companyLabels: QaCompanyLabelEntity[];

  @Column('int', {
    nullable: true,
    name: 'user_id',
  })
  createUserId?: number;
}
