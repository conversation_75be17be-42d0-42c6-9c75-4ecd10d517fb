import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsNotEmpty, IsOptional } from 'class-validator';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { Type } from 'class-transformer';
import { RiskModelTypeEnums, RiskModelTypes } from '@domain/enums/RiskModelTypeEnums';
import { DataCategoryEnums } from '@domain/enums/DataCategoryEnums';
import { ProductCodeAllowValues } from '@domain/enums/ProductCodeEnums';
import { GroupEntity } from './GroupEntity';
import { DiligenceResultDefaultSetting, DiligenceResultPO } from '@domain/constants/diligence.constants';
import { DistributedSystemResourceEntity } from './DistributedSystemResourceEntity';

@Entity('risk_model')
export class RiskModelEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'model_id',
  })
  @ApiProperty()
  @IsNotEmpty()
  modelId: number;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'model_name',
  })
  @ApiProperty({ description: '模型名称' })
  modelName: string;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: string;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'comment',
  })
  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  comment: string | null;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty({ description: '创建者' })
  @Type(() => Number)
  createBy: number;

  @Column('int', {
    nullable: true,
    name: 'publish_by',
  })
  @ApiPropertyOptional({ description: '发布者' })
  @Type(() => Number)
  publishBy?: number;

  @Column('int', {
    nullable: true,
    name: 'update_by',
  })
  @ApiProperty({ description: '更新者' })
  @Type(() => Number)
  updateBy?: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('int', {
    nullable: true,
    default: () => "'1'",
    name: 'score_strategy',
  })
  @ApiPropertyOptional({ description: '模型分数计算策略' })
  @IsOptional()
  scoreStrategy: number;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'branch_tier',
  })
  @ApiPropertyOptional({ description: '同一个分组下，模型的级别 0 根目录级别， 1 子级别 ， 0 最高， 数字越大层级越低' })
  branchTier: number;

  @Column('int', {
    nullable: false,
    default: () => "'1'",
    name: 'branch_count',
  })
  @ApiPropertyOptional({ description: '同一个分组下面模型的数量 ' })
  branchCount: number;

  @Column('varchar', {
    nullable: true,
    name: 'branch_code',
  })
  @ApiProperty({ description: '模型分组编码,同一个编码的模型认为是同一个模型的多个版本' })
  branchCode: string;

  @Column('int', {
    nullable: false,
    default: () => "'1'",
    name: 'model_type',
  })
  @ApiPropertyOptional({ description: '模型类型', enum: RiskModelTypes })
  @IsIn(RiskModelTypes)
  modelType: RiskModelTypeEnums = RiskModelTypeEnums.GeneralRiskModel;

  @Column('int', {
    nullable: true,
    default: () => "'1'",
    name: 'category',
  })
  @ApiProperty({ description: '模型类别 1 系统模型， 2 用户模型' })
  category: DataCategoryEnums = DataCategoryEnums.System;

  @Column('int', {
    nullable: true,
    default: () => '0',
    name: 'org_id',
  })
  @ApiProperty({ description: '模型归属的组织, 已发布的系统模型必须有归属的组织， 用户模型也必须有归属的组织' })
  orgId?: number;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'extend_from',
  })
  @ApiPropertyOptional({ description: '完整的继承路径(如果有)' })
  extendFrom: string | null;

  @Column('int', {
    nullable: true,
    default: () => "'1'",
    name: 'version_major',
  })
  versionMajor: number | null;

  @Column('int', {
    nullable: true,
    default: () => "'0'",
    name: 'version_minor',
  })
  versionMinor: number | null;

  @Column('int', {
    nullable: true,
    default: () => "'0'",
    name: 'version_patch',
  })
  versionPatch: number | null;

  @Column('int', {
    nullable: true,
    default: () => "'0'",
    name: 'status',
  })
  @ApiProperty({
    description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;

  @Column('json', {
    nullable: true,
    name: 'result_setting',
  })
  @ApiProperty({ description: '尽调结果等级设置', default: DiligenceResultDefaultSetting })
  resultSetting: DiligenceResultPO[];

  @Column('json', {
    nullable: true,
    name: 'published_details',
    select: false, // 添加这个配置后，默认查询将不会返回该字段
  })
  @ApiProperty({ description: '模型json' })
  publishedContent: JSON;

  @Column('datetime', {
    nullable: true,
    name: 'published_date',
  })
  publishedDate?: Date;

  @Column('datetime', {
    nullable: true,
    name: 'modified_date',
  })
  @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  @IsOptional()
  modifiedDate?: Date;

  @Column('datetime', {
    nullable: true,
    name: 'deprecated_date',
  })
  @ApiPropertyOptional({ description: '废弃日期' })
  @IsOptional()
  deprecatedDate?: Date;

  @Column('datetime', {
    nullable: true,
    name: 'deprecate_start_date',
  })
  @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  @IsOptional()
  deprecateStartDate?: Date;

  @OneToMany(() => GroupEntity, (groupEntity) => groupEntity.riskModelEntity)
  groups?: GroupEntity[];

  @OneToMany(() => DistributedSystemResourceEntity, (entity) => entity.riskModelEntity)
  distributedResource?: DistributedSystemResourceEntity[];
}
