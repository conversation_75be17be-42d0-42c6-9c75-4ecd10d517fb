import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, IsIn, IsNumber, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { BatchJobInfoPO } from '@domain/model/batch/po/BatchJobInfoPO';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';

@Entity('batch_job')
@Index('batch_id', ['batchId', 'status'], { unique: false })
export class BatchJobEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'job_id',
  })
  jobId: number;

  @Column({
    type: 'int',
    name: 'batch_id',
  })
  batchId: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('datetime', {
    nullable: true,
    name: 'start_date',
  })
  startDate: Date;

  @Column('datetime', {
    nullable: true,
    name: 'end_date',
  })
  endDate: Date;

  @Column('datetime', {
    nullable: true,
    name: 'error_date',
  })
  errorDate: Date | null;

  @Column('int', {
    nullable: false,
    name: 'status',
  })
  @ApiProperty({
    description: '0: 待处理, 1: 处理中， 2: 处理完成， 21: 批量任务超时了(仍然被标记成功了，可以重试), 3: 处理失败, 4: 队列中排队, 5: 套餐失效被冻结 ',
  })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1, 2, 3, 4, 5])
  status: BatchStatusEnums;

  @Column('json', {
    nullable: false,
    name: 'job_info',
  })
  jobInfo: BatchJobInfoPO;

  @Column('varchar', {
    nullable: true,
    name: 'comment',
    length: 1000,
  })
  @ApiPropertyOptional({ description: '导出任务失败原因' })
  @MaxLength(1000)
  comment: string | null;
}
