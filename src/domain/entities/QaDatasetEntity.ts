import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsNumber, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { QaTaskEntity } from './QaTaskEntity';
import { QaTypeEnumAllowedValues, QaTypeEnums } from '@modules/ai-intelligence/qa/model/enums/QaTypeEnums';
import { QaDatasetItemEntity } from './QaDatasetItemEntity';

@Entity('qa_dataset')
export class QaDatasetEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty()
  @IsNumber()
  @Type(() => Number)
  createBy: number;

  @Column('varchar', {
    nullable: false,
    length: 64,
    name: 'name',
  })
  @ApiProperty({ description: '名称' })
  name: string;

  @Column('varchar', {
    nullable: false,
    length: 64,
    name: 'product',
  })
  @ApiProperty({ description: '归属产品' })
  product: ProductCodeEnums;

  @Column('int', {
    nullable: false,
    name: 'status',
  })
  @ApiProperty({ description: '0: 禁用, 1: 可用' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1])
  status: number;

  @Column('varchar', {
    nullable: true,
    name: 'comment',
  })
  @ApiPropertyOptional({ description: '描述信息，可以是普通描述及错误信息等' })
  @MaxLength(200)
  comment: string | null;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  @Type(() => Date)
  updateDate: Date;

  @OneToMany(() => QaDatasetItemEntity, (item) => item.qaDatasetEntity)
  items: QaDatasetItemEntity[];

  @Column('int', {
    nullable: true,
    name: 'qa_type',
    comment: '数据集类型： 0-监控，1-尽调',
  })
  @ApiProperty({ description: '数据集类型： 0-监控，1-尽调', enum: QaTypeEnumAllowedValues })
  @IsIn(QaTypeEnumAllowedValues)
  qaType: QaTypeEnums;

  @Column('int', {
    nullable: true,
    name: 'ref_monitor_group_id',
    comment: '关联的监控分组ID(qaType=1时)',
  })
  @ApiPropertyOptional({ description: '关联的监控分组ID(qaType=1时), 当数据集是监控数据集时，需要关联监控分组' })
  refMonitorGroupId?: number;

  @OneToMany(() => QaTaskEntity, (task) => task.qaDatasetEntity)
  qaTasks: QaTaskEntity[];

  @Column('int', {
    nullable: true,
    name: 'user_id',
  })
  createUserId?: number;
}
