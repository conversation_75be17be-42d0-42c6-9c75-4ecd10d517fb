import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsOptional, ValidateNested } from 'class-validator';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { Type } from 'class-transformer';
import { DimensionHitStrategyFieldsEntity } from './DimensionHitStrategyFieldsEntity';
import { MetricDimensionRelationEntity } from './MetricDimensionRelationEntity';
import { DimensionDefinitionEntity } from './DimensionDefinitionEntity';
import { DataCategoryEnums } from '@domain/enums/DataCategoryEnums';
import { QueryHitStrategyPO } from '@domain/model/QueryHitStrategyPO';
import { StrategyRoleEnums } from '@domain/enums/StrategyRoleEnums';
import { StrategyExtendPO } from '@domain/model/StrategyExtendPO';

@Entity('dimension_hit_strategy')
export class DimensionHitStrategyEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'strategy_id',
  })
  strategyId: number;

  @Column('int', {
    nullable: false,
    name: 'dimension_id',
  })
  @ApiProperty({ description: '维度ID' })
  dimensionId: number;

  @Column('int', {
    nullable: true,
    default: () => '0',
    name: 'org_id',
  })
  @ApiProperty({ description: '命中规则归属的组织，如果是用户创建的，该字段不为空' })
  orgId?: number;

  @Column('int', {
    nullable: true,
    name: 'publish_by',
  })
  @ApiPropertyOptional({ description: '发布者' })
  @Type(() => Number)
  publishBy?: number;

  @Column('datetime', {
    nullable: true,
    name: 'published_date',
  })
  publishedDate?: Date;

  @Column('varchar', {
    nullable: false,
    length: 80,
    name: 'strategy_name',
  })
  @ApiProperty({ description: '策略名称' })
  strategyName: string;

  @Column('int', {
    nullable: false,
    default: () => "'1'",
    name: 'strategy_role',
  })
  @ApiProperty({ description: '维度策略角色 1 普通策略维度，排查正常计数和结果展示； 2 仅过滤策略维度，只参与指标命中的判定，不参与排查技术和结果展示;' })
  strategyRole: StrategyRoleEnums = StrategyRoleEnums.Normal;

  @ApiProperty({ description: '展示信息时候的模板' })
  @Column('varchar', {
    nullable: false,
    length: 200,
    name: 'template',
    default: '匹配到目标主体 <em class="#level#">【#name#】</em>',
  })
  template? = '匹配到目标主体 <em class="#level#">【#name#】</em>';

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'comment',
  })
  @ApiPropertyOptional({ description: '备注' })
  @IsOptional()
  comment?: string;

  @Column('int', {
    nullable: true,
    default: () => "'1'",
    name: 'category',
  })
  @ApiProperty({ description: '模型类别 1 系统模型， 2 用户模型' })
  category: DataCategoryEnums = DataCategoryEnums.System;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'extend_from',
  })
  @ApiPropertyOptional({ description: '完整的继承路径(如果有)' })
  extendFrom: string | null;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ description: '创建时间' })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  @ApiProperty({ description: '更新时间' })
  updateDate: Date;

  @Column('int', {
    nullable: false,
    default: () => "'1'",
    name: 'status',
  })
  @ApiProperty({
    description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;

  @Column('datetime', {
    nullable: true,
    name: 'deprecated_date',
  })
  @ApiPropertyOptional({ description: '废弃日期' })
  @IsOptional()
  deprecatedDate?: Date;

  @Column('datetime', {
    nullable: true,
    name: 'deprecate_start_date',
  })
  @ApiPropertyOptional({ description: '进入维护阶段的日期' })
  @IsOptional()
  deprecateStartDate?: Date;

  @Column('datetime', {
    nullable: true,
    name: 'modified_date',
  })
  @ApiPropertyOptional({ description: '记录用户主动修改时间，不同与update_date' })
  @IsOptional()
  modifiedDate?: Date;

  @Column('int', {
    nullable: false,
    name: 'create_by',
  })
  @ApiProperty({ description: '创建者' })
  @Type(() => Number)
  createBy: number;

  @Column('int', {
    nullable: true,
    name: 'update_by',
  })
  @ApiProperty({ description: '更新者' })
  @Type(() => Number)
  updateBy?: number;

  @Column('json', {
    nullable: false,
    name: 'hit_strategy',
  })
  @ApiProperty({
    description: '命中规则,  维度多字段间的命中规则只能是must, 特殊维度单独开放',
    type: QueryHitStrategyPO,
    isArray: false,
  })
  @Type(() => QueryHitStrategyPO)
  @ValidateNested()
  fieldHitStrategy: QueryHitStrategyPO;

  @Column('json', {
    nullable: true,
    name: 'extend_json',
  })
  @ApiPropertyOptional({ description: '其他设置详情', type: StrategyExtendPO })
  @IsOptional()
  @Type(() => StrategyExtendPO)
  @ValidateNested()
  extendJson?: StrategyExtendPO;

  @ApiProperty({ description: '维度字段基本信息' })
  @ManyToOne(() => DimensionDefinitionEntity, (dimensionFieldsEntity) => dimensionFieldsEntity.dimensionHitStrategy)
  @JoinColumn({ name: 'dimension_id' })
  dimensionDef: DimensionDefinitionEntity;

  @ApiProperty({ description: '命中条件组', type: DimensionHitStrategyFieldsEntity, isArray: true })
  @OneToMany(() => DimensionHitStrategyFieldsEntity, (strategyField) => strategyField.dimensionHitStrategyEntity)
  strategyFields: DimensionHitStrategyFieldsEntity[];

  @OneToMany(() => MetricDimensionRelationEntity, (relation) => relation.dimensionHitStrategyEntity)
  dimensionHitStrategies: MetricDimensionRelationEntity[];
}
