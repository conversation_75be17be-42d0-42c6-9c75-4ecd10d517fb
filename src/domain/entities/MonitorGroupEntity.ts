import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProductCodeAllowValues, ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { IsIn, IsOptional } from 'class-validator';
import { MonitorStatusEnums } from '@domain/enums/monitor/MonitorStatusEnums';
import { RiskModelEntity } from './RiskModelEntity';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { PushRuleEntity } from './PushRuleEntity';
import { MonitorGroupScopeEnum, PushEnableEnum } from '@domain/enums/push/PushEnableEnum';
import { MonitorGroupUserEntity } from './MonitorGroupUserEntity';

@Entity('monitor_group')
export class MonitorGroupEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  monitorGroupId: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'owner_id',
  })
  ownerId: number;

  @Column('int', {
    nullable: true,
    name: 'changes_count',
    default: 0,
  })
  changesCount: number;

  @Column('int', {
    nullable: true,
    name: 'company_count',
    default: 0,
  })
  companyCount: number;

  @ApiProperty({
    description: '分组状态： 0-暂停，1-启用, 2-禁用',
    type: Number,
    isArray: false,
    required: false,
    enum: MonitorStatusEnums,
  })
  @IsIn(Object.values(MonitorStatusEnums))
  @IsOptional()
  @Column('int', {
    nullable: false,
    name: 'monitor_status',
  })
  monitorStatus?: MonitorStatusEnums;

  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'name',
  })
  @ApiProperty({ description: '名称' })
  name: string;

  // @Column('varchar', {
  //   nullable: false,
  //   name: 'push_method',
  // })
  // @ApiPropertyOptional({ description: '推送方式, 逗号分割的字符串' })
  // pushMethod: string;

  @ApiPropertyOptional({ description: '监控模型id' })
  @Column('int', {
    nullable: true,
    name: 'monitor_model_id',
  })
  monitorModelId?: number;

  @Column('int', {
    nullable: true,
    default: () => "'1'",
    name: 'status',
  })
  @ApiProperty({
    description: '数据状态：0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;

  //
  // @Column('int', {
  //   nullable: false,
  //   name: 'run_frequency',
  // })
  // @ApiPropertyOptional({ description: '运行频率, 1 每天， 2 每周， 3 每个月' })
  // pushFrequency: number;
  //
  // @Column('int', {
  //   nullable: false,
  //   name: 'run_at_hour',
  // })
  // @ApiPropertyOptional({ description: '在每天的几点跑任务' })
  // @IsOptional()
  // @IsIn([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23])
  // runAtHour: number;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'order',
    comment: '排序',
  })
  @ApiProperty({ description: '排序' })
  order: number;

  @Column('tinyint', {
    name: 'push_enable',
    nullable: false,
    comment: '是否开启消息推送： 0-不开启，1-开启',
    width: 1,
    default: () => "'0'",
  })
  @ApiProperty({
    description: '是否开启消息推送： 0-不开启，1-开启',
    type: Number,
    isArray: false,
    required: false,
    enum: PushEnableEnum,
  })
  @IsIn(Object.values(PushEnableEnum))
  @IsOptional()
  pushEnable: PushEnableEnum;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'comment',
  })
  comment: string;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('int', {
    nullable: true,
    name: 'update_by',
  })
  updateBy: number;

  @Column('int', {
    nullable: true,
    name: 'scope',
  })
  @Column('tinyint', {
    name: 'scope',
    nullable: false,
    comment: '监控分组的可见范围： -1-全部可见，1-指定人可见，2-指定人不可见',
    width: 1,
    default: () => "'-1'",
  })
  scope: MonitorGroupScopeEnum;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: ProductCodeEnums;

  @OneToOne(() => RiskModelEntity, (riskModelEntity) => riskModelEntity.modelId)
  @JoinColumn({ name: 'monitor_model_id' })
  riskModelEntity: RiskModelEntity;

  @OneToMany(() => PushRuleEntity, (pushRule) => pushRule.monitorGroup)
  @JoinColumn({ name: 'id' })
  pushRules: PushRuleEntity[];

  @OneToMany(() => MonitorGroupUserEntity, (monitorGroupUser) => monitorGroupUser.monitorGroup)
  @JoinColumn({ name: 'id' })
  monitorGroupUsers: MonitorGroupUserEntity[];

  /* @OneToMany(() => MonitorGroupCompanyEntity, (c) => c.monitorGroup, { onDelete: 'CASCADE', onUpdate: 'RESTRICT' })
  @ApiProperty({ type: MonitorGroupCompanyEntity, isArray: true })
  @Type(() => MonitorGroupCompanyEntity)
  monitorCompanies: MonitorGroupCompanyEntity[];*/
}
