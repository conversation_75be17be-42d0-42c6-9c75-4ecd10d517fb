// import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
//
// @Entity('data_source', { schema: 'qcc_scorecard_dev' })
// export class DataSourceEntity {
//   @PrimaryGeneratedColumn({
//     type: 'int',
//     name: 'id',
//   })
//   id: number;
//
//   @Column('varchar', {
//     nullable: false,
//     length: 45,
//     name: 'source_name',
//   })
//   sourceName: string;
//
//   @Column('int', {
//     nullable: false,
//     name: 'source_type',
//     comment: '1  es\\n2 api\\n3 mysql\\n4 postgres\\n5 mongodb',
//   })
//   sourceType: number;
//
//   @Column('varchar', {
//     nullable: true,
//     length: 45,
//     name: 'comment',
//   })
//   comment: string | null;
//
//   @Column('varchar', {
//     nullable: true,
//     length: 45,
//     name: 'url',
//   })
//   url: string | null;
//
//   @Column('varchar', {
//     nullable: true,
//     length: 45,
//     name: 'username',
//   })
//   username: string | null;
//
//   @Column('varchar', {
//     nullable: true,
//     length: 45,
//     name: 'passwd',
//   })
//   passwd: string | null;
//
//   @Column('datetime', {
//     nullable: false,
//     default: () => 'CURRENT_TIMESTAMP',
//     name: 'create_date',
//   })
//   createDate: Date;
//
//   @Column('datetime', {
//     nullable: false,
//     default: () => 'CURRENT_TIMESTAMP',
//     name: 'update_date',
//   })
//   updateDate: Date;
//
//   @Column('json', {
//     nullable: true,
//     name: 'call_params',
//   })
//   callParams: object | null;
// }
