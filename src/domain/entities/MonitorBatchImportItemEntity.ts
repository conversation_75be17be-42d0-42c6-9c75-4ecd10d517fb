import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserEntity } from './UserEntity';
import { IsDate, IsIn, IsNumber, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { ProductCodeAllowValues, ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { MonitorGroupEntity } from './MonitorGroupEntity';
import { MonitorCompanyStatusEnums } from '@domain/enums/monitor/MonitorCompanyStatusEnums';
import { ImportParseErrorItem } from '@domain/model/verification/VerificationImportFilePo';

@Entity('monitor_batch_import_item')
export class MonitorBatchImportItemEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column({
    type: 'int',
    name: 'batch_id',
  })
  batchId: number;

  @Column('varchar', {
    nullable: false,
    length: 500,
    name: 'comp_name',
  })
  @MaxLength(500)
  companyName: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'comp_id',
  })
  companyId: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'comp_credit_code',
  })
  compCreditCode: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('tinyint', {
    nullable: false,
    name: 'flag',
  })
  @ApiProperty({
    description:
      '1-匹配成功,21-匹配失败(未匹配到企业名称),22-匹配失败(企业名称和统一社会信用代码不一致),23-匹配失败(数据核验重复), 24-匹配失败(数据格式错误), 25-匹配失败(不支持的企业)',
  })
  @IsNumber()
  @Type(() => Number)
  @IsIn([1, 0])
  flag: number;

  @Column('tinyint', {
    nullable: false,
    name: 'match_by',
  })
  @ApiProperty({ description: '0-名称匹配 1-曾用名匹配' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1])
  matchBy: number;

  @Column('json', {
    nullable: true,
    name: 'parsed_item',
  })
  @ApiProperty({ description: 'excel解析后的条目数据' })
  parsedItem: ImportParseErrorItem;

  @Column('varchar', {
    nullable: false,
    name: 'product_code',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: ProductCodeEnums;

  @Column('varchar', {
    nullable: false,
    name: 'company_status',
  })
  companyStatus: string;

  @Column('int', {
    nullable: true,
    name: 'is_success',
  })
  isSuccess: number;

  @Column('varchar', {
    nullable: true,
    name: 'error_msg',
  })
  errorMsg: string;
}
