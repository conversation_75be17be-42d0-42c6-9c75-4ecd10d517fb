// import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
// import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
// import { IsIn, IsNumber, MaxLength } from 'class-validator';
// import { Type } from 'class-transformer';
// import { DatasetItemEntity } from './DatasetItemEntity';

// @Entity('dataset')
// export class DatasetEntity {
//   @PrimaryGeneratedColumn({
//     type: 'int',
//     name: 'id',
//   })
//   id: number;

//   @Column('int', {
//     nullable: false,
//     name: 'org_id',
//   })
//   orgId: number;

//   @Column('int', {
//     nullable: false,
//     name: 'create_by',
//   })
//   @ApiProperty()
//   @IsNumber()
//   @Type(() => Number)
//   createBy: number;

//   @Column('varchar', {
//     nullable: false,
//     length: 64,
//     name: 'name',
//   })
//   @ApiProperty({ description: '名称' })
//   name: string;

//   @Column('varchar', {
//     nullable: false,
//     length: 64,
//     name: 'product',
//   })
//   @ApiProperty({ description: '租户归属产品' })
//   product: string;

//   @Column('int', {
//     nullable: false,
//     name: 'status',
//   })
//   @ApiProperty({ description: '0: 公司, 1: 人员' })
//   @IsNumber()
//   @Type(() => Number)
//   @IsIn([0, 1])
//   type: number;

//   @Column('varchar', {
//     nullable: true,
//     name: 'comment',
//   })
//   @ApiPropertyOptional({ description: '描述信息，可以是普通描述及错误信息等' })
//   @MaxLength(200)
//   comment: string | null;

//   @Column('datetime', {
//     nullable: false,
//     default: () => 'CURRENT_TIMESTAMP',
//     name: 'create_date',
//   })
//   @Type(() => Date)
//   createDate: Date;

//   @Column('datetime', {
//     nullable: false,
//     default: () => 'CURRENT_TIMESTAMP',
//     name: 'update_date',
//   })
//   updateDate: Date;

//   @OneToMany(() => DatasetItemEntity, (item) => item.datasetEntity)
//   items: DatasetItemEntity[];
// }
