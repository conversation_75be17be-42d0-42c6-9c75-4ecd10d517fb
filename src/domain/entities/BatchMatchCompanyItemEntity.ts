import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsIn, IsNumber, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';
import { ParsedRecordBase, ParseErrorItem } from '@domain/model/batch/po/parse/ParsedRecordBase';

@Entity('batch_match_company_item')
export class BatchMatchCompanyItemEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @Column({
    type: 'int',
    name: 'batch_id',
  })
  batchId: number;

  @Column('varchar', {
    nullable: true,
    length: 500,
    name: 'name',
  })
  @MaxLength(500)
  name: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'company_id',
  })
  companyId: string;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  @ApiProperty({ type: Date })
  @IsDate()
  @Type(() => Date)
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('tinyint', {
    nullable: false,
    name: 'flag',
  })
  @ApiProperty({ description: '0-匹配成功 1-不支持 2-匹配失败' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1, 2])
  flag: number;

  @Column('tinyint', {
    nullable: false,
    name: 'match_by',
  })
  @ApiProperty({ description: '0-名称匹配 1-统一社会信用代码匹配 2-注册号匹配 3-曾用名匹配' })
  @IsNumber()
  @Type(() => Number)
  @IsIn([0, 1, 2, 3])
  matchBy: number;

  @Column('json', {
    nullable: true,
    name: 'parsed_item',
  })
  @ApiProperty({ description: 'excel解析后的条目数据' })
  parsedItem: ParsedRecordBase | ParseErrorItem;
}
