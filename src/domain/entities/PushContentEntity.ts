import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsIn, ValidateNested } from 'class-validator';
import { HitPushRulePo } from '@modules/communication/push/po/HitPushSettingPo';
import { DataStatusAllowedStatus, DataStatusEnums } from '@domain/enums/DataStatusEnums';

@Entity('push_content')
export class PushContentEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  pushContentId: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('int', {
    nullable: false,
    name: 'push_rule_id',
  })
  pushRuleId: number;

  @Column('json', {
    nullable: true,
    name: 'pushed_info_json',
  })
  @ApiPropertyOptional({ description: '已经推送的信息', type: HitPushRulePo })
  @Type(() => HitPushRulePo)
  @ValidateNested()
  pushedInfoJson?: HitPushRulePo;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column('int', {
    nullable: false,
    default: () => "'0'",
    name: 'status',
  })
  @ApiProperty({
    description: '数据状态：0-未推送, 1-已推送',
    enum: DataStatusAllowedStatus,
  })
  @IsIn(DataStatusAllowedStatus)
  status: DataStatusEnums;
}
