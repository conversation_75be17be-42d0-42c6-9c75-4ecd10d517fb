import { ApiProperty, ApiPropertyOptional, IntersectionType, PartialType, PickType } from '@nestjs/swagger';
// import { PersonOrgCompanyEntity } from 'libs/entities/PersonOrgCompanyEntity';
import { DimensionTypeEnums } from './DimensionTypeEnums';
import { IsIn, IsOptional, MaxLength } from 'class-validator';

// extends IntersectionType(
//   PickType(PersonOrgCompanyEntity, ['personId', 'companyId', 'status'] as const),
//   PartialType(PickType(PersonOrgCompanyEntity, ['verifyType', 'comment'] as const)),
// )
export class MarkPersonModel {
  @ApiProperty({ description: '当前尽调结果的ID' })
  diligenceId: number;

  @ApiProperty({
    description: '当前维度的 dimensionKey(核实人员时候是否需要传该字段)',
    //核实人员维度，包含疑似利益冲突、疑似被处罚员工-在外任职、疑似被处罚员工-对外投资
    enum: ['SuspectedInterestConflict', 'PunishedEmployeesWorkingOutside', 'PunishedEmployeesForeignInvestment'],
  })
  @IsIn(['SuspectedInterestConflict', 'PunishedEmployeesWorkingOutside', 'PunishedEmployeesForeignInvestment'])
  key: DimensionTypeEnums;

  @ApiProperty({ description: '当前公司名称' })
  companyName: string;

  @ApiProperty({ type: String, description: '人员keyNo' })
  keyNo: string;

  @ApiPropertyOptional({ description: '如果该字段不为空，会获取该id对应的尽调结果(快照ID)' })
  @MaxLength(100)
  @IsOptional()
  snapshotId?: string;

  @ApiProperty({ description: '人员id' })
  personId: number;

  @ApiProperty({ description: '公司ID' })
  @MaxLength(50)
  companyId: string;

  @ApiProperty({ description: '0: 无关系（确认非本人）, 1: 有关系（确认本人）' })
  @IsIn([0, 1])
  status: number;

  @ApiProperty({ description: '1: 电话核实, 2: 实地核实, 3: 网络核实, 4: 其他, 5: 个人履历, 6: 员工Workday' })
  @IsIn([1, 2, 3, 4, 5, 6])
  verifyType?: number;

  @ApiProperty({ type: String, description: '备注' })
  @IsOptional()
  comment?: string | null;
}
