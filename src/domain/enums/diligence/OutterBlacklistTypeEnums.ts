import { DimensionTypeEnums } from './DimensionTypeEnums';

export type OutterBlacklistTypeEnums =
  | DimensionTypeEnums.AntiFraud
  | DimensionTypeEnums.FinancialInvolved
  | DimensionTypeEnums.SafetyProductionEnterprise
  | DimensionTypeEnums.CustomsList
  | DimensionTypeEnums.InspectionAuthority
  | DimensionTypeEnums.QualityCredit
  | DimensionTypeEnums.TransportCredit
  | DimensionTypeEnums.MigrantWorkers
  | DimensionTypeEnums.Statistics
  | DimensionTypeEnums.EnvironmentalProtection
  | DimensionTypeEnums.LaborGuarantee
  | DimensionTypeEnums.ListedCompanyIllegal
  | DimensionTypeEnums.EcBlacklist;

// export const getOutterBlacklistTypes = (): string[] => {
//   return [DimensionTypeEnums.AntiFraud
//     , DimensionTypeEnums.FinancialInvolved
//     , DimensionTypeEnums.SafetyProductionEnterprise
//     , DimensionTypeEnums.CustomsList
//     , DimensionTypeEnums.InspectionAuthority
//     , DimensionTypeEnums.QualityCredit
//     , DimensionTypeEnums.TransportCredit
//     , DimensionTypeEnums.MigrantWorkers
//     , DimensionTypeEnums.Statistics
//     , DimensionTypeEnums.EnvironmentalProtection
//     , DimensionTypeEnums.LaborGuarantee
//     , DimensionTypeEnums.ListedCompanyIllegal
//     , DimensionTypeEnums.EcBlacklist];
// };
