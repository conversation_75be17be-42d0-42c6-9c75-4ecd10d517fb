import { NebulaRelatedEdgeEnums } from './NebulaRelatedEdgeEnums';

export enum RelatedTypeEnums {
  /** 主要人员控制企业 */
  PrincipalControl = 'PrincipalControl',
  /** 法定代表人控制企业 */
  LegalRepresentativeControl = 'LegalRepresentativeControl',
  /** 实际控制人控制企业(非国资实控人) */
  ActualControllerControl = 'ActualControllerControl',
  /** 实际控制人控制企业(非国资实控人)（新） */
  AC = 'AC',
  /** 受益人控制企业 */
  BeneficiaryControl = 'BeneficiaryControl',
  /** 分支机构 */
  Branch = 'Branch',
  /** 母公司（股东信息(股比>50%)） */
  MotherCompanyMajorityShareholder = 'MotherCompanyMajorityShareholder',
  /** 母公司控制企业 */
  MotherCompanyControl = 'MotherCompanyControl',
  // 子公司（对外投资(>50%的企业)）
  MajorityInvestment = 'MajorityInvestment',
  /** 包含以上所有从图谱或取到的关联方 */
  RelatedMember = 'RelatedMember',
  /** 对外投资企业(C端接口) */
  InvestCompany = 'InvestCompany',
  /** 主体控制企业(C端接口) */
  ControlCompany = 'ControlCompany',
  /** 实际控制人(C端接口,包含自然人)) */
  ActualController = 'ActualController',
  /** 大股东(C端接口,包含自然人)) */
  MajorShareholder = 'MajorShareholder',
  /** 实控路线企业(C端接口,实控路线上的大股东企业，也就是母公司及祖母公司，直到实控人) */
  ShareholderChain = 'ShareholderChain',
  /** 上市主体企业(C端接口) */
  StockControlCompany = 'StockControlCompany',
  /** 控股股东(持股比例>50%的股东)) */
  ControllingShareholder = 'ControllingShareholder',
  /** 法人(C端接口) */
  LegalPerson = 'LegalPerson',
  /** 公司董监高(C端接口) */
  Employees = 'Employees',
  /** 担保方(C端接口) */
  Guarantor = 'Guarantor',
  /** 上交所规则关联方 */
  SSERelatedParties = 'SSERelatedParties',
}

// 定义注解
export const relatedTypeAnnotations = {
  [RelatedTypeEnums.PrincipalControl]: '主要人员控制企业',
  [RelatedTypeEnums.LegalRepresentativeControl]: '法定代表人控制企业',
  [RelatedTypeEnums.ActualControllerControl]: '实际控制人控制企业',
  [RelatedTypeEnums.BeneficiaryControl]: '受益人控制企业',
  [RelatedTypeEnums.Branch]: '分支机构',
  [RelatedTypeEnums.MotherCompanyMajorityShareholder]: '母公司（股东信息(股比>50%)）',
  [RelatedTypeEnums.MotherCompanyControl]: '母公司控制企业',
  [RelatedTypeEnums.MajorityInvestment]: '子公司（对外投资(>50%的企业)）',
  [RelatedTypeEnums.RelatedMember]: '关联方成员包含以上所有',
  [NebulaRelatedEdgeEnums.FinalBenefit]: '受益人控制企业',
  [NebulaRelatedEdgeEnums.Legal]: '法定代表人控制企业',
  [NebulaRelatedEdgeEnums.ActualController]: '实际控制人',
  // [NebulaRelatedEdgeEnums.Branch]: '分支机构',
  // [NebulaRelatedEdgeEnums.MotherCompanyMajorityShareholder]: '母公司（股东信息(股比>50%)）',
  // [NebulaRelatedEdgeEnums.MotherCompanyControl]: '母公司控制企业',
  // [NebulaRelatedEdgeEnums.MajorityInvestment]: '子公司（对外投资(>50%的企业)）',
  [NebulaRelatedEdgeEnums.Employ]: '主要人员控制企业',
  [RelatedTypeEnums.InvestCompany]: '对外投资企业',
  [RelatedTypeEnums.ControlCompany]: '控制企业',
  [RelatedTypeEnums.ShareholderChain]: '实控路线企业',
  [RelatedTypeEnums.MajorShareholder]: '大股东',
  [RelatedTypeEnums.StockControlCompany]: '上市主体',
  [RelatedTypeEnums.ControllingShareholder]: '控股股东',
  [RelatedTypeEnums.LegalPerson]: '法人',
  [RelatedTypeEnums.Employees]: '董监高',
  [RelatedTypeEnums.Guarantor]: '担保方',
};
