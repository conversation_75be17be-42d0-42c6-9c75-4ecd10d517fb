export enum NebulaRelatedEdgeEnums {
  /** 分支机构 */
  Branch = 'Branch',
  /** 主要人员控制企业 */
  Employ = 'Employ',
  /** 历史主要人员控制企业 */
  HisEmploy = 'HisEmploy',
  /** 对外投资 */
  Invest = 'Invest',
  /** 历史对外投资 */
  HisInvest = 'HisInvest',
  /** 法人 */
  Legal = 'Legal',
  /** 历史法人 */
  HisLegal = 'HisLegal',
  /** 股东 */
  Shareholder = 'Shareholder',
  /** 历史股东 */
  HisShareholder = 'HisShareholder',
  /** 实际控制人 */
  ActualController = 'ActualController',
  /** 实际控制人(新) */
  AC = 'AC',
  /** 受益人 */
  FinalBenefit = 'FinalBenefit',
  /** 相同地址企业 */
  HasAddress = 'HasAddress',
  /** 相同电话企业 */
  HasPhone = 'HasPhone',
  /** 相同邮箱企业 */
  HasEmail = 'HasEmail',
  /** 裁判文书 */
  HasJudgement = 'HasJudgement',
  /** 控制企业 */
  Hold = 'Hold',
  /** 母公司控制企业 */
  MotherCompanyControl = 'MotherCompanyControl',
  /** 母公司 */
  MotherCompanyMajorityShareholder = 'MotherCompanyMajorityShareholder',
  /** 子公司（投资股比 50%） */
  MajorityInvestment = 'MajorityInvestment',
  /** 关联方成员，包含高管、董事、监事、高级管理人员，子公司母公司 */
  RelatedMember = 'RelatedMember',
}
