import { ApiProperty } from '@nestjs/swagger';
import { DimensionRiskLevelEnum } from '../diligence/DimensionRiskLevelEnum';
import { GroupMetricScorePO } from '@domain/model/group/GroupMetricScorePO';

// /**
//  * 新版的维度分组（先按照 风险级别分组，再按照以及维度二次分组）
//  */
// export class GroupDimensionPO {
//   @ApiProperty({ description: '一级维度的分类', type: DimensionLevel1Enums, enum: values(DimensionLevel1Enums) })
//   groupKey: DimensionLevel1Enums;
//   @ApiProperty({ description: '一组维度的分数详情', type: DimensionScorePO, isArray: true })
//   scoreDetails?: DimensionScorePO[];
//   @ApiProperty({ description: '风险的等级', type: DimensionRiskLevelEnum, enum: values(DimensionRiskLevelEnum) })
//   level: DimensionRiskLevelEnum;
// }

export class LevelGroupDimensionPO {
  @ApiProperty({ description: '高风险项', type: GroupMetricScorePO, isArray: true })
  [DimensionRiskLevelEnum.High]: GroupMetricScorePO[];
  @ApiProperty({ description: '中风险项', type: GroupMetricScorePO, isArray: true })
  [DimensionRiskLevelEnum.Medium]: GroupMetricScorePO[];
  @ApiProperty({ description: '低风险项', type: GroupMetricScorePO, isArray: true })
  [DimensionRiskLevelEnum.Alert]: GroupMetricScorePO[];
}
