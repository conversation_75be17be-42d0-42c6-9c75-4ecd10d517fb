/* eslint-disable @typescript-eslint/naming-convention */
export enum FieldParseErrorTypeEnums {
  Required = 'miss_required', // 缺少必要字段
  Unmatched_Format = 'unmatched_format', // 数据格式不对
  Duplicated = 'duplicated', // 数据重复
  Failed_Valid = 'failed_valid', // 数据格式不对
  Unmatched_Company = 'unmatched_company', // 未匹配到的企业 // companyName
  Unmatched_CreditCode = 'unmatched_creditCode', // 统一社会信用代码不一致  // creditcode
  Unsupported_Company = 'Unsupported_Company', // 不支持的企业  // companyName
  CREDIT_CODE_PATTERN_FORMAT = 'credit_code_pattern_format', // 统一社会信用代码格式不正确  // creditcode
  ID_CARD_PATTERN_FORMAT = 'id_card_pattern_format', // 身份证格式不正确  //personIdcard
  Failed_Empty = 'Failed_Empty', // 数据为空
}
