/* eslint-disable @typescript-eslint/naming-convention */
export enum BatchBusinessTypeEnums {
  /**
   * 任务-批量排查-输入框手动填写
   */
  Diligence_ID = 11, // 批量排查-传入
  Diligence_Continuous = 14, // 风险监控(持续尽调)
  Diligence_PDF_Export = 34, // 洞察 pdf 生成报告(PDF导出)
  monitor_Related_Company = 35,
  Verification_Import = 36, // 人企核验-批量导入核验
  Verification_Result_Regular_Export = 37, // 人企核验-常规核验-核验结果-导出
  Verification_Result_Deep_Export = 38, // 人企核验-深度核验-核验结果-导出
  Verification_Record_Export = 39, // 人企核验-核验记录-导出
  Verification_Regular_Import = 40, // 人企核验-常规核验-批量导入核验
  Verification_Deep_Import = 41, // 人企核验-深度核验-批量导入核验
  Verification_Package_Export = 42, // 人企核验-套餐消费-导出
  Monitor_Import = 43, // 监控-批量导入
  Monitor_Failed_Export = 44, // 监控-批量导出失败数据
}

/**
 * 批量任务对象消息标题
 */
export const BatchBusinessMessageTitle = {
  [BatchBusinessTypeEnums.Diligence_ID]: '批量排查-输入公司ID',
  [BatchBusinessTypeEnums.Diligence_Continuous]: '风险监控',
  [BatchBusinessTypeEnums.Diligence_PDF_Export]: '风险洞察报告',
  [BatchBusinessTypeEnums.monitor_Related_Company]: '监控关联方通知',
  [BatchBusinessTypeEnums.Verification_Regular_Import]: '导入-人企常规核验',
  [BatchBusinessTypeEnums.Verification_Deep_Import]: '导入-人企深度核验',
  [BatchBusinessTypeEnums.Verification_Result_Regular_Export]: '人企核验-常规核验结果导出',
  [BatchBusinessTypeEnums.Verification_Result_Deep_Export]: '人企核验-深度核验结果导出',
  [BatchBusinessTypeEnums.Verification_Record_Export]: '人企核验-核验记录导出',
  [BatchBusinessTypeEnums.Verification_Package_Export]: '人企核验-消费明细导出',
  [BatchBusinessTypeEnums.Monitor_Import]: '导入-批量监控',
  [BatchBusinessTypeEnums.Monitor_Failed_Export]: '导出-批量监控失败数据',
};
