import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

@Entity('org_bundles')
@Index('org_id', ['orgId'])
export class OrgBundleEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'org_bundle_id', comment: '主键' })
  @ApiProperty()
  orgBundleId: number;

  @Column('json', { name: 'parameters_setting', comment: '参数设置' })
  @ApiProperty()
  paramsSetting: JSON;

  @Column('int', {
    name: 'org_id',
    nullable: false,
    comment: '对应组织的ID',
  })
  @ApiProperty()
  orgId: number;

  @Column('int', {
    name: 'belong_user',
    nullable: false,
    comment: '对应组织的ID',
  })
  @ApiProperty()
  belongTo: number;

  @Column('int', {
    name: 'bundle_id',
    nullable: false,
    comment: '对应bundle的ID',
  })
  @ApiProperty()
  bundleId: number;

  @Column('datetime', {
    name: 'create_date',
    comment: '创建时间',
    default: () => 'CURRENT_TIMESTAMP',
  })
  @Type(() => Date)
  @ApiProperty()
  createDate: Date;

  @Column('datetime', {
    name: 'update_date',
    comment: '更新时间',
    default: () => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
  })
  @ApiProperty()
  @Type(() => Date)
  updateDate: Date;

  @Column('datetime', {
    name: 'active_date',
    comment: '创建时间',
    default: () => 'CURRENT_TIMESTAMP',
  })
  @Type(() => Date)
  @ApiProperty()
  activeDate: Date;

  @Column('int', {
    name: 'period',
    nullable: false,
    comment: '套餐的有效天数',
  })
  @ApiProperty()
  period: number;

  @Column('datetime', {
    name: 'expire_date',
    comment: '创建时间',
    default: () => 'CURRENT_TIMESTAMP',
  })
  @Type(() => Date)
  @ApiProperty()
  expireDate: Date;

  @Column('datetime', {
    name: 'lock_date',
    comment: '冻结日期',
    default: () => 'CURRENT_TIMESTAMP',
  })
  @Type(() => Date)
  @ApiProperty()
  lockDate: Date;

  @Column('tinyint', { name: 'active', comment: '是否生效：0-无效（过期）；1-有效；2-冻结；', default: 1 })
  @ApiProperty()
  active: number;

  // @Column("json", { name: "parameters", comment: "参数" })
  // parameters: object;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'order_code',
    comment: '订单号',
  })
  @ApiProperty()
  orderCode: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'contract_code',
    comment: '合同号',
  })
  @ApiProperty()
  contractCode: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'trail_code',
    comment: '试用号',
  })
  @ApiProperty()
  trailCode: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'employee_id',
    comment: '试用申请工号',
  })
  @ApiProperty()
  employeeId: string;

  @Column('tinyint', { name: 'fee_type', comment: '费用类型：0-赠送；1-付费；2-抵扣；', default: 1 })
  @ApiProperty()
  feeType: number;

  @Column('tinyint', { name: 'free_vip', comment: '赠送vip数量', default: 1 })
  @ApiProperty()
  freeVip: number;

  @Column('decimal', {
    name: 'price_paid',
    comment: '实付价格',
    precision: 19,
    scale: 2,
  })
  @ApiProperty()
  pricePaid: number;

  @Column('varchar', {
    nullable: true,
    length: 200,
    name: 'note',
    comment: '备注',
  })
  @ApiProperty()
  note: string;

  @Column('int', { name: 'create_by', nullable: false, default: () => "'0'", comment: '创建人id' })
  createBy: number;

  @Column('int', { name: 'update_by', nullable: false, default: () => "'0'", comment: '最后更新人id' })
  updateBy: number;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'lock_code',
    comment: '关联的订单号/合同号',
  })
  @ApiProperty()
  lockCode: string;

  @Column('varchar', {
    nullable: true,
    length: 45,
    name: 'related_order',
    comment: '关联c端订单号',
  })
  @ApiProperty()
  relatedOrder: string;
}
