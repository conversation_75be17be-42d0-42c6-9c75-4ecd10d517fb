# 全局拦截器模块 (Interceptors Module)

## 📝 概述

全局拦截器模块提供请求和响应的预处理、后处理功能，包括日志记录、性能监控、数据转换等。

## 🎯 职责

- 请求/响应日志记录
- 性能监控和统计
- 响应数据格式化
- 缓存控制
- 请求追踪和链路跟踪

## 📁 预期文件结构

```
interceptors/
├── README.md                        # 本说明文件
├── logging.interceptor.ts           # 日志拦截器
├── response-transform.interceptor.ts # 响应转换拦截器
├── performance.interceptor.ts       # 性能监控拦截器
├── cache.interceptor.ts             # 缓存拦截器
├── tracing.interceptor.ts           # 链路追踪拦截器
└── __tests__/                       # 测试文件
    ├── logging.interceptor.spec.ts
    ├── response-transform.interceptor.spec.ts
    └── performance.interceptor.spec.ts
```

## 🔧 拦截器类型

- **LoggingInterceptor**: 记录请求和响应日志
- **ResponseTransformInterceptor**: 统一响应格式
- **PerformanceInterceptor**: 监控接口性能
- **CacheInterceptor**: 缓存控制和管理
- **TracingInterceptor**: 分布式链路追踪

## 📊 标准响应格式

```typescript
{
  "success": true,
  "data": {
    // 实际响应数据
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "req-123456",
    "duration": 150
  }
}
```

## 🚀 性能监控指标

- 请求处理时间
- 并发请求数量
- 错误率统计
- 内存使用情况

## 🔗 相关模块

- 依赖：@shared/logger, @shared/cache
- 被使用：全局应用
- 集成：APM 系统、监控平台
