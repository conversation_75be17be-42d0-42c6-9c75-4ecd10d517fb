# 🏗️ Core Layer (框架核心层) - 横切关注点

框架核心层是 DD 平台的横切关注点层，提供 NestJS 框架级别的基础设施和全局功能。本层负责为整个应用提供统一的框架支撑和技术基础。

## 🎯 层级职责

### 核心职责

- **全局配置管理**: 统一管理应用的配置信息和环境变量
- **数据库配置**: 提供数据库连接和 ORM 配置
- **安全守护**: 实现认证、授权、限流等安全机制
- **异常处理**: 统一的异常过滤和错误处理
- **请求处理**: 全局拦截器和中间件
- **工具装饰器**: 提供常用的自定义装饰器
- **技术服务**: 提供搜索等通用技术服务

### 设计原则

- **框架专注**: 只提供框架级别的基础设施，不涉及业务逻辑
- **全局复用**: 可被 app 层和 modules 层复用的横切功能
- **配置驱动**: 通过配置文件驱动功能的启用和参数
- **标准化**: 提供标准化的技术实现和规范

## 📁 目录结构

```
core/
├── config/                    # 配置管理
│   ├── app.config.ts         # 应用配置
│   ├── database.config.ts    # 数据库配置
│   ├── redis.config.ts       # Redis配置
│   └── validation.config.ts  # 验证配置
├── database/                 # 数据库配置
│   ├── mysql.module.ts       # MySQL模块
│   ├── mongodb.module.ts     # MongoDB模块
│   └── migrations/           # 数据库迁移
├── guards/                   # 全局守卫
│   ├── auth.guard.ts         # 认证守卫
│   ├── roles.guard.ts        # 角色授权守卫
│   └── throttler.guard.ts    # 限流守卫
├── filters/                  # 全局过滤器
│   ├── all-exceptions.filter.ts  # 全局异常过滤器
│   ├── http-exception.filter.ts  # HTTP异常过滤器
│   └── validation.filter.ts      # 验证异常过滤器
├── interceptors/             # 全局拦截器
│   ├── logging.interceptor.ts     # 日志拦截器
│   ├── response.interceptor.ts    # 响应格式化拦截器
│   └── timeout.interceptor.ts     # 超时拦截器
├── decorators/               # 自定义装饰器
│   ├── roles.decorator.ts         # 角色装饰器
│   ├── public.decorator.ts        # 公开接口装饰器
│   └── current-user.decorator.ts  # 当前用户装饰器
├── search/                   # 搜索服务
│   └── es.base.service.ts    # Elasticsearch基础服务
├── file/                     # 文件处理
│   └── file.config.ts        # 文件配置
├── constants/                # 全局常量
│   ├── app.constants.ts      # 应用常量
│   └── error.constants.ts    # 错误码常量
└── README.md                 # 本文档
```

## 🏗️ 核心组件详解

### Config (配置管理)

提供统一的配置管理功能：

```typescript
// app.config.ts
@Injectable()
export class AppConfig {
  @IsNumber()
  @Min(1000)
  @Max(65535)
  port: number = 3000;

  @IsString()
  @IsNotEmpty()
  globalPrefix: string = 'api';

  @IsBoolean()
  corsEnabled: boolean = true;
}
```

### Guards (全局守卫)

实现认证、授权等安全机制：

```typescript
// auth.guard.ts
@Injectable()
export class AuthGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [context.getHandler(), context.getClass()]);

    if (isPublic) return true;

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) throw new UnauthorizedException();

    // 验证token逻辑
    return true;
  }
}
```

### Filters (全局过滤器)

统一的异常处理机制：

```typescript
// all-exceptions.filter.ts
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status = this.getStatus(exception);
    const message = this.getMessage(exception);

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message,
    });
  }
}
```

### Search (搜索服务)

提供 Elasticsearch 等搜索引擎的基础服务：

```typescript
// es.base.service.ts
export class EsBaseService {
  public esClientRead: Client;
  public esClientWrite: Client;

  constructor(protected readonly configService: ConfigService) {
    this.esClientRead = new Client({
      nodes: this.configService.esConfig.metricDynamics.nodesQuery,
      ssl: { rejectUnauthorized: false },
    });
    this.esClientWrite = new Client({
      nodes: this.configService.esConfig.metricDynamics.nodesWrite,
      ssl: { rejectUnauthorized: false },
    });
  }

  protected async insertDocsToEs(items: any[], refreshNow = true, params?: any) {
    // ES批量插入逻辑
  }

  public getWriteIndexName(): string {
    return `${this.indexName}_write`;
  }

  public getReadIndexName(): string {
    return `${this.indexName}_query`;
  }
}
```

**使用示例**:

```typescript
// 在业务模块中继承使用
@Injectable()
export class MonitorDynamicEsService extends EsBaseService {
  constructor(protected readonly configService: ConfigService) {
    super(configService);
  }

  async searchDynamics(request: SearchDynamicEsPO): Promise<SearchMetricsDynamicResponse> {
    // 具体的业务搜索逻辑
  }
}
```

## 🔄 依赖关系

### 依赖的层级

```typescript
// 可以依赖commons和domain层
import { ErrorCodes } from '@commons/constants/error-codes'; // → commons层
import { UserEntity } from '@domain/entities/user.entity'; // → domain层
import { BusinessException } from '@commons/exceptions/business.exception'; // → commons层
```

### 被依赖关系

```typescript
// 被app层和modules层依赖
import { AuthGuard } from '@core/guards/auth.guard'; // app/modules → core层
import { DatabaseModule } from '@core/database/mysql.module'; // app/modules → core层
import { CurrentUser } from '@core/decorators/current-user.decorator'; // modules → core层
```

## 🛡️ 安全和中间件

### 认证和授权

```typescript
// 使用示例
@Controller('users')
@UseGuards(AuthGuard, RolesGuard)
export class UsersController {
  @Get('profile')
  @Roles('user', 'admin')
  getProfile(@CurrentUser() user: any) {
    return user;
  }

  @Post('public-info')
  @Public()
  getPublicInfo() {
    return { message: 'This is public information' };
  }
}
```

## 🛠️ 开发指南

### 新增全局功能

1. **确定类型**: 判断功能属于 guard、filter、interceptor 还是 decorator
2. **创建实现**: 在对应目录下创建实现文件
3. **注册全局**: 在 CoreModule 或 AppModule 中注册
4. **编写测试**: 编写对应的单元测试和集成测试

### 配置管理最佳实践

```typescript
// 推荐的配置结构
interface AppConfiguration {
  app: {
    port: number;
    globalPrefix: string;
    corsEnabled: boolean;
  };
  database: {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
  };
}
```

## ⚠️ 注意事项

### 性能考虑

1. **拦截器顺序**: 合理安排拦截器的执行顺序，避免不必要的处理
2. **异常处理**: 避免在全局过滤器中进行重复的异常处理
3. **日志级别**: 根据环境配置合适的日志级别
4. **缓存策略**: 对配置信息进行合理的缓存

### 安全考虑

1. **敏感信息**: 不在日志中输出敏感信息
2. **错误信息**: 生产环境不暴露详细的错误信息
3. **访问控制**: 确保守卫的正确实现和配置
4. **输入验证**: 在全局层面进行基础的输入验证

## 📚 相关文档

- [项目整体架构说明](../README.md)
- [App 层应用组装](../app/README.md)
- [Modules 层业务模块](../modules/README.md)
- [NestJS 官方文档](https://nestjs.com/)
