import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { BadRequestException } from '@nestjs/common';
import { extname } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { v4 as uuid } from 'uuid';
import { diskStorage } from 'multer';
import { RoverExceptions } from '@commons/exceptions/exceptionConstants';

export const defaultFileUploadOptions: MulterOptions = {
  limits: {
    fileSize: 2 * 1000 * 1000, // 2Mb
  },
  // Check the mimetypes to allow for upload
  fileFilter: (req: any, file: any, cb: any) => {
    if (file.originalname.match(/.(xls|xlsx)$/)) {
      cb(null, true);
    } else {
      // Reject file
      cb(new BadRequestException(RoverExceptions.Import.FileError));
    }
  },
  // Storage properties
  storage: diskStorage({
    // Destination storage path details
    destination: (req: any, file: any, cb: any) => {
      const uploadPath = '/tmp/upload';
      // Create folder if doesn't exist
      if (!existsSync(uploadPath)) {
        mkdirSync(uploadPath);
      }
      cb(null, uploadPath);
    },
    // File modification details
    filename: (req: any, file: any, cb: any) => {
      if (file.originalname.length > 100) {
        cb(new BadRequestException(RoverExceptions.Import.FileNameError));
      }
      // Calling the callback passing the random name generated with the original extension name
      cb(null, `${uuid()}${extname(file.originalname)}`);
    },
  }),
};

export const commonFileUploadOptions: MulterOptions = {
  limits: {
    fileSize: 20 * 1000 * 1000, // 2Mb
  },
  // Storage properties
  storage: diskStorage({
    // Destination storage path details
    destination: (req: any, file: any, cb: any) => {
      const uploadPath = '/tmp/upload';
      // Create folder if doesn't exist
      if (!existsSync(uploadPath)) {
        mkdirSync(uploadPath);
      }
      cb(null, uploadPath);
    },
    // File modification details
    filename: (req: any, file: any, cb: any) => {
      const fileName = req?.query?.fileName || file.originalname; // file.originalname 中文会乱码，目前先这样解决
      if (fileName.length > 100) {
        cb(new BadRequestException({ ...RoverExceptions.Import.FileNameError, message: [fileName, fileName.length] }));
      }
      // Calling the callback passing the random name generated with the original extension name
      cb(null, `${uuid()}${extname(fileName)}`);
    },
  }),
};
