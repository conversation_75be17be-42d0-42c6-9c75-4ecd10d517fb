# 全局常量模块 (Constants Module)

## 📝 概述

全局常量模块定义应用程序中使用的所有常量值，确保魔法数字和字符串的统一管理。

## 🎯 职责

- 系统级常量定义
- 错误码和消息定义
- API 版本和路径常量
- 业务规则常量
- 配置默认值

## 📁 预期文件结构

```
constants/
├── README.md                    # 本说明文件
├── error-codes.constant.ts      # 错误码常量
├── api-routes.constant.ts       # API路由常量
├── cache-keys.constant.ts       # 缓存键常量
├── business-rules.constant.ts   # 业务规则常量
├── system.constant.ts           # 系统常量
├── validation.constant.ts       # 验证规则常量
└── __tests__/                   # 测试文件
    ├── error-codes.constant.spec.ts
    └── business-rules.constant.spec.ts
```

## 📋 常量分类

### 错误码常量

```typescript
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  BUSINESS_ERROR: 'BUSINESS_ERROR',
} as const;
```

### API 路由常量

```typescript
export const API_ROUTES = {
  VERSION: 'v1',
  USERS: 'users',
  COMPANIES: 'companies',
  DILIGENCE: 'diligence',
} as const;
```

### 缓存键常量

```typescript
export const CACHE_KEYS = {
  USER_PROFILE: 'user:profile:',
  COMPANY_INFO: 'company:info:',
  RISK_MODEL: 'risk:model:',
} as const;
```

## 💡 使用示例

```typescript
import { ERROR_CODES, CACHE_KEYS } from '@core/constants';

// 使用错误码
throw new BusinessException(ERROR_CODES.VALIDATION_ERROR, '参数验证失败');

// 使用缓存键
const cacheKey = `${CACHE_KEYS.USER_PROFILE}${userId}`;
```

## 🔗 相关模块

- 被使用：所有业务模块
- 集成：错误处理、缓存系统、API 文档
