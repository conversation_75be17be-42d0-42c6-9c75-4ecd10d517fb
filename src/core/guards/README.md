# 全局守卫模块 (Guards Module)

## 📝 概述

全局守卫模块提供应用程序级别的访问控制和权限验证，包括身份认证、授权检查等安全功能。

## 🎯 职责

- JWT 令牌验证
- 用户身份认证
- 角色权限检查
- API 访问控制
- 速率限制保护

## 📁 预期文件结构

```
guards/
├── README.md                    # 本说明文件
├── auth.guard.ts                # 身份认证守卫
├── roles.guard.ts               # 角色权限守卫
├── jwt.guard.ts                 # JWT令牌守卫
├── rate-limit.guard.ts          # 速率限制守卫
├── api-key.guard.ts             # API密钥守卫
└── __tests__/                   # 测试文件
    ├── auth.guard.spec.ts
    ├── roles.guard.spec.ts
    └── jwt.guard.spec.ts
```

## 🛡️ 守卫类型

- **AuthGuard**: 基础身份认证
- **RolesGuard**: 基于角色的访问控制
- **JwtGuard**: JWT 令牌验证
- **RateLimitGuard**: API 调用频率限制
- **ApiKeyGuard**: API 密钥验证

## 💡 使用示例

```typescript
@UseGuards(JwtGuard, RolesGuard)
@Roles('admin', 'user')
@Controller('api/v1/users')
export class UserController {
  // 需要JWT认证和admin或user角色的端点
}
```

## 🔗 相关模块

- 依赖：@core/config, @shared/logger
- 被使用：@modules/\* (控制器)
- 集成：JWT 库、权限系统
