import { HttpService } from '@nestjs/axios';
import { ThrottleAxiosRequest } from '@kezhaozhao/qcc-common-utils';
import { ConfigService } from '@core/config/config.service';
import { ExecutionContext, ForbiddenException, Injectable, InternalServerErrorException, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';
import { RoverExceptions } from '@commons/exceptions/exceptionConstants';
import { KzzHttpException } from '@commons/exceptions/KzzHttpException';

/**
 * 控制外部访问者的访问权限
 *
 * 只能访问 ApiGuardExternal 声明的接口
 */
@Injectable()
export class ApiGuardExternal extends AuthGuard('jwt') {
  readonly throttleInstance: ThrottleAxiosRequest;

  constructor(private readonly jwtService: JwtService, protected readonly httpService: HttpService, protected readonly configService: ConfigService) {
    super();
    this.throttleInstance = ThrottleAxiosRequest.getInstance(httpService.axiosRef);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();

    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      throw new UnauthorizedException(RoverExceptions.UserRelated.Auth.InvalidJWT);
    }
    try {
      const user = await this.jwtService.verifyAsync(token, this.configService.jwt);
      if (!user.currentOrg) {
        throw new ForbiddenException(RoverExceptions.UserRelated.Auth.OrgNotCreated);
      }
      if (!user.userId) {
        throw new ForbiddenException(RoverExceptions.UserRelated.User.ApplicationForbidden);
      }
      req.user = user;
      // req.user = {
      //   loginUserId: 1,
      //   userId: 1,
      //   currentProduct: ProductCodeEnums.Pro,
      //   currentOrg: 1,
      //   orgName: '测试组织',
      // };
      return true;
    } catch (err) {
      // let message = err.message;
      // // if (message == 'invalid token' || message?.startsWith('Unexpected token')) {
      // //   throw new UnauthorizedException(RoverExceptions.UserRelated.Auth.InvalidJWT);
      // // }
      // // if (message == 'jwt expired') {
      // //   throw new UnauthorizedException(RoverExceptions.UserRelated.Auth.JWTExpired);
      // // }
      // // if (err?.response?.data) {
      // //   message = JSON.stringify(err.response.data);
      // // }
      // // {
      // //   "name": "JsonWebTokenError",
      // //   "message": "invalid signature"
      // // }
      // if (err?.name == 'JsonWebTokenError') {
      //   throw new UnauthorizedException(RoverExceptions.UserRelated.Auth.InvalidJWT);
      // } else if (isAxiosError(err)) {
      //   message = JSON.stringify(err.response.data);
      //   const t = err.response.data as KzzExceptionResponse;
      //   if (t.error && t.code && t.statusCode) {
      //     // 说明是 内部服务抛出的错误
      //     throw new KzzHttpException({
      //       ...t,
      //     });
      //   }
      //   throw new KzzHttpException(Object.assign({ ...t }, { message: err.response.data || err.message }));
      // }

      const httpException = KzzHttpException.getError(err);
      if (httpException) {
        throw httpException;
      }
      throw new InternalServerErrorException({
        ...RoverExceptions.UserRelated.User.GetUserOrgInfoFailed,
        internalMessage: err.message,
      });
    }
  }
}
