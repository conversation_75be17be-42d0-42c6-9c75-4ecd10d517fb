import { BadRequestException, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@core/config/config.service';
import { ThrottleAxiosRequest } from '@kezhaozhao/qcc-common-utils';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { RoverExceptions } from '@commons/exceptions/exceptionConstants';
import { AuthGuard } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';
/**
 * 控制内部访问者的访问权限
 * 可以访问所有 ApiGuardExternal 中的接口 以及  ApiGuardInternal 中的接口
 */
@Injectable()
export class ApiGuardInternal extends AuthGuard('jwt') {
  readonly throttleInstance: ThrottleAxiosRequest;

  constructor(private readonly jwtService: JwtService, protected readonly httpService: HttpService, protected readonly configService: ConfigService) {
    super();
    this.throttleInstance = ThrottleAxiosRequest.getInstance(httpService.axiosRef);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const headers = req.headers || {};
    if (!headers['x-kzz-bo-product']) {
      throw new BadRequestException(RoverExceptions.UserRelated.Auth.InvalidJWT);
    }
    const bearToken = headers.authorization || headers.authentication || headers['x-kzz-bo-token'];
    const token = bearToken?.replace('Bearer ', '');

    if (!token) {
      throw new UnauthorizedException(RoverExceptions.UserRelated.Auth.InvalidJWT);
    }

    const user = await this.jwtService.verifyAsync(token, this.configService.jwtBO);
    req.user = {
      loginUserId: user.adminId,
      userId: user.adminId,
      currentProduct: headers['x-kzz-bo-product'] || ProductCodeEnums.Pro,
      currentOrg: 1,
      orgName: '测试组织',
    };
    return true;
  }
}
