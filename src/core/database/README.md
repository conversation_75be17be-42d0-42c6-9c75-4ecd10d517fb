# 数据库模块 (Database Module)

## 📝 概述

数据库模块负责应用程序的数据库连接配置、连接池管理、ORM 设置等核心数据访问基础设施。

## 🎯 职责

- 数据库连接配置和管理
- ORM (如 MikroORM) 初始化设置
- 数据库连接池优化
- 数据库迁移管理
- 事务管理配置

## 📁 预期文件结构

```
database/
├── README.md                    # 本说明文件
├── database.module.ts           # 数据库模块定义
├── database.service.ts          # 数据库服务
├── connection.provider.ts       # 连接提供者
├── migration.config.ts          # 迁移配置
├── transaction.manager.ts       # 事务管理器
└── __tests__/                   # 测试文件
    ├── database.service.spec.ts
    └── connection.provider.spec.ts
```

## 🔧 功能特性

- **连接管理**: 自动重连、连接池配置
- **性能优化**: 查询缓存、连接复用
- **监控支持**: 连接状态、查询性能监控
- **安全配置**: SQL 注入防护、访问控制

## 🗄️ 支持的数据库

- PostgreSQL (主要)
- MySQL (兼容)
- Redis (缓存)

## 🔗 相关模块

- 依赖：@core/config
- 被依赖：@domain/entities, @modules/\*
- 集成：数据库迁移工具、监控系统
