import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigService } from './config.service';
import { HttpUtilsService } from './httputils.service';
import { BatchCacheHelper } from './batch-cache-helper.service';
import { QueueService } from './queue.service';
import { JwtModule } from '@nestjs/jwt';

@Global()
@Module({
  providers: [
    {
      provide: ConfigService,
      useValue: new ConfigService(),
    },
    HttpUtilsService,
    BatchCacheHelper,
    QueueService,
  ],
  imports: [
    HttpModule.register({}),
    JwtModule.registerAsync({
      useFactory: async (configService: ConfigService) => configService.jwt,
      inject: [ConfigService],
    }),
  ],
  exports: [ConfigService, HttpModule, JwtModule, HttpUtilsService, BatchCacheHelper, QueueService],
})
export class ConfigModule {}
