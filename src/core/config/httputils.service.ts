import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BadParamsException, DefaultExceptionsCode, KzzErrorMessage, axiosErrorExtraData } from '@kezhaozhao/qcc-exception-handler';
import { HttpService } from '@nestjs/axios';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { AxiosRequestConfig } from 'axios';
import * as crypto from 'crypto';
import { Logger } from 'log4js';
import { ConfigService } from './config.service';

@Injectable()
export class HttpUtilsService {
  private readonly logger: Logger = QccLogger.getLogger(HttpUtilsService.name);

  constructor(private readonly configService: ConfigService, private readonly httpService: HttpService) {}

  public async postRequest<T>(url: string, data: T) {
    // 判断是单元测试走下面的逻辑，不是单元测试走postRequestDirect
    const isTestEnvironment = process.env.NODE_ENV === 'test' && process.env.JEST_WORKER_ID;
    if (isTestEnvironment) {
      // 测试环境：使用原来的逻辑，确保 headers 正确传递
      const requestParams: AxiosRequestConfig = {
        method: 'POST',
        url: url,
        data: data,
        headers: {
          'x-request-from-head-app-name': this.configService.axiosConfig.headers['x-request-from-head-app-name'],
        },
      };
      return await this.sendResquest(requestParams);
    } else {
      // 非测试环境：使用直接调用方式，依赖全局配置
      return await this.postRequestDirect(url, data);
    }
  }

  public async getRequest<T>(url: string, data: T) {
    // 判断是单元测试走下面的逻辑，不是单元测试走getRequestDirect
    const isTestEnvironment = process.env.NODE_ENV === 'test' && process.env.JEST_WORKER_ID;

    if (isTestEnvironment) {
      // 测试环境：使用原来的逻辑，确保 headers 正确传递
      const requestParams: AxiosRequestConfig = {
        method: 'GET',
        url: url,
        params: data,
        headers: {
          'x-request-from-head-app-name': this.configService.axiosConfig.headers['x-request-from-head-app-name'],
        },
      };
      return await this.sendResquest(requestParams);
    } else {
      // 非测试环境：使用直接调用方式，依赖全局配置
      return await this.getRequestDirect(url, data);
    }
  }

  public async sendResquest(requestParams: AxiosRequestConfig) {
    const url = requestParams.url;
    const dataServiceUrl = this.configService.proxyServer.dataService;
    // 确保 headers 对象存在
    if (!requestParams.headers) {
      requestParams.headers = {};
    }
    if (url.startsWith(dataServiceUrl)) {
      // 请求数据服务添加token
      const timestamp = (new Date().getTime() / 1000).toString().split('.')[0];
      const tokenKey = 'cabae57f-d28e-4e5f-abf5-e0a88800ebd4';
      const fromApp = 'dd-platform-service';
      // 保持原有的 headers，避免覆盖
      requestParams.headers = {
        ...requestParams.headers, // 保持原有的 headers，包括 x-request-from-head-app-name
        'x-request-from-app-name': fromApp,
        'x-request-timestamp': timestamp,
        'x-request-access-token': crypto.createHash('md5').update(`${fromApp}${timestamp}${tokenKey}`).digest('hex'),
      };
    }

    const result = await this.httpService.axiosRef
      .request(requestParams)
      .then((res) => {
        return res?.data;
      })
      .catch((error) => {
        return this.handleAxiosError(error, url);
      });
    if (result.Status === 299) {
      // 打印299错误的详细日志
      this.logger.error(`🚨 外部接口返回299业务错误码`, {
        url: url,
        requestMethod: requestParams.method,
        requestHeaders: requestParams.headers,
        requestData: requestParams.data,
        responseStatus: result.Status,
        responseMessage: result.Message,
        responseResult: result.Result,
        timestamp: new Date().toISOString(),
        errorType: '外部接口认证失败',
        possibleCause: 'x-request-from-head-app-name 或其他 header 配置错误',
      });
    }
    return result;
  }

  /**
   * 直接使用 axios.post 方法发送 POST 请求
   * @param url 请求地址
   * @param data 请求数据
   * @param additionalHeaders 额外的请求头
   */
  public async postRequestDirect<T>(url: string, data: T) {
    try {
      const response = await this.httpService.axiosRef.post(url, data);
      return response.data;
    } catch (error) {
      return this.handleAxiosError(error, url);
    }
  }

  /**
   * 直接使用 axios.get 方法发送 GET 请求
   * @param url 请求地址
   * @param params 查询参数
   * @param additionalHeaders 额外的请求头
   */
  public async getRequestDirect<T>(url: string, params?: T) {
    try {
      const response = await this.httpService.axiosRef.get(url, {
        params,
      });
      return response.data;
    } catch (error) {
      return this.handleAxiosError(error, url);
    }
  }

  /**
   * 统一的 Axios 错误处理方法
   * @param error Axios 错误对象
   * @param url 请求地址
   */
  private handleAxiosError(error: any, url: string) {
    const errorMessage = `url=${url}, message=${error.message}`;
    if (error.response?.data) {
      const data = error.response.data;
      if (data?.statusCode == 404 && data?.code == 900103) {
        // 招投标号码查询接口 api/search/company/${companyId}/contacts 未查到公司会返回404 ，data code 900103
        return [];
      }
      const kzzErrorMessage = Object.assign(new KzzErrorMessage(), {
        code: data.code,
        error: errorMessage,
        message: errorMessage,
      });
      if (error.response.status >= 500) {
        throw new InternalServerErrorException({
          ...DefaultExceptionsCode.Common.QccService.Error,
          message: errorMessage,
          internalMessage: error.message,
          errorExtraData: axiosErrorExtraData(error),
        });
      }
      throw new BadParamsException(kzzErrorMessage);
    } else {
      this.logger.error(`errorInfo url: ${url}`);
      this.logger.error(error);
    }
    throw new InternalServerErrorException({
      ...DefaultExceptionsCode.Common.QccService.Error,
      message: errorMessage,
      internalMessage: error.message,
      errorExtraData: axiosErrorExtraData(error),
    });
  }
}
