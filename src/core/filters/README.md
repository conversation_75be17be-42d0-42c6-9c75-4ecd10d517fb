# 全局过滤器模块 (Filters Module)

## 📝 概述

全局过滤器模块处理应用程序的异常捕获、错误处理、响应格式化等横切关注点。

## 🎯 职责

- 全局异常捕获和处理
- HTTP 错误响应格式化
- 错误日志记录
- 敏感信息过滤
- 响应数据转换

## 📁 预期文件结构

```
filters/
├── README.md                        # 本说明文件
├── http-exception.filter.ts         # HTTP异常过滤器
├── all-exceptions.filter.ts         # 全局异常过滤器
├── validation-exception.filter.ts   # 验证异常过滤器
├── business-exception.filter.ts     # 业务异常过滤器
└── __tests__/                       # 测试文件
    ├── http-exception.filter.spec.ts
    ├── all-exceptions.filter.spec.ts
    └── validation-exception.filter.spec.ts
```

## 🔧 过滤器类型

- **HttpExceptionFilter**: HTTP 状态码异常处理
- **AllExceptionsFilter**: 捕获所有未处理异常
- **ValidationExceptionFilter**: 数据验证异常处理
- **BusinessExceptionFilter**: 业务逻辑异常处理

## 📋 错误响应格式

```typescript
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ],
    "timestamp": "2024-01-01T00:00:00.000Z",
    "path": "/api/v1/users"
  }
}
```

## 🔗 相关模块

- 依赖：@shared/logger, @shared/exceptions
- 被使用：全局应用
- 集成：日志系统、监控告警
