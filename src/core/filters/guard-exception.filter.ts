import { ArgumentsHost, Catch, ExceptionFilter, HttpException } from '@nestjs/common';
import { Request, Response } from 'express';
import * as cookie from 'cookie';
import { API_BASE } from '@commons/constants/common';

@Catch(HttpException)
export class GuardExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    if (request?.query?.sid) {
      const sessionId = request.query.sid.toString();
      response.setHeader(
        'Set-Cookie',
        cookie.serialize('QCCSESSID', sessionId, {
          httpOnly: true,
          secure: true,
          path: '/',
          sameSite: 'none',
          maxAge: 60 * 60 * 24 * 7, // 1 week
        }),
      );
      // set cookie and try again
      const target = request?.query?.target?.toString();
      if (target?.startsWith('/')) {
        response.redirect(`${API_BASE}/transit?target=${target}`);
      } else {
        response.redirect(`${API_BASE}/transit`);
      }
    } else {
      response.redirect('/');
    }
  }
}
