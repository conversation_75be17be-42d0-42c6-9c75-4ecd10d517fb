import { Client } from '@elastic/elasticsearch';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { EventEmitter } from 'events';
import { RoverEventEmitter } from '@commons/RoverEventEmitter';
import { ConfigService } from '@core/config/config.service';
import * as Bluebird from 'bluebird';
import { chunk, omit } from 'lodash';
import { captureException } from '@sentry/node';

export class EsBaseService {
  public esClientRead: Client;
  public esClientWrite: Client;
  private readonly indexName: string;
  protected readonly logger = QccLogger.getLogger(EsBaseService.name);
  private docsToInsert: any[] = [];
  private esBatchSize = 1000; //这里是bulk API的参数，所以实际应该是 500个item
  private eventEmitter: EventEmitter = RoverEventEmitter.getInstance();

  constructor(protected readonly configService: ConfigService) {
    this.esClientRead = new Client({
      nodes: this.configService.esConfig.metricDynamics.nodesQuery.split(','),
      ssl: { rejectUnauthorized: false },
    });
    this.esClientWrite = new Client({
      nodes: this.configService.esConfig.metricDynamics.nodesWrite.split(','),
      ssl: { rejectUnauthorized: false },
    });
    this.indexName = this.configService.esConfig.metricDynamics.indexName;
    // setInterval(this.flushEs.bind(this), 3000); //每隔3s执行一次写入
    // this.eventEmitter.on(SystemEvents.ProcessExit, this.flushEs.bind(this, [true]));
  }

  protected async insertDocsToEs(items: any[], refreshNow = true, params?: any) {
    if (!items?.length) {
      return;
    }
    await Bluebird.map(
      chunk(items, this.esBatchSize),
      async (body: any[]) => {
        try {
          const start = Date.now();
          const { body: bulkResponse } = await this.esClientWrite.bulk({ refresh: refreshNow, body });
          if (bulkResponse.errors) {
            const erroredDocuments = [];
            bulkResponse.items.forEach((action: any, i: number) => {
              const operation = Object.keys(action)[0];
              if (action[operation].error) {
                erroredDocuments.push({
                  status: action[operation].status,
                  error: action[operation].error,
                  operation: body[i * 2],
                  document: body[i * 2 + 1],
                });
              }
            });
            const message = `insert docs to index failed(partial failure):  indexName=${this.getWriteIndexName()}, error_size=${erroredDocuments.length}`;
            this.logger.error(message);
            captureException(
              { message },
              {
                extra: params ? omit(params, ['items']) : {},
              },
            );
            this.logger.error(erroredDocuments);
          }
          this.logger.info(`insert ${body.length / 2} items to es cost ${Date.now() - start}ms`);
        } catch (error) {
          const message = `insert docs to index failed(partial failure):  indexName=${this.getWriteIndexName()}, errorMessage=` + error.message;
          this.logger.error(message);
          captureException(Object.assign(error, { message }), {
            extra: params ? omit(params, ['items']) : {},
          });
          this.logger.error(error);
        }
      },
      { concurrency: 5 },
    );
    if (refreshNow) {
      await this.refresh();
    }
  }

  public async refresh() {
    const response = await this.esClientWrite.indices.refresh({ index: this.getReadIndexName() });
    return response;
  }

  public getWriteIndexName(): string {
    return `${this.indexName}_write`;
  }

  public getReadIndexName(): string {
    return `${this.indexName}_query`;
  }
}
