# 自定义装饰器模块 (Decorators Module)

## 📝 概述

自定义装饰器模块提供业务特定的装饰器，简化开发并提供更好的代码可读性和复用性。

## 🎯 职责

- 自定义业务装饰器
- 元数据管理
- 代码简化和复用
- 业务规则封装
- 权限控制装饰器

## 📁 预期文件结构

```
decorators/
├── README.md                    # 本说明文件
├── roles.decorator.ts           # 角色权限装饰器
├── public.decorator.ts          # 公开接口装饰器
├── api-key.decorator.ts         # API密钥装饰器
├── rate-limit.decorator.ts      # 限流装饰器
├── cache.decorator.ts           # 缓存装饰器
├── audit.decorator.ts           # 审计日志装饰器
└── __tests__/                   # 测试文件
    ├── roles.decorator.spec.ts
    ├── cache.decorator.spec.ts
    └── audit.decorator.spec.ts
```

## 🎨 装饰器类型

- **@Roles()**: 指定接口所需角色
- **@Public()**: 标记公开接口，跳过认证
- **@ApiKey()**: 要求 API 密钥验证
- **@RateLimit()**: 设置接口限流规则
- **@Cache()**: 设置响应缓存
- **@Audit()**: 记录操作审计日志

## 💡 使用示例

```typescript
@Controller('api/v1/users')
export class UserController {
  @Get()
  @Roles('admin', 'user')
  @Cache(300) // 缓存5分钟
  async getUsers() {
    // 需要admin或user角色，结果缓存5分钟
  }

  @Post()
  @Roles('admin')
  @Audit('USER_CREATE') // 记录用户创建审计日志
  async createUser() {
    // 需要admin角色，记录审计日志
  }

  @Get('public')
  @Public() // 公开接口，无需认证
  @RateLimit(100, 60) // 每分钟最多100次请求
  async getPublicInfo() {
    // 公开接口，但有限流保护
  }
}
```

## 🔗 相关模块

- 依赖：@core/guards, @core/interceptors
- 被使用：@modules/\* (控制器和服务)
- 集成：权限系统、缓存系统、审计系统
