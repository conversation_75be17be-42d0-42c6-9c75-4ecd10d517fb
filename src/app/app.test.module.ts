import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { RedisModule } from '@kezhaozhao/nestjs-redis';
import { GracefulShutdown } from './GracefulShutdown';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Product } from '@kezhaozhao/saas-bundle-service';
import { QccAuthModule } from '@kezhaozhao/saas-auth';
import { QichachaModule } from '@kezhaozhao/qichacha-service-utils';
import { AliyunModule } from '@kezhaozhao/qcc-aliyun-utils';
import { ConfigService } from '@core/config/config.service';
import { ConfigModule } from '@core/config/config.module';
import { ApiGuardExternal } from '@core/guards/api.guard.external';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { HandlebarsAdapter, MailerModule } from '@kezhaozhao/nest-mailer';
import { join } from 'path';
import { SmsModule } from '@kezhaozhao/qcc-sms-utils';

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    ConfigModule,
    TypeOrmModule.forRootAsync({
      useFactory: async (configService: ConfigService) => configService.typeorm,
      inject: [ConfigService],
    }),
    /*TypeOrmModule.forRootAsync({
      name: 'saasdb',
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => configService.saasDB,
      inject: [ConfigService],
    }),*/
    AliyunModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return {
          oss: configService.server.oss,
        };
      },
      inject: [ConfigService],
    }),
    SmsModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return configService.server.smsService;
      },
      inject: [ConfigService],
    }),
    MailerModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        transport: configService.server.mailerService,
        defaults: {
          from: '"企查查" <<EMAIL>>',
        },
        template: {
          dir: join(__dirname, '..', '..', '..', 'templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
      inject: [ConfigService],
    }),
    RedisModule.forRootAsync({
      useFactory: (configService: ConfigService) => configService.redis, // or use async method
      inject: [ConfigService],
    }),
    QccAuthModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          jwt: configService.jwt,
          server: {
            comDomainService: configService.server.comDomainService,
            ssoService: configService.server.ssoService,
            appService: configService.server.appService,
            wxAdminService: configService.server.wxAdminService,
            saasService: configService.server.saasService,
            entService: configService.kzzServer.enterpriseService,
            bundleService: configService.kzzServer.bundleService,
          },
          sessionName: 'QCCSESSID',
          mobileSessionName: 'MQCCSESSID',
          redis: configService.redis,
          product: Product.Rover,
          validBundle: true,
        };
      },
      inject: [ConfigService],
    }),
    QichachaModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          comDomainService: configService.server.comDomainService,
          extDomainService: configService.server.extDomainService,
          appService: configService.server.appService,
          saasService: configService.server.saasService,
          bossService: configService.server.bossService,
          wxQccDomainService: configService.server.wxQccDomainService,
        };
      },
      inject: [ConfigService],
    }),
  ],
  controllers: [AppController],
  providers: [ConfigService, GracefulShutdown, ApiGuardExternal],
})
export class AppTestModule {}
