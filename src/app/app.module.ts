import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { RedisModule } from '@kezhaozhao/nestjs-redis';
import { KzzHealthIndicator } from './KzzHealthIndicator';
import { HealthController } from './health.controller';
import { GracefulShutdown } from './GracefulShutdown';
import { HealthModule } from '@kezhaozhao/nest-sentinel';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SchemaModule } from '@modules/system/schema/schema.module';
import { DiligenceModule } from '@modules/risk-assessment/diligence/diligence.module';
import { BatchModule } from '@modules/batch-processing/batch.module';
import { CompanySearchModule } from '@modules/company-management/company/company-search.module';
import { Product } from '@kezhaozhao/saas-bundle-service';
import { QccAuthModule } from '@kezhaozhao/saas-auth';
import { QichachaModule } from '@kezhaozhao/qichacha-service-utils';
import { SmsModule } from '@kezhaozhao/qcc-sms-utils';
import { AliyunModule } from '@kezhaozhao/qcc-aliyun-utils';
import { TerminusModule } from '@nestjs/terminus';
import { ConfigService } from '@core/config/config.service';
import { ConfigModule } from '@core/config/config.module';
import { ApiGuardExternal } from '@core/guards/api.guard.external';
import { DataModule } from '@modules/data-processing/data-source/data.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { BasicModule } from '@modules/system/basic/basic.module';
import { RiskModelModule } from '@modules/risk-assessment/risk-model/risk_model.module';
import { DimensionModule } from '@modules/system/dimension/dimension.module';
import { GroupModule } from '@modules/system/group/group.module';
import { MetricModule } from '@modules/system/metric/metric.module';
import { InternalModule } from '@modules/internal/internal.module';
import { MonitorModule } from '@modules/risk-assessment/monitor/monitor.module';
import { UserSocketModule } from '@modules/communication/websocket/usersocket/usersocket.mdoule';
import { PushModule } from '@modules/communication/push/push.module';
import { join } from 'path';
import { HandlebarsAdapter, MailerModule } from '@kezhaozhao/nest-mailer';
import { PlatformScheduleModule } from '@modules/system/schedule/schedule.module';
import { UserModule } from '@modules/user-management/user/user.module';
import { MessageModule } from '@modules/communication/message/message.module';
import { AiAnalyzerModule } from '@modules/ai-intelligence/ai_analyzer/ai.analyzer.module';
import { VerificationModule } from '@modules/verification/verification.module';
import { QaModule } from '@modules/ai-intelligence/qa/qa.module';
import { DataCleanerModule } from '@modules/data-processing/data-cleaner/data-cleaner.module';

@Module({
  imports: [
    ConfigModule,
    TerminusModule,
    HealthModule.registerAsync({
      useFactory: () => {
        return {
          terminationGracePeriodSeconds: 60,
          canary: false,
          preStopPauseSeconds: 5,
          gracefulShutdown: {
            pauseSecond: 3,
          },
        };
      },
    }),
    /*TypeOrmModule.forRootAsync({
      name: 'saasdb',
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => configService.saasDB,
      inject: [ConfigService],
    }),*/
    TypeOrmModule.forRootAsync({
      useFactory: async (configService: ConfigService) => configService.typeorm,
      inject: [ConfigService],
    }),
    AliyunModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return {
          oss: configService.server.oss,
        };
      },
      inject: [ConfigService],
    }),
    SmsModule.registerAsync({
      useFactory: (configService: ConfigService) => {
        return configService.server.smsService;
      },
      inject: [ConfigService],
    }),
    MailerModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        transport: configService.server.mailerService,
        defaults: {
          from: '"企查查" <<EMAIL>>',
        },
        template: {
          dir: join(process.cwd(), 'templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
      inject: [ConfigService],
    }),
    RedisModule.forRootAsync({
      useFactory: (configService: ConfigService) => configService.redis, // or use async method
      inject: [ConfigService],
    }),
    QccAuthModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          jwt: configService.jwt,
          server: {
            comDomainService: configService.server.comDomainService,
            ssoService: configService.server.ssoService,
            appService: configService.server.appService,
            wxAdminService: configService.server.wxAdminService,
            saasService: configService.server.saasService,
            entService: configService.kzzServer.enterpriseService,
            bundleService: configService.kzzServer.bundleService,
            authService: configService.kzzServer.authService,
          },
          sessionName: 'QCCSESSID',
          mobileSessionName: 'MQCCSESSID',
          redis: configService.redis,
          product: Product.Rover,
          validBundle: true,
        };
      },
      inject: [ConfigService],
    }),
    QichachaModule.registerAsync({
      useFactory: async (configService: ConfigService) => {
        return {
          comDomainService: configService.server.comDomainService,
          extDomainService: configService.server.extDomainService,
          appService: configService.server.appService,
          saasService: configService.server.saasService,
          bossService: configService.server.bossService,
          wxQccDomainService: configService.server.wxQccDomainService,
        };
      },
      inject: [ConfigService],
    }),
    DataModule,
    CompanySearchModule,
    SchemaModule,
    DiligenceModule,
    BatchModule,
    BasicModule,
    EventEmitterModule.forRoot(),
    RiskModelModule,
    DimensionModule,
    GroupModule,
    MetricModule,
    InternalModule,
    MonitorModule,
    UserSocketModule,
    PushModule,
    PlatformScheduleModule,
    UserModule,
    MessageModule,
    AiAnalyzerModule,
    VerificationModule,
    QaModule,
    DataCleanerModule,
  ],
  controllers: [AppController, HealthController],
  providers: [ConfigService, KzzHealthIndicator, GracefulShutdown, ApiGuardExternal],
  exports: [KzzHealthIndicator],
})
export class AppModule {}
