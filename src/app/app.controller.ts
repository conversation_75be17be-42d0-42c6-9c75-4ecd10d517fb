import { Controller, Get, Post, Query, Req, Request, Res, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiCookieAuth, ApiOkResponse, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { LogUtils, QccLogger } from '@kezhaozhao/qcc-logger';
import { PlatformUser } from '@domain/model/common';
import { Response } from 'express';
import { SessionGuard } from '@kezhaozhao/saas-auth';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@core/config/config.service';
import { Logger } from 'log4js';
import { createReadStream } from 'fs';
import { commonFileUploadOptions } from '@core/file/config';
import { FileInterceptor } from '@nestjs/platform-express';
import { v1 as uuidv1 } from 'uuid';
import { OssService } from '@kezhaozhao/qcc-aliyun-utils';

@Controller()
@ApiTags('Home')
export class AppController {
  private readonly logger: Logger = QccLogger.getLogger(AppController.name);

  constructor(protected readonly configService: ConfigService, protected readonly httpService: HttpService, private readonly ossService: OssService) {}

  @Get('ping')
  public ping() {
    return `Pong at ${new Date()}`;
  }

  @Get('/version')
  public getVersion() {
    return process.env.PIPELINE_ID || '';
  }

  @ApiCookieAuth()
  @UseGuards(SessionGuard)
  @Get('qccProfile')
  @ApiOperation({ summary: '获取用户信息' })
  @ApiOkResponse({ type: PlatformUser, description: '用户信息' })
  public qccProfile(@Request() req, @Res() res: Response) {
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', 0);
    res.setHeader('Surrogate-Control', 'no-store');
    req.user.ip = LogUtils.ip(req);
    res.json(req.user);
  }

  @Get('headers')
  headers(@Req() req) {
    return {
      headers: req.headers,
      ip1: LogUtils.ip(req),
      ip2: req.ip,
    };
  }

  @ApiCookieAuth()
  @UseGuards(SessionGuard)
  @Post('file/upload')
  @UseInterceptors(FileInterceptor('file', { ...commonFileUploadOptions }))
  @ApiParam({ name: 'file', type: 'file', description: '文件上传' })
  @ApiOperation({ summary: '上传文件，返回oss链接' })
  async fileUpload(@UploadedFile() file, @Request() req, @Query('fileName') fileName: string) {
    try {
      const finalFileName = fileName || file.originalname;
      let extension = '';
      if (finalFileName.lastIndexOf('.') > 0) {
        extension = finalFileName.substring(finalFileName.lastIndexOf('.'), finalFileName.length);
      }
      const ossObject = this.configService.getOssObject(`upload/${req.user.currentOrg}`, uuidv1() + extension);
      const options: any = {};
      if (finalFileName) {
        const downloadName = encodeURIComponent(finalFileName);
        options.headers = {
          'Content-Disposition': `attachment;filename=${downloadName};filename*=UTF-8''${downloadName}`,
        };
      }
      const result = await this.ossService.putStream(ossObject, createReadStream(file.path), options);
      return result['name'];
    } catch (e) {
      this.logger.error('oss error', e);
    }
  }
}
