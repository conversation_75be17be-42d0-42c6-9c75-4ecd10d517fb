# 📱 App Layer (应用层) - 第 7 层

应用层是 DD 平台架构的最顶层，作为整个应用的组装点和入口。本层负责将各个业务模块和基础设施服务组合成完整的应用系统。

## 🎯 层级职责

### 核心职责

- **应用启动**: 管理应用的启动流程和生命周期
- **模块组装**: 将各个业务模块组装成完整的应用
- **健康检查**: 提供应用和依赖服务的健康状态监控
- **全局配置**: 管理应用级别的配置和环境变量
- **异常处理**: 处理应用级别的异常和错误

### 设计原则

- **纯组装逻辑**: 只负责模块的组装和配置，不包含业务逻辑
- **依赖注入**: 通过依赖注入管理各个模块的生命周期
- **配置驱动**: 通过配置文件控制应用的行为和特性
- **监控友好**: 提供完善的健康检查和监控能力

## 📁 目录结构

```
app/
├── app.module.ts              # 主应用模块，组装所有业务模块
├── app.controller.ts          # 应用控制器，提供基础API
├── app.test.module.ts         # 测试应用模块
├── health/                    # 健康检查模块
│   └── health.controller.ts   # 健康检查控制器
├── health.controller.ts       # 健康检查控制器（根级别）
├── KzzHealthIndicator.ts      # 自定义健康指示器
├── GracefulShutdown.ts        # 优雅关闭处理
└── README.md                  # 本文档
```

## 🏗️ 核心组件详解

### AppModule (主应用模块)

负责组装整个应用的核心模块：

```typescript
@Module({
  imports: [
    // 核心框架模块
    CoreModule,

    // 业务功能模块
    RiskAssessmentModule,
    CompanyManagementModule,
    UserManagementModule,
    AiIntelligenceModule,
    CommunicationModule,
    BatchProcessingModule,
    DataProcessingModule,
    SystemModule,
    VerificationModule,
    InternalModule,

    // 健康检查模块
    HealthModule,
  ],
  controllers: [AppController],
  providers: [
    // 全局服务
    GracefulShutdownService,
  ],
})
export class AppModule {}
```

### HealthController (健康检查控制器)

提供应用和依赖服务的健康状态监控：

```typescript
@Controller('health')
export class HealthController {
  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.healthIndicators.database('database'),
      () => this.healthIndicators.redis('redis'),
      () => this.healthIndicators.elasticsearch('elasticsearch'),
      () => this.customHealthIndicator.check('custom'),
    ]);
  }
}
```

## 🔄 依赖关系

### 依赖的层级

```typescript
// 可以依赖所有其他层级
import { RiskAssessmentModule } from '@modules/risk-assessment'; // → modules层
import { DatabaseModule } from '@core/database'; // → core层
import { UserEntity } from '@domain/entities/user.entity'; // → domain层
import { LoggerService } from '@commons/utils/logger.utils'; // → commons层
```

### 被依赖关系

```typescript
// 主要被main.ts依赖
import { AppModule } from '@app/app.module'; // main.ts → app层
```

## 📊 监控和健康检查

### 健康检查端点

| 端点                        | 说明           | 响应格式                |
| --------------------------- | -------------- | ----------------------- |
| `GET /health`               | 全面健康检查   | JSON 格式的健康状态报告 |
| `GET /health/database`      | 数据库连接检查 | 数据库连接状态          |
| `GET /health/redis`         | Redis 连接检查 | Redis 连接状态          |
| `GET /health/elasticsearch` | ES 连接检查    | ES 连接状态             |

## 🚀 启动流程

### 应用启动顺序

1. **加载配置**: 读取环境变量和配置文件
2. **初始化 Core 模块**: 设置数据库连接、缓存等基础设施
3. **启动业务模块**: 按依赖顺序启动各个业务模块
4. **注册中间件**: 配置全局中间件和拦截器
5. **启动 HTTP 服务**: 开始监听 HTTP 请求
6. **健康检查就绪**: 健康检查端点开始响应

## 🛠️ 开发指南

### 新增模块集成

1. **创建模块**: 在`modules/`目录下创建新的业务模块
2. **导入模块**: 在`AppModule`中导入新模块
3. **配置路由**: 如果需要，配置模块的路由前缀
4. **更新健康检查**: 如果模块有外部依赖，添加健康检查

### 测试策略

- **单元测试**: 测试各个控制器和服务的独立功能
- **集成测试**: 测试模块间的集成和依赖注入
- **端到端测试**: 测试完整的 HTTP 请求响应流程
- **健康检查测试**: 验证健康检查逻辑的正确性

## ⚠️ 注意事项

### 避免的反模式

1. **业务逻辑泄露**: 应用层不应包含具体的业务逻辑
2. **过度配置**: 避免在应用层进行过于复杂的配置
3. **直接依赖**: 避免直接依赖具体的业务服务
4. **硬编码**: 避免硬编码配置值，使用环境变量

## 📚 相关文档

- [项目整体架构说明](../README.md)
- [Core 层框架基础设施](../core/README.md)
- [Modules 层业务模块](../modules/README.md)
