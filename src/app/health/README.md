# 健康检查模块 (Health Module)

## 📝 概述

健康检查模块提供应用程序健康状态监控，包括数据库连接、外部服务状态等关键指标的检查。

## 🎯 职责

- 应用程序健康状态检查
- 数据库连接状态监控
- 外部服务依赖检查
- 系统资源状态监控
- 为负载均衡器提供健康检查端点

## 📁 预期文件结构

```
health/
├── README.md                 # 本说明文件
├── health.module.ts          # 健康检查模块定义
├── health.controller.ts      # 健康检查控制器
├── health.service.ts         # 健康检查服务
├── interfaces/               # 健康检查相关接口
│   ├── health-check.interface.ts
│   └── health-indicator.interface.ts
└── __tests__/               # 测试文件
    ├── health.controller.spec.ts
    └── health.service.spec.ts
```

## 🔌 API 端点

- `GET /health` - 简单健康检查
- `GET /health/detailed` - 详细健康状态
- `GET /health/ready` - 就绪状态检查
- `GET /health/live` - 存活状态检查

## 📊 健康检查指标

- 数据库连接状态
- Redis 连接状态
- 外部 API 可用性
- 内存使用情况
- 磁盘空间状态

## 🔗 相关模块

- 依赖：@core/database, @shared/logger
- 集成：监控系统、负载均衡器
