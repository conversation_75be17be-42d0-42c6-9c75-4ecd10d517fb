import { Brackets, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'typeorm';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { DATE_TIME_FORMAT } from '@commons/constants/common';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';
import { RequestUtils } from '@kezhaozhao/qcc-common-utils';

export class FilterModel {
  min: number;
  max: number;
}

export const QueryBuilderHelper = {
  /**
   * 给query builde 叠加日期范围查询
   * @param qb
   * @param dateRange
   * @param applyTo  如果包含 "." 会使用字符串方式拼接query，否则是以Entity的属性方式拼接
   * @param dateFormat
   */
  applyDateRangeQuery: (qb: SelectQueryBuilder<any>, dateRange: DateRangeRelative | DateRangeRelative[], applyTo: string, dateFormat?: string) => {
    if (!qb || !dateRange || !applyTo || !dateRang<PERSON>[0]) {
      return;
    }
    const dateRangeRelative: DateRangeRelative = Array.isArray(dateRange) ? dateRange[0] : dateRange;
    const dateR = RequestUtils.ProcessDRR(dateRangeRelative, dateFormat || DATE_TIME_FORMAT);
    if (!dateR.end && !dateR.start) {
      console.warn("date range is invalid, won't apply to query builder");
      return;
    }
    const isComplex = applyTo.indexOf('.') > -1;
    qb.andWhere(
      new Brackets((qb1) => {
        if (isComplex) {
          if (dateR.start) {
            qb1.andWhere(`${applyTo} >= :dateStart `, { dateStart: dateR.start });
          }
          if (dateR.end) {
            qb1.andWhere(`${applyTo} <= :dateEnd`, { dateEnd: dateR.end });
          }
        } else {
          if (dateR.start) {
            qb1.andWhere({ [applyTo]: MoreThan(dateR.start) });
          }
          if (dateR.end) {
            qb1.andWhere({ [applyTo]: LessThan(dateR.end) });
          }
        }
      }),
    );
  },
};
