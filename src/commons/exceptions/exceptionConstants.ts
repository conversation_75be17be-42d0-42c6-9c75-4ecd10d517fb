export class ExceptionBaseInfo {
  code: number;
  error: string;
}

export const RoverExceptions = {
  // 100xxx
  BadParams: {
    Common: {
      code: 100001,
      error: '参数错误',
    },
    OrgUserDuplicated: {
      code: 100002,
      error: '该用户已经是企业成员，请勿重复加入',
    },
    DepartmentNotFound: {
      code: 100003,
      error: '部门不存在',
    },
    RoleNotFound: {
      code: 100004,
      error: '角色不存在',
    },
    UnknownRoleScope: {
      code: 100005,
      error: '未知的roleScope',
    },
    NotFound: {
      code: 100006,
      error: '记录不存在',
    },
    InnerBlackListDuplicated: {
      code: 100007,
      error: '企业在内部黑名单已存在，请勿重复添加',
    },
    Company: {
      SearchKeyNotFound: {
        code: 101000,
        error: '查询关键词不能为空',
      },
    },
    Phone: {
      code: 100008,
      error: '手机号码格式不正确',
    },
    Date: {
      code: 100009,
      error: '开始时间必须早于结束时间',
    },
    InnerBlackListMany: {
      code: 100010,
      error: '存在多条重复黑名单数据',
    },
  },
  // 20xxxx
  UserRelated: {
    User: {
      OrphanUser: {
        code: 200002,
        error: '用户不属于任何组织，请联系管理员添加',
      },
      ApplicationForbidden: {
        code: 200003,
        error: '您当前暂未开通风险排查权限，请联系管理员开通',
      },
      GetUserBundleInfoFailed: {
        code: 200009,
        error: '获取用户套餐信息失败',
      },
      GetUserOrgInfoFailed: {
        code: 200010,
        error: '获取用户组织信息失败',
      },
      ApplicationExpired: {
        code: 200011,
        error: '您的产品套餐已过期，请联系客户经理',
      },
      VerificationCodeError: {
        code: 200020,
        error: '验证码不正确',
      },
      PasswordNotMatch: {
        code: 200021,
        error: '两次密码不一致',
      },
      ChangePasswordFailed: {
        code: 200022,
        error: '修改密码失败',
      },
      RoleForbidden: {
        code: 200009,
        error: '您暂无该功能权限，请联系管理员',
      },
    },
    Auth: {
      ManMachineError: {
        code: 200502,
        error: '人机验证失败, 请重新拖动滑块',
      },
      OrgNotCreated: {
        code: 200509,
        error: '您尚未开通产品套餐，请提交试用申请或联系客服开通',
      },
      ApplicationNotFound: {
        code: 200008,
        error: '您当前所在组织尚未开通产品套餐，请提交试用申请或联系客服开通',
      },
      OrgNotMatch: {
        code: 200511,
        error: '当前组织已切换，请重新加载页面',
      },
      InvalidJWT: {
        code: 200512,
        error: '无效的token',
      },
      JWTError: {
        code: 200513,
        error: '权限校验失败',
      },
      JWTExpired: {
        code: 200513,
        error: 'token已过期',
      },
      RequestExpire: {
        code: 200520,
        error: 'timestamp已过期',
      },
      InvalidTimestamp: {
        code: 200521,
        error: '无效的请求',
      },
      InvalidSign: {
        code: 200522,
        error: '无效的签名',
      },
      InvalidAccessKey: {
        code: 200523,
        error: '无效的accessKey',
      },
      ForbiddenAccess: {
        code: 200524,
        error: '无权限访问或该资源不存在',
      },
      InvalidAccessToken: {
        code: 200525,
        error: '无效的AccessToken',
      },
      InvalidProduct: {
        code: 200526,
        error: '无效的产品',
      },
    },
    // 2003xx
    Role: {
      NotFound: {
        code: 200300,
        error: '角色不存在',
      }, //
      UnknownRoleScope: {
        code: 200301,
        error: '未知的 roleScope',
      },
      Duplicated: {
        code: 200302,
        error: '该角色名称已存在',
      },
      SystemRole: {
        code: 200303,
        error: '系统角色不可删除',
      },
      UserExists: {
        code: 200304,
        error: '该角色下有用户，请修改用户的角色后再删除',
      },
    },
    Trial: {
      TrialFailed: {
        code: 200409,
        error: '申请试用失败，请联系客服',
      },
      ApplyFailed: {
        code: 200410,
        error: '服务器异常，请稍后再试，或联系客服。',
      },
      ApplyDuplicated: {
        code: 200411,
        error: '申请记录已存在，请勿重复提交。',
      },
      DevFailed: {
        code: 200412,
        error: '开发环境暂不开放。',
      },
      LeadsDynamicObjectNotFound: {
        code: 200413,
        error: '无法找到线索对象',
      },
      ServiceStaffFailed: {
        code: 200414,
        error: '商务信息不存在',
      },
      LeadsDynamicObjectFieldNotFound: {
        code: 200415,
        error: '无法找到线索自定义字段',
      },
      BundleExist: {
        code: 200416,
        error: '当前手机号已开通, 现在登录。',
      },
    },
  },
  // 400XXX
  Import: {
    FileError: {
      code: 400001,
      error: '文件上传失败！该功能仅支持Excel（xls、xlsx）格式文件！',
    },
    TemplateError: {
      code: 400002,
      error: '文件上传失败！上传文件不符合模板要求，请下载最新模板后重新上传！',
    },
    DataNotFount: {
      code: 400003,
      error: '您导入的文件中无有效数据，请检查后重新上传！',
    },
    Limited: {
      code: 400004,
      error: '单次导入最大数量不超过#limit#条!',
    },
    FileNameError: {
      code: 400006,
      error: '您导入的文件名称长度过长，请控制在100个字符以内',
    },
    FileSizeError: {
      code: 400007,
      error: '文件上传失败！上传文件大小不能超过2M，请检查后重新上传！',
    },
  },
  // 5000XX
  Diligence: {
    Detail: {
      NeedKeyNo: {
        code: 500001,
        error: '缺少keyNo',
      },
      NeedSubDimensionKey: {
        code: 500002,
        error: '缺少subDimensionKey',
      },
      GetDetailsFailed: {
        code: 500003,
        error: '获取维度详情失败',
      },
      NeedDimensionKey: {
        code: 500004,
        error: '缺失维度key',
      },
      VerifyPersonFail: {
        code: 500005,
        error: '人员核实失败',
      },
      NeedCompanyId: {
        code: 500006,
        error: '缺少companyId',
      },
      PersonNotFound: {
        code: 500007,
        error: '人员不存在，请核实',
      },
    },
    Analyze: {
      NoResult: {
        code: 500101,
        error: '第三方列表企业还未执行过全量风险排查年检',
      },
    },
    Snapshot: {
      NotFound: {
        code: 500201,
        error: '快照不存在或者无权限访问',
      },
      //未生成
      NotGenerated: {
        code: 500202,
        error: '数据处理中，请稍后再试',
      },
    },
    Setting: {
      Empty: {
        code: 5000401,
        error: '您指定的模型尚未设置启用风险排查事项，请前往设置中心进行设置',
      },
    },
    Common: {
      NotFound: {
        code: 500501,
        error: '找不到持续排查记录或者没有访问权限',
      },
      Nonsupport: {
        code: 500502,
        error: '暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位发起监控',
      },
      Throttled: {
        code: 500503,
        error: '尽调请求过于频繁，请稍后再试',
      },
    },
    // 已废弃
    ContinuousDiligence: {
      DataIncomplete: {
        code: 500601,
        error: '持续监控-数据不完整',
      },
      MissedEndDiligenceData: {
        code: 500602,
        error: '持续监控-截止时间当天的数据不完整',
      },
    },
    // 招标排查
    Tender: {
      OnlyOneProcess: {
        code: 500701,
        error: '已有任务进行中，请稍后',
      },
      OutOfCompanyCount: {
        code: 500702,
        error: '单次招标排查企业数量不超过#limit#家',
      },
    },
  },
  // 6000XX
  Batch: {
    Base: {
      code: 600000,
      error: '批量任务报错',
    },
    CreateFailed: {
      code: 600001,
      error: '创建批量任务失败',
    },
    OnlyOneBatchProcess: {
      code: 600002,
      error: '存在进行中的任务，请稍后',
    },
    DataNotFount: {
      code: 600003,
      error: '无有效导出数据！',
    },
    OutOfTimes: {
      code: 600004,
      error: '已达到每天最执行次数上限！',
    },
    ThirdPartyEmpty: {
      code: 600005,
      error: '您还未在第三方列表添加企业，风险年检对象为第三方列表企业，请先前往第三方列表添加企业',
    },
    ExportBatchLimited: {
      code: 600006,
      error: '单次导出最大数量不超过#limit#条！',
    },
    RetryFailed: {
      code: 600007,
      error: '任务重试失败',
    },
    NotNeedRetry: {
      code: 600008,
      error: '无需要重试的内容',
    },
    DiscontinueFailed: {
      code: 600009,
      error: '任务中止失败',
    },
    UnSupportedDiscontinue: {
      code: 600010,
      error: '任务不支持中止操作',
    },
    BatchNotFound: {
      code: 600011,
      error: '批次不存在',
    },
    BatchAddJobsDenied: {
      code: 600012,
      error: '当前状态不允许添加新任务',
    },
    OutOfLimit: {
      code: 600012,
      error: '当前已达到任务执行数量上限，请稍后再试！',
    },
  },
  // 7000XX
  ResourceAccess: {
    ResourceBaseError: {
      code: 700001,
      error: '资源操作被拒绝',
    },
    ScopeError: {
      code: 700002,
      error: '资源操作权限不足',
      description: '只能 分发/发布 系统模型或者用户创建的模型给用户',
    },
    TargetError: {
      code: 700003,
      error: '不能分发/发布用户创建的资源给其他用户',
    },
    PublishStatusError: {
      code: 700004,
      error: '当前状态不可发布',
    },
    RiskModelEditDenied: {
      code: 700005,
      error: '当前模型不允许编辑',
    },
    MetricEditCheckDenied: {
      code: 700006,
      error: '指标不可编辑',
    },
    RiskModelNotFound: {
      code: 700007,
      error: '指定的模型不存在，',
    },
    MetricNotFound: {
      code: 700008,
      error: '指定的指标不存在，',
    },
    HitStrategyNotFound: {
      code: 700009,
      error: '指定的命中规则不存在，',
    },
    ResourceDeleteDenied: {
      code: 700010,
      error: '已发布的资源不允许删除，请先废弃，',
    },
    DistributedResourceOperateDenied: {
      code: 700011,
      error: '已分发的资源不允许废弃或删除',
    },
    SystemResourceOperateDenied: {
      code: 700011,
      error: '系统资源不允许废弃或删除',
    },
    ResourceUnauthorized: {
      code: 700012,
      error: '存在未授权的资源(模型，指标，命中规则)',
    },
    ResourceInActive: {
      code: 700013,
      error: '存在不可用的资源(模型，指标，命中规则)',
    },
    HitStrategyFieldNotFound: {
      code: 7000014,
      error: '指定的命中规则属性不存在，',
    },
    NoMorePublishAllowed: {
      code: 7000015,
      error: '同一个模型已经有发布状态的模型，如需发布新版本，请先废弃旧版本.',
    },
    MainRiskModelNotDistributed: {
      code: 7000016,
      error: '该模型对应的主模型未分发给指定组织，无法分发子模型',
    },
    CopyRiskModelMaxLimit: {
      code: 7000017,
      error: '当前分组下开发中状态的模型数量已达到上限，无法再创建模型',
    },
    MaxPublishMaxLimit: {
      code: 7000018,
      error: '当前组织已启用状态的模型数量已达到上限，无法发布新模型',
    },
    RiskModelNameDuplicate: {
      code: 7000019,
      error: '模型名称重复',
    },
  },
  // 8000xx
  Common: {
    CallbackFailed: {
      code: 800002,
      error: '回调失败',
    },
    RequestFailed: {
      code: 800003,
      error: '服务端接口请求失败',
    },
    SMS: {
      Error: {
        code: 900200,
        error: '短信发送失败',
      },
      Duplicated: {
        code: 900201,
        error: '60秒内只允许发送一次验证码',
      },
    },
    Request: {
      AccessDenied: {
        code: 900100,
        error: '访问被拒绝',
      },
      ReachLimit: {
        code: 900101,
        error: '请求数达到最大限制',
      },
      Duplicated: {
        code: 900102,
        error: '记录已存在，请勿重复提交',
      },
      NotFound: {
        code: 900103,
        error: '记录不存在',
      },
      BatchLimited: {
        code: 900104,
        error: '批量操作数量超过上限',
      },
      CreateReachLimit: {
        code: 900105,
        error: '创建数量已达上限！',
      },
    },
  },
  // 9000xx
  Bundle: {
    PersonReachLimit: {
      code: 900001,
      error: '您可管理的人员数量已达到上限！',
    },
    InnerBlacklistReachLimit: {
      code: 900002,
      error: '您可管理的内部黑名单数量已达到上限！',
    },
    CustomerReachLimit: {
      code: 900003,
      error: '您可管理的第三方数量已达到上限！',
    },
    DailyDiligencReachLimit: {
      code: 900004,
      error: '您可每日排查此时已达到上限！',
    },
    CounterOperationNotFound: {
      code: 900005,
      error: '计数器操作失败！ CounterOperation 不存在！',
    },
    PublishedModelReachedLimit: {
      code: 900006,
      error: '已发布的模型数量已达到上限！',
    },
  },
  Monitor: {
    // 10000xx
    Group: {
      NotFound: {
        code: 1000001,
        error: '分组不存在',
      },
      NotDelete: {
        code: 1000002,
        error: '无法删除，至少要保留一个分组',
      },
      RecycleFull: {
        code: 1000003,
        error: '回收站已满，无法删除',
      },
      TypeError: {
        code: 1000004,
        error: '分组类型错误',
      },
      AddError: {
        code: 1000005,
        error: '添加分组失败',
      },
      EditError: {
        code: 1000006,
        error: '编辑分组失败',
      },
      DuplicatedError: {
        code: 1000007,
        error: '新增分组名重复',
      },
      DeleteError: {
        code: 1000008,
        error: '移除分组失败',
      },
      NotMonitor: {
        code: 1000009,
        error: '监控未开启',
      },
      NoPermission: {
        code: 1000010,
        error: '无查看权限',
      },
      BatchLimited: {
        code: 1000011,
        error: '批量添加超限',
      },
      Limited: {
        code: 1000012,
        error: '分组数量超限',
      },
      CanNotEdited: {
        code: 1000013,
        error: '该分组不可编辑',
      },
      CanNotDeleteWithCompany: {
        code: 1000014,
        error: '该分组下存在持续排查企业，不能删除分组！',
      },
      GroupSameName: {
        code: 1000015,
        error: '分组名字已经存在，不能重复添加',
      },
      DefaultGroupDelete: {
        code: 1000016,
        error: '默认分组不可被删除',
      },
      NotGroupAuth: {
        code: 1000017,
        error: '你没有权限查看这些分组',
      },
    },
    // 10001xx
    Company: {
      Duplicated: {
        code: 1000102,
        error: '记录已存在，请勿重复提交',
      },
      GroupNotExists: {
        code: 1000102,
        error: '分组不存在',
      },
      GroupCompanyLimit: {
        code: 1000102,
        error: '该分组下的公司数量已达到上限',
      },
      AccessDenied: {
        code: 1000100,
        error: '访问被拒绝',
      },
      ReachLimit: {
        code: 1000101,
        error: '请求数达到最大限制',
      },
      NotFound: {
        code: 1000103,
        error: '记录不存在',
      },
      BatchLimited: {
        code: 1000104,
        error: '批量操作数量超过上限',
      },
      CreateReachLimit: {
        code: 1000105,
        error: '创建数量已达到最大限制',
      },
      InvalidRelatedCompanies: {
        code: 1000106,
        error: '无效的关联方',
      },
      DuplicatedMonitorCompanies: {
        code: 1000107,
        error: '存在重复的监控企业数据',
      },
      MonitorCompanyNotFound: {
        code: 1000108,
        error: '监控企业不存在',
      },
      MonitorRelatedCompanyLength: {
        code: 1000109,
        error: '批量添加关联方企业为空或者超出100家企业',
      },
    },
    // 10002xx
    Dynamic: {
      MonitorDynamicTypeNotFound: {
        code: 1000201,
        error: '监控动态类型不存在',
      },
      NotNeedHandle: {
        code: 1000202,
        error: '监控动态无需处理',
      },
      MonitorGroupIdNotNull: {
        code: 1000203,
        error: '分组id不可为空',
      },
    },
    // 10003xx
    Settings: {
      NoDefaultSetting: {
        code: 1000300,
        error: '没有默认的监控设置',
      },
      NoDefaultRiskModel: {
        code: 1000302,
        error: '没有默认的监控模型',
      },
      RiskModelNotExist: {
        code: 1000303,
        error: '没找到监控模型',
      },
    },
    // 10004xx
    Message: {
      Duplicated: {
        code: 1000401,
        error: '记录已存在，请勿重复提交',
      },
    },
    // 10005xx
    Import: {
      UnsuccessfulData: {
        code: 1000501,
        error: '未识别到符合格式的数据',
      },
      PreventInitialSelection: {
        code: 1000502,
        error: '首次执行监控不可选中监控',
      },
      ImportMonitorBatchNotFound: {
        code: 1000503,
        error: '不存在的批量监控批次',
      },
      ImportMonitorItemNotFound: {
        code: 1000504,
        error: '不存在匹配成功或待重试的批量监控数据，无法执行批量排查',
      },
      ImportMonitorItemUpdateNotFound: {
        code: 1000505,
        error: '不存在匹配成功或待重试的批量监控数据，无法执行批量排查',
      },
    },
  },
  // 11xxxx
  RiskModel: {
    NotFound: {
      code: 110001,
      error: '模型不存在',
    },
    NeedBranchCode: {
      Duplicated: {
        code: 110001,
        error: '请指定模型编号',
      },
    },
    Dimension: {
      //从 111 000 开始
      Duplicated: {
        code: 111001,
        error: '风险维度已存，请勿重复提交',
      },
    },
    DimensionFields: {
      //从 112 000 开始
      Duplicated: {
        code: 112001,
        error: '风险维度属性已存，请勿重复提交',
      },
    },
    Operation: {
      //从 113 000 开始
      TurnOffFailed: {
        code: 113001,
        error: '无法禁用非发布状态的模型',
      },
    },
    Deprecated: {
      code: 114001,
      error: '模型已废弃',
    },
  },
  // 1200xxx
  Setting: {
    CreateReachLimit: {
      code: 1200001,
      error: '创建数量已达到最大限制',
    },
    NameDuplicated: {
      code: 1200002,
      error: '模型名称重复',
    },
    AtLeastOne: {
      code: 1200003,
      error: '必须存在一个模型',
    },
    CanNotDelete: {
      code: 1200004,
      error: '模型被使用，无法删除',
    },
    InvalidId: {
      code: 1200005,
      error: '模型ID不合法',
    },
    CreateMonitorGroupLimit: {
      code: 1200006,
      error: '当前组织仅支持不超过 50 个分组，请联系管理员',
    },
    pageQueryLimit: {
      code: 1200007,
      error: '分页查询不可超过五万',
    },
    monitorRelatedCompaniesNull: {
      code: 1200008,
      error: '无可监控关联方企业',
    },
    monitorRelatedCompanyLengthMax: {
      code: 1200009,
      error: '监控全部单次最多可监控六千家企业',
    },
    transferError: {
      code: 120010,
      error: '监控企业移组失败',
    },
    addRelatedError: {
      code: 120011,
      error: '添加关联方企业失败',
    },
    transferDuplicateError: {
      code: 120012,
      error: '目标分组已监控待移组企业',
    },
  },
  // 1xxx
  Openapi: {
    Bidding: {
      UnMatchedCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
      InvalidTenderNo: {
        code: 1003,
        error: '排查编号不存在相应的排查记录',
      },
    },
    Diligence: {
      UnMatchedCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
    },
    Customer: {
      UnMatchedCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
    },
    InnerBlacklist: {
      UnMatchedCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
    },
    Monitor: {
      UselessCompany: {
        code: 1001,
        error: '不匹配的企业名称或统一社会信用代码',
      },
      UnSupportedCompany: {
        code: 1002,
        error: '不支持的企业',
      },
      InvalidCompany: {
        code: 1003,
        error: '企业不存在',
      },
    },
  },
  // 1400xxx
  Push: {
    GroupNotExist: {
      code: 1400001,
      error: '分组信息不存在',
    },
    RuleNotExist: {
      code: 1400002,
      error: '推送设置不存在',
    },
  },
  AI: {
    AIBaseException: {
      code: 1400000,
      error: 'AI模块错误',
    },
    ToAnalyzeContentShouldNotEmpty: {
      code: 1400001,
      error: 'AI分析报告时候，待分析内容不能为空',
    },
    UnsupportedProduct: {
      code: 1400002,
      error: '不支持的产品',
    },
    UnsupportedModel: {
      code: 1400003,
      error: '不支持的模型',
    },
    UnsupportedBusinessType: {
      code: 1400004,
      error: '不支持的业务类型',
    },
    RequestChatException: {
      code: 1400005,
      error: '发起Chat失败',
    },
    AIUsageLimitExceeded: {
      code: 1400006,
      error: 'AI使用次数超限',
    },
  },
  // 1500xxx
  Verification: {
    UnsuccessfulData: {
      code: 150001,
      error: '未识别到符合格式的数据',
    },
    RequestCheckException: {
      code: 150002,
      error: '请求入参校验失败',
    },
  },
};
