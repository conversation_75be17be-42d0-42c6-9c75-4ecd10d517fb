import { ArgumentsHost, BadRequestException, Catch, ExceptionFilter, PayloadTooLargeException } from '@nestjs/common';
import { RoverExceptions } from './exceptionConstants';

@Catch(PayloadTooLargeException)
export class FileSizeLimitExceptionFilter implements ExceptionFilter {
  catch(exception: PayloadTooLargeException, host: ArgumentsHost) {
    throw new BadRequestException(RoverExceptions.Import.FileSizeError);
  }
}
