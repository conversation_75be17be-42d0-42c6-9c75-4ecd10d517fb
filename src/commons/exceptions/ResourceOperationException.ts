import { ForbiddenException } from '@nestjs/common';
import { RoverExceptions } from './exceptionConstants';

export class ResourceOperationException extends ForbiddenException {
  constructor(msg = RoverExceptions.ResourceAccess.ResourceBaseError, message?: string) {
    let msgObj = msg;
    if (message) {
      msgObj = Object.assign({ ...msgObj }, { error: msgObj.error + ':' + message });
    }
    super(msgObj);
  }
}
