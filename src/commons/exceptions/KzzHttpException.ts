import { HttpException, UnauthorizedException } from '@nestjs/common';
import { RoverExceptions } from './exceptionConstants';
import { isAxiosError } from '@nestjs/terminus/dist/utils';
import { KzzExceptionResponse } from '@kezhaozhao/qcc-exception-handler';

export class KzzHttpException extends HttpException {
  constructor(kzzExceptionResponse: { statusCode: number; error?: string; path?: string }) {
    super(
      Object.assign({ ...kzzExceptionResponse }, { message: `error=${kzzExceptionResponse.error},path=${kzzExceptionResponse.path}` }),
      kzzExceptionResponse.statusCode,
    );
  }

  static getError(err: Error): HttpException {
    if (err?.name == 'JsonWebTokenError') {
      return new UnauthorizedException(RoverExceptions.UserRelated.Auth.InvalidJWT);
    } else if (isAxiosError(err)) {
      const t = err.response.data as KzzExceptionResponse;
      if (t.error && t.code && t.statusCode) {
        // 说明是 内部服务抛出的错误
        return new KzzHttpException({
          ...t,
        });
      }
      return new KzzHttpException(Object.assign({ ...t }, { error: err.response.data || err.message }));
    }
    return null;
  }
}
