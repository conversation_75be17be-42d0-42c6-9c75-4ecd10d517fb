import { ClassTransformOptions, plainToInstance } from 'class-transformer';
import { getMetadataArgsStorage } from 'typeorm';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';

/**
 * 把数据库对象直接转换成对应的entity 对象
 * @param entity
 * @param plainObject
 * @param transformOptions
 */
export const rowToEntity = <T>(entity: new () => T, plainObject: Record<string, any>, transformOptions?: ClassTransformOptions): T => {
  const entityInstance: any = {};
  // 获取TypeORM的列元数据
  const columns = getMetadataArgsStorage().columns.filter((column) => column.target === entity);
  columns.forEach((column) => {
    const propertyName = column.propertyName;
    const columnName = column.options.name || propertyName;

    if (plainObject.hasOwnProperty(columnName)) {
      const value = plainObject[columnName];
      // 检查并转换复杂对象
      if (column.options.type === 'json' && typeof value === 'string') {
        try {
          entityInstance[propertyName] = JSON.parse(value);
        } catch (e) {
          console.error(`Error parsing JSON for ${propertyName}: ${e}`);
        }
      } else {
        entityInstance[propertyName] = value;
      }
    }
  });

  return plainToInstance(DiligenceHistoryEntity, entityInstance) as T;
};
