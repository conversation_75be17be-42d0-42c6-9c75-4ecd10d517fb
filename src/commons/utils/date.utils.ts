import { DATE_FORMAT } from '@commons/constants/common';
import * as moment from 'moment/moment';
import { DateRange, DateRangeRelative } from '@kezhaozhao/qcc-model';
import { NumberRange } from '@domain/model/data/source/CreditSearchFilter';

export const dateTransform = (date: any, dateFormat = DATE_FORMAT) => {
  if (!date) return '-';
  if (typeof date === 'number') {
    return moment(date * 1000).format(dateFormat);
  } else if (date instanceof Date || typeof date === 'string') {
    return moment(date, moment.ISO_8601).format(dateFormat);
  } else {
    return '-';
  }
};

/**
 * 获取未来近多少时间
 * @param dateType
 */
export const getExpirationDate = (dateType = 2) => {
  switch (dateType) {
    case 1:
      // 近 7 天
      return moment().add('7', 'days').startOf('day').unix();
    case 3:
      // 近 3 个月
      return moment().add('3', 'months').startOf('day').unix();
    default:
      // 近 1 个月
      return moment().add('1', 'months').startOf('day').unix();
  }
};

/**
 * 获取过去近多少时间
 * @param dateType
 */
export const getPastExpirationDate = (dateType = 2) => {
  switch (dateType) {
    case 1:
      // 近 7 天
      return moment().add('-7', 'days').startOf('day').unix();
    case 3:
      // 近 3 个月
      return moment().add('-3', 'months').startOf('day').unix();
    default:
      // 近 1 个月
      return moment().add('-1', 'months').startOf('day').unix();
  }
};

/**
 *  20210501  -> 2021-05-01
 * @param dateString
 * @private
 */
export const convertDateFormat = (dateString: string): string => {
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);
  return `${year}-${month}-${day}`;
};

export const dateRangeRelativeToDateRange = (d: DateRangeRelative, format = 'YYYY-MM-DD'): DateRange => {
  const d1: DateRange = new DateRange();
  const currently: boolean = d.currently;
  const quantity: number = d.number;
  let startDate: moment.Moment;
  let endDate: moment.Moment | void = moment();
  let baseDate = moment().endOf('day');
  let unit: moment.unitOfTime.StartOf;

  switch (d.unit) {
    case 'day':
      unit = 'day';
      break;
    case 'week':
      unit = 'week';
      break;
    case 'month':
      unit = 'month';
      break;
    case 'quarter':
      unit = 'quarter';
      break;
    case 'year':
      unit = 'year';
      break;
    default: {
      unit = 'day';
      break;
    }
  }

  if (!currently) {
    // 实时查询操作
    baseDate = moment().subtract(1, unit).endOf(unit);
  }
  switch (d.flag) {
    case 0:
      // 本日，本周，本月，本年
      startDate = moment(baseDate).startOf(unit).startOf('day');
      endDate = moment(baseDate).endOf('day');
      break;
    case 1:
      // 近几日，近几周，近几月，近几年
      startDate = moment(baseDate)
        .subtract(quantity - 1, unit)
        .startOf(unit);
      endDate = moment(baseDate).endOf('day');
      break;
    case 2:
      // 昨日，前天 等之前的某一日
      startDate = moment(baseDate).subtract(quantity, 'day').startOf('day');
      endDate = moment(startDate).endOf('day');
      break;
    case 3:
      // xx 日，周，月，年 以上 成立日期 < endDate
      startDate = undefined;
      endDate = moment(baseDate).subtract(quantity, unit).endOf('day');
      break;
    case 4:
      // N ~ M 日，周，月，年 之间
      // 例如 3到5年  N = 3, M = 5
      startDate = moment(baseDate).subtract(Number(d.max), unit).startOf('day');
      endDate = moment(baseDate).subtract(Number(d.min), unit).endOf('day');
      break;
    case 5:
      // 固定日期之间
      // 例如 2018-08-08到2019-09-09
      startDate = moment(d.min);
      endDate = moment(d.max);
      break;
    case 6:
      // xxxx年xx月 或 xxxx年第x季度 起止时间
      if (d.year != null) {
        startDate = moment()
          .set('year', d.year)
          .set(unit, unit == 'month' ? quantity - 1 : quantity)
          .startOf(unit);
      }
      if (d.year != null) {
        endDate = moment()
          .set('year', d.year)
          .set(unit, unit == 'month' ? quantity - 1 : quantity)
          .endOf(unit);
      }
      break;
    case 7:
      // 昨天到期，n 天前到期，不需要开始时间
      if (quantity < 0) {
        startDate = undefined;
      }
      // 当天到期，未来时间到期，n 天内到期，开始时间为当天开始
      else {
        startDate = moment().startOf('day');
      }
      endDate = moment().add(quantity, unit).endOf('day');
      break;
    default:
      break;
  }

  d1.start = startDate?.isValid() ? startDate.format(format) : (undefined as any);
  d1.end = endDate?.isValid() ? endDate.format(format) : (undefined as any);
  return d1;
};

export const dateRangeRelativeToTimestamp = (dateRangeRelative: DateRangeRelative): NumberRange => {
  const dateRange = dateRangeRelativeToDateRange(dateRangeRelative);
  return Object.assign(new NumberRange(), {
    min: moment(dateRange.start).startOf('day').unix(),
    max: moment(dateRange.end).endOf('day').unix(),
  });
};

/**
 * 标准日期格式转换工具
 * @param inputDate 支持格式：YYYYMMDD | YYYY-MM-DD | YYYY/MM/DD
 * @param isStartOfDay 是否取当天起始时间（默认取当前时区0点）
 */
export const dateToTimestamp = (inputDate: string, isStartOfDay = true): number | null => {
  // 统一处理分隔符
  const normalized = inputDate.replace(/[^0-9]/g, '');

  // 格式校验（支持6位简写格式如202410）
  if (!/^\d{6,8}$/.test(normalized)) return null;

  // 自动补全日期
  const formatStr = 'YYYYMMDD';
  let dateStr = normalized.padEnd(8, '0');

  // 处理6位简写格式（如202410 -> 20241001）
  if (dateStr.length === 6) {
    dateStr += '01';
  }

  const m = moment(dateStr, formatStr, true);
  if (!m.isValid()) return null;

  return (isStartOfDay ? m.startOf('day') : m).valueOf() / 1000;
};
