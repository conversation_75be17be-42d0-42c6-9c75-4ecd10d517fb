// //------------------ 加密数据 -------------------
// import { DateRangeRelative } from '@kezhaozhao/search-utils';
// import * as crypto from 'crypto';
// import * as moment from 'moment';
// import { RequestUtils } from '@kezhaozhao/qcc-utils';
// import { PaginationParams, PaginationResponse } from '../model/common';

// const algorithm = 'aes-128-cbc';

// /**
//  * @description 加密数据
//  * @description params: data--要加密的数据，必须是utf8格式的字符串；callback--处理结果集的回调函数
//  * @param data {string}
//  */
// export const encrypt = async (data: string): Promise<string> => {
//   const password = crypto.randomBytes(16).toString('hex'); // password是用于生产密钥的密码
//   const salt = crypto.randomBytes(16).toString('hex'); // 生成盐值
//   const iv = crypto.randomBytes(8).toString('hex'); // 初始化向量

//   return new Promise((resolve, reject) => {
//     crypto.scrypt(password, salt, 16, function (err, derivedKey) {
//       if (err) {
//         reject(err);
//       } else {
//         const cipher = crypto.createCipheriv(algorithm, derivedKey, iv); // 创建 cipher 实例

//         // 加密数据
//         let cipherText = cipher.update(data, 'utf8', 'hex');
//         cipherText += cipher.final('hex');
//         cipherText += password + salt + iv;

//         resolve(cipherText);
//       }
//     });
//   });
// };

// /**
//  * @description 解密通过 encrypt(); 加密的数据
//  * @description param: cipherText--通过 encrypt() 加密的数据; callback--处理结果集的回调函数。
//  * @param cipherText {string}
//  */
// export const decrypt = async (cipherText: string): Promise<string> => {
//   const iv = cipherText.slice(-16); // 获取初始化向量
//   const salt = cipherText.slice(-48, -16); // 获取盐值
//   const password = cipherText.slice(-80, -48); // 获取密钥密码
//   const data = cipherText.slice(0, -80); //获取密文

//   return new Promise((resolve, reject) => {
//     crypto.scrypt(password, salt, 16, function (err, derivedKey) {
//       if (err) {
//         reject(err);
//       } else {
//         const decipher = crypto.createDecipheriv(algorithm, derivedKey, iv); // 创建 decipher 实例

//         // 解密数据
//         let txt = decipher.update(data, 'hex', 'utf8');
//         txt += decipher.final('utf8');
//         resolve(txt);
//       }
//     });
//   });
// };

// /**
//  * 四舍五入
//  * @param num
//  * @param fractionDigits
//  * @returns
//  */
// export const toRoundFixedOld = (num: number, fractionDigits = 2): string => {
//   let result = num.toString();

//   const arr = num.toString().split('.');
//   const integer = arr[0]; // 整数部分
//   let decimal = arr[1]; // 小数部分
//   if (!decimal) {
//     //小数点处理
//     result += '.';
//     for (let i = 0; i < fractionDigits; i += 1) {
//       result += '0';
//     }
//     return result;
//   }
//   const length = decimal?.length || 0;

//   // 如果小于指定的位数，补上
//   if (length < fractionDigits) {
//     for (let i = 0; i < fractionDigits - length; i += 1) {
//       result += '0';
//     }
//     return result;
//   }

//   // 如果已经符合要求位数，直接返回
//   if (decimal?.length == fractionDigits) {
//     return result;
//   }

//   // 大于指定位数四舍五入处理

//   // 小数部分保留 指定位数
//   decimal = decimal.substring(0, fractionDigits) + '.' + decimal.substring(fractionDigits, fractionDigits + 1);
//   // 四舍五入处理
//   decimal = Math.round(parseFloat(decimal)).toString();
//   // 整数和小数部分拼接
//   result = integer + '.' + decimal;
//   return result;
// };

// /**
//  * formatMoney
//  * @param inputNumber
//  */
// export const formatMoney = (inputNumber) => {
//   if (!inputNumber) {
//     return '-';
//   }
//   let transferNumber = transferToNumber(inputNumber);
//   transferNumber = parseFloat(transferNumber);
//   const res = transferNumber.toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
//   if (isNaN(inputNumber)) {
//     return inputNumber;
//   }
//   return res;
// };

export const toRoundFixed = (num: number, fractionDigits = 2): string => {
  // 为了解决 142.605 保留两位四舍五入会变成 142.6的问题 ，强制加上 0.00001
  return Number((num + 0.00001).toFixed(fractionDigits)).toString();
};

/**
 * 科学计数法转数字
 * @param inputNumber
 */
export const transferToNumber = (inputNumber) => {
  if (isNaN(inputNumber)) {
    return inputNumber;
  }
  inputNumber = '' + inputNumber;
  inputNumber = parseFloat(inputNumber);
  if (isNaN(inputNumber)) {
    return inputNumber;
  }
  const eformat = inputNumber.toExponential(); // 转换为标准的科学计数法形式（字符串）
  const tmpArray = eformat.match(/\d(?:\.(\d*))?e([+-]\d+)/); // 分离出小数值和指数值
  return inputNumber.toFixed(Math.max(0, (tmpArray[1] || '').length - tmpArray[2]));
};

export const stringToSeed = (str: string): number => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash |= 0; // Convert to 32bit integer
  }
  return hash;
};

export const getEconTypeListNamesByValueList = (valueList, list: any[]) => {
  const nameList = [];

  valueList.forEach((value) => {
    let foundName = null;
    for (const item of list) {
      if (item.value === value) {
        foundName = item.name;
        break;
      }
      if (item.list) {
        const found = item.list.find((subItem) => subItem.value === value);
        if (found) {
          foundName = found.name;
          break;
        }
      }
    }
    if (foundName) {
      nameList.push(foundName);
    }
  });

  return nameList;
};

export const paginate = <T>(list: T[], pageIndex: number, pageSize: number): T[] => {
  if (!pageIndex) {
    pageIndex = 1;
  }
  if (!pageSize) {
    pageSize = 10; // 可以设置一个默认每页显示数量，这里设为10，你可按需修改
  }
  const startIndex = (pageIndex - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  if (startIndex >= list.length) {
    return [];
  }
  return list.slice(startIndex, Math.min(endIndex, list.length));
};

/**
 * 辅助函数：标准化 JSON 字符串
 * @param jsonString
 * @returns
 */
export const normalizeJsonString = (jsonString: string): string => {
  try {
    const obj = JSON.parse(jsonString);
    return JSON.stringify(obj, Object.keys(obj).sort());
  } catch (error) {
    return jsonString;
  }
};

/**
 * 通用方法：处理金额字符串，提取数值并统一转换为元
 * @param amountStr 金额字符串，如"4500万元人民币"、"3.59%"等
 * @param isPercentage 是否为百分比
 * @returns 转换后的数值
 */
export const processAmountString = (amountStr: string, isPercentage = false): number | null => {
  if (!amountStr) {
    return null;
  }

  const match = amountStr.match(/(\d+(?:\.\d+)?)/);
  if (!match) {
    return null;
  }

  let value = parseFloat(match[1]);

  if (!isPercentage) {
    // 转换单位为元
    if (amountStr.includes('万')) {
      value = value * 10000;
    } else if (amountStr.includes('亿')) {
      value = value * 100000000;
    }
  }
  return value;
};

/**
 * 通用方法，判断目标码值是否是当前码值的前缀
 * @param targetCode
 * @param currentCode
 */
export const isCodePrefixMatch = (targetCode: string, currentCode: string): boolean => {
  // 取目标码值长度作为比较位数
  const prefixLength = targetCode.length;
  return currentCode.length >= prefixLength && currentCode.slice(0, prefixLength) === targetCode;
};

/**
 * 通用方法，判断目标码值是否与当前码值完全相同
 * @param targetCode 目标码值
 * @param currentCode 当前码值
 */
export const isCodeExactMatch = (targetCode: string, currentCode: string): boolean => {
  return targetCode === currentCode;
};

/**
 * 检查字符串是否包含指定关键词
 * @param target 被检查的字符串
 * @param keywords 关键词数组
 * @param excludeKeywords 需要排除的关键字数组（可选）
 * @returns 包含任意关键词且不包含排除词返回true，否则false
 */
export const containsKeywords = (target: string, keywords: string[], excludeKeywords?: string[]): boolean => {
  if (!target || !keywords?.length) return false;
  const lowerTarget = target.toLowerCase();
  return keywords.some(
    (kw) => lowerTarget.includes(kw.toLowerCase()) && (!excludeKeywords?.length || !excludeKeywords.some((ex) => lowerTarget.includes(ex.toLowerCase()))),
  );
};

// 基于时间戳生成规则结果
export const getTimeBasedRule = (): 0 | 1 => {
  const timestamp = new Date().getTime();
  return (timestamp % 2) as 0 | 1;
};
