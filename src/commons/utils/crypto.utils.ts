import * as crypto from 'crypto';

/**
 * 计算字符串的 MD5 哈希值
 * @param input 需要计算 MD5 的字符串
 * @returns MD5 哈希值（32位十六进制字符串）
 */
export const md5 = (input: string): string => {
  return crypto.createHash('md5').update(input).digest('hex');
};

/**
 * 计算字符串的 SHA256 哈希值
 * @param input 需要计算 SHA256 的字符串
 * @returns SHA256 哈希值（64位十六进制字符串）
 */
export const sha256 = (input: string): string => {
  return crypto.createHash('sha256').update(input).digest('hex');
};

/**
 * 生成随机字符串
 * @param length 随机字符串长度
 * @returns 随机十六进制字符串
 */
export const randomHex = (length: number): string => {
  return crypto
    .randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
};
