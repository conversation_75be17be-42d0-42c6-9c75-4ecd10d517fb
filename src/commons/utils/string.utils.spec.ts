import { toFullWidth } from './string.utils';
import { dateTransform } from './date.utils';

describe.skip('toFullWidth test', () => {
  it('should pass', () => {
    expect(toFullWidth(12)).toBe(12);
    expect(toFullWidth(null)).toBe(null);
    expect(toFullWidth({})).toStrictEqual({});
    expect(toFullWidth('')).toBe('');
    expect(toFullWidth('小米(中国)有限公司')).toBe('小米（中国）有限公司');
    expect(toFullWidth('小米（中国）有限公司')).toBe('小米（中国）有限公司');
    expect(toFullWidth('小米中国有限公司')).toBe('小米中国有限公司');
    expect(['小米(中国)有限公司', '小米中国有限公司'].map(toFullWidth)).toStrictEqual(['小米（中国）有限公司', '小米中国有限公司']);
  });

  it('test transform null', () => {
    const r: any = {};
    const res = dateTransform(r.name);
    expect(res).toBe('-');
  });
});
