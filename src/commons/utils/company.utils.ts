import { CommonListItem } from '@kezhaozhao/company-search-api';
import { find } from 'lodash';

/**
 * 根据Person的 operType 获取身份标签
 * @param operType
 * @returns
 */
export const getLegalManType = (operType: number) => {
  const kvOperType = {
    1: '法定代表人',
    2: '执行事务合伙人',
    3: '负责人',
    4: '经营者',
    5: '投资人',
    6: '董事长',
    7: '理事长',
    8: '代表人',
  };

  let legalManLabel = '法定代表人';
  if (kvOperType?.[operType]) {
    legalManLabel = kvOperType[operType];
  }
  return legalManLabel;
};

/**
 * 从commonlist中取Person的keyNo
 * @param personName
 * @param commonlist
 * @returns
 */
export const getPersonKeyNo = (personName: string, commonlist: CommonListItem[]): string => {
  //  董事长、总经理、大股东信息
  // EmployeInfo = 9,
  // {
  //   "k": "9",
  //   "v": "[{\"k\":\"pa8318b7534e822a824bf48a2dd58989\",\"n\":\"伍昕\",\"o\":2,\"j\":\"经理,执行董事\",\"bp\":0},{\"k\":\"p96ed71582b5263be3f173eff5ffb005\",\"n\":\"郝日芳\",\"o\":2,\"j\":null,\"bp\":1}]"
  // },
  //  MultipleOper
  // MultipleOper = 10,
  // {
  //   "k": "10",
  //   "v": "{\"OperType\":1,\"OperList\":[{\"k\":\"p4be96820184ebbfe40984870540b2f5\",\"n\":\"吴孟\",\"o\":2,\"h\":false}]}"
  // }

  personName = personName.replace(/<em>/g, '').replace(/<\/em>/g, '');
  let keyNo = '';
  let employeInfo = find(commonlist, ['k', '9'])?.v;
  if (employeInfo) {
    employeInfo = find(JSON.parse(employeInfo), ['n', personName]);
    keyNo = employeInfo?.['k'];
    if (keyNo) {
      return keyNo;
    }
  }

  let multipleOper = find(commonlist, ['k', '10'])?.v;
  if (multipleOper) {
    multipleOper = find(JSON.parse(multipleOper)['OperList'], ['n', personName]);
    keyNo = multipleOper?.['k'];
    if (keyNo) {
      return keyNo;
    }
  }
  return keyNo;
};

/**
 * 判断是否社会组织
 * @param keyNo
 */
export const isOrganism = (keyNo: string): boolean => {
  if (keyNo && keyNo.startsWith('s')) {
    return true;
  }
  return false;
};

/**
 * 获取营业额
 * @param commonList
 * @param key 搜索Count信息通用类别
 * @param isLast
 * @param reportType
 */
export const getRevenue = (commonList: any[], key: string, isLast: number, reportType: number): number | string => {
  const item = commonList?.find((c) => c.k == key);
  if (!item) return '-';

  try {
    const parsedValue = JSON.parse(item.v);
    if (!Array.isArray(parsedValue)) return '-';

    const foundItem = parsedValue.find((f) => f.IsLast == isLast && f.ReportType == reportType);
    if (!foundItem || foundItem.Revenue === undefined) return '-';

    return Number(foundItem.Revenue) * 10000;
  } catch (error) {
    console.error('Error parsing JSON or processing data:', error);
    return '-';
  }
};
