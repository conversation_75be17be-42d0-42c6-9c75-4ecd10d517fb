# 🛠️ Commons Layer (公共基础层) - 第 3 层

公共基础层是 DD 平台架构的最底层，提供纯工具函数和基础类型定义，不包含任何业务逻辑。本层为整个应用提供稳定、可复用的基础工具支撑。

## 🎯 职责

- 提供纯工具函数（无副作用）
- 定义基础类型和常量
- 提供通用的格式化和验证函数
- 保持完全的技术中立性

## 📁 目录结构

```
commons/
├── utils/                  # 纯工具函数
│   ├── string.company.utils.ts     # 字符串工具
│   ├── date.company.utils.ts       # 日期工具
│   ├── math.company.utils.ts       # 数学计算
│   ├── validation.company.utils.ts # 纯验证函数
│   ├── format.company.utils.ts     # 格式化工具
│   └── array.company.utils.ts      # 数组工具
├── constants/              # 通用常量
│   ├── regex.constants.ts  # 正则表达式常量
│   ├── format.constants.ts # 格式化常量
│   └── http.constants.ts   # HTTP状态码等
└── types/                  # 基础类型定义
    ├── common.types.ts     # 通用类型
    ├── utility.types.ts    # 工具类型
    └── exception.types.ts  # 基础异常类型
```

## 🔗 依赖关系

- **依赖**: 无（除第三方库如 lodash、moment 等）
- **被依赖**: @domain, @infrastructure, @modules, @core

## 📋 设计原则

### 1. 纯函数原则

所有工具函数都应该是纯函数：

```typescript
// ✅ 好的示例 - 纯函数
export function formatCurrency(amount: number, currency = 'CNY'): string {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency,
  }).format(amount);
}

// ❌ 避免 - 有副作用
export function logAndFormat(amount: number): string {
  console.log(`Formatting amount: ${amount}`); // 副作用
  return formatCurrency(amount);
}
```

### 2. 技术中立原则

不包含任何业务逻辑或业务概念：

```typescript
// ✅ 好的示例 - 技术中立
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// ❌ 避免 - 包含业务概念
export function isValidUserEmail(email: string): boolean {
  // 包含了"用户"这个业务概念
  return isValidEmail(email) && !email.includes('admin');
}
```

### 3. 可测试原则

所有函数都应该易于测试：

```typescript
// utils/date.company.utils.ts
export function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

// 对应的测试
describe('addDays', () => {
  it('应该正确增加天数', () => {
    const date = new Date('2024-01-01');
    const result = addDays(date, 5);
    expect(result).toEqual(new Date('2024-01-06'));
  });
});
```

## 📖 使用规范

### 导入规范

```typescript
// 推荐的导入方式
import { formatCurrency, isValidEmail } from '@commons/utils/string.utils';
import { addDays, formatDate } from '@commons/utils/date.utils';
import { HTTP_STATUS_CODES } from '@commons/constants/http.constants';

// 避免的导入方式
import * as StringUtils from '@commons/utils/string.utils'; // 避免全量导入
```

### 函数命名规范

- 使用动词开头: `format`, `validate`, `convert`, `calculate`
- 布尔函数使用 `is`, `has`, `can` 前缀
- 保持函数名简洁且描述性强

### 类型定义规范

```typescript
// types/common.types.ts
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// 基础响应类型
export interface BaseResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  timestamp: number;
}
```

## 🚫 禁止事项

1. **不要包含业务逻辑**

   ```typescript
   // ❌ 错误 - 包含业务逻辑
   export function calculateRiskScore(company: Company): number {
     // 风险评分是业务逻辑，不应该在commons层
   }
   ```

2. **不要依赖其他业务层**

   ```typescript
   // ❌ 错误 - 依赖了domain层
   import { User } from '@domain/entities/user.entity';
   ```

3. **不要产生副作用**

   ```typescript
   // ❌ 错误 - 产生副作用
   export function processData(data: any[]): any[] {
     console.log('Processing...'); // 副作用
     someGlobalVariable = data; // 副作用
     return data.map((item) => (item.processed = true));
   }
   ```

4. **不要包含配置或环境相关代码**
   ```typescript
   // ❌ 错误 - 包含环境配置
   export function getApiUrl(): string {
     return process.env.API_URL; // 配置相关，应该在core层
   }
   ```

## 🧪 测试要求

- **覆盖率要求**: 100%函数覆盖率，95%+行覆盖率
- **测试文件命名**: `*.utils.unittest.spec.ts`
- **测试文件位置**: 与源文件同目录

### 测试示例

```typescript
// string.utils.unittest.spec.ts
import { formatCurrency, isValidEmail } from './string.utils';

describe('StringUtils', () => {
  describe('formatCurrency', () => {
    it('应该正确格式化人民币', () => {
      expect(formatCurrency(1000)).toBe('¥1,000.00');
    });

    it('应该支持不同货币', () => {
      expect(formatCurrency(1000, 'USD')).toBe('$1,000.00');
    });
  });

  describe('isValidEmail', () => {
    it('应该验证有效邮箱', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('应该拒绝无效邮箱', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
    });
  });
});
```

## 📦 迁移指南

从原有代码迁移到 commons 层：

### 1. 识别纯工具函数

```bash
# 查找可能的工具函数
grep -r "export function" src/ --include="*.company.utils.ts"
```

### 2. 移动文件

```bash
# 移动工具函数到commons
mv src/shared/utils/* src/commons/utils/
mv src/domain/utils/* src/commons/utils/
```

### 3. 更新导入路径

```bash
# 批量更新导入路径
find src/ -name "*.ts" -exec sed -i 's/from.*shared\/utils/from @commons\/utils/g' {} \;
```

### 4. 清理业务逻辑

检查移动过来的函数，移除任何业务逻辑到合适的层级。

## 🔄 版本管理

commons 层作为基础层，需要特别注意版本兼容性：

- **向后兼容**: 新版本必须向后兼容
- **废弃标记**: 使用 `@deprecated` 标记废弃函数
- **渐进式更新**: 提供迁移指南和过渡期

```typescript
/**
 * @deprecated 使用 formatCurrency 替代
 * @see formatCurrency
 */
export function formatMoney(amount: number): string {
  console.warn('formatMoney is deprecated, use formatCurrency instead');
  return formatCurrency(amount);
}
```
