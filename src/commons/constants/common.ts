export const API_BASE = '/dd';
export const API_PORT = 7001;

export const SMS_SIGN_NAME = '企查查';

export const SystemDefaultOrgId = 1;

export enum ServiceCode {
  SAAS_CZB = 'SAAS_CZB',
  SAAS_JZCC = 'SAAS_JZCC',
  SAAS_KZZ = 'SAAS_KZZ',
  KZZ_CRM = 'KZZ_CRM',
  KZZ_ENT = 'KZZ_ENT',
  SAAS_ROVER = 'SAAS_ROVER',
  OUT_BOUND = 'OUT_BOUND',
  SAAS_PRO = 'SAAS_PRO',
}

export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
export const DATE_FORMAT_DEFAULT_EXPORT = 'YYYYMMDDHHmmss';

export const DATE_FORMAT = 'YYYY-MM-DD';

const prefix = process.env.QueuePrefix || '';
export const QueueNames = {
  BatchImportMonitor: 'dd-platform-batch-import-monitor-queue' + prefix,
  BatchJobVerification: 'dd-platform-batch-job-verification-queue' + prefix,
  BatchJobDD: 'dd-platform-batch-job-queue-dd' + prefix,
  BatchMonitor: 'dd-platform-batch-job-monitor-queue' + prefix,
  DiligenceAnalyze: 'dd-platform-diligence-analyze' + prefix,
  DiligenceSnapshot: 'dd-platform-diligence-snapshot' + prefix,
  BatchDiligenceSnapshot: 'dd-platform-diligence-snapshot-batch' + prefix,
  // DDPlatformDataSyncQueue: 'ent-data-sync-dd-platform',
  ContinuousDiligence: 'dd-platform-continuous-diligence' + prefix,
  ContinuousDiligenceAnalyze: 'dd-platform-continuous-diligence-analyze-delay' + prefix,
  BatchExportJob: 'dd-platform-batch-export-job-queue' + prefix,
  BatchResultBusinessQueue: 'dd-platform-batch-result-business-queue' + prefix,
  //ScheduleQueue: 'dd-platform-schedule-queue' + prefix,
  MessageQueue: 'dd-platform-message-queue' + prefix,
};

/**
 * B端和C端数据隔离 isValid枚举值0:历史 1:非历史 10:网站临时屏蔽数据 11:网信办屏蔽 12:人行屏蔽 13:客户屏蔽 14:法院屏蔽 91:网信办屏蔽历史 92:人行屏蔽历史 93:客户屏蔽历史 94:法院屏蔽历史
 */
export const IsValidNumbers = '10,11,13,91,93';

/**
 * 历史
 */
export const HistoryValidNumbers = '0' + ',' + IsValidNumbers;

/**
 * 不限
 */
export const NoLimitValidNumbers = '0,1' + ',' + IsValidNumbers;

export const NoLimitValidNumbersArr = NoLimitValidNumbers.split(',').map(Number);

export const HistoryValidNumbersArr = HistoryValidNumbers.split(',').map(Number);

/**
 * 用于维度命中描述的正则匹配规则（出现维度变更需要修改时使用）
 */
export const DescriptionRegex = /\b(\d+)\s*条记录/;

/**
 *不支持的企业类型
 */
export const ForbiddenStandardCode = ['001003', '001013', '001014'];

export const OperInfoMapping = {
  Org: {
    // 公司
    Company: 0,
    // 社会组织
    Org: 1,
    // 主要人员
    Employee: 2,
    // 香港公司
    HKCompany: 3,
    // 政府机构和学校
    Government: 4,
    // 台湾公司
    TWCompany: 5,
    // 私募基金产品
    PefundProduct: 6,
    // 医院
    Hospital: 7,
    // 海外公司
    Oversea: 8,
    // 海外公司
    Oversea2: 9,
    // 基金会
    Fund: 10,
    // 事业单位
    Institution: 11,
    // 律师事务所
    LawOffice: 12,
    // 投资机构
    Invest: 13,
    // 美股
    UsStock: 14,
    // 无法判断
    CompanyWithoutKeyNo: -1,
    // 没有Id的人名
    PersonWithoutCerNo: -2,
    // 其他
    Other: -3,
  },
  OperType: {
    1: '法定代表人',
    2: '执行事务合伙人',
    3: '负责人',
    4: '经营者',
    5: '投资人',
    6: '董事长',
    7: '理事长',
    8: '代表人',
  },
};
