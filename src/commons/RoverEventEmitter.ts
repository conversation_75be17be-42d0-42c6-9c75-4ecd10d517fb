import { EventEmitter } from 'events';
import { QccLogger } from '@kezhaozhao/qcc-logger';

const logger = QccLogger.getLogger('RoverEventEmitter');

export enum SystemEvents {
  ProcessExit = 'ProcessExit',
}

// export enum SocketServerEvents {
//   EventToUser = 'SendEventToUser',
//   EventToRoom = 'EventToRoom',
// }

export enum DiligenceRelatedEventEnums {
  RefreshDiligenceAnalyze = 'RefreshDiligenceAnalyze',
  UpdateSpecificDiligence = 'UpdateSpecificDiligence',
}

export type RoverEventEnums = SystemEvents | DiligenceRelatedEventEnums;

export class RoverEventEmitter extends EventEmitter {
  private static instance: RoverEventEmitter;

  static getInstance(): RoverEventEmitter {
    if (!RoverEventEmitter.instance) {
      RoverEventEmitter.instance = new RoverEventEmitter();
      RoverEventEmitter.instance.setMaxListeners(1000);
    }
    return RoverEventEmitter.instance;
  }

  on<T extends RoverEventEnums>(eventType: T, listener: (...args: any[]) => void) {
    super.on(eventType as any, listener);
    return this;
  }

  emit<T extends RoverEventEnums>(eventType: T, ...data: any[]) {
    return super.emit(eventType as any, data);
  }

  /**
   * 给指定用户发送 socket 事件
   * @param currentUser
   * @param socketEvent
   */
  // emitSocketEventToUser(toUsers: number[], socketEvent: SocketEventPO) {
  //   logger.info(`emitSocketEventToUser: toUsers=${toUsers}, socketEvent=${socketEvent}`);
  //   super.emit(SocketServerEvents.EventToUser, [toUsers, socketEvent]);
  // }
  //
  // /**
  //  * 给指定组织或特殊房间发送 socket 事件
  //  * @param socketEvent
  //  * @param orgId
  //  * @param specialRoom
  //  */
  // emitSocketEventToRoom(toRooms: string[], socketEvent: SocketEventPO) {
  //   logger.info(`emitSocketEventToRoom: toRooms=${toRooms}, socketEvent=${socketEvent}`);
  //   super.emit(SocketServerEvents.EventToRoom, [toRooms, socketEvent]);
  // }

  private constructor() {
    super();
  }
}
