// 分组配置：财报数据
// 生成时间：2025/7/2 23:25:54
// 📌 此文件通过解析原始代码生成，参数来自原始文件

import { ScoreStrategyEnums } from '@domain/enums/ScoreStrategyEnums';
import { DimensionTypeEnums } from '@domain/enums/diligence/DimensionTypeEnums';
import { DimensionFieldCompareTypeEnums } from '@domain/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFieldKeyEnums } from '@domain/enums/dimension/dimension.filter.params';
import { MetricTypeEnums } from '@domain/enums/metric/MetricTypeEnums';
import { DiligenceTargetTypeEnum, MetricDynamicDataSourceTypeEnum } from '@domain/model/metric/MetricDynamicStrategy';
import { GroupConfigInterface } from '../../../interfaces/model-config.interface';

export const financialReportRiskConfig: GroupConfigInterface = {
  name: '财报数据',
  description: '财报数据',
  order: 4,
  metrics: [
    {
      name: '最新一期营业利润下滑',
      description: '最新一期营业利润下滑',
      order: 0,
      dimensions: [
        {
          order: 0,
          maxScore: 10,
          riskLevel: 0,
          dimsFields: [
            {
              dimStrategyName: '最新一期营业利润同比 < 50%',
              comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202 营业润同比 < 50%',
              dimKey: DimensionTypeEnums.ListedEntityRiskChange,
              fields: [
                { fieldKey: DimensionFieldKeyEnums.isValid, fieldValue: [1], accessScope: 1, compareType: DimensionFieldCompareTypeEnums.ContainsAny },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [113],
                  options: [{ value: 113, label: '企业公告' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: ['StockControlCompany'],
                  accessScope: 2,
                  options: [{ value: 'StockControlCompany', label: '上市主体企业' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.annualReportType,
                  fieldValue: [203, 204, 202],
                  options: [
                    { value: 203, label: '年报' },
                    { value: 204, label: '季报' },
                    { value: 202, label: '半年报' },
                  ],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.operatingProfitRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
              ],
            },
          ],
        },
      ],
      metricType: MetricTypeEnums.MonitorSupervisionMetric,
      dynamicStrategy: {
        mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
        allowMultipleDynamics: false,
        allowRepeatedHits: true,
        target: DiligenceTargetTypeEnum.PrimaryCompnay,
      },
      scoreStrategy: ScoreStrategyEnums.MaxLevel,
    },
    {
      name: '最新一期净利润/净资产为负',
      description: '最新一期净利润/净资产为负',
      order: 1,
      dimensions: [
        {
          order: 0,
          maxScore: 10,
          riskLevel: 0,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '最新一期净利润<0',
              comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202 净利润 =0',
              dimKey: DimensionTypeEnums.ListedEntityRiskChange,
              fields: [
                { fieldKey: DimensionFieldKeyEnums.isValid, fieldValue: [1], accessScope: 1, compareType: DimensionFieldCompareTypeEnums.ContainsAny },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: ['StockControlCompany'],
                  accessScope: 2,
                  options: [{ value: 'StockControlCompany', label: '上市主体企业' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [113],
                  options: [{ value: 113, label: '企业公告' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.annualReportType,
                  fieldValue: [203, 204, 202],
                  options: [
                    { value: 203, label: '年报' },
                    { value: 204, label: '季报' },
                    { value: 202, label: '半年报' },
                  ],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.netProfitAmount,
                  fieldValue: [0],
                  options: [{ unit: '元', min: 0, max: 99999999 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
              ],
            },
            {
              dimStrategyName: '最新一期净资产<0',
              comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202 净资产 =0',
              dimKey: DimensionTypeEnums.ListedEntityRiskChange,
              fields: [
                { fieldKey: DimensionFieldKeyEnums.isValid, fieldValue: [1], accessScope: 1, compareType: DimensionFieldCompareTypeEnums.ContainsAny },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: ['StockControlCompany'],
                  accessScope: 2,
                  options: [{ value: 'StockControlCompany', label: '上市主体企业' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [113],
                  options: [{ value: 113, label: '企业公告' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.annualReportType,
                  fieldValue: [203, 204, 202],
                  options: [
                    { value: 203, label: '年报' },
                    { value: 204, label: '季报' },
                    { value: 202, label: '半年报' },
                  ],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.netAssetsAmount,
                  fieldValue: [0],
                  options: [{ unit: '元', min: 0, max: 99999999 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
              ],
            },
          ],
        },
      ],
      metricType: MetricTypeEnums.MonitorSupervisionMetric,
      dynamicStrategy: {
        mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
        allowMultipleDynamics: false,
        allowRepeatedHits: true,
        target: DiligenceTargetTypeEnum.PrimaryCompnay,
      },
      scoreStrategy: ScoreStrategyEnums.MaxLevel,
    },
    {
      name: '最新一期归母净利润下滑',
      description: '最新一期归母净利润下滑',
      order: 2,
      dimensions: [
        {
          order: 0,
          maxScore: 10,
          riskLevel: 0,
          dimsFields: [
            {
              dimStrategyName: '最新一期归属于母公司股东的净利润同比 < 0%',
              comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202 ,最新一期归属于母公司股东的净利润同比 < 0%',
              dimKey: DimensionTypeEnums.ListedEntityRiskChange,
              fields: [
                { fieldKey: DimensionFieldKeyEnums.isValid, fieldValue: [1], accessScope: 1, compareType: DimensionFieldCompareTypeEnums.ContainsAny },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [113],
                  options: [{ value: 113, label: '企业公告' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: ['StockControlCompany'],
                  accessScope: 2,
                  options: [{ value: 'StockControlCompany', label: '上市主体企业' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.annualReportType,
                  fieldValue: [203, 204, 202],
                  options: [
                    { value: 203, label: '年报' },
                    { value: 204, label: '季报' },
                    { value: 202, label: '半年报' },
                  ],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.netProfitToParentRatio,
                  fieldValue: [0],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
              ],
            },
          ],
        },
      ],
      metricType: MetricTypeEnums.MonitorSupervisionMetric,
      dynamicStrategy: {
        mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
        allowMultipleDynamics: false,
        allowRepeatedHits: true,
        target: DiligenceTargetTypeEnum.PrimaryCompnay,
      },
      scoreStrategy: ScoreStrategyEnums.MaxLevel,
    },
    {
      name: '客户交易金额同比变化',
      description: '客户交易金额同比变化',
      order: 3,
      dimensions: [
        {
          order: 0,
          maxScore: 10,
          riskLevel: 0,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '今年上一季度和去年同期同比增加>=50%',
              comment: '命中条件：【1】类型：客户，【2】交易金额同比>=50% 【3】公开时间：AnnouncementDate 【4】变更趋势：增加',
              dimKey: DimensionTypeEnums.SupplierOrCustomer,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cooperationType,
                  fieldValue: [1],
                  options: [{ value: 1, label: '客户' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [1],
                  accessScope: 2,
                  options: [{ value: 1, label: '增加' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcementDate', order: 'DESC', fieldSnapshot: 'AnnouncementDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '今年上一季度和去年同期同比增加<50%',
              comment: '命中条件：【1】类型：客户，【2】交易金额同比>50% 【3】公开时间：AnnouncementDate 【4】变更趋势：增加',
              dimKey: DimensionTypeEnums.SupplierOrCustomer,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cooperationType,
                  fieldValue: [1],
                  options: [{ value: 1, label: '客户' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [1],
                  accessScope: 2,
                  options: [{ value: 1, label: '增加' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcementDate', order: 'DESC', fieldSnapshot: 'AnnouncementDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '今年上一季度和去年同期同比减少>=50%',
              comment: '命中条件：【1】类型：客户，【2】交易金额同比>=50% 【3】公开时间：AnnouncementDate 【4】变更趋势：减少',
              dimKey: DimensionTypeEnums.SupplierOrCustomer,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cooperationType,
                  fieldValue: [1],
                  options: [{ value: 1, label: '客户' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [2],
                  accessScope: 2,
                  options: [{ value: 2, label: '减少' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcementDate', order: 'DESC', fieldSnapshot: 'AnnouncementDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '今年上一季度和去年同期同比减少<50%',
              comment: '命中条件：【1】类型：客户，【2】交易金额同比>50% 【3】公开时间：AnnouncementDate 【4】变更趋势：减少',
              dimKey: DimensionTypeEnums.SupplierOrCustomer,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cooperationType,
                  fieldValue: [1],
                  options: [{ value: 1, label: '客户' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [2],
                  accessScope: 2,
                  options: [{ value: 2, label: '减少' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcementDate', order: 'DESC', fieldSnapshot: 'AnnouncementDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ],
      metricType: MetricTypeEnums.MonitorSupervisionMetric,
      dynamicStrategy: {
        mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
        allowMultipleDynamics: false,
        allowRepeatedHits: true,
        target: DiligenceTargetTypeEnum.PrimaryCompnay,
      },
      scoreStrategy: ScoreStrategyEnums.MaxLevel,
    },
    {
      name: '供应商交易金额同比变化',
      description: '供应商交易金额同比变化',
      order: 4,
      dimensions: [
        {
          order: 0,
          maxScore: 10,
          riskLevel: 0,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '今年上一季度和去年同期同比增加>=50%',
              comment: '命中条件：【1】类型：供应商，【2】交易金额同比>=50% 【3】公开时间：AnnouncementDate 【4】变更趋势：增加',
              dimKey: DimensionTypeEnums.SupplierOrCustomer,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cooperationType,
                  fieldValue: [0],
                  options: [{ value: 0, label: '供应商' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [1],
                  accessScope: 2,
                  options: [{ value: 1, label: '增加' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcementDate', order: 'DESC', fieldSnapshot: 'AnnouncementDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '今年上一季度和去年同期同比增加<50%',
              comment: '命中条件：【1】类型：供应商，【2】交易金额同比<50% 【3】公开时间：AnnouncementDate 【4】变更趋势：增加',
              dimKey: DimensionTypeEnums.SupplierOrCustomer,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cooperationType,
                  fieldValue: [0],
                  options: [{ value: 0, label: '供应商' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [1],
                  accessScope: 2,
                  options: [{ value: 1, label: '增加' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcementDate', order: 'DESC', fieldSnapshot: 'AnnouncementDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '今年上一季度和去年同期同比减少>=50%',
              comment: '命中条件：【1】类型：供应商，【2】交易金额同比>=50% 【3】公开时间：AnnouncementDate 【4】变更趋势：减少',
              dimKey: DimensionTypeEnums.SupplierOrCustomer,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cooperationType,
                  fieldValue: [0],
                  options: [{ value: 0, label: '供应商' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [2],
                  accessScope: 2,
                  options: [{ value: 2, label: '减少' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcementDate', order: 'DESC', fieldSnapshot: 'AnnouncementDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '今年上一季度和去年同期同比减少<50%',
              comment: '命中条件：【1】类型：供应商，【2】交易金额同比<50% 【3】公开时间：AnnouncementDate 【4】变更趋势：减少',
              dimKey: DimensionTypeEnums.SupplierOrCustomer,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cooperationType,
                  fieldValue: [0],
                  options: [{ value: 0, label: '供应商' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [2],
                  accessScope: 2,
                  options: [{ value: 2, label: '减少' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcementDate', order: 'DESC', fieldSnapshot: 'AnnouncementDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ],
      metricType: MetricTypeEnums.MonitorSupervisionMetric,
      dynamicStrategy: {
        mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
        allowMultipleDynamics: false,
        allowRepeatedHits: true,
        target: DiligenceTargetTypeEnum.PrimaryCompnay,
      },
      scoreStrategy: ScoreStrategyEnums.MaxLevel,
    },
    {
      name: '财报披露关联交易风险',
      description: '财报披露关联交易风险',
      order: 5,
      dimensions: [
        {
          order: 0,
          maxScore: 10,
          riskLevel: 0,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '今年上一季度和去年同期同比增加>=50%',
              comment: '命中条件：【1】交易金额同比>=50% 【2】公开时间：AnnounceDate 【3】变更趋势：增加',
              dimKey: DimensionTypeEnums.TradeDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [1],
                  accessScope: 2,
                  options: [{ value: 1, label: '增加' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcement_date', order: 'DESC', fieldSnapshot: 'AnnounceDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '今年上一季度和去年同期同比增加<50%',
              comment: '命中条件：【1】交易金额同比<50% 【2】公开时间：AnnounceDate 【3】变更趋势：增加',
              dimKey: DimensionTypeEnums.TradeDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [1],
                  accessScope: 2,
                  options: [{ value: 1, label: '增加' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcement_date', order: 'DESC', fieldSnapshot: 'AnnounceDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '今年上一季度和去年同期同比减少>=50%',
              comment: '命中条件:【1】交易金额同比>=50% 【2】公开时间：AnnounceDate 【3】变更趋势：减少',
              dimKey: DimensionTypeEnums.TradeDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [2],
                  accessScope: 2,
                  options: [{ value: 2, label: '减少' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcement_date', order: 'DESC', fieldSnapshot: 'AnnounceDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '今年上一季度和去年同期同比减少<50%',
              comment: '命中条件：【1】交易金额同比<50% 【2】公开时间：AnnounceDate 【3】变更趋势：减少',
              dimKey: DimensionTypeEnums.TradeDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.transactionAmountRatio,
                  fieldValue: [50],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.changeStatus,
                  fieldValue: [2],
                  accessScope: 2,
                  options: [{ value: 2, label: '减少' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'announcement_date', order: 'DESC', fieldSnapshot: 'AnnounceDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ],
      metricType: MetricTypeEnums.MonitorSupervisionMetric,
      dynamicStrategy: {
        mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
        allowMultipleDynamics: false,
        allowRepeatedHits: true,
        target: DiligenceTargetTypeEnum.PrimaryCompnay,
      },
      scoreStrategy: ScoreStrategyEnums.MaxLevel,
    },
  ],
};

// 默认导出，方便动态导入
export default financialReportRiskConfig;
