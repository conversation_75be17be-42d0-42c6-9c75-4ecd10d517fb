import { AppTestModule } from '@app/app.test.module';
import { DATE_TIME_FORMAT } from '@commons/constants/common';
import { ResourceEditForbiddenException } from '@commons/exceptions/ResourceEditForbiddenException';
import { ResourceOperationException } from '@commons/exceptions/ResourceOperationException';
import { MetricCopyResult, RiskModelCopyResultPO, StrategyCopyResult } from '@domain/db_helpers/resource.copy.helper';
import { DistributedSystemResourceEntity } from '@domain/entities/DistributedSystemResourceEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { MonitorGroupEntity } from '@domain/entities/MonitorGroupEntity';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DistributedResourceTypeEnums } from '@domain/enums/DistributedResourceTypeEnums';
import { DistributeResourceStatusEnums } from '@domain/enums/DistributeResourceStatusEnums';
import { MonitorStatusEnums } from '@domain/enums/monitor/MonitorStatusEnums';
import { PushEnableEnum } from '@domain/enums/push/PushEnableEnum';
import { RiskModelTypeEnums } from '@domain/enums/RiskModelTypeEnums';
import { CheckWhetherDeprecateRequest, CopyResourceRequest, UpdateRiskModelRequest } from '@domain/model/riskModel';
import { MongodbSearchHelper } from '@modules/data-processing/data-source/helper/mongodb.search.helper';
import { Test, TestingModule } from '@nestjs/testing';
import { PulsarMock, setupPulsarMock } from '@testing/pulsar.spy';
import { clearAllTestRiskModelTestData } from '@testing/riskmodel.test.utils';
import { generateUniqueTestIds, getTestUser } from '@testing/test.user';
import * as moment from 'moment';
import { EntityManager, getConnection, getRepository } from 'typeorm';
import { RiskModelTemplateEnum } from './init_mode/constants/risk-model-init.enums';
import { RiskModelInitV2Service } from './init_mode/v2/risk-model-init-v2.service';
import { RiskModelModule } from './risk_model.module';
import { RiskModelService } from './risk_model.service';
import * as RiskModelUtils from './risk_model.utils';

jest.setTimeout(100000);

describe('RiskModelService', () => {
  let riskModelService: RiskModelService;
  let entityManager: EntityManager;
  let riskModelInitV2Service: RiskModelInitV2Service;
  const [testOrgId, testUserId] = generateUniqueTestIds('risk_model.service.spec.ts');
  console.log('test data');
  console.log('testOrgId', testOrgId);
  console.log('testUserId', testUserId);
  console.log('test data');
  const testUser = getTestUser(testOrgId, testUserId);
  let pulsarMock: PulsarMock;
  let module: TestingModule;

  /**
   * 创建测试模型的辅助函数
   * @param modelType 模型类型
   * @param status 模型状态
   * @returns 创建的模型实体
   */
  const createTestRiskModel = async (
    modelType: RiskModelTypeEnums = RiskModelTypeEnums.GeneralRiskModel,
    status: DataStatusEnums = DataStatusEnums.Developing,
  ): Promise<RiskModelEntity> => {
    const modelName = `【测试模型】${modelType === RiskModelTypeEnums.MonitorModel ? '监控' : '尽调'}模型-${moment().format(DATE_TIME_FORMAT)}-${Math.random()
      .toString(36)
      .substring(2, 8)}`;

    const model = await riskModelInitV2Service.initModel(RiskModelTemplateEnum.HTF, testUser, modelName, testUser.currentOrg, modelType);

    // 如果需要特定状态，更新模型状态
    if (status !== DataStatusEnums.Developing) {
      await entityManager.update(RiskModelEntity, model.modelId, { status });
    }

    return riskModelService.getRiskModelFullDetails(model.modelId, testUser);
  };

  // 全局设置
  beforeAll(async () => {
    pulsarMock = setupPulsarMock();

    module = await Test.createTestingModule({
      providers: [
        {
          provide: MongodbSearchHelper,
          useValue: {
            findListByIds: jest.fn(),
          },
        },
      ],
      imports: [AppTestModule, RiskModelModule],
    }).compile();

    riskModelService = module.get<RiskModelService>(RiskModelService);
    riskModelInitV2Service = module.get<RiskModelInitV2Service>(RiskModelInitV2Service);
    entityManager = getRepository(RiskModelEntity).manager;
  });

  // 每个测试组之前的通用设置
  beforeEach(async () => {
    // 重置所有模拟函数
    jest.clearAllMocks();
  });

  afterEach(async () => {
    // 清理所有相关数据
    await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel);

    // 清理监控分组数据
    await entityManager.delete(MonitorGroupEntity, {
      orgId: testUser.currentOrg,
    });

    // 清理分发资源数据
    await entityManager.delete(DistributedSystemResourceEntity, {
      orgId: testUser.currentOrg,
    });
  });

  afterAll(async () => {
    // 关闭连接
    const connection = getConnection();
    await connection.close();
  });

  describe('模型列表查询', () => {
    let testRiskModel: RiskModelEntity;

    // 为这组测试创建测试数据
    beforeEach(async () => {
      // 创建一个测试模型
      // 添加唯一标识，避免测试之间的干扰
      testRiskModel = await createTestRiskModel();
    });

    // 每个测试后清理
    afterEach(async () => {
      if (testRiskModel?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, null, [testRiskModel.modelId]);
      }
    });

    it('模型列表：searchRiskModel()', async () => {
      const searchRes = await riskModelService.searchRiskModel({
        pageSize: 10,
        pageIndex: 1,
        orgId: testUser.currentOrg,
      });
      expect(searchRes.data.length).toBeGreaterThan(0);
    });

    it('模型列表：searchRiskModel() - 无结果', async () => {
      const searchRes = await riskModelService.searchRiskModel({
        pageSize: 10,
        pageIndex: 1,
        orgId: testUser.currentOrg,
        modelName: '不存在的模型名',
      });
      expect(searchRes.data.length).toEqual(0);
    });

    it('模型列表：searchRiskModel() - 超过最大页码', async () => {
      const searchRes = await riskModelService.searchRiskModel({
        pageSize: 10,
        pageIndex: 999,
        orgId: testUser.currentOrg,
      });
      expect(searchRes.data.length).toBe(0);
    });

    it('模型列表：searchRiskModel() - 只查询已废弃模型', async () => {
      const searchRes = await riskModelService.searchRiskModel({
        pageSize: 10,
        pageIndex: 1,
        orgId: testUser.currentOrg,
        statuses: [DataStatusEnums.Deprecated],
      });
      expect(searchRes.data.length).toBe(0);
    });

    it('模型列表：searchRiskModel() - 查询特定组织的模型', async () => {
      const searchRes = await riskModelService.searchRiskModel({
        pageSize: 10,
        pageIndex: 1,
        orgId: testUser.currentOrg - 1,
      });
      expect(searchRes.data.length).toEqual(0);
    });

    it('branchCode上的所有模型列表：searchRiskModelBranches()', async () => {
      const res = await riskModelService.searchRiskModelBranches(
        {
          riskModelId: testRiskModel.modelId,
          product: testUser.currentProduct,
          orgId: testUser.currentOrg,
          pageIndex: 1,
          pageSize: 5,
          branchCode: testRiskModel.branchCode,
        },
        testUser,
        false,
      );
      expect(res.data.length).toEqual(1);
    });

    it('branchCode上的所有模型列表(忽略riskModelId)：searchRiskModelBranches()', async () => {
      const res = await riskModelService.searchRiskModelBranches(
        {
          riskModelId: testRiskModel.modelId,
          product: testUser.currentProduct,
          orgId: testUser.currentOrg,
          pageIndex: 1,
          pageSize: 5,
          branchCode: testRiskModel.branchCode,
        },
        testUser,
        false,
      );
      expect(res.data.length).toEqual(1);
    });
  });

  describe('模型操作', () => {
    let testRiskModel: RiskModelEntity;

    // 为每个测试创建新的测试模型
    beforeEach(async () => {
      testRiskModel = await createTestRiskModel();
    });

    // 每个测试后清理
    afterEach(async () => {
      if (testRiskModel?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, null, [testRiskModel.modelId]);
      }
    });

    it('模型详情：getRiskModelFullDetails - 模型不存在', async () => {
      await expect(riskModelService.getRiskModelFullDetails(99999, testUser)).rejects.toThrow(ResourceOperationException);
    });

    it('模型详情：getRiskModelFullDetails - 用户没有权限', async () => {
      const unauthorizedUser = { ...testUser, currentOrg: -1 }; // 模拟没有权限的用户
      await expect(riskModelService.getRiskModelFullDetails(testRiskModel.modelId, unauthorizedUser)).rejects.toThrow(ResourceOperationException);
    });

    it('编辑模型：updateRiskModel()', async () => {
      const newModelName = `【尽调模型】单元测试编辑模型${moment().format(DATE_TIME_FORMAT)}`;
      await riskModelService.updateRiskModel(
        Object.assign(new UpdateRiskModelRequest(), {
          modelId: testRiskModel.modelId,
          modelName: newModelName,
        }),
        testUser,
      );

      const result = await entityManager.findOne(RiskModelEntity, {
        where: {
          modelId: testRiskModel.modelId,
        },
      });
      expect(result).not.toBeNull();
      expect(result.modelName).toEqual(newModelName);
    });

    it('编辑模型：updateRiskModel - 模型不存在', async () => {
      await expect(
        riskModelService.updateRiskModel(
          Object.assign(new UpdateRiskModelRequest(), {
            modelId: 99999,
            modelName: '不存在的模型名',
          }),
          testUser,
        ),
      ).rejects.toThrow(ResourceOperationException);
    });

    it('编辑模型：updateRiskModel - 用户没有权限', async () => {
      const unauthorizedUser = { ...testUser, currentOrg: -1 }; // 模拟没有权限的用户
      await expect(
        riskModelService.updateRiskModel(
          Object.assign(new UpdateRiskModelRequest(), {
            modelId: testRiskModel.modelId,
            modelName: '尝试更新的模型名',
          }),
          unauthorizedUser,
        ),
      ).rejects.toThrow(ResourceOperationException);
    });

    it('检查模型是否可以被废弃：checkWhetherDeprecate()', async () => {
      const result = await riskModelService.checkWhetherDeprecate(
        Object.assign(new CheckWhetherDeprecateRequest(), { riskModelId: testRiskModel.modelId }),
        testUser,
      );
      expect(result).not.toBeNull();
      expect(result.canDeprecate).toEqual(true);
    });
  });

  describe('模型废弃', () => {
    let enabledRiskModel: RiskModelEntity;
    let monitorModel: RiskModelEntity;
    let userOwnModel: RiskModelEntity;

    // 为每个测试创建新的测试模型
    beforeEach(async () => {
      // 创建已启用状态的普通模型

      enabledRiskModel = await createTestRiskModel(RiskModelTypeEnums.GeneralRiskModel, DataStatusEnums.Enabled);

      // 创建监控模型
      // monitorModel = await createTestRiskModel(RiskModelTypeEnums.MonitorModel, DataStatusEnums.Enabled);
      monitorModel = await riskModelInitV2Service.initModel(
        RiskModelTemplateEnum.MONITOR_DEFAULT,
        testUser,
        '自动创建监控模型001',
        testUser.currentOrg,
        RiskModelTypeEnums.MonitorModel,
      );
      // 创建用户自己的模型
      userOwnModel = await entityManager.save(RiskModelEntity, {
        modelName: '用户自己的测试模型',
        orgId: testUser.currentOrg,
        product: testUser.currentProduct,
        status: DataStatusEnums.Enabled,
        modelType: RiskModelTypeEnums.GeneralRiskModel,
        branchCode: `test-branch-${Date.now()}`,
        createBy: testUser.userId,
      });

      // 创建分发资源记录
      await entityManager.save(DistributedSystemResourceEntity, {
        resourceId: userOwnModel.modelId,
        resourceType: DistributedResourceTypeEnums.RiskModel,
        orgId: testUser.currentOrg,
        product: testUser.currentProduct,
        distributeStatus: DistributeResourceStatusEnums.Enable,
        distributedBy: testUser.userId,
        branchCode: userOwnModel.branchCode,
      });

      // 创建监控分组
      await entityManager.save(MonitorGroupEntity, {
        name: '测试监控分组',
        orgId: testUser.currentOrg,
        product: testUser.currentProduct,
        ownerId: testUser.userId,
        monitorModelId: monitorModel.modelId,
        monitorStatus: MonitorStatusEnums.Enabled,
        pushEnable: PushEnableEnum.Enable,
        status: DataStatusEnums.Enabled,
      });
    });

    // 每个测试后清理
    afterEach(async () => {
      // 清理监控分组
      await entityManager.delete(MonitorGroupEntity, {
        monitorModelId: monitorModel?.modelId,
        orgId: testUser.currentOrg,
      });

      // 清理所有创建的模型
      const modelIds = [enabledRiskModel?.modelId, monitorModel?.modelId, userOwnModel?.modelId].filter(Boolean);
      if (modelIds.length > 0) {
        await clearAllTestRiskModelTestData(entityManager, testUser, null, modelIds);
      }
    });

    it('废弃模型：deprecateRiskModel - 用户废弃操作只针对distributedResources', async () => {
      // 默认的 createTestRiskModel 创建的模型是 归属于 testUser的，这里为了方便测试模型归属者是系统的时候(用户是被分配的), 手动修改数据
      await entityManager.update(RiskModelEntity, enabledRiskModel.modelId, {
        createBy: 0,
        orgId: -1,
      });

      // 执行废弃操作 - 用户端废弃操作 (fromBO = false)
      const result = await riskModelService.deprecateRiskModel(enabledRiskModel.modelId, testUser, false);

      // 验证操作结果
      expect(result).not.toBeNull();
      expect(result.affected).toEqual(1);

      // 验证分发资源状态已更新为废弃
      const updatedDistributedResource = await entityManager.findOne(DistributedSystemResourceEntity, {
        where: {
          resourceId: enabledRiskModel.modelId,
          resourceType: DistributedResourceTypeEnums.RiskModel,
          orgId: testUser.currentOrg,
          product: testUser.currentProduct,
        },
      });

      expect(updatedDistributedResource).not.toBeNull();
      expect(updatedDistributedResource.distributeStatus).toEqual(DistributeResourceStatusEnums.Deprecated);

      // 验证模型本身状态未变化（如果不是用户自己创建的模型）
      const updatedModel = await entityManager.findOne(RiskModelEntity, { where: { id: enabledRiskModel.modelId } });
      expect(updatedModel.status).toEqual(DataStatusEnums.Enabled); // 模型状态应保持不变
    });

    it('废弃模型：deprecateRiskModel - 用户废弃自己创建的模型会同时更新模型状态', async () => {
      // 执行废弃操作
      const result = await riskModelService.deprecateRiskModel(userOwnModel.modelId, testUser, false);

      // 验证操作结果
      expect(result).not.toBeNull();
      expect(result.affected).toEqual(1);

      // 验证分发资源状态已更新为废弃
      const updatedDistributedResource = await entityManager.findOne(DistributedSystemResourceEntity, {
        where: {
          resourceId: userOwnModel.modelId,
          resourceType: DistributedResourceTypeEnums.RiskModel,
          orgId: testUser.currentOrg,
        },
      });

      expect(updatedDistributedResource).not.toBeNull();
      expect(updatedDistributedResource.distributeStatus).toEqual(DistributeResourceStatusEnums.Deprecated);

      // 验证模型本身状态也被更新为废弃
      const updatedModel = await entityManager.findOne(RiskModelEntity, {
        where: { modelId: userOwnModel.modelId },
        cache: false,
        select: ['modelId', 'publishedContent', 'status'],
      });
      expect(updatedModel.status).toEqual(DataStatusEnums.Deprecated);
      expect(updatedModel.publishedContent).toBeNull();
    });

    it('废弃模型：deprecateRiskModel - 监控模型废弃会同时更新相关监控分组状态', async () => {
      // 执行废弃操作
      const result = await riskModelService.deprecateRiskModel(monitorModel.modelId, testUser, false);

      // 验证操作结果
      expect(result).not.toBeNull();
      expect(result.affected).toEqual(1);

      // 验证监控分组状态已更新
      const updatedGroup = await entityManager.findOne(MonitorGroupEntity, {
        where: {
          monitorModelId: monitorModel.modelId,
          orgId: testUser.currentOrg,
          product: testUser.currentProduct,
        },
      });

      expect(updatedGroup).not.toBeNull();
      expect(updatedGroup.monitorStatus).toEqual(MonitorStatusEnums.Disabled);
      expect(updatedGroup.pushEnable).toEqual(PushEnableEnum.Disable);
    });

    it('废弃模型：deprecateRiskModel - 平台管理员废弃已分发的模型应该失败', async () => {
      // 模拟平台管理员用户
      const adminUser = { ...testUser, currentOrg: 0 }; // 系统默认组织ID

      // 执行废弃操作 - 平台管理员废弃操作 (fromBO = true)
      const result = await riskModelService.deprecateRiskModel(enabledRiskModel.modelId, adminUser, true);

      // 验证操作结果 - 应该失败，affected应为0
      expect(result).not.toBeNull();
      expect(result.affected).toEqual(0);

      // 验证分发资源状态未变化
      const distributedResource = await entityManager.findOne(DistributedSystemResourceEntity, {
        where: {
          resourceId: enabledRiskModel.modelId,
          resourceType: DistributedResourceTypeEnums.RiskModel,
          orgId: testUser.currentOrg,
        },
      });

      expect(distributedResource).not.toBeNull();
      expect(distributedResource.distributeStatus).not.toEqual(DistributeResourceStatusEnums.Deprecated);
    });
  });

  describe('模型发布', () => {
    let testRiskModel: RiskModelEntity;

    beforeEach(async () => {
      testRiskModel = await createTestRiskModel();
    });

    afterEach(async () => {
      if (testRiskModel?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, null, [testRiskModel.modelId]);
      }
    });

    it('发布模型：publishModelFromClient - 成功发布开发中的模型', async () => {
      // Given
      const publishRequest = {
        riskModelId: testRiskModel.modelId,
        maxPublishCount: 1,
      };
      // When
      const result = await riskModelService.publishModelFromClient(publishRequest, testUser);

      // Then
      expect(result).toBeDefined();
      // 验证模型状态
      const publishedModel = await riskModelService.getRiskModelFullDetails(testRiskModel.modelId, testUser);
      expect(publishedModel.status).toBe(DataStatusEnums.Enabled);
    });

    it('发布模型：publishModelFromClient - 非开发中状态的模型不能发布', async () => {
      // Given
      await entityManager.update(RiskModelEntity, testRiskModel.modelId, {
        status: DataStatusEnums.Enabled,
      });
      // When & Then
      await expect(
        riskModelService.publishModelFromClient(
          {
            riskModelId: testRiskModel.modelId,
            maxPublishCount: 1,
          },
          testUser,
        ),
      ).rejects.toThrow(ResourceOperationException);
    });

    it('发布模型：publishModelFromClient - 用户没有权限时不能发布', async () => {
      // Given
      const unauthorizedUser = { ...testUser, currentOrg: -1 };

      // When & Then
      await expect(
        riskModelService.publishModelFromClient(
          {
            riskModelId: testRiskModel.modelId,
            maxPublishCount: 1,
          },
          unauthorizedUser,
        ),
      ).rejects.toThrow(ResourceOperationException);
    });
  });

  describe('检查模型以及其他资源是否可以编辑', () => {
    let testRiskModel: RiskModelEntity;

    beforeEach(async () => {
      testRiskModel = await createTestRiskModel();
    });

    afterEach(async () => {
      if (testRiskModel?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, null, [testRiskModel.modelId]);
      }
    });

    it('资源编辑检查：resourceEditableCheck - 开发中的模型可以编辑', async () => {
      // Given
      const checkRequest = {
        riskModelId: testRiskModel.modelId,
      };

      // When
      const results = await riskModelService.resourceEditableCheck(checkRequest, testUser);

      // Then
      expect(results).toBeDefined();
      expect(results.length).toBe(1);
      expect(results[0]).toEqual(
        expect.objectContaining({
          type: 'riskModel',
          id: testRiskModel.modelId,
          canBeEdit: true,
          targetResource: 1,
        }),
      );
    });

    it('资源编辑检查：resourceEditableCheck - 已发布的模型不能编辑', async () => {
      // Given
      await entityManager.update(RiskModelEntity, testRiskModel.modelId, {
        status: DataStatusEnums.Enabled,
      });

      // When
      const results = await riskModelService.resourceEditableCheck({ riskModelId: testRiskModel.modelId }, testUser);

      // Then
      expect(results[0].canBeEdit).toBe(false);
    });

    it('资源编辑检查：resourceEditableCheck - 检查多个资源类型', async () => {
      // Given
      const metric = testRiskModel.groups[0].groupMetrics[0];
      const strategy = metric.metricEntity.dimensionHitStrategies[0];

      const checkRequest = {
        riskModelId: testRiskModel.modelId,
        metricsId: metric.metricsId,
        hitStrategyId: strategy.dimensionStrategyId,
      };

      // When
      const results = await riskModelService.resourceEditableCheck(checkRequest, testUser);

      // Then
      expect(results.length).toBe(3);
      expect(results).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ type: 'riskModel', canBeEdit: true }),
          expect.objectContaining({ type: 'metric', canBeEdit: true }),
          expect.objectContaining({ type: 'strategy', canBeEdit: true }),
        ]),
      );
    });
  });

  describe('根据 strategyIds 查询维度策略规则', () => {
    let testRiskModel: RiskModelEntity;

    beforeEach(async () => {
      testRiskModel = await createTestRiskModel();
    });

    afterEach(async () => {
      if (testRiskModel?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, null, [testRiskModel.modelId]);
      }
    });

    it('查询维度策略规则：getDimensionHitStrategies - 成功获取策略规则', async () => {
      // Given
      const metric = testRiskModel.groups[0].groupMetrics[0];
      const strategy = metric.metricEntity.dimensionHitStrategies[0];

      // When
      const result = await riskModelService.getDimensionHitStrategies(
        {
          modelId: testRiskModel.modelId,
          strategyIds: [strategy.dimensionStrategyId],
        },
        testUser,
      );

      // Then
      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0].strategyId).toBe(strategy.dimensionStrategyId);
    });

    it('查询维度策略规则：getDimensionHitStrategies - 查询不存在的策略ID', async () => {
      // When
      const result = await riskModelService.getDimensionHitStrategies(
        {
          modelId: testRiskModel.modelId,
          strategyIds: [99999],
        },
        testUser,
      );

      // Then
      expect(result).toBeDefined();
      expect(result.length).toBe(0);
    });

    it('查询维度策略规则：getDimensionHitStrategies - 查询多个策略ID', async () => {
      // Given
      const strategies = testRiskModel.groups[0].groupMetrics[0].metricEntity.dimensionHitStrategies;
      const strategyIds = strategies.map((s) => s.dimensionStrategyId);

      // When
      const result = await riskModelService.getDimensionHitStrategies(
        {
          modelId: testRiskModel.modelId,
          strategyIds,
        },
        testUser,
      );

      // Then
      expect(result).toBeDefined();
      expect(result.length).toBe(strategyIds.length);
      expect(result.map((r) => r.strategyId)).toEqual(expect.arrayContaining(strategyIds));
    });
  });

  describe('复制-模型复制', () => {
    let defaultRiskModelEntity: RiskModelEntity;
    beforeEach(async () => {
      defaultRiskModelEntity = await createTestRiskModel();
    });
    afterEach(async () => {
      // await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel);
      if (defaultRiskModelEntity?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, null, [defaultRiskModelEntity.modelId]);
      }
    });
    // 模型维度-只复制模型，分组变成新的分组，指标不变
    it('模型复制case0：copyResource() - 直接复制模型', async () => {
      //这里只复制模型，理论上应该只有模型ID以及下面的分组全部是新的，其他的都应该是一样的
      const result = (await riskModelService.copyResource(
        Object.assign(new CopyResourceRequest(), {
          riskModelId: defaultRiskModelEntity.modelId,
          targetResource: 1,
        }),
        testUser,
      )) as RiskModelCopyResultPO;

      // 验证模型是否成功复制
      expect(result).not.toBeNull();
      expect(result.copied).toEqual(true);
      expect(result.newId).not.toBeNull();

      // 验证新模型的属性
      const newModel = await riskModelService.getRiskModelFullDetails(result.newId, testUser);
      expect(newModel).not.toBeNull();
      expect(newModel.modelName).toContain('副本'); // 验证新模型名称是否包含"副本"
      expect(newModel.branchCode).toEqual(defaultRiskModelEntity.branchCode); // 验证分支代码是否一致

      // 验证分组复制结果
      expect(result.groupResults).toHaveLength(defaultRiskModelEntity.groups.length); // 验证分组数量是否一致

      // 使用 groupResults 中的 oldId 和 newId 映射关系来验证分组复制
      result.groupResults.forEach((groupCopyResult) => {
        const originalGroup = defaultRiskModelEntity.groups.find((g) => g.groupId === groupCopyResult.oldId);
        const newGroup = newModel.groups.find((g) => g.groupId === groupCopyResult.newId);

        expect(originalGroup).not.toBeNull(); // 确保能找到原始分组
        expect(newGroup).not.toBeNull(); // 确保能找到新分组

        // 验证分组基本属性
        expect(newGroup.groupName).toEqual(originalGroup.groupName);
        expect(newGroup.groupMetrics).toHaveLength(originalGroup.groupMetrics.length);

        // 如果有 metricResults，验证指标复制结果
        if (groupCopyResult.metricResults) {
          groupCopyResult.metricResults.forEach((metricResult) => {
            const originalMetric = originalGroup.groupMetrics.find((gm) => gm.metricsId === metricResult.oldId);
            const newMetric = newGroup.groupMetrics.find((gm) => gm.metricsId === metricResult.newId);

            if (metricResult.copied) {
              // 如果指标被复制了，验证新旧指标ID不同但其他属性相同
              expect(newMetric.metricsId).not.toEqual(originalMetric.metricsId);
              expect(newMetric.metricEntity.name).toEqual(originalMetric.metricEntity.name);
              expect(newMetric.metricEntity.metricType).toEqual(originalMetric.metricEntity.metricType);
            } else {
              // 如果指标没有被复制，只是重用，验证指标ID相同
              expect(newMetric.metricsId).toEqual(originalMetric.metricsId);
            }
          });
        } else {
          const metricIdSet1 = new Set(originalGroup.groupMetrics.map((gm) => gm.metricsId));
          const metricIdSet2 = new Set(newGroup.groupMetrics.map((gm) => gm.metricsId));
          expect(metricIdSet1).toEqual(metricIdSet2);
        }
      });
    });

    it('模型复制：copyResource() - 模型不存在', async () => {
      await expect(
        riskModelService.copyResource(
          Object.assign(new CopyResourceRequest(), {
            riskModelId: 99999,
            targetResource: 1,
          }),
          testUser,
        ),
      ).rejects.toThrow(ResourceOperationException);
    });

    it('模型复制：copyResource() - 用户没有权限', async () => {
      const unauthorizedUser = { ...testUser, currentOrg: -1 }; // 模拟没有权限的用户
      await expect(
        riskModelService.copyResource(
          Object.assign(new CopyResourceRequest(), {
            riskModelId: defaultRiskModelEntity.modelId,
            targetResource: 1,
          }),
          unauthorizedUser,
        ),
      ).rejects.toThrow(ResourceOperationException);
    });

    it('模型复制：copyResource() - 超过最大复制限制', async () => {
      const countSpy = jest.spyOn(riskModelService['riskModelRepo'], 'count').mockResolvedValue(4);
      // 模拟超过最大复制限制的情况
      await expect(
        riskModelService.copyResource(
          Object.assign(new CopyResourceRequest(), {
            riskModelId: defaultRiskModelEntity.modelId,
            targetResource: 1,
          }),
          testUser,
        ),
      ).rejects.toThrow(ResourceOperationException);
    });
  });

  describe('复制-指标复制', () => {
    let defaultRiskModelEntity: RiskModelEntity;
    beforeEach(async () => {
      defaultRiskModelEntity = await createTestRiskModel();
    });
    afterEach(async () => {
      // await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel);
      if (defaultRiskModelEntity?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, null, [defaultRiskModelEntity.modelId]);
      }
    });

    // 指标维度-已经发布的模型不可以复制指标
    it('模型复制case1-1：copyResource() - 复制指标并替换新指标到原来分组 - (模拟模型已经发布)不可以复制', async () => {
      // 要求所属的模型是发布中状态，否则会禁止复制(因为涉及到对分组及模型的修改了，所以需要先复制模型)
      const originalMetric = defaultRiskModelEntity.groups[0].groupMetrics[0].metricEntity;
      const toCopyMetricsId = originalMetric.metricsId;
      const bindToGroupId = defaultRiskModelEntity.groups[0].groupId;
      //1. 先spy riskModelScopeCheck， 返回的riskModel手动把状态改为发布中，这个时候复制指标并替换到原来的分组就应该报错
      const spy1 = jest.spyOn(RiskModelUtils, 'riskModelScopeCheck').mockResolvedValue({
        riskModelEntities: [
          {
            ...defaultRiskModelEntity,
            status: DataStatusEnums.Enabled,
          },
        ],
      } as unknown as RiskModelUtils.RiskModelScopeCheckResult);
      await expect(
        riskModelService.copyResource(
          Object.assign(new CopyResourceRequest(), {
            riskModelId: defaultRiskModelEntity.modelId,
            targetResource: 2,
            metricsIds: [toCopyMetricsId],
            bindMetricToGroupId: bindToGroupId,
            needReplace: 1, // 替换老的指标
          }),
          testUser,
        ),
      ).rejects.toThrow(ResourceEditForbiddenException);
      expect(spy1).toHaveBeenCalled();
      spy1.mockRestore();
    });
    // 指标维度--未发布的模型，复制指标并替换
    it('模型复制case1-2：copyResource() - 复制指标并替换新指标到原来分组(可以正常复制)', async () => {
      // 复制指标并替换新指标到原来分组
      // 要求所属的模型是发布中状态，否则会禁止复制(因为涉及到对分组及模型的修改了，所以需要先复制模型)
      const originalMetric = defaultRiskModelEntity.groups[0].groupMetrics[0].metricEntity;
      const toCopyMetricsId = originalMetric.metricsId;
      const bindToGroupId = defaultRiskModelEntity.groups[0].groupId;

      //开发中的模型正常复制，应该就可以把新复制的指标加入到原来分组中

      const results = (await riskModelService.copyResource(
        Object.assign(new CopyResourceRequest(), {
          riskModelId: defaultRiskModelEntity.modelId,
          targetResource: 2,
          metricsIds: [toCopyMetricsId],
          bindMetricToGroupId: bindToGroupId,
          needReplace: 1, // 替换老的指标
        }),
        testUser,
      )) as MetricCopyResult[];

      // 验证复制结果的基本结构
      expect(results).not.toBeNull();
      expect(results.length).toBe(1);

      const copyResult = results[0];
      expect(copyResult.oldId).toEqual(toCopyMetricsId);
      expect(copyResult.newId).not.toBeNull();

      // 重新获取风险模型的详细信息
      const updatedRiskModelDetails = await riskModelService.getRiskModelFullDetails(defaultRiskModelEntity.modelId, testUser);

      // 找到对应的分组
      const targetGroup = updatedRiskModelDetails.groups.find((g) => g.groupId === bindToGroupId);
      expect(targetGroup).not.toBeNull();

      // 验证指标复制和替换结果
      const originalMetrics = defaultRiskModelEntity.groups[0].groupMetrics;
      const updatedMetrics = targetGroup.groupMetrics;

      // 1. 验证被复制的指标
      const newMetric = updatedMetrics.find((gm) => gm.metricsId === copyResult.newId);
      expect(newMetric).not.toBeNull();
      expect(newMetric.metricEntity).not.toBeNull();

      // 验证新指标的属性与原指标一致
      expect(newMetric.metricEntity.name).toEqual(originalMetric.name);
      expect(newMetric.metricEntity.metricType).toEqual(originalMetric.metricType);
      expect(newMetric.metricEntity.riskLevel).toEqual(originalMetric.riskLevel);
      expect(newMetric.metricEntity.isVeto).toEqual(originalMetric.isVeto);
      expect(newMetric.metricEntity.score).toEqual(originalMetric.score);

      // 2. 验证原指标已被替换
      const oldMetric = updatedMetrics.find((gm) => gm.metricsId === toCopyMetricsId);
      expect(oldMetric).toBeUndefined(); // 原指标应该已经被移除

      // 3. 验证其他指标保持不变
      const otherOriginalMetricIds = new Set(originalMetrics.filter((gm) => gm.metricsId !== toCopyMetricsId).map((gm) => gm.metricsId));
      const otherUpdatedMetricIds = new Set(updatedMetrics.filter((gm) => gm.metricsId !== copyResult.newId).map((gm) => gm.metricsId));

      // 验证除了被复制/替换的指标外，其他指标的集合保持不变
      expect(otherUpdatedMetricIds).toEqual(otherOriginalMetricIds);

      // 4. 如果有策略复制结果，验证策略复制
      if (copyResult.strategyResults?.length > 0) {
        copyResult.strategyResults.forEach((strategyResult) => {
          const originalStrategy = originalMetric.dimensionHitStrategies.find((s) => s.dimensionStrategyId === strategyResult.oldId);

          expect(originalStrategy).not.toBeNull();
          expect(strategyResult.newId).not.toBeNull();
          expect(strategyResult.copied).toBe(true);

          // 验证策略字段复制结果
          if (strategyResult.fieldResults?.length > 0) {
            strategyResult.fieldResults.forEach((fieldResult) => {
              expect(fieldResult.oldId).not.toBeNull();
              expect(fieldResult.newId).not.toBeNull();
              expect(fieldResult.copied).toBe(true);
            });
          }
        });
      }
    });
    // 指标维度--已发布的模型， 复制指标，不作替换
    it('模型复制case2-1：copyResource() - 只复制指标，不做任何其他操作(模型已发布)', async () => {
      // 单纯是要复制一个指标，不对模型做任何修改，所以无论模型是什么状态都不会有影响(即使模型是已发布)
      const originalMetric = defaultRiskModelEntity.groups[0].groupMetrics[0].metricEntity;
      const toCopyMetricsId = originalMetric.metricsId;
      const spy1 = jest.spyOn(RiskModelUtils, 'riskModelScopeCheck').mockResolvedValue({
        riskModelEntities: [
          {
            ...defaultRiskModelEntity,
            status: DataStatusEnums.Enabled,
          },
        ],
      } as unknown as RiskModelUtils.RiskModelScopeCheckResult);
      const results = (await riskModelService.copyResource(
        Object.assign(new CopyResourceRequest(), {
          riskModelId: defaultRiskModelEntity.modelId,
          targetResource: 2,
          metricsIds: [toCopyMetricsId], // 假设有这两个指标
          bindMetricToGroupId: null, //绑定到复制的指标原来的分组
          needReplace: 0, // 替换老的指标
        }),
        testUser,
      )) as MetricCopyResult[];
      expect(spy1).toHaveBeenCalled();
      spy1.mockRestore();
      const copyResult: MetricCopyResult = results[0];
      expect(copyResult).not.toBeNull();
      expect(copyResult.newId).not.toBeNull();
      const copiedMetric: MetricsEntity = copyResult.newMetric;

      expect(copiedMetric).not.toBeNull();
      expect(copiedMetric.name).toEqual(originalMetric.name); // 验证名称是否一致
      expect(copiedMetric.metricType).toEqual(originalMetric.metricType); // 验证类型是否一致
      // 重新获取风险模型的详细信息
      const updatedRiskModelDetails = await riskModelService.getRiskModelFullDetails(defaultRiskModelEntity.modelId, testUser);

      // 验证 groupMetrics 是否不再包含原来的 metricsId，并且包含新复制的 metricsId
      const groupMetrics = updatedRiskModelDetails.groups[0].groupMetrics;
      expect(groupMetrics.some((metric) => metric.metricsId === toCopyMetricsId)).toBe(true); // 原指标依然包含
      expect(groupMetrics.some((metric) => metric.metricsId === copyResult.newId)).toBe(false); // 新指标不被包含
    });
    // 指标维度--未发布的模型，复制指标，不作替换
    it('模型复制case2-2：copyResource() - 只复制指标，不做任何其他操作(模型未发布)', async () => {
      // 单纯是要复制一个指标，不对模型做任何修改，所以无论模型是什么状态都不会有影响(即使模型是已发布)
      const originalMetric = defaultRiskModelEntity.groups[0].groupMetrics[0].metricEntity;
      const toCopyMetricsId = originalMetric.metricsId;
      const results = (await riskModelService.copyResource(
        Object.assign(new CopyResourceRequest(), {
          riskModelId: defaultRiskModelEntity.modelId,
          targetResource: 2,
          metricsIds: [toCopyMetricsId], // 假设有这两个指标
          bindMetricToGroupId: null, //绑定到复制的指标原来的分组
          needReplace: 0, // 替换老的指标
        }),
        testUser,
      )) as MetricCopyResult[];

      const copyResult: MetricCopyResult = results[0];
      expect(copyResult).not.toBeNull();
      expect(copyResult.newId).not.toBeNull();
      const copiedMetric: MetricsEntity = copyResult.newMetric;

      expect(copiedMetric).not.toBeNull();
      expect(copiedMetric.name).toEqual(originalMetric.name); // 验证名称是否一致
      expect(copiedMetric.metricType).toEqual(originalMetric.metricType); // 验证类型是否一致
      // 重新获取风险模型的详细信息
      const updatedRiskModelDetails = await riskModelService.getRiskModelFullDetails(defaultRiskModelEntity.modelId, testUser);

      // 验证 groupMetrics 是否不再包含原来的 metricsId，并且包含新复制的 metricsId
      const groupMetrics = updatedRiskModelDetails.groups[0].groupMetrics;
      expect(groupMetrics.some((metric) => metric.metricsId === toCopyMetricsId)).toBe(true); // 原指标依然包含
      expect(groupMetrics.some((metric) => metric.metricsId === copyResult.newId)).toBe(false); // 新指标不被包含
    });
  });

  describe('复制-策略复制', () => {
    let defaultRiskModelEntity: RiskModelEntity;

    beforeEach(async () => {
      defaultRiskModelEntity = await createTestRiskModel();
    });

    afterEach(async () => {
      if (defaultRiskModelEntity?.modelId) {
        await clearAllTestRiskModelTestData(entityManager, testUser, null, [defaultRiskModelEntity.modelId]);
      }
    });

    // 策略维度--已发布的模型不可以复制策略并绑定
    it('策略复制case1-1：copyResource() - 复制策略并绑定到指定指标(模型已发布，应该失败)', async () => {
      // Given
      const originalMetric = defaultRiskModelEntity.groups[0].groupMetrics[0].metricEntity;
      const toCopyMetricsId = originalMetric.metricsId;
      const toCopyStrategyId = originalMetric.dimensionHitStrategies[0].dimensionStrategyId;
      const bindToMetricId = originalMetric.metricsId;

      // Mock模型为已发布状态
      const spy1 = jest.spyOn(RiskModelUtils, 'riskModelScopeCheck').mockResolvedValue({
        riskModelEntities: [
          {
            ...defaultRiskModelEntity,
            status: DataStatusEnums.Enabled,
          },
        ],
      } as unknown as RiskModelUtils.RiskModelScopeCheckResult);

      // When & Then
      await expect(
        riskModelService.copyResource(
          Object.assign(new CopyResourceRequest(), {
            riskModelId: defaultRiskModelEntity.modelId,
            targetResource: 3,
            metricsIds: [toCopyMetricsId],
            strategyIds: [toCopyStrategyId],
            bindStrategyToMetricId: bindToMetricId,
          }),
          testUser,
        ),
      ).rejects.toThrow(ResourceEditForbiddenException);

      expect(spy1).toHaveBeenCalled();
      spy1.mockRestore();
    });

    // 策略维度--未发布的模型，复制策略并绑定
    it('策略复制case1-2：copyResource() - 复制策略并绑定到指定指标(模型未发布，应该成功)', async () => {
      // Given
      const originalMetric = defaultRiskModelEntity.groups[0].groupMetrics[0].metricEntity;
      const toCopyMetricsId = originalMetric.metricsId;
      const toCopyStrategyId = originalMetric.dimensionHitStrategies[0].dimensionStrategyId;
      const bindToMetricId = originalMetric.metricsId;

      // When
      const copyResult = (await riskModelService.copyResource(
        Object.assign(new CopyResourceRequest(), {
          riskModelId: defaultRiskModelEntity.modelId,
          targetResource: 3,
          metricsIds: [toCopyMetricsId],
          strategyIds: [toCopyStrategyId],
          bindStrategyToMetricId: bindToMetricId,
        }),
        testUser,
      )) as MetricCopyResult;

      // Then
      expect(copyResult).not.toBeNull();
      const copiedMetric: MetricsEntity = copyResult.newMetric;
      expect(copiedMetric).not.toBeNull();
      expect(copiedMetric.name).toEqual(originalMetric.name); // 验证名称是否一致
      expect(copiedMetric.metricType).toEqual(originalMetric.metricType); // 验证类型是否一致
      // 策略验证
      const strategyCopyResult: StrategyCopyResult = copyResult.strategyResults[0];
      expect(strategyCopyResult.newId).toBeDefined();
      expect(strategyCopyResult.oldId).toBeDefined();
      expect(strategyCopyResult.oldId).toEqual(toCopyStrategyId);
      expect(strategyCopyResult.copied).toBeTruthy();

      // 验证绑定关系
      const updatedModel = await riskModelService.getRiskModelFullDetails(defaultRiskModelEntity.modelId, testUser);
      const targetMetric = updatedModel.groups[0].groupMetrics.find((gm) => gm.metricsId === bindToMetricId);
      expect(targetMetric.metricEntity.dimensionHitStrategies).toContainEqual(
        expect.objectContaining({
          dimensionStrategyId: strategyCopyResult.newId,
        }),
      );

      // 验证复制的策略属性
      const copiedStrategy = targetMetric.metricEntity.dimensionHitStrategies.find((s) => s.dimensionStrategyId === strategyCopyResult.newId);
      expect(copiedStrategy.dimensionHitStrategyEntity.strategyName).toEqual(originalMetric.dimensionHitStrategies[0].dimensionHitStrategyEntity.strategyName);
      expect(copiedStrategy.dimensionHitStrategyEntity.dimensionId).toEqual(originalMetric.dimensionHitStrategies[0].dimensionHitStrategyEntity.dimensionId);
    });

    // 策略维度--已发布的模型，复制策略不绑定
    it('策略复制case2-1：copyResource() - 只复制策略，不绑定(模型已发布)', async () => {
      // Given
      const originalMetric = defaultRiskModelEntity.groups[0].groupMetrics[0].metricEntity;
      const toCopyMetricsId = originalMetric.metricsId;
      const toCopyStrategyId = originalMetric.dimensionHitStrategies[0].dimensionStrategyId;

      // Mock模型为已发布状态
      const spy1 = jest.spyOn(RiskModelUtils, 'riskModelScopeCheck').mockResolvedValue({
        riskModelEntities: [
          {
            ...defaultRiskModelEntity,
            status: DataStatusEnums.Enabled,
          },
        ],
      } as unknown as RiskModelUtils.RiskModelScopeCheckResult);

      // When
      const copyResult = (await riskModelService.copyResource(
        Object.assign(new CopyResourceRequest(), {
          riskModelId: defaultRiskModelEntity.modelId,
          targetResource: 3,
          metricsIds: [toCopyMetricsId],
          strategyIds: [toCopyStrategyId],
          bindStrategyToMetricId: null,
        }),
        testUser,
      )) as MetricCopyResult;
      // Then
      expect(copyResult).not.toBeNull();
      // 策略验证
      const strategyCopyResult: StrategyCopyResult = copyResult.strategyResults[0];
      expect(strategyCopyResult.newId).toBeDefined();
      expect(strategyCopyResult.oldId).toBeDefined();
      expect(strategyCopyResult.oldId).toEqual(toCopyStrategyId);
      expect(strategyCopyResult.copied).toBeTruthy();

      // 验证原模型中的策略关系未改变
      const updatedModel = await riskModelService.getRiskModelFullDetails(defaultRiskModelEntity.modelId, testUser);
      const originalMetricInModel = updatedModel.groups[0].groupMetrics.find((gm) => gm.metricsId === toCopyMetricsId);
      expect(originalMetricInModel.metricEntity.dimensionHitStrategies).not.toContainEqual(
        expect.objectContaining({
          dimensionStrategyId: strategyCopyResult.newId,
        }),
      );
    });

    // 策略维度--未发布的模型，复制策略不绑定
    it('策略复制case2-2：copyResource() - 只复制策略，不绑定(模型未发布)', async () => {
      // Given
      const originalMetric = defaultRiskModelEntity.groups[0].groupMetrics[0].metricEntity;
      const toCopyMetricsId = originalMetric.metricsId;
      const toCopyStrategyId = originalMetric.dimensionHitStrategies[0].dimensionStrategyId;

      // When
      const copyResult = (await riskModelService.copyResource(
        Object.assign(new CopyResourceRequest(), {
          riskModelId: defaultRiskModelEntity.modelId,
          targetResource: 3,
          metricsIds: [toCopyMetricsId],
          strategyIds: [toCopyStrategyId],
          bindStrategyToMetricId: null,
        }),
        testUser,
      )) as MetricCopyResult;

      // Then
      // 策略验证
      const strategyCopyResult: StrategyCopyResult = copyResult.strategyResults[0];
      expect(strategyCopyResult.newId).toBeDefined();
      expect(strategyCopyResult.oldId).toBeDefined();
      expect(strategyCopyResult.oldId).toEqual(toCopyStrategyId);
      expect(strategyCopyResult.copied).toBeTruthy();

      // 验证原模型中的策略关系未改变
      const updatedModel = await riskModelService.getRiskModelFullDetails(defaultRiskModelEntity.modelId, testUser);
      const originalMetricInModel = updatedModel.groups[0].groupMetrics.find((gm) => gm.metricsId === toCopyMetricsId);
      expect(originalMetricInModel.metricEntity.dimensionHitStrategies).not.toContainEqual(
        expect.objectContaining({
          dimensionStrategyId: strategyCopyResult.newId,
        }),
      );
    });

    // 参数验证测试
    it('策略复制case3：参数验证 - 缺少必要参数应该失败', async () => {
      // When & Then - 缺少 metricsIds
      await expect(
        riskModelService.copyResource(
          Object.assign(new CopyResourceRequest(), {
            riskModelId: defaultRiskModelEntity.modelId,
            targetResource: 3,
            strategyIds: [1],
          }),
          testUser,
        ),
      ).rejects.toThrow('metricsIds is required');

      // When & Then - 缺少 strategyIds
      await expect(
        riskModelService.copyResource(
          Object.assign(new CopyResourceRequest(), {
            riskModelId: defaultRiskModelEntity.modelId,
            targetResource: 3,
            metricsIds: [1],
          }),
          testUser,
        ),
      ).rejects.toThrow('strategyIds is required');
    });

    // 错误处理测试
    it('策略复制case4：复制不存在的命中规则应该失败', async () => {
      // Given
      const nonExistentMetricId = 99999;
      const nonExistentStrategyId = 99999;

      // When & Then
      await expect(
        riskModelService.copyResource(
          Object.assign(new CopyResourceRequest(), {
            riskModelId: defaultRiskModelEntity.modelId,
            targetResource: 3,
            metricsIds: [nonExistentMetricId],
            strategyIds: [nonExistentStrategyId],
          }),
          testUser,
        ),
      ).rejects.toThrow(ResourceOperationException);
    });
  });
});
