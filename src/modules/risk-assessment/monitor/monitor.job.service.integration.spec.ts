import { AppTestModule } from '@app/app.test.module';
import { QueueService } from '@core/config/queue.service';
import { BatchDiligenceEntity } from '@domain/entities/BatchDiligenceEntity';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { BatchJobEntity } from '@domain/entities/BatchJobEntity';
import { CompanyEntity } from '@domain/entities/CompanyEntity';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';
import { DistributedSystemResourceEntity } from '@domain/entities/DistributedSystemResourceEntity';
import { MessageEntity } from '@domain/entities/MessageEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { MonitorCompanyEntity } from '@domain/entities/MonitorCompanyEntity';
import { MonitorCompanyRelatedPartyEntity } from '@domain/entities/MonitorCompanyRelatedPartyEntity';
import { MonitorDynamicsRemarkEntity } from '@domain/entities/MonitorDynamicsRemarkEntity';
import { MonitorGroupEntity } from '@domain/entities/MonitorGroupEntity';
import { MonitorMetricsDynamicEntity } from '@domain/entities/MonitorMetricsDynamicEntity';
import { PushRuleEntity } from '@domain/entities/PushRuleEntity';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { MetricDynamicStatusEnums } from '@domain/enums/metric/MetricDynamicStatusEnums';
import { MonitorStatusEnums } from '@domain/enums/monitor/MonitorStatusEnums';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { RiskModelTypeEnums } from '@domain/enums/RiskModelTypeEnums';
import { BatchStatisticsBasePO } from '@domain/model/batch/po/BatchStatisticsBasePO';
import { PlatformUser } from '@domain/model/common';
import { RemoveDynamicDocPO } from '@domain/model/monitor/po/RemoveDynamicDocPO';
import { BatchBaseHelper } from '@modules/batch-processing/service/helper/batch.base.helper';
import { BatchCreatorHelperBase } from '@modules/batch-processing/service/helper/batch.creator.helper.base';
import { DiligenceSnapshotEsService } from '@modules/risk-assessment/diligence/snapshot/diligence.snapshot.es.service';
import { RiskModelModule } from '@modules/risk-assessment/risk-model/risk_model.module';
import { UserService } from '@modules/user-management/user/user.service';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { generateUniqueTestIds, getTestUser } from '@testing/test.user';
import { EntityManager, getConnection } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { RiskModelTemplateEnum } from '../risk-model/init_mode/constants/risk-model-init.enums';
import { RiskModelInitV2Service } from '../risk-model/init_mode/v2/risk-model-init-v2.service';
import { MonitorDynamicEsService } from './dynamic/monitor.dynamic.es.service';
import { MonitorJobService } from './monitor.job.service';
import { MonitorModule } from './monitor.module';
import { MonitorCompanyMessagePO } from './po/MonitorCompanyMessagePO';

jest.setTimeout(600000);
describe('MonitorJobService 集成测试', () => {
  let module: TestingModule;
  let monitorJobService: MonitorJobService;
  let entityManager: EntityManager;
  let riskModelInitV2Service: RiskModelInitV2Service;

  // 测试数据
  // eslint-disable-next-line prefer-const
  let [testOrgId, testUserId] = generateUniqueTestIds('monitor.job.service.integration.spec.ts');
  // 启动监控任务加了orgId>0的判断，所以这里需要设置为正数
  testOrgId = 0 - testOrgId;
  const testUser = getTestUser(testOrgId, testUserId);

  let testMonitorGroup: MonitorGroupEntity;
  let testMonitorCompany: MonitorCompanyEntity;
  let testMonitorMetricsDynamic: MonitorMetricsDynamicEntity;
  let testDiligenceHistory: DiligenceHistoryEntity;
  let testBatchDiligence: BatchDiligenceEntity;
  let testBatch: BatchEntity;
  let testRiskModel: any;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        AppTestModule,
        MonitorModule,
        RiskModelModule,
        TypeOrmModule.forFeature([
          DimensionHitStrategyEntity,
          MetricsEntity,
          MonitorGroupEntity,
          MonitorCompanyEntity,
          MonitorMetricsDynamicEntity,
          DiligenceHistoryEntity,
          BatchEntity,
          CompanyEntity,
          RiskModelEntity,
          MonitorDynamicsRemarkEntity,
          MonitorCompanyRelatedPartyEntity,
          PushRuleEntity,
          MessageEntity,
          DistributedSystemResourceEntity,
          BatchDiligenceEntity,
        ]),
      ],
      providers: [
        MonitorJobService,
        {
          provide: UserService,
          useValue: {
            getAdminList: jest.fn().mockResolvedValue([{ userId: testUserId }]),
          },
        },
        {
          provide: MonitorDynamicEsService,
          useValue: {
            deleteDynamicDoc: jest.fn().mockResolvedValue(1),
          },
        },
        {
          provide: BatchBaseHelper,
          useValue: {
            sendBatchMonitorMessage: jest.fn(),
          },
        },
        {
          provide: BatchCreatorHelperBase,
          useValue: {
            createBatchEntity: jest.fn(),
            changeBatchStatusBeforeStart: jest.fn(),
          },
        },
        {
          provide: DiligenceSnapshotEsService,
          useValue: {
            removeSnapshotDiligenceData: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: QueueService,
          useValue: {
            continuousDiligenceQueue: {
              consume: jest.fn(),
              sendMessageV2: jest.fn().mockResolvedValue({ success: true }),
            },
          },
        },
      ],
    }).compile();

    monitorJobService = module.get<MonitorJobService>(MonitorJobService);
    entityManager = module.get<EntityManager>(EntityManager);
    // monitorDynamicEsService = module.get(MonitorDynamicEsService);
    // diligenceSnapshotEsService = module.get(DiligenceSnapshotEsService);
    // queueService = module.get(QueueService);
    riskModelInitV2Service = module.get<RiskModelInitV2Service>(RiskModelInitV2Service);
    // userService = module.get(UserService);

    // 设置spy
    jest.spyOn(monitorJobService['monitorDynamicEsService'], 'deleteDynamicDoc');
    jest.spyOn(monitorJobService['diligenceSnapshotEsService'], 'removeSnapshotDiligenceData');
    jest.spyOn(monitorJobService['continuousDiligenceQueue'], 'sendMessageV2');
    jest.spyOn(monitorJobService['userService'], 'getAdminList');

    // 清理之前的测试数据
    await cleanupTestData();

    // 创建测试数据
    await prepareTestData();
  });

  beforeEach(() => {
    // jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData();
    const connection = getConnection();
    await connection.close();
  });

  /**
   * 准备测试数据
   */
  async function prepareTestData() {
    // 创建风险模型，使用时间戳确保名称唯一
    const timestamp = new Date().getTime();
    const modelName = `监控模型_${testOrgId}_${timestamp}`;
    testRiskModel = await riskModelInitV2Service.initModel(
      RiskModelTemplateEnum.MONITOR_DEFAULT,
      testUser,
      modelName,
      testUser.currentOrg,
      RiskModelTypeEnums.MonitorModel,
    );
    testRiskModel = await entityManager.findOne(RiskModelEntity, {
      where: { modelId: testRiskModel.modelId },
      relations: [
        'groups',
        'groups.groupMetrics',
        'groups.groupMetrics.metricEntity',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.dimensionDef',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.strategyFields',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.strategyFields.dimensionField',
      ],
      cache: false,
    });
    // 创建监控分组，使用时间戳确保名称唯一
    testMonitorGroup = new MonitorGroupEntity();
    testMonitorGroup.orgId = testOrgId;
    testMonitorGroup.product = ProductCodeEnums.Pro;
    testMonitorGroup.ownerId = testUserId;
    testMonitorGroup.name = `测试监控分组_${testOrgId}_${timestamp}`;
    testMonitorGroup.status = DataStatusEnums.Enabled;
    testMonitorGroup.monitorStatus = MonitorStatusEnums.Enabled;
    testMonitorGroup.monitorModelId = testRiskModel.modelId;
    await entityManager.save(testMonitorGroup);

    // 创建监控企业
    testMonitorCompany = new MonitorCompanyEntity();
    testMonitorCompany.orgId = testOrgId;
    testMonitorCompany.product = ProductCodeEnums.Pro;
    testMonitorCompany.createBy = testUserId;
    testMonitorCompany.monitorGroupId = testMonitorGroup.monitorGroupId;
    testMonitorCompany.companyId = '*********';
    testMonitorCompany.companyName = '测试企业';
    testMonitorCompany.relatedDynamicHashKey = `test_related_${testOrgId}`;
    testMonitorCompany.riskLevel = 1;
    await entityManager.save(testMonitorCompany);

    // 创建尽调历史记录
    testDiligenceHistory = new DiligenceHistoryEntity();
    testDiligenceHistory.product = ProductCodeEnums.Pro;
    testDiligenceHistory.orgId = testOrgId;
    testDiligenceHistory.operator = testUserId;
    testDiligenceHistory.orgModelId = testRiskModel.modelId;
    testDiligenceHistory.companyId = testMonitorCompany.companyId;
    testDiligenceHistory.snapshotDetails = {
      status: 1,
      successHits: ['dimension1_1', 'dimension2_1'],
    };
    await entityManager.save(testDiligenceHistory);
    // 创建batch
    testBatch = new BatchEntity();
    testBatch.product = ProductCodeEnums.Pro;
    testBatch.orgId = testOrgId;
    testBatch.createBy = testUserId;
    testBatch.status = BatchStatusEnums.Waiting;
    testBatch.recordCount = 1;
    testBatch.statisticsInfo = Object.assign(new BatchStatisticsBasePO(), { recordCount: 1 });
    await entityManager.save(testBatch);
    // 创建批次尽调记录
    testBatchDiligence = new BatchDiligenceEntity();
    testBatchDiligence.batchId = testBatch.batchId;
    testBatchDiligence.diligenceId = testDiligenceHistory.id;
    await entityManager.save(testBatchDiligence);
    // 创建废弃的监控动态数据
    testMonitorMetricsDynamic = new MonitorMetricsDynamicEntity();
    testMonitorMetricsDynamic.orgId = testOrgId;
    testMonitorMetricsDynamic.metricsId = testRiskModel.groups[0].groupMetrics[0].metricEntity.metricsId;
    testMonitorMetricsDynamic.metricsName = testRiskModel.groups[0].groupMetrics[0].metricEntity.name;
    testMonitorMetricsDynamic.batchId = testBatch.batchId;
    testMonitorMetricsDynamic.monitorGroupId = testMonitorGroup.monitorGroupId;
    testMonitorMetricsDynamic.companyId = testMonitorCompany.companyId;
    testMonitorMetricsDynamic.diligenceId = testDiligenceHistory.id;
    testMonitorMetricsDynamic.companyMetricsHashkey = uuidv4();
    testMonitorMetricsDynamic.riskModelId = testRiskModel.modelId;
    testMonitorMetricsDynamic.riskModelBranchCode = testRiskModel.branchCode;
    testMonitorMetricsDynamic.diligenceResult = 1;
    testMonitorMetricsDynamic.diligenceScore = 10;
    testMonitorMetricsDynamic.status = MetricDynamicStatusEnums.Deprecated;
    testMonitorMetricsDynamic.riskLevel = 1;
    testMonitorMetricsDynamic.riskScore = 1;
    testMonitorMetricsDynamic.product = ProductCodeEnums.Pro;
    testMonitorMetricsDynamic.uniqueHashkey = `test_dynamic_${testOrgId}`;
    await entityManager.save(testMonitorMetricsDynamic);
  }

  /**
   * 清理测试数据
   */
  async function cleanupTestData() {
    try {
      // 按照外键依赖关系的逆序删除数据

      // 1. 删除监控动态数据
      if (testMonitorMetricsDynamic?.id) {
        await entityManager.delete(MonitorMetricsDynamicEntity, { id: testMonitorMetricsDynamic.id });
      }
      // 也按 orgId 删除，防止有遗漏
      await entityManager.delete(MonitorMetricsDynamicEntity, { orgId: testOrgId });

      // 2. 删除批次尽调记录
      if (testBatchDiligence?.id) {
        await entityManager.delete(BatchDiligenceEntity, { id: testBatchDiligence.id });
      }

      // 3. 删除尽调历史记录
      if (testDiligenceHistory?.id) {
        await entityManager.delete(DiligenceHistoryEntity, { id: testDiligenceHistory.id });
      }
      // 也按 orgId 删除，防止有遗漏
      await entityManager.delete(DiligenceHistoryEntity, { orgId: testOrgId });

      // 4. 删除批次记录
      if (testBatch?.batchId) {
        await entityManager.delete(BatchEntity, { batchId: testBatch.batchId });
      }
      // 也按 orgId 删除，防止有遗漏
      await entityManager.delete(BatchEntity, { orgId: testOrgId });

      // 5. 删除监控企业
      if (testMonitorCompany?.monitorCompanyId) {
        await entityManager.delete(MonitorCompanyEntity, { monitorCompanyId: testMonitorCompany.monitorCompanyId });
      }
      // 也按 orgId 删除，防止有遗漏
      await entityManager.delete(MonitorCompanyEntity, { orgId: testOrgId });

      // 6. 删除监控分组（重点修复：按多个条件删除）
      if (testMonitorGroup?.monitorGroupId) {
        await entityManager.delete(MonitorGroupEntity, { monitorGroupId: testMonitorGroup.monitorGroupId });
      }
      // 按 orgId 和名称模式删除，确保清理干净
      await entityManager.delete(MonitorGroupEntity, { orgId: testOrgId });
      await entityManager
        .createQueryBuilder()
        .delete()
        .from(MonitorGroupEntity)
        .where('org_id = :orgId AND name LIKE :namePattern', {
          orgId: testOrgId,
          namePattern: `测试监控分组_${testOrgId}%`,
        })
        .execute();

      // 7. 删除风险模型
      if (testRiskModel?.modelId) {
        await entityManager.query(`DELETE FROM risk_model WHERE model_id = ${testRiskModel.modelId}`);
      }
      // 也按 orgId 删除，防止有遗漏
      await entityManager.query(`DELETE FROM risk_model WHERE org_id = ${testOrgId}`);
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }

  /**
   * 测试 removeDeprecatedMonitorDataJob 方法
   */
  describe('removeDeprecatedMonitorDataJob', () => {
    it('应该能成功删除废弃的监控动态数据', async () => {
      // 调用测试方法
      await monitorJobService.removeDeprecatedMonitorDataJob(testMonitorGroup.monitorGroupId);

      // 验证 ES 服务方法是否被正确调用
      expect(monitorJobService['monitorDynamicEsService'].deleteDynamicDoc).toHaveBeenCalledWith(
        expect.objectContaining(Object.assign(new RemoveDynamicDocPO(), { uniqueHashkeys: [testMonitorMetricsDynamic.uniqueHashkey] })),
      );

      expect(monitorJobService['diligenceSnapshotEsService'].removeSnapshotDiligenceData).toHaveBeenCalledWith(
        expect.objectContaining({
          diligenceId: testDiligenceHistory.id,
          orgId: testOrgId,
        }),
      );

      // 验证数据库记录是否已删除
      const remainingMetricsDynamic = await entityManager.findOne(MonitorMetricsDynamicEntity, {
        where: { id: testMonitorMetricsDynamic.id },
      });
      expect(remainingMetricsDynamic).toBeUndefined();

      const remainingDiligenceHistory = await entityManager.findOne(DiligenceHistoryEntity, {
        where: { id: testDiligenceHistory.id },
      });
      expect(remainingDiligenceHistory).toBeUndefined();

      const remainingBatchDiligence = await entityManager.findOne(BatchDiligenceEntity, {
        where: { id: testBatchDiligence.id },
      });
      expect(remainingBatchDiligence).toBeUndefined();
    });
  });

  /**
   * 测试 monitorGroupDynamicJob 方法
   */
  describe('monitorGroupDynamicJob', () => {
    it('应该正确启动监控任务并发送消息', async () => {
      // 在测试前重新创建数据，因为前一个测试会删掉数据
      await prepareTestData();

      // Mock getAdminList 返回值
      jest.spyOn(monitorJobService['userService'], 'getAdminList').mockResolvedValueOnce([{ userId: testUserId }]);

      // 调用测试方法
      await monitorJobService.monitorGroupDynamicJob(testMonitorGroup.monitorGroupId);

      // 验证消息队列是否被正确调用
      expect(monitorJobService['continuousDiligenceQueue'].sendMessageV2).toHaveBeenCalledWith(
        expect.objectContaining({
          orgId: testOrgId.toString(),
          product: ProductCodeEnums.Pro,
          monitorGroupId: testMonitorGroup.monitorGroupId,
          interval: 1,
          intervalUnit: 'day',
        }),
        expect.any(Object),
      );
    });

    it('应该可以自定义监控时间间隔', async () => {
      // Mock getAdminList 返回值
      jest.spyOn(monitorJobService['userService'], 'getAdminList').mockResolvedValueOnce([{ userId: testUserId }]);

      // 调用测试方法，使用自定义参数
      await monitorJobService.monitorGroupDynamicJob(testMonitorGroup.monitorGroupId, 7, 'day');

      // 验证消息队列是否被正确调用，并使用了自定义参数
      expect(monitorJobService['queueService'].continuousDiligenceQueue.sendMessageV2).toHaveBeenCalledWith(
        expect.objectContaining({
          orgId: testOrgId.toString(),
          product: ProductCodeEnums.Pro,
          monitorGroupId: testMonitorGroup.monitorGroupId,
          interval: 7,
          intervalUnit: 'day',
        }),
        expect.any(Object),
      );
    });
  });

  describe('startBatchForGroup', () => {
    it('应该成功创建批处理任务并发送监控消息', async () => {
      // 发送消息，将一个异步任务转成同步任务
      const spy = jest
        .spyOn(module.get(BatchBaseHelper), 'sendBatchMonitorMessage')
        .mockImplementation(async (batchEntity: BatchEntity, user: PlatformUser) => {
          // console.log('sendBatchMonitorMessage', batchEntity, user);
        });
      // 执行测试
      const result = await monitorJobService.startBatchForGroup(
        Object.assign(new MonitorCompanyMessagePO(), {
          orgId: testOrgId,
          monitorGroupId: testMonitorGroup.monitorGroupId,
          product: ProductCodeEnums.Pro,
          currentUser: testUser,
          interval: 1,
          intervalUnit: 'year',
        }),
      );
      // 验证结果
      expect(result).toBeDefined();
      expect(result.batchId).not.toBeNull();
      const batchJobEntityList = await entityManager.find(BatchJobEntity, { where: { batchId: result.batchId } });
      expect(batchJobEntityList.length).toBeGreaterThan(0);
      const batchEntity = await entityManager.find(BatchEntity, { where: { batchId: result.batchId } });
      expect(batchEntity).not.toBeNull();
      expect(spy).toHaveBeenCalled();
    });
  });
});
