生成时间：2025-06-17 17:05:20

# 风险模型数据清理策略文档

## 1. 清理目标与背景

### 1.1 清理目标

基于风险模型系统的设计理念，安全清理系统中不再使用的实体数据：

- `GroupEntity`（指标分组）
- `MetricsEntity`（风险指标）
- `DimensionHitStrategyEntity`（维度命中策略）
- 相关联的关系实体

### 1.2 清理挑战与设计约束

#### 核心挑战

- **branchCode 共享复用**：同一商业产品下的多版本模型共享指标和策略
- **跨模型引用检查**：需要验证资源在所有相关模型中的使用状态
- **状态一致性保障**：确保清理不影响生产环境的已发布模型

#### 关键约束

- **已发布资源保护**：`Enabled` 状态的资源及其依赖不能被清理
- **branchCode 隔离**：清理操作需要考虑商业产品边界
- **引用完整性**：必须检查完整的依赖链路

## 2. 数据清理架构基础

### 2.1 清理目标实体层次结构

```mermaid
graph TD
    A[RiskModelEntity<br/>风险模型] --> |1:N| B[GroupEntity<br/>指标分组]
    B --> |N:M via| C[GroupMetricRelationEntity<br/>分组-指标关联关系]
    C --> |关联到| D[MetricsEntity<br/>风险指标]
    D --> |N:M via| E[MetricDimensionRelationEntity<br/>指标-策略关联关系]
    E --> |关联到| F[DimensionHitStrategyEntity<br/>维度命中策略]
    F --> |1:N| G[DimensionHitStrategyFieldsEntity<br/>策略-字段关联关系]
    F --> |N:1| H[DimensionDefinitionEntity<br/>维度定义]
    G --> |N:1| I[DimensionFieldsEntity<br/>维度字段]

    classDef mainEntity fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef relationEntity fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef systemEntity fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px

    class A,B,D,F mainEntity
    class C,E,G relationEntity
    class H,I systemEntity
```

**图例说明**：

- 🔴 **红色**：主要实体（RiskModelEntity, GroupEntity, MetricsEntity, DimensionHitStrategyEntity）
- 🟠 **橙色**：关系实体（GroupMetricRelationEntity, MetricDimensionRelationEntity, DimensionHitStrategyFieldsEntity）
- 🟢 **绿色**：系统级实体（DimensionDefinitionEntity, DimensionFieldsEntity）

**关系类型说明**：

- **1:N**：一对多关系（如一个模型对应多个分组）
- **N:M via**：多对多关系通过中间关系表实现
- **N:1**：多对一关系（如多个策略字段关联到一个维度字段）

### 2.2 清理复杂度分析

#### 2.2.1 简单清理：GroupEntity (指标分组)

**特征**：

- ✅ **单向归属**：每个分组只归属于一个风险模型
- ✅ **生命周期绑定**：随模型状态变化
- ✅ **清理判断简单**：模型删除或废弃时直接清理

**清理条件**：

```sql
-- 孤立分组：关联模型不存在或已废弃
SELECT g.group_id
FROM `group` g
LEFT JOIN risk_model rm ON g.model_id = rm.model_id
WHERE rm.model_id IS NULL
   OR rm.status = ${DataStatusEnums.Deprecated}
```

#### 2.2.2 复杂清理：MetricsEntity (风险指标) - riskModelId 级别清理

**特征**：

- ⚠️ **branchCode 受控复用**：同一商业产品下的多版本模型可共享
- ⚠️ **跨模型引用**：需要检查所有相关模型的使用状态
- ⚠️ **状态依赖复杂**：必须考虑所有引用模型的状态
- ⚠️ **跨 branchCode 分享**：指标可能被其他 branchCode 的模型引用

**riskModelId 级别清理复杂性**：

```mermaid
flowchart TD
    A[检查指标清理<br/>riskModelId级别] --> B{被哪些模型引用?}
    B --> C[收集所有引用的模型]
    C --> D{有跨branchCode引用?}
    D -->|是| E[❌ 指标不能删除<br/>只能删除指定模型的关联关系]
    D -->|否，同branchCode| F{检查清理触发方式}
    F -->|branchCode级别触发| G{branchCode是否可删除?}
    F -->|riskModelId级别触发| H{检查所有引用模型状态}
    G -->|可删除| I[✅ 可以完全删除指标]
    G -->|不可删除| J[❌ 不能清理]
    H -->|有生产非测试组织的Enabled模型| K[❌ 指标不能删除<br/>只能删除指定模型的关联关系]
    H -->|只有Developing/Deprecated<br/>或测试组织模型| L[❌ 指标不能删除<br/>只能删除指定模型的关联关系]
```

**关键逻辑说明**：

- **跨 branchCode 引用**：指标不能删除，只能删除当前 riskModelId 与指标的关联关系
- **riskModelId 级别清理**：原则上不删除指标本身，只删除关联关系（保护共享资源）
- **branchCode 级别清理**：如果 branchCode 可删除，可以完全删除指标
- **安全保护原则**：只有通过 branchCode 级别且 branchCode 可删除时，才允许完全删除指标

#### 2.2.3 复杂清理：DimensionHitStrategyEntity (维度命中策略) - riskModelId 级别清理

**特征**：

- ⚠️ **多级复用**：可被多个指标复用，指标又可被多个模型复用
- ⚠️ **依赖链复杂**：需要追溯 策略 → 指标 → 模型 的完整链路
- ⚠️ **清理影响面大**：删除策略会影响多个指标
- ⚠️ **跨 branchCode 分享**：策略可能通过指标被其他 branchCode 的模型引用

**riskModelId 级别清理依赖链分析**：

```mermaid
graph TD
    A[DimensionHitStrategyEntity<br/>待清理策略] --> B{检查关系}
    B --> C[MetricDimensionRelationEntity<br/>策略-指标关系]
    C --> D[MetricsEntity<br/>关联的指标]
    D --> E[GroupMetricRelationEntity<br/>指标-分组关系]
    E --> F[GroupEntity<br/>指标所属分组]
    F --> G[RiskModelEntity<br/>分组所属模型]
    G --> H{跨branchCode检查}
    H -->|有跨branchCode引用| I[❌ 策略不能删除<br/>只能删除指定模型关联指标的策略关系]
    H -->|同branchCode| J{检查清理触发方式}
    J -->|branchCode级别触发| K{branchCode是否可删除?}
    J -->|riskModelId级别触发| L{检查所有引用模型状态}
    K -->|可删除| M[✅ 可以完全删除策略]
    K -->|不可删除| N[❌ 不能清理]
    L -->|有生产非测试组织的Enabled模型| O[❌ 策略不能删除<br/>只能删除指定模型关联指标的策略关系]
    L -->|只有Developing/Deprecated<br/>或测试组织模型| P[❌ 策略不能删除<br/>只能删除指定模型关联指标的策略关系]

    style I fill:#ffebee
    style N fill:#ffebee
    style O fill:#ffebee
    style P fill:#ffebee
    style M fill:#e8f5e8
```

**依赖关系说明**：清理策略时需要通过 **2 层关系实体** 和 **3 个主实体** 来追溯完整的依赖链路，确保不会误删正在使用的策略。

**关键逻辑说明**：

- **跨 branchCode 引用**：策略不能删除，只能删除指定 riskModelId 关联指标与策略的关系
- **riskModelId 级别清理**：原则上不删除策略本身，只删除关联关系（保护共享资源）
- **branchCode 级别清理**：如果 branchCode 可删除，可以完全删除策略
- **安全保护原则**：只有通过 branchCode 级别且 branchCode 可删除时，才允许完全删除策略

## 3. 数据状态管理机制

### 3.1 状态枚举定义

```typescript
// 使用现有的枚举定义 - src/domain/enums/DataStatusEnums.ts
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';

// DataStatusEnums 定义：
// Disabled = 0,   // 无效/禁用
// Enabled = 1,    // 启用/发布
// Developing = 2, // 开发中
// Deprecated = 3  // 已废弃（逻辑删除）
```

### 3.2 状态流转规则

- **创建阶段**：新实体默认为 `Developing` 状态
- **发布机制**：只有 `Developing` 状态的实体可以发布为 `Enabled`
- **修改约束**：`Enabled` 状态的实体不允许直接修改
- **版本控制**：需要修改已发布实体时，通过复制机制创建新版本

### 3.3 同 branchCode 约束

- 同一 `branchCode` 下只能有一个 `Enabled` 状态的模型
- 确保生产环境使用的风险标准一致性
- 支持多个 `Developing` 状态模型并存，便于版本迭代

## 4. 复制与继承机制

### 4.1 copyRiskModel() 机制

```typescript
// 核心复制逻辑
const copyRiskModel = async (params, entityManager, needCopyMetric?, needCopyHitStrategy?) => {
  // 1. 复制模型主体，建立继承关系
  // 2. 根据 copyType 决定是否生成新的 branchCode
  // 3. 选择性复制分组、指标、命中策略
  // 4. 更新父模型和根模型的 branchCount
};
```

### 4.2 继承关系管理

- **extendFrom 路径**：记录完整的继承链路（如：`1.5.10`）
- **版本号管理**：
  - `branchTier`：继承层级深度
  - `branchCount`：分支总数统计
- **复制策略**：
  - `copyType = 0`：同产品新版本（保持 branchCode）
  - `copyType = 1`：新产品（生成新 branchCode）

## 5. 发布与分发机制

### 5.1 publishRiskModel() 流程

```typescript
// 发布流程
const publishRiskModel = async (riskModels, params, entityManager) => {
  // 1. 检查模型状态（必须为 Developing）
  // 2. 递归发布子资源（指标 -> 策略 -> 字段）
  // 3. 更新所有相关实体状态为 Enabled
  // 4. 生成 publishedContent 快照
};
```

### 5.2 分发机制

- **DistributedSystemResourceEntity**：记录模型向组织的分发关系
- **发布快照**：`publishedContent` 字段保存发布时的完整模型结构
- **权限控制**：通过分发记录控制组织访问权限

## 6. 数据清理策略与实施方案

### 6.0 环境与组织配置

#### 6.0.1 环境区分策略

**测试环境特征**：

- 允许频繁创建和废弃 branchCode
- 数据安全要求相对较低
- 支持更激进的清理策略

**生产环境特征**：

- 严格保护商业数据
- 只允许清理特定测试组织的数据
- 采用保守的清理策略

#### 6.0.2 特定测试组织配置

```typescript
// 清理配置接口
interface CleanupEnvironmentConfig {
  environment: 'test' | 'production';
  testOrganizations: number[]; // 允许清理的测试组织ID列表
  safeCleanupEnabled: boolean; // 是否启用安全清理模式
}

// 清理类型枚举 - 明确区分清理触发方式
enum CleanupTriggerType {
  BRANCH_CODE = 'branchCode', // 根据branchCode删除
  RISK_MODEL_ID = 'riskModelId', // 根据指定riskModelId删除
}

// 生产环境配置示例
const productionConfig: CleanupEnvironmentConfig = {
  environment: 'production',
  testOrganizations: [1001, 1002, 1003], // 特定的测试组织ID
  safeCleanupEnabled: true,
};

// 测试环境配置示例
const testConfig: CleanupEnvironmentConfig = {
  environment: 'test',
  testOrganizations: [], // 测试环境可以清理所有组织
  safeCleanupEnabled: false,
};
```

#### 6.0.3 branchCode 组织分配关系检查

**关键表结构**：

- `DistributedSystemResourceEntity`：记录 branchCode 向组织的分发关系（已存在于 src/domain/entities/）
- `organization`：组织基础信息表

**组织分配状态判断**：

```sql
-- 检查 branchCode 是否被分配给任何组织
SELECT dsr.branch_code,
       GROUP_CONCAT(dsr.org_id) as assigned_orgs,
       COUNT(dsr.org_id) as org_count
FROM distributed_system_resource dsr
WHERE dsr.branch_code = ?
  AND dsr.distribute_status = 1  -- 有效分发（使用正确的字段名）
GROUP BY dsr.branch_code;
```

### 6.1 统一安全判断逻辑

#### 6.1.0 核心安全判断原则

**统一的安全检查函数**：

```typescript
// 统一的模型安全检查
async function isModelSafeToOperate(modelId: number, branchCode: string, environment: CleanupEnvironmentConfig): Promise<boolean> {
  // 1. 获取模型详细信息
  const model = await findRiskModelById(modelId);
  if (!model) return false;

  // 2. 检查模型的组织分配关系
  const distributedOrgs = await findOrganizationsUsingBranchCode(branchCode);

  // 3. 核心安全判断：生产非测试组织 + 已发布状态 = 不能清理
  if (model.status === DataStatusEnums.Enabled) {
    if (environment.environment === 'production') {
      // 生产环境：检查是否只分配给测试组织
      const nonTestOrgs = distributedOrgs.filter((orgId) => !environment.testOrganizations.includes(orgId));
      if (nonTestOrgs.length > 0) {
        return false; // 生产非测试组织且已发布 - 绝对不能清理
      }
    }
  }

  // 4. 明确的保护点：即使是Developing/Deprecated状态，如果是生产非测试组织，也暂不允许清理
  // 注：这是为了保险起见，将来如果需要放开可以修改这里
  if (environment.environment === 'production') {
    const nonTestOrgs = distributedOrgs.filter((orgId) => !environment.testOrganizations.includes(orgId));
    if (nonTestOrgs.length > 0) {
      // TODO: 将来如果要放开Developing/Deprecated状态的生产数据清理，修改这里
      return false; // 暂时保护所有生产非测试组织的数据
    }
  }

  return true; // 其他情况允许清理
}
```

**安全等级定义**：

1. **绝对保护**：生产非测试组织 + Enabled 状态
2. **暂时保护**：生产非测试组织 + Developing/Deprecated 状态（将来可能放开）
3. **可以清理**：测试环境 或 测试组织 或 未分配组织

### 6.2 清理场景与策略

#### 6.2.1 场景一：产品级清理（branchCode 级别）

**适用情况**：整个商业产品废弃，需要清理整个 branchCode 及其所有相关数据

**清理策略（基于组织分配关系 + 跨 branchCode 检查）**：

```mermaid
flowchart TD
    A[确定目标branchCode] --> B[检查组织分配关系]
    B --> C{是否被分配给组织?}
    C -->|未分配| D[检查跨branchCode分享]
    C -->|已分配| E{检查环境类型}
    E -->|测试环境| D
    E -->|生产环境| F{只分配给测试组织?}
    F -->|是| D
    F -->|否| G[❌ 禁止清理]

    D --> H{有跨branchCode分享的指标/策略?}
    H -->|有| I[保留跨branchCode分享的资源<br/>删除其他资源]
    H -->|无| J[✅ 可以完全清理]

    I --> K[清理非分享的模型]
    I --> L[清理非分享的分组]
    I --> M[保留跨branchCode分享的指标/策略]
    J --> N[级联清理所有数据]
    N --> O[清理所有版本模型]
    O --> P[清理所有指标和策略]
    P --> Q[清理所有关联关系]
```

**关键判断逻辑（两层检查）**：

1. **组织分配检查**：首先检查 branchCode 是否被分配给任何组织
2. **环境区分**：测试环境和生产环境采用不同的安全策略
3. **测试组织保护**：生产环境只允许清理特定测试组织的 branchCode
4. **跨 branchCode 分享检查**：检查指标和策略是否被其他 branchCode 引用

**清理粒度说明**：

- **完全清理**：branchCode 通过组织分配检查且无跨 branchCode 分享时
- **部分清理**：保留跨 branchCode 分享的指标/策略，清理其他资源
- **禁止清理**：branchCode 被生产组织使用时

**优势**：

- 基于业务规则进行安全判断
- 支持跨 branchCode 分享场景的数据保护
- 灵活的清理粒度控制

**安全保障**：

- 生产环境严格保护商业客户数据
- 跨 branchCode 分享的资源得到保护
- 测试组织可配置，便于环境管理

#### 6.2.2 场景二：模型级清理（单模型清理）

**适用情况**：清理特定版本模型，保留其他版本

**清理策略（基于统一安全判断）**：

```mermaid
flowchart TD
    A[选择目标模型] --> B{检查模型安全性}
    B -->|生产非测试组织且Enabled| C[❌ 禁止清理]
    B -->|其他情况| D[分析依赖关系]
    D --> E[清理模型专有分组]
    E --> F[检查共享指标]
    F --> G{指标被其他模型使用?}
    G -->|跨branchCode引用| H[只删除当前模型的关联关系]
    G -->|同branchCode引用| I[只删除当前模型的关联关系]
    G -->|无其他引用| J[完全删除指标]
    H --> K[检查共享策略]
    I --> K
    J --> K
    K --> L{策略被其他指标使用?}
    L -->|跨branchCode引用| M[只删除相关指标的策略关联]
    L -->|同branchCode引用| N[只删除相关指标的策略关联]
    L -->|无其他引用| O[完全删除策略]
    M --> P[完成清理]
    N --> P
    O --> P
```

**关键验证步骤**：

1. **统一安全检查**：只有生产非测试组织且已发布状态的模型不能清理
2. **明确检查点**：即使是 Developing/Deprecated 状态，如果属于生产非测试组织也暂不允许清理
3. **关联关系清理**：riskModelId 级别清理原则上只删除关联关系，保护共享资源

**核心原则**：

- **完全删除实体**：只有无其他引用时才允许
- **关联关系清理**：riskModelId 级别清理的主要操作
- **安全保护**：统一基于组织分配和发布状态判断

### 6.3 分层清理实施方案

#### 6.3.1 第一层：分组清理（简单）

**清理目标**：

- **主实体**：`GroupEntity`（指标分组）
- **关系实体**：`GroupMetricRelationEntity`（分组-指标关联关系）

**关系说明**：

- `RiskModelEntity` ↔ `GroupEntity`：1:N 直接关系
- `GroupEntity` ↔ `MetricsEntity`：N:M 关系（通过 `GroupMetricRelationEntity`）

**实施方案**：

```typescript
// 1. 识别孤立分组（无有效模型关联）
const orphanGroups = await findOrphanGroups(manager);

// 2. 先清理关系实体：分组-指标关联关系
await manager.delete(GroupMetricRelationEntity, { groupId: In(orphanGroups) });

// 3. 再清理主实体：分组本身
await manager.delete(GroupEntity, { groupId: In(orphanGroups) });
```

**安全检查**：

- ✅ 验证关联模型状态（检查 1:N 关系完整性）
- ✅ 检查 GroupMetricRelationEntity 清理完整性

#### 6.3.2 第二层：指标清理（复杂）

**清理目标**：

- **主实体**：`MetricsEntity`（风险指标）
- **关系实体**：
  - `GroupMetricRelationEntity`（分组-指标关联关系）
  - `MetricDimensionRelationEntity`（指标-策略关联关系）

**关系说明**：

- `GroupEntity` ↔ `MetricsEntity`：N:M 关系（通过 `GroupMetricRelationEntity`）
- `MetricsEntity` ↔ `DimensionHitStrategyEntity`：N:M 关系（通过 `MetricDimensionRelationEntity`）

**实施方案**：

```typescript
// 指标清理的两种模式实现
async function cleanupMetrics(
  targetMetricIds: number[],
  cleanupTrigger: CleanupTriggerType,
  targetBranchCode: string,
  targetModelId?: number,
  manager: EntityManager,
) {
  const metricsToDelete: number[] = [];
  const relationsToDelete: { metricId: number; modelId?: number }[] = [];

  for (const metricId of targetMetricIds) {
    const cleanupResult = await isMetricSafeToClean(metricId, targetBranchCode, environment, cleanupTrigger, targetModelId);

    if (cleanupResult.canDeleteMetric) {
      // 可以完全删除指标
      metricsToDelete.push(metricId);
    } else if (cleanupTrigger === CleanupTriggerType.RISK_MODEL_ID) {
      // 只删除指定模型的关联关系
      relationsToDelete.push({ metricId, modelId: targetModelId });
    }
    // 否则跳过（不能清理）
  }

  // 1. 先删除指定的关联关系（riskModelId级别清理）
  for (const relation of relationsToDelete) {
    // 删除指定模型分组与指标的关联
    await manager.delete(GroupMetricRelationEntity, {
      metricsId: relation.metricId,
      groupId: In(await getGroupIdsByModelId(relation.modelId)),
    });
  }

  // 2. 完全清理可删除的指标（branchCode级别或无其他引用）
  if (metricsToDelete.length > 0) {
    // 清理指标-策略关联关系
    await manager.delete(MetricDimensionRelationEntity, {
      metricsId: In(metricsToDelete),
    });

    // 清理分组-指标关联关系
    await manager.delete(GroupMetricRelationEntity, {
      metricsId: In(metricsToDelete),
    });

    // 清理指标本身
    await manager.delete(MetricsEntity, { metricsId: In(metricsToDelete) });
  }
}
```

**安全检查**：

- ⚠️ 跨模型引用验证（通过 `GroupMetricRelationEntity` 追溯）
- ⚠️ branchCode 边界检查（受控复用约束）
- ⚠️ 发布状态保护（检查所有关联模型状态）

#### 6.3.3 第三层：策略清理（最复杂）

**清理目标**：

- **主实体**：`DimensionHitStrategyEntity`（维度命中策略）
- **关系实体**：
  - `MetricDimensionRelationEntity`（指标-策略关联关系）
  - `DimensionHitStrategyFieldsEntity`（策略-字段关联关系）

**关系说明**：

- `MetricsEntity` ↔ `DimensionHitStrategyEntity`：N:M 关系（通过 `MetricDimensionRelationEntity`）
- `DimensionHitStrategyEntity` ↔ `DimensionFieldsEntity`：1:N 关系（通过 `DimensionHitStrategyFieldsEntity`）
- `DimensionHitStrategyEntity` → `DimensionDefinitionEntity`：N:1 直接关系

**实施方案**：

```typescript
// 1. 构建策略依赖图（分析所有关系实体）
const strategyDependencies = await buildStrategyDependencyGraph(manager);

// 2. 识别无引用策略（检查 MetricDimensionRelationEntity）
const unusedStrategies = strategyDependencies.filter((s) => s.activeReferences === 0);

// 3. 先清理关系实体：策略-字段关联关系
await manager.delete(DimensionHitStrategyFieldsEntity, {
  strategyId: In(unusedStrategies),
});

// 4. 再清理关系实体：指标-策略关联关系（防御性清理）
await manager.delete(MetricDimensionRelationEntity, {
  dimensionStrategyId: In(unusedStrategies),
});

// 5. 最后清理主实体：策略本身
await manager.delete(DimensionHitStrategyEntity, {
  strategyId: In(unusedStrategies),
});
```

**安全检查**：

- ⚠️ 多级依赖链验证（策略 → 指标 → 分组 → 模型）
- ⚠️ 跨指标影响评估（通过 `MetricDimensionRelationEntity`）
- ⚠️ 关系实体完整性验证（确保所有关系实体都已清理）

### 6.4 清理执行流程

#### 6.4.1 标准清理流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant API as 清理API
    participant Analyzer as 依赖分析器
    participant Cleaner as 清理执行器
    participant DB as 数据库
    participant Cache as 缓存

    Admin->>API: 发起清理请求
    API->>Analyzer: 分析依赖关系
    Analyzer->>DB: 查询实体引用关系
    DB-->>Analyzer: 返回依赖数据
    Analyzer-->>API: 返回清理计划

    API->>Admin: 返回影响评估
    Admin->>API: 确认执行清理

    API->>Cleaner: 执行清理
    Cleaner->>DB: 事务开始
    Cleaner->>DB: 清理关联关系
    Cleaner->>DB: 清理主实体
    Cleaner->>DB: 事务提交
    Cleaner->>Cache: 清理相关缓存
    Cleaner-->>API: 返回清理结果
    API-->>Admin: 返回最终结果
```

#### 6.4.2 安全机制保障

**三层安全检查**：

1. **预检查**：分析阶段的影响范围评估
2. **执行检查**：清理前的二次验证
3. **后检查**：清理后的数据一致性验证

**回滚机制**：

- 事务级回滚：单个实体清理失败时的即时回滚
- 批次级回滚：整个清理任务失败时的完整回滚
- 数据恢复：基于备份的数据恢复机制

### 6.5 关键技术要点

#### 6.5.1 branchCode 组织分配检查逻辑

```typescript
// branchCode 清理安全检查（含跨branchCode分享检查）
async function isBranchCodeSafeToClean(branchCode: string, environment: CleanupEnvironmentConfig): Promise<BranchCodeCleanupResult> {
  // 1. 检查 branchCode 是否被分配给任何组织
  const distributedOrgs = await findOrganizationsUsingBranchCode(branchCode);

  // 2. 组织分配安全检查
  let canCleanByOrgCheck = false;
  if (distributedOrgs.length === 0) {
    canCleanByOrgCheck = true; // 未分配给任何组织
  } else if (environment.environment === 'test') {
    canCleanByOrgCheck = true; // 测试环境可以清理所有 branchCode
  } else if (environment.environment === 'production') {
    const nonTestOrgs = distributedOrgs.filter((orgId) => !environment.testOrganizations.includes(orgId));
    canCleanByOrgCheck = nonTestOrgs.length === 0; // 只分配给测试组织
  }

  if (!canCleanByOrgCheck) {
    return {
      canClean: false,
      canFullClean: false,
      reason: 'branchCode被生产组织使用',
      sharedMetrics: [],
      sharedStrategies: [],
    };
  }

  // 3. 检查跨branchCode分享情况
  const sharedMetrics = await findCrossBranchCodeSharedMetrics(branchCode);
  const sharedStrategies = await findCrossBranchCodeSharedStrategies(branchCode);

  return {
    canClean: true,
    canFullClean: sharedMetrics.length === 0 && sharedStrategies.length === 0,
    reason: sharedMetrics.length > 0 || sharedStrategies.length > 0 ? '存在跨branchCode分享资源' : '可以完全清理',
    sharedMetrics,
    sharedStrategies,
  };
}

// 清理结果接口
interface BranchCodeCleanupResult {
  canClean: boolean; // 是否可以清理
  canFullClean: boolean; // 是否可以完全清理
  reason: string; // 清理结果原因
  sharedMetrics: number[]; // 跨branchCode分享的指标ID
  sharedStrategies: number[]; // 跨branchCode分享的策略ID
}

// 查找跨branchCode分享的指标
async function findCrossBranchCodeSharedMetrics(branchCode: string): Promise<number[]> {
  const query = `
    SELECT DISTINCT m.metrics_id
    FROM metrics m
    JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
    JOIN \`group\` g ON gmr.group_id = g.group_id
    JOIN risk_model rm ON g.model_id = rm.model_id
    WHERE rm.branch_code != ? 
      AND m.metrics_id IN (
        SELECT DISTINCT m2.metrics_id
        FROM metrics m2
        JOIN group_metric_relation gmr2 ON m2.metrics_id = gmr2.metrics_id
        JOIN \`group\` g2 ON gmr2.group_id = g2.group_id
        JOIN risk_model rm2 ON g2.model_id = rm2.model_id
        WHERE rm2.branch_code = ?
      )
  `;

  const result = await entityManager.query(query, [branchCode, branchCode]);
  return result.map((row) => row.metrics_id);
}

// 查找跨branchCode分享的策略
async function findCrossBranchCodeSharedStrategies(branchCode: string): Promise<number[]> {
  const query = `
    SELECT DISTINCT dhs.strategy_id
    FROM dimension_hit_strategy dhs
    JOIN metric_dimension_relation mdr ON dhs.strategy_id = mdr.dimension_strategy_id
    JOIN metrics m ON mdr.metrics_id = m.metrics_id
    JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
    JOIN \`group\` g ON gmr.group_id = g.group_id
    JOIN risk_model rm ON g.model_id = rm.model_id
    WHERE rm.branch_code != ?
      AND dhs.strategy_id IN (
        SELECT DISTINCT dhs2.strategy_id
        FROM dimension_hit_strategy dhs2
        JOIN metric_dimension_relation mdr2 ON dhs2.strategy_id = mdr2.dimension_strategy_id
        JOIN metrics m2 ON mdr2.metrics_id = m2.metrics_id
        JOIN group_metric_relation gmr2 ON m2.metrics_id = gmr2.metrics_id
        JOIN \`group\` g2 ON gmr2.group_id = g2.group_id
        JOIN risk_model rm2 ON g2.model_id = rm2.model_id
        WHERE rm2.branch_code = ?
      )
  `;

  const result = await entityManager.query(query, [branchCode, branchCode]);
  return result.map((row) => row.strategy_id);
}

// 查找使用 branchCode 的组织
async function findOrganizationsUsingBranchCode(branchCode: string): Promise<number[]> {
  const distributions = await entityManager.find(DistributedSystemResourceEntity, {
    where: {
      branchCode: branchCode,
      distributeStatus: 1, // 有效分发（使用正确的字段名）
    },
    select: ['orgId'],
  });

  return distributions.map((d) => d.orgId);
}

// 指标清理检查（基于清理触发方式和统一安全判断）
async function isMetricSafeToClean(
  metricId: number,
  targetBranchCode: string,
  environment: CleanupEnvironmentConfig,
  cleanupTrigger: CleanupTriggerType,
  targetModelId?: number,
): Promise<{ canDeleteMetric: boolean; reason: string }> {
  // 1. 查找所有使用该指标的模型
  const referencingModels = await findModelsUsingMetric(metricId);

  // 2. 检查是否有跨 branchCode 引用
  const crossBranchReferences = referencingModels.filter((model) => model.branchCode !== targetBranchCode);
  if (crossBranchReferences.length > 0) {
    if (cleanupTrigger === CleanupTriggerType.RISK_MODEL_ID) {
      return {
        canDeleteMetric: false,
        reason: '有跨branchCode引用，只能删除指定模型的关联关系',
      };
    } else {
      return {
        canDeleteMetric: false,
        reason: '有跨branchCode引用，不能删除',
      };
    }
  }

  // 3. 根据清理触发方式采用不同策略
  if (cleanupTrigger === CleanupTriggerType.BRANCH_CODE) {
    // branchCode级别：如果branchCode可以删除，指标可以完全删除
    const branchCodeCleanupResult = await isBranchCodeSafeToClean(targetBranchCode, environment);
    return {
      canDeleteMetric: branchCodeCleanupResult.canClean,
      reason: branchCodeCleanupResult.reason,
    };
  } else {
    // riskModelId级别：检查是否有其他模型在使用
    const otherReferences = referencingModels.filter((model) => model.branchCode === targetBranchCode && model.modelId !== targetModelId);

    if (otherReferences.length > 0) {
      return {
        canDeleteMetric: false,
        reason: '有其他模型引用，只能删除指定模型的关联关系',
      };
    } else {
      return {
        canDeleteMetric: true,
        reason: '无其他引用，可以完全删除指标',
      };
    }
  }
}

// 策略清理检查（基于清理触发方式和统一安全判断）
async function isStrategySafeToClean(
  strategyId: number,
  environment: CleanupEnvironmentConfig,
  cleanupTrigger: CleanupTriggerType,
  targetModelId?: number,
  targetBranchCode?: string,
): Promise<{ canDeleteStrategy: boolean; reason: string }> {
  // 1. 通过策略找到所有关联的指标，再找到所有关联的模型
  const referencingModels = await findModelsUsingStrategy(strategyId);
  const branchCodes = [...new Set(referencingModels.map((model) => model.branchCode))];

  // 2. 检查是否有跨 branchCode 引用
  if (branchCodes.length > 1) {
    if (cleanupTrigger === CleanupTriggerType.RISK_MODEL_ID) {
      return {
        canDeleteStrategy: false,
        reason: '有跨branchCode引用，只能删除指定模型关联指标的策略关系',
      };
    } else {
      return {
        canDeleteStrategy: false,
        reason: '有跨branchCode引用，不能删除',
      };
    }
  }

  if (branchCodes.length === 0) {
    return {
      canDeleteStrategy: true,
      reason: '没有任何引用，可以删除',
    };
  }

  // 3. 根据清理触发方式采用不同策略
  const currentBranchCode = targetBranchCode || branchCodes[0];

  if (cleanupTrigger === CleanupTriggerType.BRANCH_CODE) {
    // branchCode级别：如果branchCode可以删除，策略可以完全删除
    const branchCodeCleanupResult = await isBranchCodeSafeToClean(currentBranchCode, environment);
    return {
      canDeleteStrategy: branchCodeCleanupResult.canClean,
      reason: branchCodeCleanupResult.reason,
    };
  } else {
    // riskModelId级别：检查是否有其他模型通过指标在使用该策略
    const currentBranchModels = referencingModels.filter((model) => model.branchCode === currentBranchCode);
    const otherReferences = currentBranchModels.filter((model) => model.modelId !== targetModelId);

    if (otherReferences.length > 0) {
      return {
        canDeleteStrategy: false,
        reason: '有其他模型通过指标引用，只能删除指定模型关联指标的策略关系',
      };
    } else {
      return {
        canDeleteStrategy: true,
        reason: '无其他引用，可以完全删除策略',
      };
    }
  }
}
```

#### 6.5.2 级联清理顺序策略

**清理顺序设计原则**：

```mermaid
graph TD
    A["关联关系优先"] --> B["从外层到内层"]
    B --> C["从具体到抽象"]
    C --> D["保护系统级数据"]

    subgraph "具体执行顺序"
        E1["1. DistributedSystemResourceEntity<br/>(分发关系)"]
        E2["2. GroupMetricRelationEntity<br/>(分组-指标关联)"]
        E3["3. MetricDimensionRelationEntity<br/>(指标-策略关联)"]
        E4["4. DimensionHitStrategyFieldsEntity<br/>(策略字段关联)"]
        E5["5. GroupEntity (分组)"]
        E6["6. MetricsEntity (指标)"]
        E7["7. DimensionHitStrategyEntity (策略)"]

        E1 --> E2 --> E3 --> E4 --> E5 --> E6 --> E7
    end

    subgraph "永不清理"
        F["DimensionDefinitionEntity<br/>DimensionFieldsEntity"]
    end
```

#### 6.5.3 数据一致性保障

**事务边界设计**：

- **单实体事务**：每个实体的清理在独立事务中
- **批次事务**：相关联的实体清理在同一批次事务中
- **全局事务**：整个清理任务在最外层事务保护下

**一致性检查点**：

- 清理前：验证依赖关系完整性
- 清理中：检查外键约束不被违反
- 清理后：验证数据状态一致性

## 7. 暂存式分阶段清理机制

### 7.1 设计思路

为了提高数据清理的安全性和可追溯性，引入三阶段清理机制：

1. **分析暂存阶段**：分析依赖关系，将分析结果暂存到专门的表中
2. **标记废弃阶段**：将可清理的实体状态标记为 `Deprecated`，但不执行物理删除
3. **定期清理阶段**：定期扫描废弃超过 1 个月的数据，执行物理删除

### 7.2 暂存表结构设计

```typescript
@Entity('data_cleanup_staging')
export class DataCleanupStagingEntity {
  @PrimaryGeneratedColumn()
  id: number;

  // 清理目标标识
  @Column({ name: 'target_type', type: 'varchar', length: 50, comment: '清理目标类型: branchCode|riskModelId' })
  targetType: 'branchCode' | 'riskModelId';

  @Column({ name: 'target_value', type: 'varchar', length: 200, comment: '清理目标值' })
  targetValue: string;

  @Column({ name: 'risk_model_id', type: 'int', nullable: true, comment: '关联的风险模型ID' })
  riskModelId?: number;

  @Column({ name: 'branch_code', type: 'varchar', length: 100, nullable: true, comment: '关联的branchCode' })
  branchCode?: string;

  // 分析结果
  @Column({ name: 'analysis_result', type: 'json', comment: '清理分析结果详情' })
  analysisResult: CleanupAnalysisResult;

  // 状态管理
  @Column({ name: 'staging_status', type: 'tinyint', default: DataCleanupStagingStatus.Analyzed, comment: '暂存状态: 0-已分析, 1-已废弃, 2-已删除' })
  stagingStatus: DataCleanupStagingStatus;

  @Column({ name: 'deprecated_date', type: 'datetime', nullable: true, comment: '标记废弃时间' })
  deprecatedDate?: Date;

  @Column({ name: 'deleted_date', type: 'datetime', nullable: true, comment: '彻底删除时间' })
  deletedDate?: Date;

  @Column({ name: 'rollback_date', type: 'datetime', nullable: true, comment: '撤回操作时间' })
  rollbackDate?: Date;

  @Column({ name: 'rollback_count', type: 'int', default: 0, comment: '撤回操作次数' })
  rollbackCount: number;

  // 审计字段
  @Column({ name: 'create_date', type: 'datetime', default: () => 'CURRENT_TIMESTAMP' })
  createDate: Date;

  @Column({ name: 'update_date', type: 'datetime', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updateDate: Date;

  @Column({ name: 'created_by', type: 'int', comment: '创建人ID' })
  createdBy: number;

  @Column({ name: 'last_operated_by', type: 'int', nullable: true, comment: '最后操作人ID' })
  lastOperatedBy?: number;
}

// 暂存状态枚举
export enum DataCleanupStagingStatus {
  Analyzed = 0, // 已分析，等待废弃
  Deprecated = 1, // 已废弃，等待删除
  Deleted = 2, // 已彻底删除
}

// 清理分析结果接口
interface CleanupAnalysisResult {
  // 影响评估
  impactAssessment: {
    affectedGroups: number;
    affectedMetrics: number;
    affectedStrategies: number;
    affectedRelations: number;
  };

  // 可清理的实体列表
  cleanableEntities: {
    groupIds: number[];
    metricIds: number[];
    strategyIds: number[];
    relationIds: {
      groupMetricRelations: number[];
      metricDimensionRelations: number[];
      strategyFieldsRelations: number[];
    };
  };

  // 实体原始状态备份（用于撤回操作）
  originalStatusBackup: {
    groups: Array<{ id: number; originalStatus: DataStatusEnums }>;
    metrics: Array<{ id: number; originalStatus: DataStatusEnums }>;
    strategies: Array<{ id: number; originalStatus: DataStatusEnums }>;
    models: Array<{ id: number; originalStatus: DataStatusEnums }>;
  };

  // 共享数据分析
  sharedDataAnalysis: {
    exclusiveMetrics: number[];
    sharedMetrics: number[];
    exclusiveStrategies: number[];
    sharedStrategies: number[];
  };

  // 风险评估
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  riskFactors: string[];
}
```

### 7.3 三阶段清理流程（含撤回机制）

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant API as 清理API
    participant Analyzer as 依赖分析器
    participant Staging as 暂存服务
    participant Scheduler as 定时调度器
    participant Cleaner as 清理执行器
    participant DB as 数据库

    Note over Admin, DB: 第一阶段：分析与暂存
    Admin->>API: 发起清理分析请求
    API->>Analyzer: 分析依赖关系
    Analyzer->>DB: 查询实体引用关系
    DB-->>Analyzer: 返回依赖数据
    Analyzer->>Staging: 保存分析结果到暂存表
    Staging-->>API: 返回暂存记录ID
    API-->>Admin: 返回分析结果摘要

    Note over Admin, DB: 第二阶段：标记废弃（含状态备份）
    Admin->>API: 确认废弃操作
    API->>Staging: 备份实体原始状态
    Staging->>DB: 查询实体当前状态
    DB-->>Staging: 返回状态信息
    Staging->>Staging: 保存状态备份到分析结果
    API->>Cleaner: 执行状态标记废弃
    Cleaner->>DB: 更新实体状态为Deprecated
    Cleaner-->>API: 返回废弃结果
    API-->>Admin: 确认废弃完成

    Note over Admin, DB: 撤回机制（可选）
    Admin->>API: 请求撤回废弃操作
    API->>Staging: 验证暂存状态和备份数据
    Staging->>Cleaner: 执行状态恢复
    Cleaner->>DB: 恢复实体到原始状态
    Staging->>Staging: 更新暂存状态为已分析
    Cleaner-->>API: 返回撤回结果
    API-->>Admin: 确认撤回完成

    Note over Admin, DB: 第三阶段：定期清理
    Scheduler->>Staging: 扫描废弃超过1个月的记录
    Staging->>Cleaner: 发起彻底删除任务
    Cleaner->>DB: 执行物理删除
    Cleaner->>Staging: 更新暂存状态为已删除
    Staging-->>Scheduler: 返回清理结果
```

### 7.4 核心服务实现

```typescript
@Injectable()
export class DataCleanupStagingService {
  /**
   * 分析并暂存清理计划
   */
  async analyzeAndStage(targetType: 'branchCode' | 'riskModelId', targetValue: string, operatorId: number): Promise<DataCleanupStagingEntity> {
    // 1. 执行依赖分析（根据清理类型采用不同策略）
    let analysisResult: CleanupAnalysisResult;

    if (targetType === 'branchCode') {
      // branchCode 级别：基于组织分配关系分析
      analysisResult = await this.analyzeBranchCodeCleanup(targetValue);
    } else {
      // riskModelId 级别：基于状态和依赖关系分析
      analysisResult = await this.analyzeRiskModelCleanup(parseInt(targetValue));
    }

    // 2. 保存到暂存表
    const stagingEntity = new DataCleanupStagingEntity();
    stagingEntity.targetType = targetType;
    stagingEntity.targetValue = targetValue;
    stagingEntity.analysisResult = analysisResult;
    stagingEntity.stagingStatus = DataCleanupStagingStatus.Analyzed;
    stagingEntity.createdBy = operatorId;

    if (targetType === 'riskModelId') {
      stagingEntity.riskModelId = parseInt(targetValue);
    } else {
      stagingEntity.branchCode = targetValue;
    }

    return await this.stagingRepo.save(stagingEntity);
  }

  /**
   * branchCode 清理分析：基于组织分配关系 + 跨branchCode检查
   */
  private async analyzeBranchCodeCleanup(branchCode: string): Promise<CleanupAnalysisResult> {
    // 1. 检查组织分配关系和跨branchCode分享
    const distributedOrgs = await this.findOrganizationsUsingBranchCode(branchCode);
    const cleanupResult = await this.isBranchCodeSafeToClean(branchCode, this.environmentConfig);

    if (!cleanupResult.canClean) {
      // 不能清理时返回空的清理计划
      return {
        impactAssessment: { affectedGroups: 0, affectedMetrics: 0, affectedStrategies: 0, affectedRelations: 0 },
        cleanableEntities: {
          groupIds: [],
          metricIds: [],
          strategyIds: [],
          relationIds: { groupMetricRelations: [], metricDimensionRelations: [], strategyFieldsRelations: [] },
        },
        originalStatusBackup: { groups: [], metrics: [], strategies: [], models: [] },
        sharedDataAnalysis: { exclusiveMetrics: [], sharedMetrics: [], exclusiveStrategies: [], sharedStrategies: [] },
        riskLevel: 'HIGH',
        riskFactors: [cleanupResult.reason],
      };
    }

    // 2. 收集该 branchCode 下所有实体
    const allModels = await this.findModelsByBranchCode(branchCode);
    const allGroups = await this.findGroupsByBranchCode(branchCode);
    const allMetrics = await this.findMetricsByBranchCode(branchCode);
    const allStrategies = await this.findStrategiesByBranchCode(branchCode);

    // 3. 计算可清理的实体（排除跨branchCode分享的）
    const cleanableMetrics = allMetrics.filter((m) => !cleanupResult.sharedMetrics.includes(m.metricsId));
    const cleanableStrategies = allStrategies.filter((s) => !cleanupResult.sharedStrategies.includes(s.strategyId));

    return {
      impactAssessment: {
        affectedGroups: allGroups.length, // 分组可以全部清理（属于单一branchCode）
        affectedMetrics: cleanableMetrics.length,
        affectedStrategies: cleanableStrategies.length,
        affectedRelations: 0, // 计算关系数量
      },
      cleanableEntities: {
        groupIds: allGroups.map((g) => g.groupId),
        metricIds: cleanableMetrics.map((m) => m.metricsId),
        strategyIds: cleanableStrategies.map((s) => s.strategyId),
        relationIds: {
          groupMetricRelations: [], // 从关系表查询
          metricDimensionRelations: [],
          strategyFieldsRelations: [],
        },
      },
      originalStatusBackup: { groups: [], metrics: [], strategies: [], models: [] },
      sharedDataAnalysis: {
        exclusiveMetrics: cleanableMetrics.map((m) => m.metricsId),
        sharedMetrics: cleanupResult.sharedMetrics,
        exclusiveStrategies: cleanableStrategies.map((s) => s.strategyId),
        sharedStrategies: cleanupResult.sharedStrategies,
      },
      riskLevel: cleanupResult.canFullClean ? (distributedOrgs.length > 0 ? 'MEDIUM' : 'LOW') : 'MEDIUM',
      riskFactors: [
        ...(distributedOrgs.length > 0 ? [`影响 ${distributedOrgs.length} 个组织`] : []),
        ...(cleanupResult.sharedMetrics.length > 0 ? [`${cleanupResult.sharedMetrics.length} 个指标被其他branchCode引用`] : []),
        ...(cleanupResult.sharedStrategies.length > 0 ? [`${cleanupResult.sharedStrategies.length} 个策略被其他branchCode引用`] : []),
      ],
    };
  }

  /**
   * riskModelId 清理分析：基于状态和依赖关系（保持现有逻辑）
   */
  private async analyzeRiskModelCleanup(riskModelId: number): Promise<CleanupAnalysisResult> {
    // 1. 检查模型状态
    const model = await this.findRiskModelById(riskModelId);
    if (!model || model.status === DataStatusEnums.Enabled) {
      return {
        impactAssessment: { affectedGroups: 0, affectedMetrics: 0, affectedStrategies: 0, affectedRelations: 0 },
        cleanableEntities: {
          groupIds: [],
          metricIds: [],
          strategyIds: [],
          relationIds: { groupMetricRelations: [], metricDimensionRelations: [], strategyFieldsRelations: [] },
        },
        originalStatusBackup: { groups: [], metrics: [], strategies: [], models: [] },
        sharedDataAnalysis: { exclusiveMetrics: [], sharedMetrics: [], exclusiveStrategies: [], sharedStrategies: [] },
        riskLevel: 'HIGH',
        riskFactors: ['模型已发布，无法清理'],
      };
    }

    // 2. 分析依赖关系，检查复用情况（现有逻辑）
    return await this.dependencyAnalyzer.analyze('riskModelId', riskModelId.toString());
  }

  /**
   * 执行废弃操作
   */
  async executeDeprecation(stagingId: number): Promise<void> {
    const staging = await this.stagingRepo.findOne({ where: { id: stagingId } });
    if (!staging || staging.stagingStatus !== DataCleanupStagingStatus.Analyzed) {
      throw new Error('暂存记录状态不正确');
    }

    await this.entityManager.transaction(async (manager) => {
      // 1. 备份当前状态（如果还没有备份）
      if (!staging.analysisResult.originalStatusBackup) {
        staging.analysisResult.originalStatusBackup = await this.backupOriginalStatus(manager, staging.analysisResult);
        await manager.update(DataCleanupStagingEntity, stagingId, {
          analysisResult: staging.analysisResult,
        });
      }

      // 2. 标记实体为废弃状态
      await this.markEntitiesAsDeprecated(manager, staging.analysisResult);

      // 3. 更新暂存状态
      await manager.update(DataCleanupStagingEntity, stagingId, {
        stagingStatus: DataCleanupStagingStatus.Deprecated,
        deprecatedDate: new Date(),
      });
    });
  }

  /**
   * 撤回废弃操作，恢复到原始状态
   */
  async rollbackDeprecation(stagingId: number, operatorId: number): Promise<void> {
    const staging = await this.stagingRepo.findOne({ where: { id: stagingId } });
    if (!staging || staging.stagingStatus !== DataCleanupStagingStatus.Deprecated) {
      throw new Error('暂存记录状态不正确，只能撤回已废弃状态的记录');
    }

    if (!staging.analysisResult.originalStatusBackup) {
      throw new Error('没有找到原始状态备份，无法执行撤回操作');
    }

    await this.entityManager.transaction(async (manager) => {
      // 1. 恢复实体到原始状态
      await this.restoreOriginalStatus(manager, staging.analysisResult.originalStatusBackup);

      // 2. 更新暂存状态回到已分析状态，记录撤回信息
      await manager.update(DataCleanupStagingEntity, stagingId, {
        stagingStatus: DataCleanupStagingStatus.Analyzed,
        deprecatedDate: null,
        rollbackDate: new Date(),
        rollbackCount: staging.rollbackCount + 1,
        lastOperatedBy: operatorId,
      });

      // 3. 记录撤回操作日志
      this.logger.info(`数据清理撤回操作完成: stagingId=${stagingId}, operator=${operatorId}, rollbackCount=${staging.rollbackCount + 1}`);
    });
  }

  /**
   * 快速批量回退已废弃的分析结果（独立方法）
   * 用于紧急情况下快速恢复多个已标记废弃的记录
   */
  async quickRollbackDeprecatedAnalysis(options: {
    targetType?: 'branchCode' | 'riskModelId'; // 可选：只回退特定类型
    branchCode?: string; // 可选：只回退特定branchCode的记录
    createdAfter?: Date; // 可选：只回退指定时间后创建的记录
    operatorId: number; // 必需：操作人ID
    dryRun?: boolean; // 可选：是否只模拟运行，不实际执行
  }): Promise<{
    affectedRecords: number;
    rollbackDetails: Array<{
      stagingId: number;
      targetType: string;
      targetValue: string;
      status: 'success' | 'error';
      error?: string;
    }>;
  }> {
    const { targetType, branchCode, createdAfter, operatorId, dryRun = false } = options;

    // 1. 构建查询条件
    const whereConditions: any = {
      stagingStatus: DataCleanupStagingStatus.Deprecated,
    };

    if (targetType) {
      whereConditions.targetType = targetType;
    }

    if (branchCode) {
      whereConditions.branchCode = branchCode;
    }

    if (createdAfter) {
      whereConditions.createDate = MoreThan(createdAfter);
    }

    // 2. 查找符合条件的已废弃记录
    const deprecatedRecords = await this.stagingRepo.find({
      where: whereConditions,
      order: { createDate: 'DESC' },
    });

    const rollbackDetails: Array<{
      stagingId: number;
      targetType: string;
      targetValue: string;
      status: 'success' | 'error';
      error?: string;
    }> = [];

    // 3. 如果是模拟运行，直接返回要处理的记录信息
    if (dryRun) {
      return {
        affectedRecords: deprecatedRecords.length,
        rollbackDetails: deprecatedRecords.map((record) => ({
          stagingId: record.id,
          targetType: record.targetType,
          targetValue: record.targetValue,
          status: 'success' as const,
        })),
      };
    }

    // 4. 执行实际回退操作
    for (const record of deprecatedRecords) {
      try {
        await this.entityManager.transaction(async (manager) => {
          // 检查状态备份完整性
          if (!record.analysisResult.originalStatusBackup) {
            throw new Error(`暂存记录 ${record.id} 缺少状态备份数据`);
          }

          // 恢复实体状态
          await this.restoreOriginalStatus(manager, record.analysisResult.originalStatusBackup);

          // 更新暂存记录状态
          await manager.update(DataCleanupStagingEntity, record.id, {
            stagingStatus: DataCleanupStagingStatus.Analyzed,
            deprecatedDate: null,
            rollbackDate: new Date(),
            rollbackCount: record.rollbackCount + 1,
            lastOperatedBy: operatorId,
          });
        });

        rollbackDetails.push({
          stagingId: record.id,
          targetType: record.targetType,
          targetValue: record.targetValue,
          status: 'success',
        });

        this.logger.info(`快速回退完成: stagingId=${record.id}, targetType=${record.targetType}, targetValue=${record.targetValue}`);
      } catch (error) {
        rollbackDetails.push({
          stagingId: record.id,
          targetType: record.targetType,
          targetValue: record.targetValue,
          status: 'error',
          error: error.message,
        });

        this.logger.error(`快速回退失败: stagingId=${record.id}, error=${error.message}`);
      }
    }

    return {
      affectedRecords: rollbackDetails.filter((d) => d.status === 'success').length,
      rollbackDetails,
    };
  }

  /**
   * 定期扫描并清理废弃超过1个月的数据
   */
  async scanAndCleanExpiredData(): Promise<CleanupSummary> {
    const oneMonthAgo = moment().subtract(1, 'month').toDate();

    // 查找废弃超过1个月的记录
    const expiredStagings = await this.stagingRepo.find({
      where: {
        stagingStatus: DataCleanupStagingStatus.Deprecated,
        deprecatedDate: LessThan(oneMonthAgo),
      },
    });

    const summary = new CleanupSummary();

    for (const staging of expiredStagings) {
      try {
        // 按原始目标类型执行清理
        if (staging.targetType === 'branchCode') {
          await this.cleanupByBranchCode(staging.branchCode, staging.analysisResult);
        } else {
          await this.cleanupByRiskModelId(staging.riskModelId, staging.analysisResult);
        }

        // 更新暂存状态
        await this.stagingRepo.update(staging.id, {
          stagingStatus: DataCleanupStagingStatus.Deleted,
          deletedDate: new Date(),
        });

        summary.successCount++;
      } catch (error) {
        this.logger.error(`清理暂存记录失败: stagingId=${staging.id}`, error);
        summary.errorCount++;
      }
    }

    return summary;
  }

  /**
   * 备份实体的原始状态
   */
  private async backupOriginalStatus(manager: EntityManager, analysisResult: CleanupAnalysisResult): Promise<any> {
    const backup = {
      groups: [],
      metrics: [],
      strategies: [],
      models: [],
    };

    const { cleanableEntities } = analysisResult;

    // 备份分组状态
    if (cleanableEntities.groupIds.length > 0) {
      const groups = await manager.find(GroupEntity, {
        where: { groupId: In(cleanableEntities.groupIds) },
        select: ['groupId', 'status'],
      });
      backup.groups = groups.map((g) => ({ id: g.groupId, originalStatus: g.status }));
    }

    // 备份指标状态
    if (cleanableEntities.metricIds.length > 0) {
      const metrics = await manager.find(MetricsEntity, {
        where: { metricsId: In(cleanableEntities.metricIds) },
        select: ['metricsId', 'status'],
      });
      backup.metrics = metrics.map((m) => ({ id: m.metricsId, originalStatus: m.status }));
    }

    // 备份策略状态
    if (cleanableEntities.strategyIds.length > 0) {
      const strategies = await manager.find(DimensionHitStrategyEntity, {
        where: { strategyId: In(cleanableEntities.strategyIds) },
        select: ['strategyId', 'status'],
      });
      backup.strategies = strategies.map((s) => ({ id: s.strategyId, originalStatus: s.status }));
    }

    return backup;
  }

  /**
   * 恢复实体到原始状态
   */
  private async restoreOriginalStatus(manager: EntityManager, originalStatusBackup: any): Promise<void> {
    // 恢复分组状态
    for (const group of originalStatusBackup.groups) {
      await manager.update(GroupEntity, { groupId: group.id }, { status: group.originalStatus });
    }

    // 恢复指标状态
    for (const metric of originalStatusBackup.metrics) {
      await manager.update(MetricsEntity, { metricsId: metric.id }, { status: metric.originalStatus });
    }

    // 恢复策略状态
    for (const strategy of originalStatusBackup.strategies) {
      await manager.update(DimensionHitStrategyEntity, { strategyId: strategy.id }, { status: strategy.originalStatus });
    }

    // 恢复模型状态（如果有）
    for (const model of originalStatusBackup.models || []) {
      await manager.update(RiskModelEntity, { modelId: model.id }, { status: model.originalStatus });
    }
  }

  /**
   * 标记实体为废弃状态（不删除）
   */
  private async markEntitiesAsDeprecated(manager: EntityManager, analysisResult: CleanupAnalysisResult): Promise<void> {
    const { cleanableEntities } = analysisResult;

    // 标记分组为废弃
    if (cleanableEntities.groupIds.length > 0) {
      await manager.update(GroupEntity, { groupId: In(cleanableEntities.groupIds) }, { status: DataStatusEnums.Deprecated });
    }

    // 标记指标为废弃
    if (cleanableEntities.metricIds.length > 0) {
      await manager.update(MetricsEntity, { metricsId: In(cleanableEntities.metricIds) }, { status: DataStatusEnums.Deprecated });
    }

    // 标记策略为废弃
    if (cleanableEntities.strategyIds.length > 0) {
      await manager.update(DimensionHitStrategyEntity, { strategyId: In(cleanableEntities.strategyIds) }, { status: DataStatusEnums.Deprecated });
    }
  }
}
```

### 7.5 定时任务配置

```typescript
@Injectable()
export class DataCleanupScheduleService {
  @Cron('0 0 2 * * 0') // 每周日凌晨2点执行
  async weeklyCleanupScan() {
    try {
      this.logger.info('开始执行定期数据清理扫描...');
      const summary = await this.stagingService.scanAndCleanExpiredData();
      this.logger.info(`定期清理完成: 成功=${summary.successCount}, 失败=${summary.errorCount}`);
    } catch (error) {
      this.logger.error('定期数据清理失败', error);
    }
  }
}
```

### 7.6 暂存机制优势

1. **安全性提升**：分离分析、废弃和删除三个阶段，降低误删风险
2. **可追溯性**：完整记录清理分析结果和操作历史
3. **可恢复性**：废弃阶段可以恢复，删除前有充分的缓冲期
4. **审计友好**：所有操作都有详细的审计记录
5. **分阶段处理**：避免一次性大批量操作对系统性能的影响
6. **智能筛选**：只删除通过分析确认可以安全删除的数据，避免误删用户手动废弃的指标
7. **状态备份与恢复**：自动备份实体原始状态，支持精确撤回操作，恢复到废弃前的确切状态
8. **灵活的撤回窗口**：在物理删除前的任何时间都可以撤回废弃操作
9. **数据完整性保障**：确保撤回操作不会丢失任何状态信息，维护数据一致性

### 7.7 快速批量回退功能

#### 7.7.1 使用场景

**快速回退方法** `quickRollbackDeprecatedAnalysis()` 适用于以下紧急情况：

1. **误操作批量废弃**：意外标记了大量不应该废弃的记录
2. **紧急恢复需求**：需要快速恢复某个 branchCode 下的所有已废弃记录
3. **测试环境清理**：测试完成后快速恢复环境状态
4. **时间范围回退**：回退特定时间段内的所有废弃操作

#### 7.7.2 使用示例

```typescript
// 示例1：模拟运行，查看会回退哪些记录
const dryRunResult = await cleanupStagingService.quickRollbackDeprecatedAnalysis({
  operatorId: 12345,
  dryRun: true,
});
console.log(`模拟运行：将回退 ${dryRunResult.affectedRecords} 条记录`);

// 示例2：回退特定branchCode的所有已废弃记录
const branchCodeRollback = await cleanupStagingService.quickRollbackDeprecatedAnalysis({
  branchCode: 'RISK_MODEL_V2_2024',
  operatorId: 12345,
});

// 示例3：回退最近1小时内的所有废弃操作
const recentRollback = await cleanupStagingService.quickRollbackDeprecatedAnalysis({
  createdAfter: new Date(Date.now() - 60 * 60 * 1000), // 1小时前
  operatorId: 12345,
});

// 示例4：只回退riskModelId类型的记录
const modelRollback = await cleanupStagingService.quickRollbackDeprecatedAnalysis({
  targetType: 'riskModelId',
  operatorId: 12345,
});

// 处理回退结果
branchCodeRollback.rollbackDetails.forEach((detail) => {
  if (detail.status === 'success') {
    console.log(`成功回退: ${detail.targetType}=${detail.targetValue}`);
  } else {
    console.error(`回退失败: ${detail.targetType}=${detail.targetValue}, 错误: ${detail.error}`);
  }
});
```

#### 7.7.3 方法特性

**灵活过滤**：

- 按目标类型过滤（branchCode 或 riskModelId）
- 按特定 branchCode 过滤
- 按创建时间范围过滤
- 支持组合条件

**安全保障**：

- 模拟运行模式（dryRun），预览要回退的记录
- 事务保护，确保单条记录回退的原子性
- 详细的错误处理和日志记录
- 状态备份完整性检查

**批量处理**：

- 支持一次性回退多条记录
- 返回详细的处理结果
- 区分成功和失败的记录

### 7.8 撤回操作的限制与注意事项

1. **时间限制**：只能撤回状态为`Deprecated`的暂存记录，已删除的记录无法撤回
2. **状态备份完整性**：系统会在执行废弃操作时自动备份状态，确保撤回的准确性
3. **依赖关系检查**：撤回时会重新验证依赖关系，确保恢复后的状态不会产生数据不一致
4. **操作权限**：撤回操作需要相应的权限，建议由原操作人员或管理员执行
5. **审计记录**：撤回操作会被完整记录到审计日志中，便于追踪和分析
6. **批量回退风险**：快速批量回退虽然便利，但建议先使用 dryRun 模式确认影响范围

## 8. 清理实施指南

### 8.1 实施前准备

#### 8.1.1 环境准备

- **数据备份**：完整备份目标数据库
- **权限确认**：确保清理账号具有必要权限
- **监控配置**：设置清理过程监控和告警

#### 8.1.2 影响评估

```typescript
// 清理影响评估示例
interface CleanupImpactAssessment {
  targetScope: {
    branchCodes?: string[];
    modelIds?: number[];
    entityTypes: EntityType[];
  };
  estimatedImpact: {
    affectedGroups: number;
    affectedMetrics: number;
    affectedStrategies: number;
    affectedModels: number;
  };
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  recommendations: string[];
}
```

### 8.2 分阶段实施策略

#### 8.2.1 阶段一：试运行验证

- **目标**：验证清理逻辑正确性
- **范围**：小批量数据试运行
- **验证点**：依赖关系分析准确性

#### 8.2.2 阶段二：增量清理

- **目标**：清理明确废弃的数据
- **范围**：已确认废弃的模型和相关数据
- **验证点**：清理后系统功能正常

#### 8.2.3 阶段三：深度清理

- **目标**：清理复杂引用的数据
- **范围**：需要复杂分析的共享数据
- **验证点**：业务功能完整性

### 8.3 技术实施要点

#### 8.3.1 关键 SQL 查询模板

**孤立分组识别**：

```sql
SELECT g.group_id, g.group_name, rm.model_id, rm.status
FROM `group` g
LEFT JOIN risk_model rm ON g.model_id = rm.model_id
WHERE rm.model_id IS NULL
   OR rm.status = ${DataStatusEnums.Deprecated};
```

**branchCode 组织分配关系检查**：

```sql
-- 检查 branchCode 的组织分配状态
SELECT dsr.branch_code,
       GROUP_CONCAT(dsr.org_id) as assigned_orgs,
       COUNT(dsr.org_id) as org_count,
       GROUP_CONCAT(o.org_name) as org_names
FROM distributed_system_resource dsr
LEFT JOIN organization o ON dsr.org_id = o.org_id
WHERE dsr.branch_code = ?
  AND dsr.status = 1  -- 有效分发
GROUP BY dsr.branch_code;

-- 检查是否只分配给测试组织（生产环境使用）
SELECT dsr.branch_code,
       COUNT(*) as total_orgs,
       SUM(CASE WHEN dsr.org_id IN (1001, 1002, 1003) THEN 1 ELSE 0 END) as test_orgs,
       COUNT(*) - SUM(CASE WHEN dsr.org_id IN (1001, 1002, 1003) THEN 1 ELSE 0 END) as production_orgs
FROM distributed_system_resource dsr
WHERE dsr.branch_code = ?
  AND dsr.distribute_status = 1
GROUP BY dsr.branch_code;
```

**跨 branchCode 指标检查（用于 riskModelId 级别清理）**：

```sql
SELECT m.metrics_id, m.name,
       GROUP_CONCAT(DISTINCT rm.branch_code) as branch_codes,
       COUNT(DISTINCT rm.branch_code) as branch_count
FROM metrics m
JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
JOIN `group` g ON gmr.group_id = g.group_id
JOIN risk_model rm ON g.model_id = rm.model_id
WHERE rm.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
GROUP BY m.metrics_id
HAVING branch_count > 1;
```

**查找 branchCode 下所有实体（用于 branchCode 级别清理）**：

```sql
-- 查找 branchCode 下所有模型
SELECT rm.model_id, rm.model_name, rm.status
FROM risk_model rm
WHERE rm.branch_code = ?;

-- 查找 branchCode 下所有分组
SELECT g.group_id, g.group_name, g.status
FROM `group` g
JOIN risk_model rm ON g.model_id = rm.model_id
WHERE rm.branch_code = ?;

-- 查找 branchCode 下所有指标
SELECT DISTINCT m.metrics_id, m.name, m.status
FROM metrics m
JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
JOIN `group` g ON gmr.group_id = g.group_id
JOIN risk_model rm ON g.model_id = rm.model_id
WHERE rm.branch_code = ?;
```

#### 8.3.2 性能优化建议

**批量操作优化**：

- 使用 `IN` 子句批量删除，但限制批次大小（建议 ≤ 1000）
- 避免在高峰期执行大批量删除操作
- 使用合适的事务隔离级别

**索引优化建议**：

```sql
-- 建议添加的索引（如果不存在）
CREATE INDEX idx_group_model_id ON `group`(model_id);
CREATE INDEX idx_risk_model_branch_code ON risk_model(branch_code);
CREATE INDEX idx_risk_model_status ON risk_model(status);
```

---

## 9. 代码实现指导

### 9.1 代码组织结构

基于项目的六层分层架构，建议按以下方式组织代码：

**临时开发目录**（用于开发和测试验证）：

```
src/modules/data-processing/data-cleaner/risk-model/
├── entities/                     # 暂存表等新增实体
│   └── DataCleanupStagingEntity.ts
├── enums/                        # 清理相关枚举
│   └── CleanupTriggerType.ts
├── interfaces/                   # 清理接口定义
│   ├── CleanupEnvironmentConfig.ts
│   └── CleanupAnalysisResult.ts
├── services/                     # 清理服务实现
│   ├── BranchCodeCleanupService.ts
│   └── RiskModelCleanupService.ts
└── utils/                        # 清理工具函数
    ├── safety-checker.ts
    └── dependency-analyzer.ts
```

**正式迁移目标**（开发完成后按架构规则迁移）：

- `entities/` → `src/domain/entities/`
- `enums/` → `src/domain/enums/cleanup/`
- `interfaces/` → `src/domain/model/cleanup/`
- `services/` → `src/modules/data-processing/data-cleaner/`
- `utils/` → `src/commons/utils/cleanup/`

### 9.2 关键代码文件引用

**现有依赖（已确认存在）**：

```typescript
// 数据状态枚举
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';

// 分发资源实体
import { DistributedSystemResourceEntity } from '@domain/entities/DistributedSystemResourceEntity';
import { DistributeResourceStatusEnums } from '@domain/enums/DistributeResourceStatusEnums';

// 风险模型相关实体
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { GroupEntity } from '@domain/entities/GroupEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';

// 关系实体
import { GroupMetricRelationEntity } from '@domain/entities/GroupMetricRelationEntity';
import { MetricDimensionRelationEntity } from '@domain/entities/MetricDimensionRelationEntity';
import { DimensionHitStrategyFieldsEntity } from '@domain/entities/DimensionHitStrategyFieldsEntity';
```

### 9.3 实现优先级

1. **第一阶段**：实现核心安全检查逻辑

   - `isModelSafeToOperate()` 统一安全判断
   - `isBranchCodeSafeToClean()` branchCode 级别检查
   - `CleanupTriggerType` 枚举定义

2. **第二阶段**：实现暂存机制

   - `DataCleanupStagingEntity` 暂存表
   - 三阶段清理流程（分析 → 废弃 → 删除）
   - 撤回机制实现
   - **快速批量回退方法** `quickRollbackDeprecatedAnalysis()`

3. **第三阶段**：实现具体清理逻辑
   - 指标和策略的两种清理模式
   - 关联关系的精确清理
   - 批量操作优化

### 9.4 关键方法实现建议

#### 9.4.1 快速回退方法的关键要点

```typescript
// 建议的服务类结构
export class DataCleanupStagingService {
  // 常规撤回方法 - 单条记录
  async rollbackDeprecation(stagingId: number, operatorId: number): Promise<void>;

  // 快速批量回退方法 - 多条记录（独立方法）
  async quickRollbackDeprecatedAnalysis(options: QuickRollbackOptions): Promise<QuickRollbackResult>;

  // 私有辅助方法
  private async restoreOriginalStatus(manager: EntityManager, backup: any): Promise<void>;
}

// 类型定义
interface QuickRollbackOptions {
  targetType?: 'branchCode' | 'riskModelId';
  branchCode?: string;
  createdAfter?: Date;
  operatorId: number;
  dryRun?: boolean;
}

interface QuickRollbackResult {
  affectedRecords: number;
  rollbackDetails: RollbackDetail[];
}
```

#### 9.4.2 安全检查优先级

1. **参数验证**：operatorId 必须提供，其他参数可选
2. **权限检查**：验证操作人员是否有批量回退权限
3. **dryRun 推荐**：建议在 UI 中默认启用 dryRun 模式
4. **批量限制**：考虑设置单次回退的最大记录数限制

## 10. 实施指南

### 10.1 开发环境准备

1. **数据库迁移**：创建暂存表

```sql
-- 创建数据清理暂存表
CREATE TABLE `data_cleanup_staging` (
  `id` int NOT NULL AUTO_INCREMENT,
  `target_type` varchar(50) NOT NULL COMMENT '清理目标类型: branchCode|riskModelId',
  `target_value` varchar(200) NOT NULL COMMENT '清理目标值',
  `risk_model_id` int DEFAULT NULL COMMENT '关联的风险模型ID',
  `branch_code` varchar(100) DEFAULT NULL COMMENT '关联的branchCode',
  `analysis_result` json NOT NULL COMMENT '清理分析结果详情',
  `staging_status` tinyint NOT NULL DEFAULT '0' COMMENT '暂存状态: 0-已分析, 1-已废弃, 2-已删除',
  `deprecated_date` datetime DEFAULT NULL COMMENT '标记废弃时间',
  `deleted_date` datetime DEFAULT NULL COMMENT '彻底删除时间',
  `rollback_date` datetime DEFAULT NULL COMMENT '撤回操作时间',
  `rollback_count` int NOT NULL DEFAULT '0' COMMENT '撤回操作次数',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL COMMENT '创建人ID',
  `last_operated_by` int DEFAULT NULL COMMENT '最后操作人ID',
  PRIMARY KEY (`id`),
  KEY `idx_target_type_value` (`target_type`, `target_value`),
  KEY `idx_branch_code` (`branch_code`),
  KEY `idx_staging_status` (`staging_status`),
  KEY `idx_deprecated_date` (`deprecated_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据清理暂存表';
```

2. **环境配置**：

```typescript
// config/cleanup.config.ts
export const cleanupConfig: CleanupEnvironmentConfig = {
  environment: process.env.NODE_ENV === 'production' ? 'production' : 'test',
  testOrganizations: process.env.TEST_ORGANIZATIONS?.split(',').map(Number) || [],
  safeCleanupEnabled: process.env.SAFE_CLEANUP_ENABLED !== 'false',
  batchSize: parseInt(process.env.CLEANUP_BATCH_SIZE) || 100,
  maxRollbackCount: parseInt(process.env.MAX_ROLLBACK_COUNT) || 3,
};
---

**总结**：本文档基于风险模型系统的 branchCode 分组机制、单向数据流和受控复用架构设计，为数据清理提供了系统性的策略指导。实施时应严格遵循设计约束，确保清理操作不破坏系统的商业逻辑和数据完整性。

**重要提醒**：

1. 所有代码实现必须引用现有的实体和枚举，避免重复定义
2. 临时代码放在 `src/modules/data-processing/data-cleaner/risk-model/` 下便于统一检查
3. 开发完成后按六层架构规则将代码迁移到对应目录
4. 清理触发方式通过 `CleanupTriggerType` 枚举明确区分
```
