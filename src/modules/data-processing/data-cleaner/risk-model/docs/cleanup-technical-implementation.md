生成时间：2025-06-17 17:22:28

# 风险模型数据清理技术实现指南

## 1. 文档概述

### 1.1 实现目标

本文档基于 `cleanup-technical-guide.md` 中的策略设计，提供具体的技术实现方案，包括：

- **代码结构设计**：基于六层分层架构的代码组织
- **核心服务实现**：清理逻辑的具体代码实现
- **数据库操作**：高效安全的数据清理 SQL
- **测试策略**：完整的单元测试和集成测试方案

### 1.2 实现范围

**主要清理实体**：

- `GroupEntity`（指标分组）- 简单清理
- `MetricsEntity`（风险指标）- 复杂清理，支持跨 branchCode 检查
- `DimensionHitStrategyEntity`（维度命中策略）- 最复杂清理，多级依赖

**清理触发方式**：

- **branchCode 级别**：基于组织分配关系的产品级清理
- **riskModelId 级别**：单模型清理，保护共享资源

## 2. 技术架构设计

### 2.1 代码组织结构

```
src/modules/data-processing/data-cleaner/risk-model/
├── enums/
│   ├── CleanupTriggerType.ts          # 清理触发类型
│   └── DataCleanupStagingStatus.ts    # 暂存状态枚举
├── interfaces/
│   ├── cleanup-config.interface.ts    # 清理配置接口
│   ├── cleanup-result.interface.ts    # 清理结果接口
│   └── analysis-result.interface.ts   # 分析结果接口
├── entities/
│   └── DataCleanupStagingEntity.ts    # 暂存表实体
├── services/
│   ├── DataCleanupStagingService.ts   # 暂存机制核心服务
│   ├── SafetyCheckerService.ts        # 安全检查服务
│   ├── DependencyAnalyzerService.ts   # 依赖分析服务
│   └── CleanupExecutorService.ts      # 清理执行服务
├── utils/
│   ├── sql-builders.ts                # SQL 构建工具
│   └── batch-processor.ts             # 批量处理工具
└── tests/
    ├── safety-checker.unittest.spec.ts
    ├── dependency-analyzer.unittest.spec.ts
    └── cleanup-integration.spec.ts
```

### 2.2 核心枚举定义

```typescript
// src/modules/data-processing/data-cleaner/risk-model/enums/CleanupTriggerType.ts
export enum CleanupTriggerType {
  BRANCH_CODE = 'branchCode', // 根据branchCode删除
  RISK_MODEL_ID = 'riskModelId', // 根据指定riskModelId删除
}

// src/modules/data-processing/data-cleaner/risk-model/enums/DataCleanupStagingStatus.ts
export enum DataCleanupStagingStatus {
  Analyzed = 0, // 已分析，等待废弃
  Deprecated = 1, // 已废弃，等待删除
  Deleted = 2, // 已彻底删除
}
```

## 3. 核心接口定义

### 3.1 清理配置接口

```typescript
// src/modules/data-processing/data-cleaner/risk-model/interfaces/cleanup-config.interface.ts
export interface CleanupEnvironmentConfig {
  environment: 'test' | 'production';
  testOrganizations: number[]; // 允许清理的测试组织ID列表
  safeCleanupEnabled: boolean; // 是否启用安全清理模式
  batchSize: number; // 批量操作大小
  maxRollbackCount: number; // 最大撤回次数
}

export interface QuickRollbackOptions {
  targetType?: 'branchCode' | 'riskModelId';
  branchCode?: string;
  createdAfter?: Date;
  operatorId: number;
  dryRun?: boolean;
}
```

### 3.2 清理结果接口

```typescript
// src/modules/data-processing/data-cleaner/risk-model/interfaces/cleanup-result.interface.ts
export interface BranchCodeCleanupResult {
  canClean: boolean;
  canFullClean: boolean;
  reason: string;
  sharedMetrics: number[];
  sharedStrategies: number[];
}

export interface MetricCleanupResult {
  canDeleteMetric: boolean;
  reason: string;
  crossBranchReferences: Array<{
    modelId: number;
    branchCode: string;
    modelName: string;
  }>;
}

export interface QuickRollbackResult {
  affectedRecords: number;
  rollbackDetails: Array<{
    stagingId: number;
    targetType: string;
    targetValue: string;
    status: 'success' | 'error';
    error?: string;
  }>;
}
```

### 3.3 分析结果接口

```typescript
// src/modules/data-processing/data-cleaner/risk-model/interfaces/analysis-result.interface.ts
export interface CleanupAnalysisResult {
  // 影响评估
  impactAssessment: {
    affectedGroups: number;
    affectedMetrics: number;
    affectedStrategies: number;
    affectedRelations: number;
  };

  // 可清理的实体列表
  cleanableEntities: {
    groupIds: number[];
    metricIds: number[];
    strategyIds: number[];
    relationIds: {
      groupMetricRelations: number[];
      metricDimensionRelations: number[];
      strategyFieldsRelations: number[];
    };
  };

  // 实体原始状态备份（用于撤回操作）
  originalStatusBackup: {
    groups: Array<{ id: number; originalStatus: DataStatusEnums }>;
    metrics: Array<{ id: number; originalStatus: DataStatusEnums }>;
    strategies: Array<{ id: number; originalStatus: DataStatusEnums }>;
    models: Array<{ id: number; originalStatus: DataStatusEnums }>;
  };

  // 共享数据分析
  sharedDataAnalysis: {
    exclusiveMetrics: number[];
    sharedMetrics: number[];
    exclusiveStrategies: number[];
    sharedStrategies: number[];
  };

  // 风险评估
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  riskFactors: string[];
}
```

## 4. 清理场景分析与策略

### 4.1 场景矩阵

| 清理类型      | 复杂度 | 关系实体处理 | 共享数据策略       | 安全等级 |
| ------------- | ------ | ------------ | ------------------ | -------- |
| branchCode 级 | 中等   | 级联删除     | 保护跨 branch 共享 | 高       |
| 单模型        | 复杂   | 选择性删除   | 严格引用检查       | 极高     |
| 孤立数据      | 简单   | 直接清理     | 无需考虑           | 中等     |

### 4.2 场景一：branchCode 级别清理（完整产品删除）

**设计理念对接**：

- 遵循 branchCode 商业产品边界
- 利用单向数据流特性，确保清理完整性
- 保护跨 branchCode 共享的数据资源

**特点**：相对简单，但需要精确识别和保护跨 branchCode 共享数据

```typescript
async cleanupByBranchCode(branchCode: string, options: CleanupOptions): Promise<CleanupResult> {
  return await this.riskModelRepo.manager.transaction(async manager => {
    // 1. 预分析：识别共享数据
    const sharedAnalysis = await this.analyzeBranchCodeSharedData(manager, branchCode);

    // 2. 查找该 branchCode 下所有模型
    const models = await manager.find(RiskModelEntity, {
      where: { branchCode },
      relations: ['groups', 'groups.groupMetrics']
    });

    // 3. 验证清理安全性
    await this.validateBranchCodeDeletion(manager, branchCode, models, options);

    // 4. 分阶段级联删除
    const result = await this.deleteBranchCodeCascade(manager, branchCode, models, sharedAnalysis);

    // 5. 清理相关缓存
    await this.clearBranchCodeCache(branchCode);

    return {
      models: result.deletedModels,
      groups: result.deletedGroups,
      metrics: result.deletedMetrics,
      strategies: result.deletedStrategies,
      groupMetricRelations: result.deletedGroupMetricRelations,
      metricDimensionRelations: result.deletedMetricDimensionRelations,
      distributedResources: result.deletedDistributedResources,
      affectedBranchCode: branchCode,
      sharedDataAnalysis: sharedAnalysis.report,
      summary: result.summary
    };
  });
}

/**
 * 分析 branchCode 的共享数据情况
 */
private async analyzeBranchCodeSharedData(
  manager: EntityManager,
  branchCode: string
): Promise<BranchCodeSharedAnalysis> {
  // 1. 查找该 branchCode 使用的所有指标
  const metricsInBranch = await manager.query(`
    SELECT DISTINCT m.metrics_id, m.metrics_name
    FROM metrics m
    INNER JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
    INNER JOIN \`group\` g ON gmr.group_id = g.group_id
    INNER JOIN risk_model rm ON g.model_id = rm.model_id
    WHERE rm.branch_code = ?
      AND rm.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
  `, [branchCode]);

  // 2. 检查这些指标是否被其他 branchCode 使用
  const sharedMetrics = new Map<number, string[]>();
  for (const metric of metricsInBranch) {
    const otherBranches = await manager.query(`
      SELECT DISTINCT rm.branch_code
      FROM risk_model rm
      INNER JOIN \`group\` g ON rm.model_id = g.model_id
      INNER JOIN group_metric_relation gmr ON g.group_id = gmr.group_id
      WHERE gmr.metrics_id = ?
        AND rm.branch_code != ?
        AND rm.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
    `, [metric.metrics_id, branchCode]);

    if (otherBranches.length > 0) {
      sharedMetrics.set(
        metric.metrics_id,
        otherBranches.map(b => b.branch_code)
      );
    }
  }

  // 3. 分析策略共享情况
  const sharedStrategies = await this.analyzeStrategySharing(manager, branchCode);

  return {
    branchCode,
    exclusiveMetrics: metricsInBranch
      .filter(m => !sharedMetrics.has(m.metrics_id))
      .map(m => m.metrics_id),
    sharedMetrics,
    sharedStrategies,
    report: {
      totalMetrics: metricsInBranch.length,
      exclusiveMetrics: metricsInBranch.length - sharedMetrics.size,
      sharedMetrics: sharedMetrics.size,
      riskLevel: this.assessDeletionRisk(sharedMetrics, sharedStrategies)
    }
  };
}

private async deleteBranchCodeCascade(
  manager: EntityManager,
  branchCode: string,
  models: RiskModelEntity[],
  sharedAnalysis: BranchCodeSharedAnalysis
): Promise<BranchCodeDeletionResult> {
  const result = new BranchCodeDeletionResult();
  const modelIds = models.map(m => m.modelId);

  // 第一阶段：删除关系实体（自下而上）
  result.deletedDistributedResources = await this.deleteDistributedResources(manager, modelIds);
  result.deletedMetricDimensionRelations = await this.deleteMetricDimensionRelations(manager, modelIds, sharedAnalysis);
  result.deletedGroupMetricRelations = await this.deleteGroupMetricRelations(manager, modelIds);

  // 第二阶段：删除独有的主实体
  result.deletedStrategies = await this.deleteExclusiveStrategies(manager, branchCode, sharedAnalysis);
  result.deletedMetrics = await this.deleteExclusiveMetrics(manager, branchCode, sharedAnalysis);

  // 第三阶段：删除该 branchCode 独有的分组和模型
  result.deletedGroups = await this.deleteGroups(manager, modelIds);
  result.deletedModels = await this.deleteModels(manager, branchCode);

  // 验证删除完整性
  await this.validateDeletionCompleteness(manager, branchCode, result);

  return result;
}

/**
 * 删除分发资源关系
 */
private async deleteDistributedResources(
  manager: EntityManager,
  modelIds: number[]
): Promise<CleanupItemResult[]> {
  const results: CleanupItemResult[] = [];

  if (modelIds.length === 0) return results;

  try {
    // 查找待删除的分发关系
    const distributedResources = await manager.find(DistributedSystemResourceEntity, {
      where: {
        resourceId: In(modelIds),
        resourceType: In([DistributedResourceTypeEnums.RiskModel])
      }
    });

    // 执行删除
    const deleteResult = await manager.delete(DistributedSystemResourceEntity, {
      resourceId: In(modelIds),
      resourceType: In([DistributedResourceTypeEnums.RiskModel])
    });

    results.push({
      type: 'distributed_resource',
      status: 'success',
      affectedRows: deleteResult.affected || 0,
      details: `删除了 ${distributedResources.length} 个模型分发关系`
    });

  } catch (error) {
    results.push({
      type: 'distributed_resource',
      status: 'error',
      error: error.message
    });
  }

  return results;
}

/**
 * 删除指标-维度关联（需要考虑共享策略保护）
 */
private async deleteMetricDimensionRelations(
  manager: EntityManager,
  modelIds: number[],
  sharedAnalysis: BranchCodeSharedAnalysis
): Promise<CleanupItemResult[]> {
  const results: CleanupItemResult[] = [];

  try {
    // 查找需要删除的关系，排除被其他 branchCode 共享的策略
    const sharedStrategyIds = Array.from(sharedAnalysis.sharedStrategies.keys());
    const excludeCondition = sharedStrategyIds.length > 0
      ? `AND mdr.dimension_hit_strategy_id NOT IN (${sharedStrategyIds.join(',')})`
      : '';

    const deleteQuery = `
      DELETE mdr FROM metric_dimension_relation mdr
      INNER JOIN metrics m ON mdr.metrics_id = m.metrics_id
      INNER JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
      INNER JOIN \`group\` g ON gmr.group_id = g.group_id
      WHERE g.model_id IN (${modelIds.join(',')})
        ${excludeCondition}
    `;

    const deleteResult = await manager.query(deleteQuery);

    results.push({
      type: 'metric_dimension_relation',
      status: 'success',
      affectedRows: deleteResult.affectedRows,
      details: `删除指标-维度关联，保护了 ${sharedStrategyIds.length} 个共享策略`
    });

  } catch (error) {
    results.push({
      type: 'metric_dimension_relation',
      status: 'error',
      error: error.message
    });
  }

  return results;
}

/**
 * 删除分组-指标关联
 */
private async deleteGroupMetricRelations(
  manager: EntityManager,
  modelIds: number[]
): Promise<CleanupItemResult[]> {
  const results: CleanupItemResult[] = [];

  try {
    const deleteQuery = `
      DELETE gmr FROM group_metric_relation gmr
      INNER JOIN \`group\` g ON gmr.group_id = g.group_id
      WHERE g.model_id IN (${modelIds.join(',')})
    `;

    const deleteResult = await manager.query(deleteQuery);

    results.push({
      type: 'group_metric_relation',
      status: 'success',
      affectedRows: deleteResult.affectedRows,
      details: `删除了该 branchCode 下所有分组-指标关联`
    });

  } catch (error) {
    results.push({
      type: 'group_metric_relation',
      status: 'error',
      error: error.message
    });
  }

  return results;
}
```

### 4.2 场景二：单个模型清理（复杂场景）

**特点**：需要检查共享数据的引用关系

```typescript
async cleanupSingleModel(modelId: number, options: CleanupOptions): Promise<CleanupResult> {
  return await this.riskModelRepo.manager.transaction(async manager => {
    // 1. 获取模型信息
    const model = await this.getFullModel(manager, modelId);
    if (!model) throw new Error('Model not found');

    // 2. 分析共享数据引用
    const sharedAnalysis = await this.analyzeSharedDataReferences(manager, model);

    // 3. 执行selective清理
    await this.deleteSingleModelSelective(manager, model, sharedAnalysis);

    return {
      deletedModel: modelId,
      sharedDataKept: sharedAnalysis.keptEntities,
      summary: { /* ... */ }
    };
  });
}

private async analyzeSharedDataReferences(
  manager: EntityManager,
  model: RiskModelEntity
): Promise<SharedDataAnalysis> {
  const analysis = new SharedDataAnalysis();

  // 分析指标的共享情况
  for (const group of model.groups) {
    for (const relation of group.groupMetrics) {
      const metricId = relation.metricsId;

      // 检查该指标是否被同 branchCode 下其他模型使用
      const refCount = await manager.query(`
        SELECT COUNT(DISTINCT rm.model_id) as count
        FROM metrics m
        JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
        JOIN \`group\` g ON gmr.group_id = g.group_id
        JOIN risk_model rm ON g.model_id = rm.model_id
        WHERE m.metrics_id = ?
          AND rm.branch_code = ?
          AND rm.model_id != ?
          AND rm.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
      `, [metricId, model.branchCode, model.modelId]);

      if (refCount[0].count === 0) {
        analysis.exclusiveMetrics.push(metricId);
      } else {
        analysis.sharedMetrics.push(metricId);
      }
    }
  }

  // 分析策略的共享情况
  analysis.exclusiveStrategies = await this.findExclusiveStrategies(
    manager, model, analysis.exclusiveMetrics
  );

  return analysis;
}
```

## 5. 核心算法实现

### 5.1 关系实体感知的删除顺序算法

**设计原则**：

1. **先关系后主体**：删除顺序严格遵循外键约束，先删除关系实体，再删除主实体
2. **层次化清理**：按照实体层级和依赖深度进行分层清理
3. **共享数据保护**：通过关系实体追溯，精确识别跨 branchCode 共享的数据

```mermaid
graph TD
    A["开始清理"] --> B["第一层：外部关系<br/>DistributedSystemResourceEntity"]
    B --> C["第二层：业务关系<br/>MetricDimensionRelationEntity"]
    C --> D["第三层：核心关系<br/>GroupMetricRelationEntity"]
    D --> E["第四层：策略主体<br/>DimensionHitStrategyEntity"]
    E --> F["第五层：指标主体<br/>MetricsEntity"]
    F --> G["第六层：分组主体<br/>GroupEntity"]
    G --> H["第七层：模型主体<br/>RiskModelEntity"]
    H --> I["完成清理"]

    B --> J["检查分发状态"]
    C --> K["验证策略共享"]
    D --> L["验证指标共享"]
    E --> M["跨branch引用检查"]
    F --> N["跨branch引用检查"]
```

```typescript
enum DeletionLayer {
  EXTERNAL_RELATIONS = 1, // 外部系统关系
  BUSINESS_RELATIONS = 2, // 业务逻辑关系
  CORE_RELATIONS = 3, // 核心数据关系
  STRATEGY_ENTITIES = 4, // 策略主实体
  METRIC_ENTITIES = 5, // 指标主实体
  GROUP_ENTITIES = 6, // 分组主实体
  MODEL_ENTITIES = 7, // 模型主实体
}

class LayeredDeletionOrchestrator {
  async executeLayeredDeletion(manager: EntityManager, context: DeletionContext): Promise<LayeredDeletionResult> {
    const result = new LayeredDeletionResult();

    // 按层次依次执行删除
    for (const layer of Object.values(DeletionLayer)) {
      await this.executeLayer(manager, layer, context, result);
    }

    return result;
  }

  private async executeLayer(manager: EntityManager, layer: DeletionLayer, context: DeletionContext, result: LayeredDeletionResult): Promise<void> {
    switch (layer) {
      case DeletionLayer.EXTERNAL_RELATIONS:
        result.externalRelations = await this.deleteExternalRelations(manager, context);
        break;
      case DeletionLayer.BUSINESS_RELATIONS:
        result.businessRelations = await this.deleteBusinessRelations(manager, context);
        break;
      case DeletionLayer.CORE_RELATIONS:
        result.coreRelations = await this.deleteCoreRelations(manager, context);
        break;
      // ... 其他层级处理
    }

    // 每层删除后验证完整性
    await this.validateLayerCompleteness(manager, layer, context);
  }
}
```

### 5.2 依赖关系分析算法（增强版）

```typescript
async analyzeDataDependencies(
  manager: EntityManager,
  options: CleanupOptions
): Promise<DependencyAnalysis> {
  // 1. 基础实体分析
  const orphanGroups = await this.findOrphanGroups(manager);
  const unusedMetrics = await this.findUnusedMetrics(manager);
  const unusedStrategies = await this.findUnusedStrategies(manager);

  // 2. 关系实体分析
  const relationshipBreakdown = await this.analyzeRelationshipEntities(manager, options);

  // 3. branchCode感知分析
  const branchCodeAnalysis = await this.analyzeBranchCodeDependencies(manager);

  // 4. 跨branchCode共享分析
  const crossBranchSharing = await this.analyzeCrossBranchSharing(manager);

  return {
    orphanGroups,
    unusedMetrics,
    unusedStrategies,
    relationshipBreakdown,
    branchCodeGroups: branchCodeAnalysis.branchCodeGroups,
    sharedDataMapping: branchCodeAnalysis.sharedDataMapping,
    branchCodeMetrics: branchCodeAnalysis.branchCodeMetrics,
    crossBranchSharing
  };
}

/**
 * 分析关系实体的完整情况
 */
private async analyzeRelationshipEntities(
  manager: EntityManager,
  options: CleanupOptions
): Promise<RelationshipBreakdown> {
  // 1. 统计关系实体总量
  const [totalGroupMetricRelations] = await manager.query(`
    SELECT COUNT(*) as count FROM group_metric_relation
    WHERE status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
  `);

  const [totalMetricDimensionRelations] = await manager.query(`
    SELECT COUNT(*) as count FROM metric_dimension_relation
    WHERE status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
  `);

  // 2. 找出孤立的关系实体
  const orphanGroupMetricRelations = await this.findOrphanGroupMetricRelations(manager);
  const orphanMetricDimensionRelations = await this.findOrphanMetricDimensionRelations(manager);

  // 3. 构建关系映射
  const groupsToMetricsMapping = await this.buildGroupMetricMapping(manager);
  const metricsToStrategiesMapping = await this.buildMetricStrategyMapping(manager);

  return {
    totalGroupMetricRelations: totalGroupMetricRelations.count,
    totalMetricDimensionRelations: totalMetricDimensionRelations.count,
    orphanGroupMetricRelations,
    orphanMetricDimensionRelations,
    groupsToMetricsMapping,
    metricsToStrategiesMapping
  };
}

/**
 * 找出孤立的分组-指标关联关系
 */
private async findOrphanGroupMetricRelations(manager: EntityManager): Promise<number[]> {
  const query = `
    SELECT gmr.id
    FROM group_metric_relation gmr
    LEFT JOIN \`group\` g ON gmr.group_id = g.group_id
    LEFT JOIN metrics m ON gmr.metrics_id = m.metrics_id
    WHERE g.group_id IS NULL
       OR m.metrics_id IS NULL
       OR g.status = ${DataStatusEnums.Deprecated}
       OR m.status = ${DataStatusEnums.Deprecated}
  `;

  const result = await manager.query(query);
  return result.map(row => row.id);
}

/**
 * 找出孤立的指标-维度关联关系
 */
private async findOrphanMetricDimensionRelations(manager: EntityManager): Promise<number[]> {
  const query = `
    SELECT mdr.id
    FROM metric_dimension_relation mdr
    LEFT JOIN metrics m ON mdr.metrics_id = m.metrics_id
    LEFT JOIN dimension_hit_strategy dhs ON mdr.dimension_hit_strategy_id = dhs.dimension_hit_strategy_id
    WHERE m.metrics_id IS NULL
       OR dhs.dimension_hit_strategy_id IS NULL
       OR m.status = ${DataStatusEnums.Deprecated}
       OR dhs.status = ${DataStatusEnums.Deprecated}
  `;

  const result = await manager.query(query);
  return result.map(row => row.id);
}

/**
 * 构建分组到指标的映射关系
 */
private async buildGroupMetricMapping(manager: EntityManager): Promise<Map<number, number[]>> {
  const mapping = new Map<number, number[]>();

  const query = `
    SELECT g.group_id, GROUP_CONCAT(DISTINCT gmr.metrics_id) as metrics_ids
    FROM \`group\` g
    INNER JOIN group_metric_relation gmr ON g.group_id = gmr.group_id
    WHERE g.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
      AND gmr.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
    GROUP BY g.group_id
  `;

  const result = await manager.query(query);
  result.forEach(row => {
    const metricsIds = row.metrics_ids ? row.metrics_ids.split(',').map(Number) : [];
    mapping.set(row.group_id, metricsIds);
  });

  return mapping;
}

/**
 * 构建指标到策略的映射关系
 */
private async buildMetricStrategyMapping(manager: EntityManager): Promise<Map<number, number[]>> {
  const mapping = new Map<number, number[]>();

  const query = `
    SELECT m.metrics_id, GROUP_CONCAT(DISTINCT mdr.dimension_hit_strategy_id) as strategy_ids
    FROM metrics m
    INNER JOIN metric_dimension_relation mdr ON m.metrics_id = mdr.metrics_id
    WHERE m.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
      AND mdr.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
    GROUP BY m.metrics_id
  `;

  const result = await manager.query(query);
  result.forEach(row => {
    const strategyIds = row.strategy_ids ? row.strategy_ids.split(',').map(Number) : [];
    mapping.set(row.metrics_id, strategyIds);
  });

  return mapping;
}

private async findOrphanGroups(manager: EntityManager): Promise<number[]> {
  const query = `
    SELECT g.group_id
    FROM \`group\` g
    LEFT JOIN risk_model rm ON g.model_id = rm.model_id
    WHERE rm.model_id IS NULL
      OR rm.status = ${DataStatusEnums.Deprecated}
  `;

  const result = await manager.query(query);
  return result.map(row => row.group_id);
}

private async findUnusedMetrics(manager: EntityManager): Promise<number[]> {
  // 考虑branchCode共享的复杂查询
  const query = `
    SELECT DISTINCT m.metrics_id
    FROM metrics m
    WHERE m.metrics_id NOT IN (
      SELECT DISTINCT gmr.metrics_id
      FROM group_metric_relation gmr
      INNER JOIN \`group\` g ON gmr.group_id = g.group_id
      INNER JOIN risk_model rm ON g.model_id = rm.model_id
      WHERE rm.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
        AND g.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
        AND gmr.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
    )
    AND m.status != ${DataStatusEnums.Deprecated}
  `;

  const result = await manager.query(query);
  return result.map(row => row.metrics_id);
}
```

### 5.3 清理执行算法

```typescript
async cleanupGroups(
  manager: EntityManager,
  orphanGroupIds: number[]
): Promise<CleanupItemResult[]> {
  const results: CleanupItemResult[] = [];

  await Bluebird.map(orphanGroupIds, async (groupId) => {
    try {
      await manager.transaction(async (transactionManager) => {
        // 1. 清理分组-指标关联
        await transactionManager.delete(GroupMetricRelationEntity, { groupId });

        // 2. 清理分组本身
        const deleteResult = await transactionManager.delete(GroupEntity, { groupId });

        results.push({
          id: groupId,
          type: 'group',
          status: 'success',
          affectedRows: deleteResult.affected || 0
        });
      });
    } catch (error) {
      results.push({
        id: groupId,
        type: 'group',
        status: 'error',
        error: error.message
      });
    }
  }, { concurrency: 5 });

  return results;
}

async cleanupMetrics(
  manager: EntityManager,
  unusedMetricIds: number[]
): Promise<CleanupItemResult[]> {
  const results: CleanupItemResult[] = [];

  await Bluebird.map(unusedMetricIds, async (metricId) => {
    try {
      // 二次验证：确保指标确实未被使用
      const isStillInUse = await this.verifyMetricNotInUse(manager, metricId);
      if (isStillInUse) {
        results.push({
          id: metricId,
          type: 'metric',
          status: 'skipped',
          reason: 'Still in use after verification'
        });
        return;
      }

      await manager.transaction(async (transactionManager) => {
        // 1. 清理指标-维度关联
        await transactionManager.delete(MetricDimensionRelationEntity, {
          metricsId: metricId
        });

        // 2. 清理分组-指标关联
        await transactionManager.delete(GroupMetricRelationEntity, {
          metricsId: metricId
        });

        // 3. 清理指标本身
        const deleteResult = await transactionManager.delete(MetricsEntity, {
          metricsId: metricId
        });

        results.push({
          id: metricId,
          type: 'metric',
          status: 'success',
          affectedRows: deleteResult.affected || 0
        });
      });
    } catch (error) {
      results.push({
        id: metricId,
        type: 'metric',
        status: 'error',
        error: error.message
      });
    }
  }, { concurrency: 3 });

  return results;
}
```

## 6. 安全机制实现

### 6.1 安全检查

```typescript
async performSafetyChecks(
  options: CleanupOptions,
  analysis: DependencyAnalysis
): Promise<SafetyCheckResult> {
  const checks: SafetyCheckResult = {
    passed: true,
    warnings: [],
    errors: []
  };

  // 1. 检查清理范围
  const totalEntities = analysis.orphanGroups.length +
                       analysis.unusedMetrics.length +
                       analysis.unusedStrategies.length;

  if (totalEntities > 1000 && !options.dryRun) {
    checks.errors.push('清理范围过大，建议先执行试运行模式');
    checks.passed = false;
  }

  // 2. 检查已发布模型影响
  const affectedPublishedModels = await this.checkPublishedModelsAffected(analysis);
  if (affectedPublishedModels.length > 0) {
    checks.warnings.push(
      `发现 ${affectedPublishedModels.length} 个已发布模型可能受影响`
    );
  }

  return checks;
}
```

### 6.2 缓存清理

```typescript
async clearRelatedCache(cleanupResult: CleanupResult): Promise<void> {
  const redis = this.redisService.getClient();

  // 1. 清理模型缓存
  const modelIds = await this.getAffectedModelIds(cleanupResult);
  await Bluebird.map(modelIds, async (modelId) => {
    await deleteRiskModelCache(modelId, redis);
  });

  // 2. 清理维度策略缓存
  const cachePattern = 'dimension:strategy:*';
  const keys = await redis.keys(cachePattern);
  if (keys.length > 0) {
    await redis.del(...keys);
  }
}
```

## 7. API 接口设计

### 7.1 控制器实现

```typescript
@Controller('risk-model/cleanup')
@ApiTags('风险模型清理')
export class RiskModelCleanupController {
  constructor(private readonly cleanupService: RiskModelCleanupService) {}

  @Post('analyze')
  @ApiOperation({ summary: '分析清理范围' })
  async analyzeCleanupScope(@Body() options: CleanupOptions): Promise<DependencyAnalysis> {
    return await this.cleanupService.analyzeDataDependencies(options);
  }

  @Post('execute')
  @ApiOperation({ summary: '执行清理操作' })
  async executeCleanup(@Body() options: CleanupOptions, @Req() req: any): Promise<CleanupResult> {
    const user = getUserFromRequest(req);
    return await this.cleanupService.cleanupUnusedEntities(options, user);
  }
}
```

## 8. 测试实现

### 8.1 单元测试

```typescript
describe('RiskModelCleanupService', () => {
  let service: RiskModelCleanupService;
  let mockEntityManager: jest.Mocked<EntityManager>;

  beforeEach(async () => {
    // 测试环境初始化
  });

  it('应该正确分析数据依赖关系', async () => {
    // Arrange
    const options: CleanupOptions = { dryRun: true };

    // Act
    const result = await service.analyzeDataDependencies(mockEntityManager, options);

    // Assert
    expect(result.orphanGroups).toBeDefined();
    expect(result.unusedMetrics).toBeDefined();
    expect(result.unusedStrategies).toBeDefined();
  });

  it('应该安全清理孤立分组', async () => {
    // 测试清理逻辑
  });
});
```

## 9. 性能优化与监控

### 9.1 性能优化策略

#### 9.1.1 批量处理优化

```typescript
class OptimizedBatchProcessor {
  private readonly DEFAULT_BATCH_SIZE = 50;
  private readonly MAX_CONCURRENT_BATCHES = 3;

  async processBatchDeletion<T>(
    items: T[],
    processor: (batch: T[]) => Promise<CleanupItemResult[]>,
    options: BatchProcessOptions = {},
  ): Promise<CleanupItemResult[]> {
    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    const concurrency = options.concurrency || this.MAX_CONCURRENT_BATCHES;

    const batches = this.chunkArray(items, batchSize);
    const results: CleanupItemResult[] = [];

    // 分批并发处理
    await Bluebird.map(
      batches,
      async (batch) => {
        const batchResults = await processor(batch);
        results.push(...batchResults);
      },
      { concurrency },
    );

    return results;
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}
```

#### 9.1.2 索引优化建议

```sql
-- 为清理操作优化的索引
CREATE INDEX idx_risk_model_branch_status ON risk_model(branch_code, status);
CREATE INDEX idx_group_model_status ON `group`(model_id, status);
CREATE INDEX idx_group_metric_relation_composite ON group_metric_relation(group_id, metrics_id, status);
CREATE INDEX idx_metric_dimension_relation_composite ON metric_dimension_relation(metrics_id, dimension_hit_strategy_id, status);

-- 临时索引（清理完成后可删除）
CREATE INDEX idx_temp_cleanup_metrics ON metrics(status, created_at);
CREATE INDEX idx_temp_cleanup_strategies ON dimension_hit_strategy(status, created_at);
```

#### 9.1.3 查询优化

```typescript
/**
 * 使用优化的SQL查询，减少N+1问题
 */
async findUnusedMetricsOptimized(manager: EntityManager): Promise<number[]> {
  // 使用EXISTS子查询替代JOIN，提高性能
  const query = `
    SELECT m.metrics_id
    FROM metrics m
    WHERE m.status != ${DataStatusEnums.Deprecated}
      AND NOT EXISTS (
        SELECT 1 FROM group_metric_relation gmr
        INNER JOIN \`group\` g ON gmr.group_id = g.group_id
        INNER JOIN risk_model rm ON g.model_id = rm.model_id
        WHERE gmr.metrics_id = m.metrics_id
          AND rm.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
          AND g.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
          AND gmr.status IN (${DataStatusEnums.Enabled}, ${DataStatusEnums.Developing})
      )
    LIMIT 10000  -- 防止一次性处理过多数据
  `;

  const result = await manager.query(query);
  return result.map(row => row.metrics_id);
}
```

### 9.2 监控与告警

#### 9.2.1 清理过程监控

```typescript
interface CleanupMetrics {
  startTime: Date;
  endTime?: Date;
  duration?: number;
  processedEntities: {
    models: number;
    groups: number;
    metrics: number;
    strategies: number;
    relations: number;
  };
  errorCount: number;
  warnings: string[];
  performanceMetrics: {
    avgBatchProcessTime: number;
    memoryUsage: number;
    dbConnectionsUsed: number;
  };
}

class CleanupMonitor {
  private metrics: CleanupMetrics;
  private logger: Logger;

  async trackCleanupOperation<T>(operation: () => Promise<T>, context: string): Promise<T> {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    try {
      this.logger.info(`开始执行清理操作: ${context}`);
      const result = await operation();

      const endTime = Date.now();
      const endMemory = process.memoryUsage();

      this.recordSuccess(context, startTime, endTime, startMemory, endMemory);
      return result;
    } catch (error) {
      this.recordError(context, error);
      throw error;
    }
  }

  private recordSuccess(context: string, startTime: number, endTime: number, startMemory: NodeJS.MemoryUsage, endMemory: NodeJS.MemoryUsage): void {
    const duration = endTime - startTime;
    const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;

    this.logger.info(`清理操作完成: ${context}`, {
      duration,
      memoryDelta,
      heapUsed: endMemory.heapUsed,
    });

    // 发送监控指标到监控系统
    this.sendMetricsToMonitoring({
      operation: context,
      duration,
      memoryDelta,
      success: true,
    });
  }
}
```

#### 9.2.2 告警规则

```typescript
class CleanupAlertManager {
  // 告警阈值配置
  private readonly ALERT_THRESHOLDS = {
    MAX_DELETION_COUNT: 1000, // 单次删除实体数量上限
    MAX_DURATION_MINUTES: 30, // 最大执行时间
    MAX_ERROR_RATE: 0.05, // 最大错误率 5%
    MAX_MEMORY_USAGE_MB: 512, // 最大内存使用
    MIN_FREE_DISK_SPACE_GB: 10, // 最小磁盘剩余空间
  };

  async checkAlertConditions(result: CleanupResult): Promise<Alert[]> {
    const alerts: Alert[] = [];

    // 检查删除数量
    const totalDeleted = this.calculateTotalDeleted(result);
    if (totalDeleted > this.ALERT_THRESHOLDS.MAX_DELETION_COUNT) {
      alerts.push({
        level: AlertLevel.WARNING,
        message: `删除数量过多: ${totalDeleted}`,
        recommendation: '建议分批次执行清理',
      });
    }

    // 检查执行时间
    if (result.duration > this.ALERT_THRESHOLDS.MAX_DURATION_MINUTES * 60 * 1000) {
      alerts.push({
        level: AlertLevel.WARNING,
        message: `执行时间过长: ${result.duration}ms`,
        recommendation: '考虑优化清理策略或增加系统资源',
      });
    }

    return alerts;
  }
}
```

## 10. 部署和运维

### 10.1 执行脚本

```bash
#!/bin/bash
# cleanup-execution.sh

# 数据备份
mysqldump -h ${DB_HOST} -u ${DB_USER} -p${DB_PASS} ${DB_NAME} > backup_$(date +%Y%m%d_%H%M%S).sql

# 执行试运行
curl -X POST "${API_BASE_URL}/risk-model/cleanup/execute" \
  -H "Content-Type: application/json" \
  -d '{"dryRun": true, "batchSize": 100}'

# 等待确认后执行实际清理
read -p "继续执行实际清理？(y/N): " confirm
if [[ $confirm == [yY] ]]; then
  curl -X POST "${API_BASE_URL}/risk-model/cleanup/execute" \
    -H "Content-Type: application/json" \
    -d '{"dryRun": false, "batchSize": 50}'
fi
```

### 10.2 执行检查清单

#### 10.2.1 执行前检查 (Pre-execution Checklist)

```typescript
interface PreExecutionChecklist {
  // 环境检查
  environmentChecks: {
    databaseBackupCompleted: boolean; // ✅ 数据库备份完成
    testEnvironmentValidated: boolean; // ✅ 测试环境验证通过
    productionReadinessConfirmed: boolean; // ✅ 生产环境就绪确认
  };

  // 权限检查
  permissionChecks: {
    userHasDeletePermission: boolean; // ✅ 用户具有删除权限
    resourceAccessValidated: boolean; // ✅ 资源访问权限验证
    auditLogEnabled: boolean; // ✅ 审计日志启用
  };

  // 系统资源检查
  systemResourceChecks: {
    diskSpaceSufficient: boolean; // ✅ 磁盘空间充足
    memoryAvailable: boolean; // ✅ 内存可用
    databaseConnectionsAvailable: boolean; // ✅ 数据库连接可用
  };

  // 业务影响检查
  businessImpactChecks: {
    noActiveUsersAffected: boolean; // ✅ 无活跃用户受影响
    maintenanceWindowScheduled: boolean; // ✅ 维护窗口已安排
    rollbackPlanPrepared: boolean; // ✅ 回滚方案已准备
  };
}
```

#### 10.2.2 执行后验证 (Post-execution Validation)

```typescript
interface PostExecutionValidation {
  // 数据完整性验证
  dataIntegrityChecks: {
    foreignKeyConstraintsValid: boolean; // ✅ 外键约束有效
    noOrphanedRecordsRemain: boolean; // ✅ 无遗留孤立记录
    relationshipConsistencyMaintained: boolean; // ✅ 关系一致性保持
  };

  // 业务功能验证
  businessFunctionChecks: {
    riskModelCalculationWorking: boolean; // ✅ 风险模型计算正常
    userInterfaceResponsive: boolean; // ✅ 用户界面响应正常
    reportGenerationSuccessful: boolean; // ✅ 报告生成成功
  };

  // 性能验证
  performanceChecks: {
    queryPerformanceAcceptable: boolean; // ✅ 查询性能可接受
    cacheHitRateNormal: boolean; // ✅ 缓存命中率正常
    memoryUsageStable: boolean; // ✅ 内存使用稳定
  };
}
```

#### 10.2.3 回滚预案

```typescript
class RollbackManager {
  async executeRollback(backupInfo: BackupInfo): Promise<RollbackResult> {
    try {
      // 1. 停止相关服务
      await this.stopRelatedServices();

      // 2. 恢复数据库备份
      await this.restoreDatabase(backupInfo);

      // 3. 清理缓存
      await this.clearAllCaches();

      // 4. 重启服务
      await this.restartServices();

      // 5. 验证回滚成功
      const validation = await this.validateRollback();

      return {
        success: true,
        restoredTables: backupInfo.affectedTables,
        validationResults: validation,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        partialRollbackCompleted: true,
        manualInterventionRequired: true,
      };
    }
  }
}
```

### 10.3 故障排查指南

#### 10.3.1 常见问题及解决方案

| 问题类型     | 症状                                   | 可能原因             | 解决方案                       |
| ------------ | -------------------------------------- | -------------------- | ------------------------------ |
| 外键约束违反 | `Cannot delete or update a parent row` | 删除顺序错误         | 检查删除顺序，先删除关系实体   |
| 超时错误     | `Lock wait timeout exceeded`           | 长事务锁表           | 减小批处理大小，优化查询       |
| 内存不足     | `Out of memory`                        | 一次性处理数据量过大 | 降低并发度，增加分批处理       |
| 共享数据误删 | 其他 branchCode 功能异常               | 共享数据识别不准确   | 使用备份恢复，改进共享分析逻辑 |

#### 10.3.2 监控指标异常处理

```typescript
interface MonitoringAlertHandler {
  async handlePerformanceDegradation(alert: PerformanceAlert): Promise<void> {
    switch (alert.type) {
      case 'HIGH_MEMORY_USAGE':
        await this.reduceProcessingConcurrency();
        await this.clearNonEssentialCaches();
        break;

      case 'SLOW_QUERY_DETECTED':
        await this.enableQueryLogging();
        await this.analyzeQueryExecution();
        break;

      case 'HIGH_ERROR_RATE':
        await this.pauseCleanupOperation();
        await this.triggerIncidentResponse();
        break;
    }
  }
}
```

---

## 总结

本技术实现指南基于风险模型系统的核心设计理念，提供了完整的数据清理解决方案。关键特点包括：

### 🎯 核心优势

1. **设计理念对接**：严格遵循 branchCode 分组机制和受控复用架构
2. **关系实体感知**：精确处理复杂的实体关系网络
3. **层次化清理**：按照依赖关系分层执行，确保数据完整性
4. **共享数据保护**：智能识别跨 branchCode 共享资源，防止误删
5. **性能优化**：批量处理、索引优化、查询优化等多重保障
6. **监控告警**：全程监控清理过程，及时发现和处理异常

### ⚠️ 重要提醒

1. **执行前务必做好数据备份**
2. **建议先在测试环境验证**
3. **生产环境建议分批执行**
4. **密切监控系统性能和数据一致性**
5. **准备好回滚预案以应对突发情况**

## 4. 暂存表实体实现

### 4.1 DataCleanupStagingEntity

```typescript
// src/modules/data-processing/data-cleaner/risk-model/entities/DataCleanupStagingEntity.ts
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DataCleanupStagingStatus } from '../enums/DataCleanupStagingStatus';
import { CleanupAnalysisResult } from '../interfaces/analysis-result.interface';

@Entity('data_cleanup_staging')
export class DataCleanupStagingEntity {
  @PrimaryGeneratedColumn()
  id: number;

  // 清理目标标识
  @Column({
    name: 'target_type',
    type: 'varchar',
    length: 50,
    comment: '清理目标类型: branchCode|riskModelId',
  })
  targetType: 'branchCode' | 'riskModelId';

  @Column({
    name: 'target_value',
    type: 'varchar',
    length: 200,
    comment: '清理目标值',
  })
  targetValue: string;

  @Column({
    name: 'risk_model_id',
    type: 'int',
    nullable: true,
    comment: '关联的风险模型ID',
  })
  riskModelId?: number;

  @Column({
    name: 'branch_code',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '关联的branchCode',
  })
  branchCode?: string;

  // 分析结果
  @Column({
    name: 'analysis_result',
    type: 'json',
    comment: '清理分析结果详情',
  })
  analysisResult: CleanupAnalysisResult;

  // 状态管理
  @Column({
    name: 'staging_status',
    type: 'tinyint',
    default: DataCleanupStagingStatus.Analyzed,
    comment: '暂存状态: 0-已分析, 1-已废弃, 2-已删除',
  })
  stagingStatus: DataCleanupStagingStatus;

  @Column({
    name: 'deprecated_date',
    type: 'datetime',
    nullable: true,
    comment: '标记废弃时间',
  })
  deprecatedDate?: Date;

  @Column({
    name: 'deleted_date',
    type: 'datetime',
    nullable: true,
    comment: '彻底删除时间',
  })
  deletedDate?: Date;

  @Column({
    name: 'rollback_date',
    type: 'datetime',
    nullable: true,
    comment: '撤回操作时间',
  })
  rollbackDate?: Date;

  @Column({
    name: 'rollback_count',
    type: 'int',
    default: 0,
    comment: '撤回操作次数',
  })
  rollbackCount: number;

  // 审计字段
  @Column({
    name: 'create_date',
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createDate: Date;

  @Column({
    name: 'update_date',
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updateDate: Date;

  @Column({
    name: 'created_by',
    type: 'int',
    comment: '创建人ID',
  })
  createdBy: number;

  @Column({
    name: 'last_operated_by',
    type: 'int',
    nullable: true,
    comment: '最后操作人ID',
  })
  lastOperatedBy?: number;
}
```

## 5. 核心服务实现

### 5.1 SafetyCheckerService - 安全检查服务

```typescript
// src/modules/data-processing/data-cleaner/risk-model/services/SafetyCheckerService.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager, In } from 'typeorm';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DistributedSystemResourceEntity } from '@domain/entities/DistributedSystemResourceEntity';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';
import { CleanupTriggerType } from '../enums/CleanupTriggerType';
import { CleanupEnvironmentConfig, BranchCodeCleanupResult, MetricCleanupResult } from '../interfaces/cleanup-config.interface';

@Injectable()
export class SafetyCheckerService {
  private readonly logger = new Logger(SafetyCheckerService.name);

  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
  ) {}

  /**
   * 统一的模型安全检查
   */
  async isModelSafeToOperate(modelId: number, branchCode: string, environment: CleanupEnvironmentConfig): Promise<boolean> {
    // 1. 获取模型详细信息
    const model = await this.entityManager.findOne(RiskModelEntity, {
      where: { modelId },
      select: ['modelId', 'status', 'branchCode', 'modelName'],
    });

    if (!model) {
      this.logger.warn(`模型不存在: modelId=${modelId}`);
      return false;
    }

    // 2. 检查模型的组织分配关系
    const distributedOrgs = await this.findOrganizationsUsingBranchCode(branchCode);

    // 3. 核心安全判断：生产非测试组织 + 已发布状态 = 不能清理
    if (model.status === DataStatusEnums.Enabled) {
      if (environment.environment === 'production') {
        // 生产环境：检查是否只分配给测试组织
        const nonTestOrgs = distributedOrgs.filter((orgId) => !environment.testOrganizations.includes(orgId));
        if (nonTestOrgs.length > 0) {
          this.logger.warn(`生产非测试组织且已发布，不能清理: modelId=${modelId}, nonTestOrgs=${nonTestOrgs}`);
          return false; // 生产非测试组织且已发布 - 绝对不能清理
        }
      }
    }

    // 4. 明确的保护点：即使是Developing/Deprecated状态，如果是生产非测试组织，也暂不允许清理
    if (environment.environment === 'production') {
      const nonTestOrgs = distributedOrgs.filter((orgId) => !environment.testOrganizations.includes(orgId));
      if (nonTestOrgs.length > 0) {
        this.logger.warn(`生产非测试组织数据暂时保护: modelId=${modelId}, status=${model.status}, nonTestOrgs=${nonTestOrgs}`);
        return false; // 暂时保护所有生产非测试组织的数据
      }
    }

    return true; // 其他情况允许清理
  }

  /**
   * branchCode 清理安全检查（含跨branchCode分享检查）
   */
  async isBranchCodeSafeToClean(branchCode: string, environment: CleanupEnvironmentConfig): Promise<BranchCodeCleanupResult> {
    // 1. 检查 branchCode 是否被分配给任何组织
    const distributedOrgs = await this.findOrganizationsUsingBranchCode(branchCode);

    // 2. 组织分配安全检查
    let canCleanByOrgCheck = false;
    if (distributedOrgs.length === 0) {
      canCleanByOrgCheck = true; // 未分配给任何组织
    } else if (environment.environment === 'test') {
      canCleanByOrgCheck = true; // 测试环境可以清理所有 branchCode
    } else if (environment.environment === 'production') {
      const nonTestOrgs = distributedOrgs.filter((orgId) => !environment.testOrganizations.includes(orgId));
      canCleanByOrgCheck = nonTestOrgs.length === 0; // 只分配给测试组织
    }

    if (!canCleanByOrgCheck) {
      return {
        canClean: false,
        canFullClean: false,
        reason: 'branchCode被生产组织使用',
        sharedMetrics: [],
        sharedStrategies: [],
      };
    }

    // 3. 检查跨branchCode分享情况
    const sharedMetrics = await this.findCrossBranchCodeSharedMetrics(branchCode);
    const sharedStrategies = await this.findCrossBranchCodeSharedStrategies(branchCode);

    return {
      canClean: true,
      canFullClean: sharedMetrics.length === 0 && sharedStrategies.length === 0,
      reason: sharedMetrics.length > 0 || sharedStrategies.length > 0 ? '存在跨branchCode分享资源' : '可以完全清理',
      sharedMetrics,
      sharedStrategies,
    };
  }

  /**
   * 指标清理检查（基于清理触发方式和统一安全判断）
   */
  async isMetricSafeToClean(
    metricId: number,
    targetBranchCode: string,
    environment: CleanupEnvironmentConfig,
    cleanupTrigger: CleanupTriggerType,
    targetModelId?: number,
  ): Promise<MetricCleanupResult> {
    // 1. 查找所有使用该指标的模型
    const referencingModels = await this.findModelsUsingMetric(metricId);

    // 2. 检查是否有跨 branchCode 引用
    const crossBranchReferences = referencingModels.filter((model) => model.branchCode !== targetBranchCode);

    if (crossBranchReferences.length > 0) {
      return {
        canDeleteMetric: false,
        reason: cleanupTrigger === CleanupTriggerType.RISK_MODEL_ID ? '有跨branchCode引用，只能删除指定模型的关联关系' : '有跨branchCode引用，不能删除',
        crossBranchReferences,
      };
    }

    // 3. 根据清理触发方式采用不同策略
    if (cleanupTrigger === CleanupTriggerType.BRANCH_CODE) {
      // branchCode级别：如果branchCode可以删除，指标可以完全删除
      const branchCodeCleanupResult = await this.isBranchCodeSafeToClean(targetBranchCode, environment);
      return {
        canDeleteMetric: branchCodeCleanupResult.canClean,
        reason: branchCodeCleanupResult.reason,
        crossBranchReferences: [],
      };
    } else {
      // riskModelId级别：检查是否有其他模型在使用
      const otherReferences = referencingModels.filter((model) => model.branchCode === targetBranchCode && model.modelId !== targetModelId);

      if (otherReferences.length > 0) {
        return {
          canDeleteMetric: false,
          reason: '有其他模型引用，只能删除指定模型的关联关系',
          crossBranchReferences: [],
        };
      } else {
        return {
          canDeleteMetric: true,
          reason: '无其他引用，可以完全删除指标',
          crossBranchReferences: [],
        };
      }
    }
  }

  // === 私有辅助方法 ===

  /**
   * 查找使用 branchCode 的组织
   */
  private async findOrganizationsUsingBranchCode(branchCode: string): Promise<number[]> {
    const distributions = await this.entityManager.find(DistributedSystemResourceEntity, {
      where: {
        branchCode: branchCode,
        distributeStatus: 1, // 有效分发
      },
      select: ['orgId'],
    });

    return distributions.map((d) => d.orgId);
  }

  /**
   * 查找跨branchCode分享的指标
   */
  private async findCrossBranchCodeSharedMetrics(branchCode: string): Promise<number[]> {
    const query = `
      SELECT DISTINCT m.metrics_id
      FROM metrics m
      JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
      JOIN \`group\` g ON gmr.group_id = g.group_id
      JOIN risk_model rm ON g.model_id = rm.model_id
      WHERE rm.branch_code != ? 
        AND m.metrics_id IN (
          SELECT DISTINCT m2.metrics_id
          FROM metrics m2
          JOIN group_metric_relation gmr2 ON m2.metrics_id = gmr2.metrics_id
          JOIN \`group\` g2 ON gmr2.group_id = g2.group_id
          JOIN risk_model rm2 ON g2.model_id = rm2.model_id
          WHERE rm2.branch_code = ?
        )
    `;

    const result = await this.entityManager.query(query, [branchCode, branchCode]);
    return result.map((row) => row.metrics_id);
  }

  /**
   * 查找跨branchCode分享的策略
   */
  private async findCrossBranchCodeSharedStrategies(branchCode: string): Promise<number[]> {
    const query = `
      SELECT DISTINCT dhs.strategy_id
      FROM dimension_hit_strategy dhs
      JOIN metric_dimension_relation mdr ON dhs.strategy_id = mdr.dimension_strategy_id
      JOIN metrics m ON mdr.metrics_id = m.metrics_id
      JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
      JOIN \`group\` g ON gmr.group_id = g.group_id
      JOIN risk_model rm ON g.model_id = rm.model_id
      WHERE rm.branch_code != ?
        AND dhs.strategy_id IN (
          SELECT DISTINCT dhs2.strategy_id
          FROM dimension_hit_strategy dhs2
          JOIN metric_dimension_relation mdr2 ON dhs2.strategy_id = mdr2.dimension_strategy_id
          JOIN metrics m2 ON mdr2.metrics_id = m2.metrics_id
          JOIN group_metric_relation gmr2 ON m2.metrics_id = gmr2.metrics_id
          JOIN \`group\` g2 ON gmr2.group_id = g2.group_id
          JOIN risk_model rm2 ON g2.model_id = rm2.model_id
          WHERE rm2.branch_code = ?
        )
    `;

    const result = await this.entityManager.query(query, [branchCode, branchCode]);
    return result.map((row) => row.strategy_id);
  }

  /**
   * 查找使用指标的所有模型
   */
  private async findModelsUsingMetric(metricId: number): Promise<
    Array<{
      modelId: number;
      branchCode: string;
      modelName: string;
      status: DataStatusEnums;
    }>
  > {
    const query = `
      SELECT DISTINCT rm.model_id, rm.branch_code, rm.model_name, rm.status
      FROM risk_model rm
      JOIN \`group\` g ON rm.model_id = g.model_id
      JOIN group_metric_relation gmr ON g.group_id = gmr.group_id
      WHERE gmr.metrics_id = ?
        AND rm.status IN (?, ?, ?)
    `;

    const result = await this.entityManager.query(query, [metricId, DataStatusEnums.Enabled, DataStatusEnums.Developing, DataStatusEnums.Deprecated]);

    return result.map((row) => ({
      modelId: row.model_id,
      branchCode: row.branch_code,
      modelName: row.model_name,
      status: row.status,
    }));
  }
}
```

### 5.2 DataCleanupStagingService - 暂存机制核心服务

```typescript
// src/modules/data-processing/data-cleaner/risk-model/services/DataCleanupStagingService.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository, In, LessThan, MoreThan } from 'typeorm';
import * as moment from 'moment';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { GroupEntity } from '@domain/entities/GroupEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';
import { DataCleanupStagingEntity } from '../entities/DataCleanupStagingEntity';
import { DataCleanupStagingStatus } from '../enums/DataCleanupStagingStatus';
import { CleanupTriggerType } from '../enums/CleanupTriggerType';
import { SafetyCheckerService } from './SafetyCheckerService';
import { DependencyAnalyzerService } from './DependencyAnalyzerService';
import { CleanupEnvironmentConfig, QuickRollbackOptions, QuickRollbackResult } from '../interfaces/cleanup-config.interface';
import { CleanupAnalysisResult } from '../interfaces/analysis-result.interface';

@Injectable()
export class DataCleanupStagingService {
  private readonly logger = new Logger(DataCleanupStagingService.name);

  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    @InjectRepository(DataCleanupStagingEntity)
    private readonly stagingRepo: Repository<DataCleanupStagingEntity>,
    private readonly safetyChecker: SafetyCheckerService,
    private readonly dependencyAnalyzer: DependencyAnalyzerService,
  ) {}

  /**
   * 分析并暂存清理计划
   */
  async analyzeAndStage(
    targetType: 'branchCode' | 'riskModelId',
    targetValue: string,
    operatorId: number,
    environment: CleanupEnvironmentConfig,
  ): Promise<DataCleanupStagingEntity> {
    // 1. 执行依赖分析（根据清理类型采用不同策略）
    let analysisResult: CleanupAnalysisResult;

    if (targetType === 'branchCode') {
      // branchCode 级别：基于组织分配关系分析
      analysisResult = await this.analyzeBranchCodeCleanup(targetValue, environment);
    } else {
      // riskModelId 级别：基于状态和依赖关系分析
      analysisResult = await this.analyzeRiskModelCleanup(parseInt(targetValue), environment);
    }

    // 2. 保存到暂存表
    const stagingEntity = new DataCleanupStagingEntity();
    stagingEntity.targetType = targetType;
    stagingEntity.targetValue = targetValue;
    stagingEntity.analysisResult = analysisResult;
    stagingEntity.stagingStatus = DataCleanupStagingStatus.Analyzed;
    stagingEntity.createdBy = operatorId;

    if (targetType === 'riskModelId') {
      stagingEntity.riskModelId = parseInt(targetValue);
      // 获取模型的branchCode
      const model = await this.findRiskModelById(parseInt(targetValue));
      if (model) {
        stagingEntity.branchCode = model.branchCode;
      }
    } else {
      stagingEntity.branchCode = targetValue;
    }

    return await this.stagingRepo.save(stagingEntity);
  }

  /**
   * 执行废弃操作
   */
  async executeDeprecation(stagingId: number, operatorId: number): Promise<void> {
    const staging = await this.stagingRepo.findOne({ where: { id: stagingId } });
    if (!staging || staging.stagingStatus !== DataCleanupStagingStatus.Analyzed) {
      throw new Error('暂存记录状态不正确');
    }

    await this.entityManager.transaction(async (manager) => {
      // 1. 备份当前状态（如果还没有备份）
      if (!staging.analysisResult.originalStatusBackup || Object.keys(staging.analysisResult.originalStatusBackup).length === 0) {
        staging.analysisResult.originalStatusBackup = await this.backupOriginalStatus(manager, staging.analysisResult);
        await manager.update(DataCleanupStagingEntity, stagingId, {
          analysisResult: staging.analysisResult,
        });
      }

      // 2. 标记实体为废弃状态
      await this.markEntitiesAsDeprecated(manager, staging.analysisResult);

      // 3. 更新暂存状态
      await manager.update(DataCleanupStagingEntity, stagingId, {
        stagingStatus: DataCleanupStagingStatus.Deprecated,
        deprecatedDate: new Date(),
        lastOperatedBy: operatorId,
      });

      this.logger.info(`数据清理废弃操作完成: stagingId=${stagingId}, operator=${operatorId}`);
    });
  }

  /**
   * 撤回废弃操作，恢复到原始状态
   */
  async rollbackDeprecation(stagingId: number, operatorId: number): Promise<void> {
    const staging = await this.stagingRepo.findOne({ where: { id: stagingId } });
    if (!staging || staging.stagingStatus !== DataCleanupStagingStatus.Deprecated) {
      throw new Error('暂存记录状态不正确，只能撤回已废弃状态的记录');
    }

    if (!staging.analysisResult.originalStatusBackup) {
      throw new Error('没有找到原始状态备份，无法执行撤回操作');
    }

    await this.entityManager.transaction(async (manager) => {
      // 1. 恢复实体到原始状态
      await this.restoreOriginalStatus(manager, staging.analysisResult.originalStatusBackup);

      // 2. 更新暂存状态回到已分析状态，记录撤回信息
      await manager.update(DataCleanupStagingEntity, stagingId, {
        stagingStatus: DataCleanupStagingStatus.Analyzed,
        deprecatedDate: null,
        rollbackDate: new Date(),
        rollbackCount: staging.rollbackCount + 1,
        lastOperatedBy: operatorId,
      });

      // 3. 记录撤回操作日志
      this.logger.info(`数据清理撤回操作完成: stagingId=${stagingId}, operator=${operatorId}, rollbackCount=${staging.rollbackCount + 1}`);
    });
  }

  /**
   * 快速批量回退已废弃的分析结果（独立方法）
   * 用于紧急情况下快速恢复多个已标记废弃的记录
   */
  async quickRollbackDeprecatedAnalysis(options: QuickRollbackOptions): Promise<QuickRollbackResult> {
    const { targetType, branchCode, createdAfter, operatorId, dryRun = false } = options;

    // 1. 构建查询条件
    const whereConditions: any = {
      stagingStatus: DataCleanupStagingStatus.Deprecated,
    };

    if (targetType) {
      whereConditions.targetType = targetType;
    }

    if (branchCode) {
      whereConditions.branchCode = branchCode;
    }

    if (createdAfter) {
      whereConditions.createDate = MoreThan(createdAfter);
    }

    // 2. 查找符合条件的已废弃记录
    const deprecatedRecords = await this.stagingRepo.find({
      where: whereConditions,
      order: { createDate: 'DESC' },
    });

    const rollbackDetails: QuickRollbackResult['rollbackDetails'] = [];

    // 3. 如果是模拟运行，直接返回要处理的记录信息
    if (dryRun) {
      return {
        affectedRecords: deprecatedRecords.length,
        rollbackDetails: deprecatedRecords.map((record) => ({
          stagingId: record.id,
          targetType: record.targetType,
          targetValue: record.targetValue,
          status: 'success' as const,
        })),
      };
    }

    // 4. 执行实际回退操作
    for (const record of deprecatedRecords) {
      try {
        await this.entityManager.transaction(async (manager) => {
          // 检查状态备份完整性
          if (!record.analysisResult.originalStatusBackup) {
            throw new Error(`暂存记录 ${record.id} 缺少状态备份数据`);
          }

          // 恢复实体状态
          await this.restoreOriginalStatus(manager, record.analysisResult.originalStatusBackup);

          // 更新暂存记录状态
          await manager.update(DataCleanupStagingEntity, record.id, {
            stagingStatus: DataCleanupStagingStatus.Analyzed,
            deprecatedDate: null,
            rollbackDate: new Date(),
            rollbackCount: record.rollbackCount + 1,
            lastOperatedBy: operatorId,
          });
        });

        rollbackDetails.push({
          stagingId: record.id,
          targetType: record.targetType,
          targetValue: record.targetValue,
          status: 'success',
        });

        this.logger.info(`快速回退完成: stagingId=${record.id}, targetType=${record.targetType}, targetValue=${record.targetValue}`);
      } catch (error) {
        rollbackDetails.push({
          stagingId: record.id,
          targetType: record.targetType,
          targetValue: record.targetValue,
          status: 'error',
          error: error.message,
        });

        this.logger.error(`快速回退失败: stagingId=${record.id}, error=${error.message}`);
      }
    }

    return {
      affectedRecords: rollbackDetails.filter((d) => d.status === 'success').length,
      rollbackDetails,
    };
  }

  /**
   * 定期扫描并清理废弃超过1个月的数据
   */
  async scanAndCleanExpiredData(): Promise<{
    successCount: number;
    errorCount: number;
    processedRecords: Array<{
      stagingId: number;
      targetType: string;
      targetValue: string;
      status: 'success' | 'error';
      error?: string;
    }>;
  }> {
    const oneMonthAgo = moment().subtract(1, 'month').toDate();

    // 查找废弃超过1个月的记录
    const expiredStagings = await this.stagingRepo.find({
      where: {
        stagingStatus: DataCleanupStagingStatus.Deprecated,
        deprecatedDate: LessThan(oneMonthAgo),
      },
    });

    const processedRecords: Array<{
      stagingId: number;
      targetType: string;
      targetValue: string;
      status: 'success' | 'error';
      error?: string;
    }> = [];

    let successCount = 0;
    let errorCount = 0;

    for (const staging of expiredStagings) {
      try {
        // 执行物理删除
        await this.executePhysicalDeletion(staging);

        // 更新暂存状态
        await this.stagingRepo.update(staging.id, {
          stagingStatus: DataCleanupStagingStatus.Deleted,
          deletedDate: new Date(),
        });

        processedRecords.push({
          stagingId: staging.id,
          targetType: staging.targetType,
          targetValue: staging.targetValue,
          status: 'success',
        });

        successCount++;
        this.logger.info(`定期清理完成: stagingId=${staging.id}, targetType=${staging.targetType}`);
      } catch (error) {
        processedRecords.push({
          stagingId: staging.id,
          targetType: staging.targetType,
          targetValue: staging.targetValue,
          status: 'error',
          error: error.message,
        });

        errorCount++;
        this.logger.error(`清理暂存记录失败: stagingId=${staging.id}`, error);
      }
    }

    return { successCount, errorCount, processedRecords };
  }

  // === 私有方法实现 ===

  /**
   * branchCode 清理分析：基于组织分配关系 + 跨branchCode检查
   */
  private async analyzeBranchCodeCleanup(branchCode: string, environment: CleanupEnvironmentConfig): Promise<CleanupAnalysisResult> {
    // 1. 检查组织分配关系和跨branchCode分享
    const cleanupResult = await this.safetyChecker.isBranchCodeSafeToClean(branchCode, environment);

    if (!cleanupResult.canClean) {
      // 不能清理时返回空的清理计划
      return {
        impactAssessment: { affectedGroups: 0, affectedMetrics: 0, affectedStrategies: 0, affectedRelations: 0 },
        cleanableEntities: {
          groupIds: [],
          metricIds: [],
          strategyIds: [],
          relationIds: { groupMetricRelations: [], metricDimensionRelations: [], strategyFieldsRelations: [] },
        },
        originalStatusBackup: { groups: [], metrics: [], strategies: [], models: [] },
        sharedDataAnalysis: { exclusiveMetrics: [], sharedMetrics: [], exclusiveStrategies: [], sharedStrategies: [] },
        riskLevel: 'HIGH',
        riskFactors: [cleanupResult.reason],
      };
    }

    // 2. 收集该 branchCode 下所有实体
    const allModels = await this.findModelsByBranchCode(branchCode);
    const allGroups = await this.findGroupsByBranchCode(branchCode);
    const allMetrics = await this.findMetricsByBranchCode(branchCode);
    const allStrategies = await this.findStrategiesByBranchCode(branchCode);

    // 3. 计算可清理的实体（排除跨branchCode分享的）
    const cleanableMetrics = allMetrics.filter((m) => !cleanupResult.sharedMetrics.includes(m.metricsId));
    const cleanableStrategies = allStrategies.filter((s) => !cleanupResult.sharedStrategies.includes(s.strategyId));

    // 4. 计算关系实体数量
    const relationCounts = await this.calculateRelationCounts(
      allGroups.map((g) => g.groupId),
      cleanableMetrics.map((m) => m.metricsId),
      cleanableStrategies.map((s) => s.strategyId),
    );

    return {
      impactAssessment: {
        affectedGroups: allGroups.length,
        affectedMetrics: cleanableMetrics.length,
        affectedStrategies: cleanableStrategies.length,
        affectedRelations: relationCounts.total,
      },
      cleanableEntities: {
        groupIds: allGroups.map((g) => g.groupId),
        metricIds: cleanableMetrics.map((m) => m.metricsId),
        strategyIds: cleanableStrategies.map((s) => s.strategyId),
        relationIds: relationCounts.relations,
      },
      originalStatusBackup: { groups: [], metrics: [], strategies: [], models: [] },
      sharedDataAnalysis: {
        exclusiveMetrics: cleanableMetrics.map((m) => m.metricsId),
        sharedMetrics: cleanupResult.sharedMetrics,
        exclusiveStrategies: cleanableStrategies.map((s) => s.strategyId),
        sharedStrategies: cleanupResult.sharedStrategies,
      },
      riskLevel: cleanupResult.canFullClean ? 'LOW' : 'MEDIUM',
      riskFactors: [
        ...(cleanupResult.sharedMetrics.length > 0 ? [`${cleanupResult.sharedMetrics.length} 个指标被其他branchCode引用`] : []),
        ...(cleanupResult.sharedStrategies.length > 0 ? [`${cleanupResult.sharedStrategies.length} 个策略被其他branchCode引用`] : []),
      ],
    };
  }

  /**
   * riskModelId 清理分析：基于状态和依赖关系
   */
  private async analyzeRiskModelCleanup(riskModelId: number, environment: CleanupEnvironmentConfig): Promise<CleanupAnalysisResult> {
    // 1. 检查模型状态
    const model = await this.findRiskModelById(riskModelId);
    if (!model) {
      throw new Error(`模型不存在: riskModelId=${riskModelId}`);
    }

    const isSafe = await this.safetyChecker.isModelSafeToOperate(riskModelId, model.branchCode, environment);

    if (!isSafe) {
      return {
        impactAssessment: { affectedGroups: 0, affectedMetrics: 0, affectedStrategies: 0, affectedRelations: 0 },
        cleanableEntities: {
          groupIds: [],
          metricIds: [],
          strategyIds: [],
          relationIds: { groupMetricRelations: [], metricDimensionRelations: [], strategyFieldsRelations: [] },
        },
        originalStatusBackup: { groups: [], metrics: [], strategies: [], models: [] },
        sharedDataAnalysis: { exclusiveMetrics: [], sharedMetrics: [], exclusiveStrategies: [], sharedStrategies: [] },
        riskLevel: 'HIGH',
        riskFactors: ['模型不能安全清理'],
      };
    }

    // 2. 使用依赖分析器分析模型的清理影响
    return await this.dependencyAnalyzer.analyzeRiskModelCleanup(riskModelId, environment);
  }

  /**
   * 备份实体的原始状态
   */
  private async backupOriginalStatus(manager: EntityManager, analysisResult: CleanupAnalysisResult): Promise<any> {
    const backup = {
      groups: [],
      metrics: [],
      strategies: [],
      models: [],
    };

    const { cleanableEntities } = analysisResult;

    // 备份分组状态
    if (cleanableEntities.groupIds.length > 0) {
      const groups = await manager.find(GroupEntity, {
        where: { groupId: In(cleanableEntities.groupIds) },
        select: ['groupId', 'status'],
      });
      backup.groups = groups.map((g) => ({ id: g.groupId, originalStatus: g.status }));
    }

    // 备份指标状态
    if (cleanableEntities.metricIds.length > 0) {
      const metrics = await manager.find(MetricsEntity, {
        where: { metricsId: In(cleanableEntities.metricIds) },
        select: ['metricsId', 'status'],
      });
      backup.metrics = metrics.map((m) => ({ id: m.metricsId, originalStatus: m.status }));
    }

    // 备份策略状态
    if (cleanableEntities.strategyIds.length > 0) {
      const strategies = await manager.find(DimensionHitStrategyEntity, {
        where: { strategyId: In(cleanableEntities.strategyIds) },
        select: ['strategyId', 'status'],
      });
      backup.strategies = strategies.map((s) => ({ id: s.strategyId, originalStatus: s.status }));
    }

    return backup;
  }

  /**
   * 恢复实体到原始状态
   */
  private async restoreOriginalStatus(manager: EntityManager, originalStatusBackup: any): Promise<void> {
    // 恢复分组状态
    for (const group of originalStatusBackup.groups || []) {
      await manager.update(GroupEntity, { groupId: group.id }, { status: group.originalStatus });
    }

    // 恢复指标状态
    for (const metric of originalStatusBackup.metrics || []) {
      await manager.update(MetricsEntity, { metricsId: metric.id }, { status: metric.originalStatus });
    }

    // 恢复策略状态
    for (const strategy of originalStatusBackup.strategies || []) {
      await manager.update(DimensionHitStrategyEntity, { strategyId: strategy.id }, { status: strategy.originalStatus });
    }

    // 恢复模型状态（如果有）
    for (const model of originalStatusBackup.models || []) {
      await manager.update(RiskModelEntity, { modelId: model.id }, { status: model.originalStatus });
    }
  }

  /**
   * 标记实体为废弃状态（不删除）
   */
  private async markEntitiesAsDeprecated(manager: EntityManager, analysisResult: CleanupAnalysisResult): Promise<void> {
    const { cleanableEntities } = analysisResult;

    // 标记分组为废弃
    if (cleanableEntities.groupIds.length > 0) {
      await manager.update(GroupEntity, { groupId: In(cleanableEntities.groupIds) }, { status: DataStatusEnums.Deprecated });
    }

    // 标记指标为废弃
    if (cleanableEntities.metricIds.length > 0) {
      await manager.update(MetricsEntity, { metricsId: In(cleanableEntities.metricIds) }, { status: DataStatusEnums.Deprecated });
    }

    // 标记策略为废弃
    if (cleanableEntities.strategyIds.length > 0) {
      await manager.update(DimensionHitStrategyEntity, { strategyId: In(cleanableEntities.strategyIds) }, { status: DataStatusEnums.Deprecated });
    }
  }

  // === 查询辅助方法（需要实现） ===

  private async findRiskModelById(modelId: number) {
    // 实现查找风险模型的逻辑
    return await this.entityManager.findOne(RiskModelEntity, { where: { modelId } });
  }

  private async findModelsByBranchCode(branchCode: string) {
    // 实现查找branchCode下所有模型的逻辑
    return await this.entityManager.find(RiskModelEntity, { where: { branchCode } });
  }

  private async findGroupsByBranchCode(branchCode: string) {
    // 实现查找branchCode下所有分组的逻辑
    const query = `
      SELECT g.group_id, g.group_name, g.status
      FROM \`group\` g
      JOIN risk_model rm ON g.model_id = rm.model_id
      WHERE rm.branch_code = ?
    `;
    return await this.entityManager.query(query, [branchCode]);
  }

  private async findMetricsByBranchCode(branchCode: string) {
    // 实现查找branchCode下所有指标的逻辑
    const query = `
      SELECT DISTINCT m.metrics_id, m.name, m.status
      FROM metrics m
      JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
      JOIN \`group\` g ON gmr.group_id = g.group_id
      JOIN risk_model rm ON g.model_id = rm.model_id
      WHERE rm.branch_code = ?
    `;
    return await this.entityManager.query(query, [branchCode]);
  }

  private async findStrategiesByBranchCode(branchCode: string) {
    // 实现查找branchCode下所有策略的逻辑
    const query = `
      SELECT DISTINCT dhs.strategy_id, dhs.strategy_name, dhs.status
      FROM dimension_hit_strategy dhs
      JOIN metric_dimension_relation mdr ON dhs.strategy_id = mdr.dimension_strategy_id
      JOIN metrics m ON mdr.metrics_id = m.metrics_id
      JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
      JOIN \`group\` g ON gmr.group_id = g.group_id
      JOIN risk_model rm ON g.model_id = rm.model_id
      WHERE rm.branch_code = ?
    `;
    return await this.entityManager.query(query, [branchCode]);
  }

  private async calculateRelationCounts(groupIds: number[], metricIds: number[], strategyIds: number[]) {
    // 实现计算关系实体数量的逻辑
    const relations = {
      groupMetricRelations: [],
      metricDimensionRelations: [],
      strategyFieldsRelations: [],
    };

    // 计算总数
    const total = relations.groupMetricRelations.length + relations.metricDimensionRelations.length + relations.strategyFieldsRelations.length;

    return { relations, total };
  }

  private async executePhysicalDeletion(staging: DataCleanupStagingEntity): Promise<void> {
    // 实现物理删除的逻辑
    // 这里会调用 CleanupExecutorService 来执行实际的删除操作
    this.logger.info(`执行物理删除: stagingId=${staging.id}, targetType=${staging.targetType}`);
  }
}
```

## 6. 测试策略

### 6.1 单元测试

#### 6.1.1 SafetyCheckerService 单元测试

```typescript
// src/modules/data-processing/data-cleaner/risk-model/tests/safety-checker.unittest.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getEntityManagerToken } from '@nestjs/typeorm';
import { SafetyCheckerService } from '../services/SafetyCheckerService';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { CleanupTriggerType } from '../enums/CleanupTriggerType';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

describe('SafetyCheckerService 单元测试', () => {
  let service: SafetyCheckerService;
  let mockEntityManager: any;
  let testOrgId: number;
  let testUserId: number;

  beforeEach(async () => {
    // 生成测试用户ID
    [testOrgId, testUserId] = generateUniqueTestIds('safety-checker.unittest.spec.ts');

    // Mock EntityManager
    mockEntityManager = {
      findOne: jest.fn(),
      find: jest.fn(),
      query: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SafetyCheckerService,
        {
          provide: getEntityManagerToken(),
          useValue: mockEntityManager,
        },
      ],
    }).compile();

    service = module.get<SafetyCheckerService>(SafetyCheckerService);
  });

  describe('isModelSafeToOperate', () => {
    it('应该拒绝清理生产环境已发布的模型', async () => {
      // Arrange
      const modelId = 1001;
      const branchCode = 'TEST_BRANCH';
      const environment = {
        environment: 'production' as const,
        testOrganizations: [testOrgId],
        safeCleanupEnabled: true,
        batchSize: 100,
        maxRollbackCount: 3,
      };

      mockEntityManager.findOne.mockResolvedValue({
        modelId,
        status: DataStatusEnums.Enabled,
        branchCode,
        modelName: '测试模型',
      });

      mockEntityManager.find.mockResolvedValue([
        { orgId: 9999 }, // 非测试组织
      ]);

      // Act
      const result = await service.isModelSafeToOperate(modelId, branchCode, environment);

      // Assert
      expect(result).toBe(false);
      expect(mockEntityManager.findOne).toHaveBeenCalledWith(expect.anything(), expect.objectContaining({ where: { modelId } }));
    });

    it('应该允许清理测试环境的模型', async () => {
      // Arrange
      const modelId = 1002;
      const branchCode = 'TEST_BRANCH';
      const environment = {
        environment: 'test' as const,
        testOrganizations: [],
        safeCleanupEnabled: true,
        batchSize: 100,
        maxRollbackCount: 3,
      };

      mockEntityManager.findOne.mockResolvedValue({
        modelId,
        status: DataStatusEnums.Developing,
        branchCode,
        modelName: '测试模型',
      });

      mockEntityManager.find.mockResolvedValue([]);

      // Act
      const result = await service.isModelSafeToOperate(modelId, branchCode, environment);

      // Assert
      expect(result).toBe(true);
    });
  });

  describe('isBranchCodeSafeToClean', () => {
    it('应该检查跨branchCode分享的指标和策略', async () => {
      // Arrange
      const branchCode = 'TEST_BRANCH';
      const environment = {
        environment: 'test' as const,
        testOrganizations: [],
        safeCleanupEnabled: true,
        batchSize: 100,
        maxRollbackCount: 3,
      };

      mockEntityManager.find.mockResolvedValue([]); // 无组织分配
      mockEntityManager.query
        .mockResolvedValueOnce([{ metrics_id: 101 }]) // 跨branchCode分享的指标
        .mockResolvedValueOnce([{ strategy_id: 201 }]); // 跨branchCode分享的策略

      // Act
      const result = await service.isBranchCodeSafeToClean(branchCode, environment);

      // Assert
      expect(result.canClean).toBe(true);
      expect(result.canFullClean).toBe(false);
      expect(result.sharedMetrics).toEqual([101]);
      expect(result.sharedStrategies).toEqual([201]);
      expect(result.reason).toContain('存在跨branchCode分享资源');
    });
  });

  describe('isMetricSafeToClean', () => {
    it('应该根据清理触发类型采用不同策略', async () => {
      // Arrange
      const metricId = 1001;
      const targetBranchCode = 'TEST_BRANCH';
      const environment = {
        environment: 'test' as const,
        testOrganizations: [],
        safeCleanupEnabled: true,
        batchSize: 100,
        maxRollbackCount: 3,
      };

      mockEntityManager.query.mockResolvedValue([
        {
          model_id: 101,
          branch_code: 'OTHER_BRANCH',
          model_name: '其他模型',
          status: DataStatusEnums.Enabled,
        },
      ]);

      // Act - branchCode级别清理
      const branchCodeResult = await service.isMetricSafeToClean(metricId, targetBranchCode, environment, CleanupTriggerType.BRANCH_CODE);

      // Act - riskModelId级别清理
      const modelIdResult = await service.isMetricSafeToClean(metricId, targetBranchCode, environment, CleanupTriggerType.RISK_MODEL_ID, 1001);

      // Assert
      expect(branchCodeResult.canDeleteMetric).toBe(false);
      expect(branchCodeResult.reason).toContain('跨branchCode引用');
      expect(modelIdResult.canDeleteMetric).toBe(false);
      expect(modelIdResult.reason).toContain('跨branchCode引用');
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
```

#### 6.1.2 DataCleanupStagingService 单元测试

```typescript
// src/modules/data-processing/data-cleaner/risk-model/tests/staging-service.unittest.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getEntityManagerToken, getRepositoryToken } from '@nestjs/typeorm';
import { DataCleanupStagingService } from '../services/DataCleanupStagingService';
import { DataCleanupStagingEntity } from '../entities/DataCleanupStagingEntity';
import { SafetyCheckerService } from '../services/SafetyCheckerService';
import { DependencyAnalyzerService } from '../services/DependencyAnalyzerService';
import { DataCleanupStagingStatus } from '../enums/DataCleanupStagingStatus';
import { generateUniqueTestIds } from 'apps/test_utils_module/test.user';

describe('DataCleanupStagingService 单元测试', () => {
  let service: DataCleanupStagingService;
  let mockEntityManager: any;
  let mockStagingRepo: any;
  let mockSafetyChecker: any;
  let mockDependencyAnalyzer: any;
  let testOrgId: number;
  let testUserId: number;

  beforeEach(async () => {
    [testOrgId, testUserId] = generateUniqueTestIds('staging-service.unittest.spec.ts');

    // Mock 依赖
    mockEntityManager = {
      findOne: jest.fn(),
      find: jest.fn(),
      query: jest.fn(),
      transaction: jest.fn(),
      update: jest.fn(),
    };

    mockStagingRepo = {
      save: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      update: jest.fn(),
    };

    mockSafetyChecker = {
      isBranchCodeSafeToClean: jest.fn(),
      isModelSafeToOperate: jest.fn(),
    };

    mockDependencyAnalyzer = {
      analyzeRiskModelCleanup: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataCleanupStagingService,
        {
          provide: getEntityManagerToken(),
          useValue: mockEntityManager,
        },
        {
          provide: getRepositoryToken(DataCleanupStagingEntity),
          useValue: mockStagingRepo,
        },
        {
          provide: SafetyCheckerService,
          useValue: mockSafetyChecker,
        },
        {
          provide: DependencyAnalyzerService,
          useValue: mockDependencyAnalyzer,
        },
      ],
    }).compile();

    service = module.get<DataCleanupStagingService>(DataCleanupStagingService);
  });

  describe('quickRollbackDeprecatedAnalysis', () => {
    it('应该支持模拟运行模式', async () => {
      // Arrange
      const options = {
        branchCode: 'TEST_BRANCH',
        operatorId: testUserId,
        dryRun: true,
      };

      const mockRecords = [
        {
          id: 1,
          targetType: 'branchCode',
          targetValue: 'TEST_BRANCH',
          stagingStatus: DataCleanupStagingStatus.Deprecated,
        },
        {
          id: 2,
          targetType: 'riskModelId',
          targetValue: '1001',
          stagingStatus: DataCleanupStagingStatus.Deprecated,
        },
      ];

      mockStagingRepo.find.mockResolvedValue(mockRecords);

      // Act
      const result = await service.quickRollbackDeprecatedAnalysis(options);

      // Assert
      expect(result.affectedRecords).toBe(2);
      expect(result.rollbackDetails).toHaveLength(2);
      expect(result.rollbackDetails[0].status).toBe('success');
      expect(mockEntityManager.transaction).not.toHaveBeenCalled(); // 模拟运行不执行事务
    });

    it('应该处理批量回退中的错误', async () => {
      // Arrange
      const options = {
        branchCode: 'TEST_BRANCH',
        operatorId: testUserId,
        dryRun: false,
      };

      const mockRecords = [
        {
          id: 1,
          targetType: 'branchCode',
          targetValue: 'TEST_BRANCH',
          stagingStatus: DataCleanupStagingStatus.Deprecated,
          analysisResult: {
            originalStatusBackup: { groups: [], metrics: [], strategies: [], models: [] },
          },
        },
        {
          id: 2,
          targetType: 'riskModelId',
          targetValue: '1001',
          stagingStatus: DataCleanupStagingStatus.Deprecated,
          analysisResult: null, // 缺少备份数据，会导致错误
        },
      ];

      mockStagingRepo.find.mockResolvedValue(mockRecords);
      mockEntityManager.transaction.mockImplementation(async (callback) => {
        return await callback(mockEntityManager);
      });

      // Act
      const result = await service.quickRollbackDeprecatedAnalysis(options);

      // Assert
      expect(result.affectedRecords).toBe(1); // 只有1个成功
      expect(result.rollbackDetails).toHaveLength(2);
      expect(result.rollbackDetails[0].status).toBe('success');
      expect(result.rollbackDetails[1].status).toBe('error');
      expect(result.rollbackDetails[1].error).toContain('缺少状态备份数据');
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
```

### 6.2 集成测试

```typescript
// src/modules/data-processing/data-cleaner/risk-model/tests/cleanup-integration.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataCleanupStagingService } from '../services/DataCleanupStagingService';
import { SafetyCheckerService } from '../services/SafetyCheckerService';
import { DataCleanupStagingEntity } from '../entities/DataCleanupStagingEntity';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { GroupEntity } from '@domain/entities/GroupEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

describe('数据清理集成测试', () => {
  let module: TestingModule;
  let stagingService: DataCleanupStagingService;
  let safetyChecker: SafetyCheckerService;
  let testOrgId: number;
  let testUserId: number;
  let testUser: any;

  beforeEach(async () => {
    [testOrgId, testUserId] = generateUniqueTestIds('cleanup-integration.spec.ts');
    testUser = getTestUser(testOrgId, testUserId);

    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          // 测试数据库配置
          type: 'mysql',
          host: process.env.DB_HOST,
          port: parseInt(process.env.DB_PORT),
          username: process.env.DB_USERNAME,
          password: process.env.DB_PASSWORD,
          database: process.env.DB_DATABASE,
          entities: [DataCleanupStagingEntity, RiskModelEntity, GroupEntity, MetricsEntity],
          synchronize: false,
        }),
        TypeOrmModule.forFeature([DataCleanupStagingEntity, RiskModelEntity, GroupEntity, MetricsEntity]),
      ],
      providers: [DataCleanupStagingService, SafetyCheckerService],
    }).compile();

    stagingService = module.get<DataCleanupStagingService>(DataCleanupStagingService);
    safetyChecker = module.get<SafetyCheckerService>(SafetyCheckerService);
  });

  describe('完整的三阶段清理流程', () => {
    let testModelId: number;
    let testBranchCode: string;

    beforeEach(async () => {
      // 创建测试数据
      testBranchCode = `TEST_BRANCH_${testOrgId}`;
      // 在数据库中创建测试模型、分组、指标等数据
    });

    it('应该完成分析-废弃-撤回的完整流程', async () => {
      // 1. 分析阶段
      const staging = await stagingService.analyzeAndStage('branchCode', testBranchCode, testUserId, {
        environment: 'test',
        testOrganizations: [testOrgId],
        safeCleanupEnabled: true,
        batchSize: 100,
        maxRollbackCount: 3,
      });

      expect(staging).toBeDefined();
      expect(staging.targetType).toBe('branchCode');
      expect(staging.targetValue).toBe(testBranchCode);

      // 2. 废弃阶段
      await stagingService.executeDeprecation(staging.id, testUserId);

      // 验证实体状态已更新为废弃
      // ... 检查数据库中的实体状态

      // 3. 撤回阶段
      await stagingService.rollbackDeprecation(staging.id, testUserId);

      // 验证实体状态已恢复
      // ... 检查数据库中的实体状态是否恢复
    });
  });

  afterEach(async () => {
    // 清理测试数据
    // 使用 testOrgId 和 testUserId 清理相关数据
    await module.close();
  });
});
```

## 7. 使用示例

### 7.1 branchCode 级别清理示例

```typescript
// 使用示例：清理整个商业产品
async function cleanupBranchCodeExample() {
  const branchCode = 'OBSOLETE_PRODUCT_V1';
  const operatorId = 12345;

  // 环境配置
  const environment: CleanupEnvironmentConfig = {
    environment: 'production',
    testOrganizations: [1001, 1002, 1003],
    safeCleanupEnabled: true,
    batchSize: 100,
    maxRollbackCount: 3,
  };

  try {
    // 1. 分析阶段
    console.log('开始分析 branchCode 清理影响...');
    const staging = await stagingService.analyzeAndStage('branchCode', branchCode, operatorId, environment);

    console.log('分析完成:', {
      stagingId: staging.id,
      impactAssessment: staging.analysisResult.impactAssessment,
      riskLevel: staging.analysisResult.riskLevel,
      riskFactors: staging.analysisResult.riskFactors,
    });

    // 2. 检查分析结果，决定是否继续
    if (staging.analysisResult.riskLevel === 'HIGH') {
      console.log('风险等级过高，建议人工审核');
      return;
    }

    // 3. 执行废弃操作
    console.log('执行废弃操作...');
    await stagingService.executeDeprecation(staging.id, operatorId);
    console.log('废弃操作完成，数据已标记为废弃状态');

    // 4. 如果需要撤回（示例）
    // await stagingService.rollbackDeprecation(staging.id, operatorId);
    // console.log('撤回操作完成，数据状态已恢复');
  } catch (error) {
    console.error('清理过程发生错误:', error.message);
    throw error;
  }
}
```

### 7.2 快速批量回退示例

```typescript
// 使用示例：紧急批量回退
async function quickRollbackExample() {
  const operatorId = 12345;

  try {
    // 1. 模拟运行，查看影响范围
    console.log('模拟运行，查看要回退的记录...');
    const dryRunResult = await stagingService.quickRollbackDeprecatedAnalysis({
      branchCode: 'PROBLEMATIC_BRANCH',
      operatorId,
      dryRun: true,
    });

    console.log(`模拟运行结果: 将回退 ${dryRunResult.affectedRecords} 条记录`);
    dryRunResult.rollbackDetails.forEach((detail) => {
      console.log(`- ${detail.targetType}: ${detail.targetValue}`);
    });

    // 2. 确认后执行实际回退
    const userConfirmed = true; // 假设用户确认
    if (userConfirmed) {
      console.log('执行实际回退操作...');
      const rollbackResult = await stagingService.quickRollbackDeprecatedAnalysis({
        branchCode: 'PROBLEMATIC_BRANCH',
        operatorId,
        dryRun: false,
      });

      console.log(`回退完成: 成功 ${rollbackResult.affectedRecords} 条`);

      // 处理失败的记录
      const failedRecords = rollbackResult.rollbackDetails.filter((d) => d.status === 'error');
      if (failedRecords.length > 0) {
        console.log('以下记录回退失败:');
        failedRecords.forEach((record) => {
          console.log(`- ${record.targetType}: ${record.targetValue}, 错误: ${record.error}`);
        });
      }
    }
  } catch (error) {
    console.error('批量回退过程发生错误:', error.message);
    throw error;
  }
}
```

### 7.3 定时清理任务示例

```typescript
// 定时任务服务
@Injectable()
export class DataCleanupScheduleService {
  constructor(private readonly stagingService: DataCleanupStagingService) {}

  @Cron('0 0 2 * * 0') // 每周日凌晨2点执行
  async weeklyCleanupScan() {
    try {
      console.log('开始执行定期数据清理扫描...');

      const result = await this.stagingService.scanAndCleanExpiredData();

      console.log(`定期清理完成: 成功=${result.successCount}, 失败=${result.errorCount}`);

      // 记录详细结果
      result.processedRecords.forEach((record) => {
        if (record.status === 'success') {
          console.log(`✅ 清理成功: ${record.targetType}=${record.targetValue}`);
        } else {
          console.log(`❌ 清理失败: ${record.targetType}=${record.targetValue}, 错误: ${record.error}`);
        }
      });

      // 发送清理报告邮件（可选）
      // await this.sendCleanupReport(result);
    } catch (error) {
      console.error('定期数据清理失败', error);
      // 发送告警通知
      // await this.sendAlertNotification(error);
    }
  }
}
```

## 8. 实施指南

### 8.1 开发环境准备

1. **数据库迁移**：创建暂存表

```sql
-- 创建数据清理暂存表
CREATE TABLE `data_cleanup_staging` (
  `id` int NOT NULL AUTO_INCREMENT,
  `target_type` varchar(50) NOT NULL COMMENT '清理目标类型: branchCode|riskModelId',
  `target_value` varchar(200) NOT NULL COMMENT '清理目标值',
  `risk_model_id` int DEFAULT NULL COMMENT '关联的风险模型ID',
  `branch_code` varchar(100) DEFAULT NULL COMMENT '关联的branchCode',
  `analysis_result` json NOT NULL COMMENT '清理分析结果详情',
  `staging_status` tinyint NOT NULL DEFAULT '0' COMMENT '暂存状态: 0-已分析, 1-已废弃, 2-已删除',
  `deprecated_date` datetime DEFAULT NULL COMMENT '标记废弃时间',
  `deleted_date` datetime DEFAULT NULL COMMENT '彻底删除时间',
  `rollback_date` datetime DEFAULT NULL COMMENT '撤回操作时间',
  `rollback_count` int NOT NULL DEFAULT '0' COMMENT '撤回操作次数',
  `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int NOT NULL COMMENT '创建人ID',
  `last_operated_by` int DEFAULT NULL COMMENT '最后操作人ID',
  PRIMARY KEY (`id`),
  KEY `idx_target_type_value` (`target_type`, `target_value`),
  KEY `idx_branch_code` (`branch_code`),
  KEY `idx_staging_status` (`staging_status`),
  KEY `idx_deprecated_date` (`deprecated_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据清理暂存表';
```

2. **环境配置**：

```typescript
// config/cleanup.config.ts
export const cleanupConfig: CleanupEnvironmentConfig = {
  environment: process.env.NODE_ENV === 'production' ? 'production' : 'test',
  testOrganizations: process.env.TEST_ORGANIZATIONS?.split(',').map(Number) || [],
  safeCleanupEnabled: process.env.SAFE_CLEANUP_ENABLED !== 'false',
  batchSize: parseInt(process.env.CLEANUP_BATCH_SIZE) || 100,
  maxRollbackCount: parseInt(process.env.MAX_ROLLBACK_COUNT) || 3,
};
```

### 8.2 部署步骤

1. **代码部署**：按照六层架构规则迁移代码到正确位置
2. **数据库更新**：执行迁移脚本创建暂存表
3. **配置更新**：设置环境变量和测试组织列表
4. **功能验证**：在测试环境验证核心功能
5. **监控配置**：设置清理操作的监控和告警

### 8.3 运维注意事项

1. **监控指标**：

   - 暂存表记录数量
   - 清理操作成功率
   - 撤回操作频率
   - 废弃状态数据的停留时间

2. **告警配置**：

   - 清理操作失败告警
   - 暂存表数据积压告警
   - 撤回操作频繁告警

3. **备份策略**：
   - 定期备份暂存表数据
   - 重要清理操作前的数据快照
   - 清理日志的长期保存

---

**总结**：本技术实现指南提供了完整的代码结构、核心服务实现、测试策略和使用示例。实施时应严格按照安全检查逻辑，确保数据清理的安全性和可恢复性。
