# 暂存机制核心服务实现

**生成时间：2025-06-17 17:26:36**

## 概述

本文档详细说明 DataCleanupStagingService 的实现，这是数据清理暂存机制的核心服务，负责管理清理操作的三个阶段：分析、废弃、删除。

## DataCleanupStagingService 实现

### 服务定义

```typescript
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import { DataCleanupStagingEntity } from '../../domain/entities/data-cleanup-staging.entity';
import { SafetyCheckerService } from './safety-checker.service';
import {
  DataCleanupConfig,
  DataCleanupResult,
  DataCleanupAnalysisResult,
  CleanupTriggerType,
  DataCleanupStagingStatus,
} from '../interfaces/cleanup.interfaces';

@Injectable()
export class DataCleanupStagingService {
  private readonly logger = new Logger(DataCleanupStagingService.name);

  constructor(
    @InjectRepository(DataCleanupStagingEntity)
    private readonly stagingRepository: Repository<DataCleanupStagingEntity>,
    private readonly safetyChecker: SafetyCheckerService,
    private readonly dataSource: DataSource,
  ) {}
}
```

### 核心方法实现

#### 1. 分析阶段 - analyzeCleanup

```typescript
/**
 * 分析阶段：识别需要清理的数据并进行安全检查
 */
async analyzeCleanup(config: DataCleanupConfig): Promise<DataCleanupAnalysisResult> {
  this.logger.log(`开始分析清理任务: ${JSON.stringify(config)}`);

  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // 1. 安全检查
    const safetyResult = await this.safetyChecker.performSafetyCheck(config);
    if (!safetyResult.isSafe) {
      throw new Error(`安全检查失败: ${safetyResult.reason}`);
    }

    // 2. 识别需要清理的数据
    const affectedData = await this.identifyAffectedData(config, queryRunner);

    // 3. 创建暂存记录
    const stagingEntity = this.stagingRepository.create({
      triggerType: config.triggerType,
      triggerValue: this.getTriggerValue(config),
      organizationId: config.organizationId,
      branchCode: config.branchCode,
      riskModelId: config.riskModelId,
      status: DataCleanupStagingStatus.ANALYZED,
      affectedTables: affectedData.tables,
      affectedRecords: affectedData.totalRecords,
      backupData: JSON.stringify(affectedData.backupData),
      createdBy: config.userId,
      createdAt: new Date(),
    });

    const savedStaging = await queryRunner.manager.save(stagingEntity);
    await queryRunner.commitTransaction();

    this.logger.log(`分析完成，暂存记录ID: ${savedStaging.id}`);

    return {
      stagingId: savedStaging.id,
      affectedTables: affectedData.tables,
      affectedRecords: affectedData.totalRecords,
      estimatedSize: affectedData.estimatedSize,
      safetyWarnings: safetyResult.warnings || [],
    };

  } catch (error) {
    await queryRunner.rollbackTransaction();
    this.logger.error(`分析阶段失败: ${error.message}`, error.stack);
    throw error;
  } finally {
    await queryRunner.release();
  }
}
```

#### 2. 废弃阶段 - markAsDeprecated

```typescript
/**
 * 废弃阶段：将数据标记为废弃状态
 */
async markAsDeprecated(stagingId: string, userId: string): Promise<DataCleanupResult> {
  this.logger.log(`开始废弃阶段，暂存ID: ${stagingId}`);

  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // 1. 获取暂存记录
    const staging = await this.getStagingRecord(stagingId, queryRunner);
    this.validateStagingStatus(staging, DataCleanupStagingStatus.ANALYZED);

    // 2. 执行废弃操作
    const deprecatedCount = await this.executeDeprecation(staging, queryRunner);

    // 3. 更新暂存状态
    staging.status = DataCleanupStagingStatus.DEPRECATED;
    staging.deprecatedAt = new Date();
    staging.deprecatedBy = userId;
    staging.actualAffectedRecords = deprecatedCount;

    await queryRunner.manager.save(staging);
    await queryRunner.commitTransaction();

    this.logger.log(`废弃完成，影响记录数: ${deprecatedCount}`);

    return {
      success: true,
      affectedRecords: deprecatedCount,
      stagingId,
      message: '数据已成功标记为废弃状态',
    };

  } catch (error) {
    await queryRunner.rollbackTransaction();
    this.logger.error(`废弃阶段失败: ${error.message}`, error.stack);
    throw error;
  } finally {
    await queryRunner.release();
  }
}
```

#### 3. 删除阶段 - executeCleanup

```typescript
/**
 * 删除阶段：物理删除废弃的数据
 */
async executeCleanup(stagingId: string, userId: string): Promise<DataCleanupResult> {
  this.logger.log(`开始删除阶段，暂存ID: ${stagingId}`);

  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // 1. 获取暂存记录
    const staging = await this.getStagingRecord(stagingId, queryRunner);
    this.validateStagingStatus(staging, DataCleanupStagingStatus.DEPRECATED);

    // 2. 执行物理删除
    const deletedCount = await this.executePhysicalDeletion(staging, queryRunner);

    // 3. 更新暂存状态
    staging.status = DataCleanupStagingStatus.COMPLETED;
    staging.completedAt = new Date();
    staging.completedBy = userId;
    staging.actualDeletedRecords = deletedCount;

    await queryRunner.manager.save(staging);
    await queryRunner.commitTransaction();

    this.logger.log(`删除完成，删除记录数: ${deletedCount}`);

    return {
      success: true,
      affectedRecords: deletedCount,
      stagingId,
      message: '数据清理已完成',
    };

  } catch (error) {
    await queryRunner.rollbackTransaction();
    this.logger.error(`删除阶段失败: ${error.message}`, error.stack);
    throw error;
  } finally {
    await queryRunner.release();
  }
}
```

#### 4. 快速批量回退 - quickBatchRollback

```typescript
/**
 * 快速批量回退：紧急情况下快速撤回多个清理操作
 */
async quickBatchRollback(
  stagingIds: string[],
  userId: string,
  reason: string
): Promise<DataCleanupResult[]> {
  this.logger.log(`开始快速批量回退，操作数量: ${stagingIds.length}`);

  const results: DataCleanupResult[] = [];
  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    for (const stagingId of stagingIds) {
      try {
        const result = await this.rollbackSingleOperation(stagingId, userId, reason, queryRunner);
        results.push(result);
      } catch (error) {
        this.logger.error(`回退操作失败，暂存ID: ${stagingId}`, error.stack);
        results.push({
          success: false,
          stagingId,
          message: `回退失败: ${error.message}`,
          affectedRecords: 0,
        });
      }
    }

    await queryRunner.commitTransaction();
    this.logger.log(`批量回退完成，成功: ${results.filter(r => r.success).length}/${results.length}`);

    return results;

  } catch (error) {
    await queryRunner.rollbackTransaction();
    this.logger.error(`批量回退失败: ${error.message}`, error.stack);
    throw error;
  } finally {
    await queryRunner.release();
  }
}
```

### 辅助方法实现

#### 数据识别方法

```typescript
/**
 * 识别受影响的数据
 */
private async identifyAffectedData(
  config: DataCleanupConfig,
  queryRunner: QueryRunner
): Promise<{
  tables: string[];
  totalRecords: number;
  estimatedSize: number;
  backupData: any;
}> {
  const affectedData = {
    tables: [] as string[],
    totalRecords: 0,
    estimatedSize: 0,
    backupData: {} as any,
  };

  if (config.triggerType === CleanupTriggerType.BRANCH_CODE) {
    // branchCode 级别清理
    await this.identifyBranchCodeData(config, affectedData, queryRunner);
  } else if (config.triggerType === CleanupTriggerType.RISK_MODEL_ID) {
    // riskModelId 级别清理
    await this.identifyRiskModelData(config, affectedData, queryRunner);
  }

  return affectedData;
}

/**
 * 识别 branchCode 相关数据
 */
private async identifyBranchCodeData(
  config: DataCleanupConfig,
  affectedData: any,
  queryRunner: QueryRunner
): Promise<void> {
  const { branchCode, organizationId } = config;

  // 1. 风险模型数据
  const riskModels = await queryRunner.query(`
    SELECT id, name, version FROM risk_models
    WHERE branch_code = ? AND organization_id = ? AND is_deleted = false
  `, [branchCode, organizationId]);

  if (riskModels.length > 0) {
    affectedData.tables.push('risk_models');
    affectedData.totalRecords += riskModels.length;
    affectedData.backupData.riskModels = riskModels;
  }

  // 2. 评估结果数据
  const assessmentResults = await queryRunner.query(`
    SELECT COUNT(*) as count FROM assessment_results ar
    JOIN risk_models rm ON ar.risk_model_id = rm.id
    WHERE rm.branch_code = ? AND rm.organization_id = ? AND ar.is_deleted = false
  `, [branchCode, organizationId]);

  if (assessmentResults[0].count > 0) {
    affectedData.tables.push('assessment_results');
    affectedData.totalRecords += parseInt(assessmentResults[0].count);
  }

  // 3. 其他相关表...
  // 根据实际业务需求添加更多表的识别逻辑
}

/**
 * 识别 riskModelId 相关数据
 */
private async identifyRiskModelData(
  config: DataCleanupConfig,
  affectedData: any,
  queryRunner: QueryRunner
): Promise<void> {
  const { riskModelId, organizationId } = config;

  // 1. 风险模型本身
  const riskModel = await queryRunner.query(`
    SELECT id, name, version, branch_code FROM risk_models
    WHERE id = ? AND organization_id = ? AND is_deleted = false
  `, [riskModelId, organizationId]);

  if (riskModel.length > 0) {
    affectedData.tables.push('risk_models');
    affectedData.totalRecords += 1;
    affectedData.backupData.riskModel = riskModel[0];
  }

  // 2. 评估结果
  const assessmentResults = await queryRunner.query(`
    SELECT COUNT(*) as count FROM assessment_results
    WHERE risk_model_id = ? AND is_deleted = false
  `, [riskModelId]);

  if (assessmentResults[0].count > 0) {
    affectedData.tables.push('assessment_results');
    affectedData.totalRecords += parseInt(assessmentResults[0].count);
  }

  // 3. 其他相关表...
}
```

#### 执行操作方法

```typescript
/**
 * 执行废弃操作
 */
private async executeDeprecation(
  staging: DataCleanupStagingEntity,
  queryRunner: QueryRunner
): Promise<number> {
  let totalAffected = 0;

  if (staging.triggerType === CleanupTriggerType.BRANCH_CODE) {
    totalAffected = await this.deprecateBranchCodeData(staging, queryRunner);
  } else if (staging.triggerType === CleanupTriggerType.RISK_MODEL_ID) {
    totalAffected = await this.deprecateRiskModelData(staging, queryRunner);
  }

  return totalAffected;
}

/**
 * 废弃 branchCode 相关数据
 */
private async deprecateBranchCodeData(
  staging: DataCleanupStagingEntity,
  queryRunner: QueryRunner
): Promise<number> {
  let totalAffected = 0;

  // 1. 废弃风险模型
  const riskModelResult = await queryRunner.query(`
    UPDATE risk_models
    SET is_deleted = true, deleted_at = NOW(), deleted_by = ?, cleanup_staging_id = ?
    WHERE branch_code = ? AND organization_id = ? AND is_deleted = false
  `, [staging.createdBy, staging.id, staging.branchCode, staging.organizationId]);

  totalAffected += riskModelResult.affectedRows || 0;

  // 2. 废弃评估结果
  const assessmentResult = await queryRunner.query(`
    UPDATE assessment_results ar
    JOIN risk_models rm ON ar.risk_model_id = rm.id
    SET ar.is_deleted = true, ar.deleted_at = NOW(), ar.deleted_by = ?, ar.cleanup_staging_id = ?
    WHERE rm.branch_code = ? AND rm.organization_id = ? AND ar.is_deleted = false
  `, [staging.createdBy, staging.id, staging.branchCode, staging.organizationId]);

  totalAffected += assessmentResult.affectedRows || 0;

  return totalAffected;
}

/**
 * 废弃 riskModelId 相关数据
 */
private async deprecateRiskModelData(
  staging: DataCleanupStagingEntity,
  queryRunner: QueryRunner
): Promise<number> {
  let totalAffected = 0;

  // 1. 废弃风险模型
  const riskModelResult = await queryRunner.query(`
    UPDATE risk_models
    SET is_deleted = true, deleted_at = NOW(), deleted_by = ?, cleanup_staging_id = ?
    WHERE id = ? AND organization_id = ? AND is_deleted = false
  `, [staging.createdBy, staging.id, staging.riskModelId, staging.organizationId]);

  totalAffected += riskModelResult.affectedRows || 0;

  // 2. 废弃评估结果
  const assessmentResult = await queryRunner.query(`
    UPDATE assessment_results
    SET is_deleted = true, deleted_at = NOW(), deleted_by = ?, cleanup_staging_id = ?
    WHERE risk_model_id = ? AND is_deleted = false
  `, [staging.createdBy, staging.id, staging.riskModelId]);

  totalAffected += assessmentResult.affectedRows || 0;

  return totalAffected;
}

/**
 * 执行物理删除
 */
private async executePhysicalDeletion(
  staging: DataCleanupStagingEntity,
  queryRunner: QueryRunner
): Promise<number> {
  let totalDeleted = 0;

  // 1. 删除评估结果
  const assessmentResult = await queryRunner.query(`
    DELETE FROM assessment_results WHERE cleanup_staging_id = ?
  `, [staging.id]);

  totalDeleted += assessmentResult.affectedRows || 0;

  // 2. 删除风险模型
  const riskModelResult = await queryRunner.query(`
    DELETE FROM risk_models WHERE cleanup_staging_id = ?
  `, [staging.id]);

  totalDeleted += riskModelResult.affectedRows || 0;

  return totalDeleted;
}
```

#### 回退操作方法

```typescript
/**
 * 单个操作回退
 */
private async rollbackSingleOperation(
  stagingId: string,
  userId: string,
  reason: string,
  queryRunner: QueryRunner
): Promise<DataCleanupResult> {
  const staging = await this.getStagingRecord(stagingId, queryRunner);

  if (staging.status === DataCleanupStagingStatus.COMPLETED) {
    // 已完成的操作需要从备份数据恢复
    return this.restoreFromBackup(staging, userId, reason, queryRunner);
  } else if (staging.status === DataCleanupStagingStatus.DEPRECATED) {
    // 废弃状态的操作只需要取消废弃标记
    return this.undoDeprecation(staging, userId, reason, queryRunner);
  } else {
    // 分析状态的操作直接标记为已取消
    return this.cancelAnalysis(staging, userId, reason, queryRunner);
  }
}

/**
 * 从备份恢复数据
 */
private async restoreFromBackup(
  staging: DataCleanupStagingEntity,
  userId: string,
  reason: string,
  queryRunner: QueryRunner
): Promise<DataCleanupResult> {
  const backupData = JSON.parse(staging.backupData);
  let restoredCount = 0;

  // 1. 恢复风险模型
  if (backupData.riskModels) {
    for (const riskModel of backupData.riskModels) {
      await queryRunner.query(`
        INSERT INTO risk_models (id, name, version, branch_code, organization_id, created_at, created_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        is_deleted = false, deleted_at = NULL, deleted_by = NULL, cleanup_staging_id = NULL
      `, [
        riskModel.id, riskModel.name, riskModel.version,
        riskModel.branch_code, staging.organizationId,
        riskModel.created_at, riskModel.created_by
      ]);
      restoredCount++;
    }
  }

  // 2. 更新暂存状态
  staging.status = DataCleanupStagingStatus.ROLLED_BACK;
  staging.rolledBackAt = new Date();
  staging.rolledBackBy = userId;
  staging.rollbackReason = reason;

  await queryRunner.manager.save(staging);

  return {
    success: true,
    affectedRecords: restoredCount,
    stagingId: staging.id,
    message: `已从备份恢复 ${restoredCount} 条记录`,
  };
}

/**
 * 取消废弃标记
 */
private async undoDeprecation(
  staging: DataCleanupStagingEntity,
  userId: string,
  reason: string,
  queryRunner: QueryRunner
): Promise<DataCleanupResult> {
  // 恢复废弃的数据
  const restoredCount = await queryRunner.query(`
    UPDATE risk_models
    SET is_deleted = false, deleted_at = NULL, deleted_by = NULL, cleanup_staging_id = NULL
    WHERE cleanup_staging_id = ?
  `, [staging.id]);

  await queryRunner.query(`
    UPDATE assessment_results
    SET is_deleted = false, deleted_at = NULL, deleted_by = NULL, cleanup_staging_id = NULL
    WHERE cleanup_staging_id = ?
  `, [staging.id]);

  // 更新暂存状态
  staging.status = DataCleanupStagingStatus.ROLLED_BACK;
  staging.rolledBackAt = new Date();
  staging.rolledBackBy = userId;
  staging.rollbackReason = reason;

  await queryRunner.manager.save(staging);

  return {
    success: true,
    affectedRecords: restoredCount.affectedRows || 0,
    stagingId: staging.id,
    message: '已取消废弃标记，数据已恢复',
  };
}
```

#### 工具方法

```typescript
/**
 * 获取暂存记录
 */
private async getStagingRecord(
  stagingId: string,
  queryRunner: QueryRunner
): Promise<DataCleanupStagingEntity> {
  const staging = await queryRunner.manager.findOne(DataCleanupStagingEntity, {
    where: { id: stagingId }
  });

  if (!staging) {
    throw new Error(`暂存记录不存在: ${stagingId}`);
  }

  return staging;
}

/**
 * 验证暂存状态
 */
private validateStagingStatus(
  staging: DataCleanupStagingEntity,
  expectedStatus: DataCleanupStagingStatus
): void {
  if (staging.status !== expectedStatus) {
    throw new Error(
      `暂存状态不正确，期望: ${expectedStatus}，实际: ${staging.status}`
    );
  }
}

/**
 * 获取触发值
 */
private getTriggerValue(config: DataCleanupConfig): string {
  if (config.triggerType === CleanupTriggerType.BRANCH_CODE) {
    return config.branchCode;
  } else if (config.triggerType === CleanupTriggerType.RISK_MODEL_ID) {
    return config.riskModelId;
  }
  throw new Error(`不支持的触发类型: ${config.triggerType}`);
}
```

## 查询方法

### 状态查询

```typescript
/**
 * 获取清理操作状态
 */
async getCleanupStatus(stagingId: string): Promise<DataCleanupStagingEntity> {
  const staging = await this.stagingRepository.findOne({
    where: { id: stagingId }
  });

  if (!staging) {
    throw new Error(`暂存记录不存在: ${stagingId}`);
  }

  return staging;
}

/**
 * 获取组织的清理历史
 */
async getCleanupHistory(
  organizationId: string,
  limit: number = 50,
  offset: number = 0
): Promise<DataCleanupStagingEntity[]> {
  return this.stagingRepository.find({
    where: { organizationId },
    order: { createdAt: 'DESC' },
    take: limit,
    skip: offset,
  });
}

/**
 * 获取待处理的清理操作
 */
async getPendingCleanups(organizationId: string): Promise<DataCleanupStagingEntity[]> {
  return this.stagingRepository.find({
    where: {
      organizationId,
      status: In([
        DataCleanupStagingStatus.ANALYZED,
        DataCleanupStagingStatus.DEPRECATED
      ])
    },
    order: { createdAt: 'ASC' },
  });
}
```

## 错误处理

### 自定义异常类

```typescript
export class CleanupOperationError extends Error {
  constructor(message: string, public readonly stagingId?: string, public readonly operation?: string) {
    super(message);
    this.name = 'CleanupOperationError';
  }
}

export class SafetyCheckError extends Error {
  constructor(message: string, public readonly reason: string, public readonly config: Partial<DataCleanupConfig>) {
    super(message);
    this.name = 'SafetyCheckError';
  }
}
```

### 错误处理装饰器

```typescript
function HandleCleanupErrors(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value;

  descriptor.value = async function (...args: any[]) {
    try {
      return await method.apply(this, args);
    } catch (error) {
      this.logger.error(`清理操作失败 [${propertyName}]: ${error.message}`, error.stack);

      if (error instanceof SafetyCheckError) {
        throw error; // 安全检查错误直接抛出
      }

      throw new CleanupOperationError(`清理操作失败: ${error.message}`, args[0]?.stagingId || args[0], propertyName);
    }
  };

  return descriptor;
}
```

## 性能优化

### 批量操作优化

```typescript
/**
 * 批量废弃操作（优化版本）
 */
private async batchDeprecateRecords(
  tableName: string,
  condition: string,
  params: any[],
  batchSize: number = 1000,
  queryRunner: QueryRunner
): Promise<number> {
  let totalAffected = 0;
  let offset = 0;

  while (true) {
    const result = await queryRunner.query(`
      UPDATE ${tableName}
      SET is_deleted = true, deleted_at = NOW(), deleted_by = ?, cleanup_staging_id = ?
      WHERE ${condition} AND is_deleted = false
      LIMIT ${batchSize}
    `, params);

    const affected = result.affectedRows || 0;
    totalAffected += affected;

    if (affected < batchSize) {
      break; // 没有更多记录需要处理
    }

    // 短暂休息，避免长时间锁表
    await new Promise(resolve => setTimeout(resolve, 10));
  }

  return totalAffected;
}
```

## 总结

DataCleanupStagingService 是数据清理系统的核心服务，提供了：

1. **三阶段清理机制**：分析 → 废弃 → 删除
2. **安全的回退机制**：支持单个和批量回退操作
3. **完整的状态管理**：跟踪每个清理操作的完整生命周期
4. **性能优化**：批量操作和事务管理
5. **错误处理**：完善的异常处理和日志记录

该服务确保了数据清理操作的安全性、可追溯性和可恢复性，为风险模型数据管理提供了可靠的技术基础。
