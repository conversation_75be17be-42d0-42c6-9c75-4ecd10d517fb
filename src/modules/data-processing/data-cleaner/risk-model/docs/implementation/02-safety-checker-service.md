生成时间：2025-06-17 17:22:28

# 风险模型数据清理 - 安全检查服务实现

## SafetyCheckerService - 安全检查服务

### 服务概述

`SafetyCheckerService` 是数据清理系统的核心安全保障组件，负责：

- **统一安全判断**：基于环境和组织分配关系的安全检查
- **跨 branchCode 检查**：防止误删共享的指标和策略
- **清理触发方式区分**：支持 branchCode 和 riskModelId 两种清理模式

### 完整实现代码

```typescript
// src/modules/data-processing/data-cleaner/risk-model/services/SafetyCheckerService.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager, In } from 'typeorm';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DistributedSystemResourceEntity } from '@domain/entities/DistributedSystemResourceEntity';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';
import { CleanupTriggerType } from '../enums/CleanupTriggerType';
import { CleanupEnvironmentConfig, BranchCodeCleanupResult, MetricCleanupResult } from '../interfaces/cleanup-config.interface';

@Injectable()
export class SafetyCheckerService {
  private readonly logger = new Logger(SafetyCheckerService.name);

  constructor(
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
  ) {}

  /**
   * 统一的模型安全检查
   */
  async isModelSafeToOperate(modelId: number, branchCode: string, environment: CleanupEnvironmentConfig): Promise<boolean> {
    // 1. 获取模型详细信息
    const model = await this.entityManager.findOne(RiskModelEntity, {
      where: { modelId },
      select: ['modelId', 'status', 'branchCode', 'modelName'],
    });

    if (!model) {
      this.logger.warn(`模型不存在: modelId=${modelId}`);
      return false;
    }

    // 2. 检查模型的组织分配关系
    const distributedOrgs = await this.findOrganizationsUsingBranchCode(branchCode);

    // 3. 核心安全判断：生产非测试组织 + 已发布状态 = 不能清理
    if (model.status === DataStatusEnums.Enabled) {
      if (environment.environment === 'production') {
        // 生产环境：检查是否只分配给测试组织
        const nonTestOrgs = distributedOrgs.filter((orgId) => !environment.testOrganizations.includes(orgId));
        if (nonTestOrgs.length > 0) {
          this.logger.warn(`生产非测试组织且已发布，不能清理: modelId=${modelId}, nonTestOrgs=${nonTestOrgs}`);
          return false; // 生产非测试组织且已发布 - 绝对不能清理
        }
      }
    }

    // 4. 明确的保护点：即使是Developing/Deprecated状态，如果是生产非测试组织，也暂不允许清理
    if (environment.environment === 'production') {
      const nonTestOrgs = distributedOrgs.filter((orgId) => !environment.testOrganizations.includes(orgId));
      if (nonTestOrgs.length > 0) {
        this.logger.warn(`生产非测试组织数据暂时保护: modelId=${modelId}, status=${model.status}, nonTestOrgs=${nonTestOrgs}`);
        return false; // 暂时保护所有生产非测试组织的数据
      }
    }

    return true; // 其他情况允许清理
  }

  /**
   * branchCode 清理安全检查（含跨branchCode分享检查）
   */
  async isBranchCodeSafeToClean(branchCode: string, environment: CleanupEnvironmentConfig): Promise<BranchCodeCleanupResult> {
    // 1. 检查 branchCode 是否被分配给任何组织
    const distributedOrgs = await this.findOrganizationsUsingBranchCode(branchCode);

    // 2. 组织分配安全检查
    let canCleanByOrgCheck = false;
    if (distributedOrgs.length === 0) {
      canCleanByOrgCheck = true; // 未分配给任何组织
    } else if (environment.environment === 'test') {
      canCleanByOrgCheck = true; // 测试环境可以清理所有 branchCode
    } else if (environment.environment === 'production') {
      const nonTestOrgs = distributedOrgs.filter((orgId) => !environment.testOrganizations.includes(orgId));
      canCleanByOrgCheck = nonTestOrgs.length === 0; // 只分配给测试组织
    }

    if (!canCleanByOrgCheck) {
      return {
        canClean: false,
        canFullClean: false,
        reason: 'branchCode被生产组织使用',
        sharedMetrics: [],
        sharedStrategies: [],
      };
    }

    // 3. 检查跨branchCode分享情况
    const sharedMetrics = await this.findCrossBranchCodeSharedMetrics(branchCode);
    const sharedStrategies = await this.findCrossBranchCodeSharedStrategies(branchCode);

    return {
      canClean: true,
      canFullClean: sharedMetrics.length === 0 && sharedStrategies.length === 0,
      reason: sharedMetrics.length > 0 || sharedStrategies.length > 0 ? '存在跨branchCode分享资源' : '可以完全清理',
      sharedMetrics,
      sharedStrategies,
    };
  }

  /**
   * 指标清理检查（基于清理触发方式和统一安全判断）
   */
  async isMetricSafeToClean(
    metricId: number,
    targetBranchCode: string,
    environment: CleanupEnvironmentConfig,
    cleanupTrigger: CleanupTriggerType,
    targetModelId?: number,
  ): Promise<MetricCleanupResult> {
    // 1. 查找所有使用该指标的模型
    const referencingModels = await this.findModelsUsingMetric(metricId);

    // 2. 检查是否有跨 branchCode 引用
    const crossBranchReferences = referencingModels.filter((model) => model.branchCode !== targetBranchCode);

    if (crossBranchReferences.length > 0) {
      return {
        canDeleteMetric: false,
        reason: cleanupTrigger === CleanupTriggerType.RISK_MODEL_ID ? '有跨branchCode引用，只能删除指定模型的关联关系' : '有跨branchCode引用，不能删除',
        crossBranchReferences,
      };
    }

    // 3. 根据清理触发方式采用不同策略
    if (cleanupTrigger === CleanupTriggerType.BRANCH_CODE) {
      // branchCode级别：如果branchCode可以删除，指标可以完全删除
      const branchCodeCleanupResult = await this.isBranchCodeSafeToClean(targetBranchCode, environment);
      return {
        canDeleteMetric: branchCodeCleanupResult.canClean,
        reason: branchCodeCleanupResult.reason,
        crossBranchReferences: [],
      };
    } else {
      // riskModelId级别：检查是否有其他模型在使用
      const otherReferences = referencingModels.filter((model) => model.branchCode === targetBranchCode && model.modelId !== targetModelId);

      if (otherReferences.length > 0) {
        return {
          canDeleteMetric: false,
          reason: '有其他模型引用，只能删除指定模型的关联关系',
          crossBranchReferences: [],
        };
      } else {
        return {
          canDeleteMetric: true,
          reason: '无其他引用，可以完全删除指标',
          crossBranchReferences: [],
        };
      }
    }
  }

  // === 私有辅助方法 ===

  /**
   * 查找使用 branchCode 的组织
   */
  private async findOrganizationsUsingBranchCode(branchCode: string): Promise<number[]> {
    const distributions = await this.entityManager.find(DistributedSystemResourceEntity, {
      where: {
        branchCode: branchCode,
        distributeStatus: 1, // 有效分发
      },
      select: ['orgId'],
    });

    return distributions.map((d) => d.orgId);
  }

  /**
   * 查找跨branchCode分享的指标
   */
  private async findCrossBranchCodeSharedMetrics(branchCode: string): Promise<number[]> {
    const query = `
      SELECT DISTINCT m.metrics_id
      FROM metrics m
      JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
      JOIN \`group\` g ON gmr.group_id = g.group_id
      JOIN risk_model rm ON g.model_id = rm.model_id
      WHERE rm.branch_code != ? 
        AND m.metrics_id IN (
          SELECT DISTINCT m2.metrics_id
          FROM metrics m2
          JOIN group_metric_relation gmr2 ON m2.metrics_id = gmr2.metrics_id
          JOIN \`group\` g2 ON gmr2.group_id = g2.group_id
          JOIN risk_model rm2 ON g2.model_id = rm2.model_id
          WHERE rm2.branch_code = ?
        )
    `;

    const result = await this.entityManager.query(query, [branchCode, branchCode]);
    return result.map((row) => row.metrics_id);
  }

  /**
   * 查找跨branchCode分享的策略
   */
  private async findCrossBranchCodeSharedStrategies(branchCode: string): Promise<number[]> {
    const query = `
      SELECT DISTINCT dhs.strategy_id
      FROM dimension_hit_strategy dhs
      JOIN metric_dimension_relation mdr ON dhs.strategy_id = mdr.dimension_strategy_id
      JOIN metrics m ON mdr.metrics_id = m.metrics_id
      JOIN group_metric_relation gmr ON m.metrics_id = gmr.metrics_id
      JOIN \`group\` g ON gmr.group_id = g.group_id
      JOIN risk_model rm ON g.model_id = rm.model_id
      WHERE rm.branch_code != ?
        AND dhs.strategy_id IN (
          SELECT DISTINCT dhs2.strategy_id
          FROM dimension_hit_strategy dhs2
          JOIN metric_dimension_relation mdr2 ON dhs2.strategy_id = mdr2.dimension_strategy_id
          JOIN metrics m2 ON mdr2.metrics_id = m2.metrics_id
          JOIN group_metric_relation gmr2 ON m2.metrics_id = gmr2.metrics_id
          JOIN \`group\` g2 ON gmr2.group_id = g2.group_id
          JOIN risk_model rm2 ON g2.model_id = rm2.model_id
          WHERE rm2.branch_code = ?
        )
    `;

    const result = await this.entityManager.query(query, [branchCode, branchCode]);
    return result.map((row) => row.strategy_id);
  }

  /**
   * 查找使用指标的所有模型
   */
  private async findModelsUsingMetric(metricId: number): Promise<
    Array<{
      modelId: number;
      branchCode: string;
      modelName: string;
      status: DataStatusEnums;
    }>
  > {
    const query = `
      SELECT DISTINCT rm.model_id, rm.branch_code, rm.model_name, rm.status
      FROM risk_model rm
      JOIN \`group\` g ON rm.model_id = g.model_id
      JOIN group_metric_relation gmr ON g.group_id = gmr.group_id
      WHERE gmr.metrics_id = ?
        AND rm.status IN (?, ?, ?)
    `;

    const result = await this.entityManager.query(query, [metricId, DataStatusEnums.Enabled, DataStatusEnums.Developing, DataStatusEnums.Deprecated]);

    return result.map((row) => ({
      modelId: row.model_id,
      branchCode: row.branch_code,
      modelName: row.model_name,
      status: row.status,
    }));
  }
}
```

### 关键方法说明

#### 1. isModelSafeToOperate()

- **用途**: 统一的模型安全检查入口
- **逻辑**: 基于环境类型和组织分配关系判断模型是否可以安全清理
- **保护原则**: 生产非测试组织的数据绝对保护

#### 2. isBranchCodeSafeToClean()

- **用途**: branchCode 级别的清理安全检查
- **特性**: 检查组织分配关系和跨 branchCode 分享情况
- **返回**: 详细的清理可行性分析结果

#### 3. isMetricSafeToClean()

- **用途**: 指标级别的清理安全检查
- **特性**: 支持两种清理触发方式，采用不同的安全策略
- **逻辑**:
  - branchCode 级别：依赖 branchCode 的整体清理可行性
  - riskModelId 级别：只删除关联关系，保护共享资源

### SQL 查询优化建议

为了提高安全检查的性能，建议添加以下索引：

```sql
-- 分发资源表索引
CREATE INDEX idx_distributed_resource_branch_status ON distributed_system_resource(branch_code, distribute_status);

-- 风险模型表索引
CREATE INDEX idx_risk_model_branch_code ON risk_model(branch_code);
CREATE INDEX idx_risk_model_status ON risk_model(status);

-- 分组指标关系表索引
CREATE INDEX idx_group_metric_relation_metrics ON group_metric_relation(metrics_id);
CREATE INDEX idx_group_metric_relation_group ON group_metric_relation(group_id);

-- 指标维度关系表索引
CREATE INDEX idx_metric_dimension_relation_metrics ON metric_dimension_relation(metrics_id);
CREATE INDEX idx_metric_dimension_relation_strategy ON metric_dimension_relation(dimension_strategy_id);
```

---

**下一步**: 请查看 `03-staging-service.md` 了解暂存机制核心服务的实现。
