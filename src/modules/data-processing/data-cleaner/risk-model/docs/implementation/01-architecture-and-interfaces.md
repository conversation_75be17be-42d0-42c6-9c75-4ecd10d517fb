生成时间：2025-06-17 17:22:28

# 风险模型数据清理 - 架构设计与接口定义

## 1. 文档概述

### 1.1 实现目标

本文档基于 `cleanup-technical-guide.md` 中的策略设计，提供具体的技术实现方案，包括：

- **代码结构设计**：基于六层分层架构的代码组织
- **核心服务实现**：清理逻辑的具体代码实现
- **数据库操作**：高效安全的数据清理 SQL
- **测试策略**：完整的单元测试和集成测试方案

### 1.2 实现范围

**主要清理实体**：

- `GroupEntity`（指标分组）- 简单清理
- `MetricsEntity`（风险指标）- 复杂清理，支持跨 branchCode 检查
- `DimensionHitStrategyEntity`（维度命中策略）- 最复杂清理，多级依赖

**清理触发方式**：

- **branchCode 级别**：基于组织分配关系的产品级清理
- **riskModelId 级别**：单模型清理，保护共享资源

## 2. 技术架构设计

### 2.1 代码组织结构

```
src/modules/data-processing/data-cleaner/risk-model/
├── enums/
│   ├── CleanupTriggerType.ts          # 清理触发类型
│   └── DataCleanupStagingStatus.ts    # 暂存状态枚举
├── interfaces/
│   ├── cleanup-config.interface.ts    # 清理配置接口
│   ├── cleanup-result.interface.ts    # 清理结果接口
│   └── analysis-result.interface.ts   # 分析结果接口
├── entities/
│   └── DataCleanupStagingEntity.ts    # 暂存表实体
├── services/
│   ├── DataCleanupStagingService.ts   # 暂存机制核心服务
│   ├── SafetyCheckerService.ts        # 安全检查服务
│   ├── DependencyAnalyzerService.ts   # 依赖分析服务
│   └── CleanupExecutorService.ts      # 清理执行服务
├── utils/
│   ├── sql-builders.ts                # SQL 构建工具
│   └── batch-processor.ts             # 批量处理工具
└── tests/
    ├── safety-checker.unittest.spec.ts
    ├── dependency-analyzer.unittest.spec.ts
    └── cleanup-integration.spec.ts
```

### 2.2 核心枚举定义

```typescript
// src/modules/data-processing/data-cleaner/risk-model/enums/CleanupTriggerType.ts
export enum CleanupTriggerType {
  BRANCH_CODE = 'branchCode', // 根据branchCode删除
  RISK_MODEL_ID = 'riskModelId', // 根据指定riskModelId删除
}

// src/modules/data-processing/data-cleaner/risk-model/enums/DataCleanupStagingStatus.ts
export enum DataCleanupStagingStatus {
  Analyzed = 0, // 已分析，等待废弃
  Deprecated = 1, // 已废弃，等待删除
  Deleted = 2, // 已彻底删除
}
```

## 3. 核心接口定义

### 3.1 清理配置接口

```typescript
// src/modules/data-processing/data-cleaner/risk-model/interfaces/cleanup-config.interface.ts
export interface CleanupEnvironmentConfig {
  environment: 'test' | 'production';
  testOrganizations: number[]; // 允许清理的测试组织ID列表
  safeCleanupEnabled: boolean; // 是否启用安全清理模式
  batchSize: number; // 批量操作大小
  maxRollbackCount: number; // 最大撤回次数
}

export interface QuickRollbackOptions {
  targetType?: 'branchCode' | 'riskModelId';
  branchCode?: string;
  createdAfter?: Date;
  operatorId: number;
  dryRun?: boolean;
}
```

### 3.2 清理结果接口

```typescript
// src/modules/data-processing/data-cleaner/risk-model/interfaces/cleanup-result.interface.ts
export interface BranchCodeCleanupResult {
  canClean: boolean;
  canFullClean: boolean;
  reason: string;
  sharedMetrics: number[];
  sharedStrategies: number[];
}

export interface MetricCleanupResult {
  canDeleteMetric: boolean;
  reason: string;
  crossBranchReferences: Array<{
    modelId: number;
    branchCode: string;
    modelName: string;
  }>;
}

export interface QuickRollbackResult {
  affectedRecords: number;
  rollbackDetails: Array<{
    stagingId: number;
    targetType: string;
    targetValue: string;
    status: 'success' | 'error';
    error?: string;
  }>;
}
```

### 3.3 分析结果接口

```typescript
// src/modules/data-processing/data-cleaner/risk-model/interfaces/analysis-result.interface.ts
export interface CleanupAnalysisResult {
  // 影响评估
  impactAssessment: {
    affectedGroups: number;
    affectedMetrics: number;
    affectedStrategies: number;
    affectedRelations: number;
  };

  // 可清理的实体列表
  cleanableEntities: {
    groupIds: number[];
    metricIds: number[];
    strategyIds: number[];
    relationIds: {
      groupMetricRelations: number[];
      metricDimensionRelations: number[];
      strategyFieldsRelations: number[];
    };
  };

  // 实体原始状态备份（用于撤回操作）
  originalStatusBackup: {
    groups: Array<{ id: number; originalStatus: DataStatusEnums }>;
    metrics: Array<{ id: number; originalStatus: DataStatusEnums }>;
    strategies: Array<{ id: number; originalStatus: DataStatusEnums }>;
    models: Array<{ id: number; originalStatus: DataStatusEnums }>;
  };

  // 共享数据分析
  sharedDataAnalysis: {
    exclusiveMetrics: number[];
    sharedMetrics: number[];
    exclusiveStrategies: number[];
    sharedStrategies: number[];
  };

  // 风险评估
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  riskFactors: string[];
}
```

## 4. 暂存表实体实现

### 4.1 DataCleanupStagingEntity

```typescript
// src/modules/data-processing/data-cleaner/risk-model/entities/DataCleanupStagingEntity.ts
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DataCleanupStagingStatus } from '../enums/DataCleanupStagingStatus';
import { CleanupAnalysisResult } from '../interfaces/analysis-result.interface';

@Entity('data_cleanup_staging')
export class DataCleanupStagingEntity {
  @PrimaryGeneratedColumn()
  id: number;

  // 清理目标标识
  @Column({
    name: 'target_type',
    type: 'varchar',
    length: 50,
    comment: '清理目标类型: branchCode|riskModelId',
  })
  targetType: 'branchCode' | 'riskModelId';

  @Column({
    name: 'target_value',
    type: 'varchar',
    length: 200,
    comment: '清理目标值',
  })
  targetValue: string;

  @Column({
    name: 'risk_model_id',
    type: 'int',
    nullable: true,
    comment: '关联的风险模型ID',
  })
  riskModelId?: number;

  @Column({
    name: 'branch_code',
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: '关联的branchCode',
  })
  branchCode?: string;

  // 分析结果
  @Column({
    name: 'analysis_result',
    type: 'json',
    comment: '清理分析结果详情',
  })
  analysisResult: CleanupAnalysisResult;

  // 状态管理
  @Column({
    name: 'staging_status',
    type: 'tinyint',
    default: DataCleanupStagingStatus.Analyzed,
    comment: '暂存状态: 0-已分析, 1-已废弃, 2-已删除',
  })
  stagingStatus: DataCleanupStagingStatus;

  @Column({
    name: 'deprecated_date',
    type: 'datetime',
    nullable: true,
    comment: '标记废弃时间',
  })
  deprecatedDate?: Date;

  @Column({
    name: 'deleted_date',
    type: 'datetime',
    nullable: true,
    comment: '彻底删除时间',
  })
  deletedDate?: Date;

  @Column({
    name: 'rollback_date',
    type: 'datetime',
    nullable: true,
    comment: '撤回操作时间',
  })
  rollbackDate?: Date;

  @Column({
    name: 'rollback_count',
    type: 'int',
    default: 0,
    comment: '撤回操作次数',
  })
  rollbackCount: number;

  // 审计字段
  @Column({
    name: 'create_date',
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createDate: Date;

  @Column({
    name: 'update_date',
    type: 'datetime',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updateDate: Date;

  @Column({
    name: 'created_by',
    type: 'int',
    comment: '创建人ID',
  })
  createdBy: number;

  @Column({
    name: 'last_operated_by',
    type: 'int',
    nullable: true,
    comment: '最后操作人ID',
  })
  lastOperatedBy?: number;
}
```

---

**下一步**: 请查看 `02-safety-checker-service.md` 了解安全检查服务的具体实现。
