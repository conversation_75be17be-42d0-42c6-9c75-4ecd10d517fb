import { MailerService } from '@kezhaozhao/nest-mailer';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MessageSearchRequest, MsgStatus, MsgType } from '@domain/model/message/MessageRequest';
import { EntityManager, getConnection } from 'typeorm';
import { QueueService } from '@core/config/queue.service';
import { MessageEntity } from '@domain/entities/MessageEntity';
import { MonitorMetricsDynamicEntity } from '@domain/entities/MonitorMetricsDynamicEntity';
import { PushContentEntity } from '@domain/entities/PushContentEntity';
import { PushMessageStatusEntity } from '@domain/entities/PushMessageStatusEntity';
import { PushRuleEntity } from '@domain/entities/PushRuleEntity';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { MessageTypeEnum } from '@domain/enums/message/MessageTypeEnum';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { PushBusinessTypeEnum } from '@domain/enums/push/PushBusinessTypeEnum';
import { AppTestModule } from '@app/app.test.module';
import { MonitorDynamicEsService } from '@modules/risk-assessment/monitor/dynamic/monitor.dynamic.es.service';
import { generateUniqueTestIds, getTestUser } from '@testing/test.user';
import { MessageService } from './message.service';
import { SmsService } from '@kezhaozhao/qcc-sms-utils';

jest.setTimeout(600000);

describe('MessageService 集成测试', () => {
  let module: TestingModule;
  let messageService: MessageService;
  let entityManager: EntityManager;

  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('message.service.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId, ProductCodeEnums.Pro);
  let testMessage: MessageEntity;
  let testPushContent: PushContentEntity;
  let testPushRule: PushRuleEntity;
  let testPushMessageStatus: PushMessageStatusEntity;

  // Mock服务
  const mockSmsService = {
    singleSend: jest.fn().mockResolvedValue({ Code: 'OK' }),
  };

  const mockMailerService = {
    sendMail: jest.fn().mockResolvedValue(true),
  };

  const mockMonitorDynamicEsService = {
    searchDynamics: jest.fn().mockResolvedValue({
      data: [
        {
          companyName: '测试公司',
          metricsContent: {
            displayContent: [
              {
                dimensionContent: [{ Content: '测试内容' }],
                dimensionKey: 'RiskChange',
              },
            ],
          },
          riskLevel: 1,
          metricsName: '测试指标',
          createDate: new Date(),
        },
      ],
      total: 1,
      aggsResponse: {
        '4_companyCount': { value: 1 },
        '4_riskLevel': {
          buckets: [
            { key: 1, doc_count: 1 },
            { key: 2, doc_count: 0 },
            { key: 3, doc_count: 0 },
          ],
        },
        '4_metricsType': {
          buckets: [
            { key: 1, doc_count: 1 },
            { key: 2, doc_count: 0 },
          ],
        },
      },
    }),
  };

  const mockQueueService = {
    messageQueue: {
      consume: jest.fn().mockResolvedValue(true),
      on: jest.fn(),
      sendMessageV2: jest.fn().mockResolvedValue(true),
    },
  };

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        AppTestModule,
        TypeOrmModule.forFeature([MessageEntity, PushContentEntity, PushRuleEntity, PushMessageStatusEntity, MonitorMetricsDynamicEntity]),
      ],
      providers: [
        MessageService,
        {
          provide: SmsService,
          useValue: mockSmsService,
        },
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: MonitorDynamicEsService,
          useValue: mockMonitorDynamicEsService,
        },
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
      ],
    }).compile();

    messageService = module.get<MessageService>(MessageService);
    entityManager = module.get<EntityManager>(EntityManager);

    // 清理之前的测试数据
    await cleanupTestData();

    // 创建测试数据
    await prepareTestData();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData();
    const connection = getConnection();
    await connection.close();
  });

  /**
   * 准备测试数据
   */
  async function prepareTestData() {
    // 创建消息
    testMessage = new MessageEntity();
    testMessage.userId = testUserId;
    testMessage.orgId = testOrgId;
    testMessage.title = '测试消息';
    testMessage.content = '测试消息内容';
    testMessage.msgType = MsgType.SystemMsg;
    testMessage.status = MsgStatus.Unread;
    testMessage.product = ProductCodeEnums.Pro;
    await entityManager.save(testMessage);

    // 创建推送规则
    testPushRule = new PushRuleEntity();
    testPushRule.orgId = testOrgId;
    testPushRule.pushRuleId = 1;
    testPushRule.type = PushBusinessTypeEnum.RiskMonitor;
    testPushRule.businessId = testOrgId;
    testPushRule.createBy = testUserId;
    await entityManager.save(testPushRule);

    // 创建推送内容
    testPushContent = new PushContentEntity();
    testPushContent.orgId = testOrgId;
    testPushContent.pushRuleId = testPushRule.pushRuleId;
    testPushContent.status = DataStatusEnums.Disabled;
    testPushContent.pushedInfoJson = {
      hitIds: ['test1', 'test2'],
      hour: 24,
      dynamicStartTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      dynamicEndTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      pushRuleId: testPushRule.pushRuleId,
    };
    await entityManager.save(testPushContent);

    // 创建推送消息状态
    testPushMessageStatus = new PushMessageStatusEntity();
    testPushMessageStatus.orgId = testOrgId;
    testPushMessageStatus.pushContentId = testPushContent.pushContentId;
    testPushMessageStatus.method = MessageTypeEnum.SMS;
    testPushMessageStatus.recipient = '***********';
    testPushMessageStatus.status = DataStatusEnums.Disabled;
    testPushMessageStatus.attempts = 0;
    await entityManager.save(testPushMessageStatus);
  }

  /**
   * 清理测试数据
   */
  async function cleanupTestData() {
    try {
      // 清理推送消息状态
      await entityManager.delete(PushMessageStatusEntity, { orgId: testOrgId });

      // 清理推送内容
      await entityManager.delete(PushContentEntity, { orgId: testOrgId });

      // 清理推送规则
      await entityManager.delete(PushRuleEntity, { orgId: testOrgId });

      // 清理消息
      await entityManager.delete(MessageEntity, { orgId: testOrgId });
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }

  describe('getMessages', () => {
    it('应该能成功获取用户消息列表', async () => {
      // 构建请求参数
      const request = new MessageSearchRequest();
      request.pageIndex = 1;
      request.pageSize = 10;

      // 调用测试方法
      const result = await messageService.getMessages(testUser, request);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.data.length).toBeGreaterThanOrEqual(1);
      expect(result.data[0].userId).toBe(testUserId);
    });

    it('应该能根据消息类型筛选消息', async () => {
      // 构建请求参数
      const request = new MessageSearchRequest();
      request.pageIndex = 1;
      request.pageSize = 10;
      request.msgType = MsgType.SystemMsg;

      // 调用测试方法
      const result = await messageService.getMessages(testUser, request);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.data.length).toBeGreaterThanOrEqual(1);
      expect(result.data[0].msgType).toBe(MsgType.SystemMsg);
    });

    it('应该能根据消息状态筛选消息', async () => {
      // 构建请求参数
      const request = new MessageSearchRequest();
      request.pageIndex = 1;
      request.pageSize = 10;
      request.msgStatus = MsgStatus.Unread;

      // 调用测试方法
      const result = await messageService.getMessages(testUser, request);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.data.length).toBeGreaterThanOrEqual(1);
      expect(result.data[0].status).toBe(MsgStatus.Unread);
    });
  });

  describe('getMessagesCount', () => {
    it('应该能成功获取用户消息数量', async () => {
      // 构建请求参数
      const request = new MessageSearchRequest();

      // 调用测试方法
      const count = await messageService.getMessagesCount(testUser, request);

      // 验证结果
      expect(count).toBeGreaterThanOrEqual(1);
    });
  });

  describe('readMessage', () => {
    it('应该能成功将消息标记为已读', async () => {
      // 调用测试方法
      const result = await messageService.readMessage(testUser, testMessage.id);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.status).toBe(MsgStatus.Read);

      // 验证数据库中的状态
      const updatedMessage = await entityManager.findOne(MessageEntity, { where: { id: testMessage.id } });
      expect(updatedMessage.status).toBe(MsgStatus.Read);
    });
  });

  describe('readAll', () => {
    it('应该能成功将所有消息标记为已读', async () => {
      // 先将消息重置为未读状态
      await entityManager.update(MessageEntity, { id: testMessage.id }, { status: MsgStatus.Unread });

      // 调用测试方法
      const result = await messageService.readAll(testUser);

      // 验证结果
      expect(result).toBeDefined();

      // 验证数据库中的状态
      const updatedMessage = await entityManager.findOne(MessageEntity, { where: { id: testMessage.id } });
      expect(updatedMessage.status).toBe(MsgStatus.Read);
    });

    it('应该能成功将指定类型的消息标记为已读', async () => {
      // 先将消息重置为未读状态
      await entityManager.update(MessageEntity, { id: testMessage.id }, { status: MsgStatus.Unread });

      // 调用测试方法
      const result = await messageService.readAll(testUser, MsgType.SystemMsg);

      // 验证结果
      expect(result).toBeDefined();

      // 验证数据库中的状态
      const updatedMessage = await entityManager.findOne(MessageEntity, { where: { id: testMessage.id } });
      expect(updatedMessage.status).toBe(MsgStatus.Read);
    });
  });

  describe('processMessageConsumer', () => {
    it('应该能成功处理SMS类型的消息', async () => {
      // 调用测试方法
      await messageService.processMessageConsumer(testPushMessageStatus);

      // 验证SMS服务是否被调用
      expect(mockSmsService.singleSend).toHaveBeenCalled();

      // 验证消息状态是否更新
      const updatedStatus = await entityManager.findOne(PushMessageStatusEntity, { where: { id: testPushMessageStatus.id } });
      expect(updatedStatus.status).toBe(DataStatusEnums.Enabled);

      // 验证推送内容状态是否更新
      const updatedContent = await entityManager.findOne(PushContentEntity, { where: { id: testPushContent.pushContentId } });
      expect(updatedContent.status).toBe(DataStatusEnums.Enabled);
    });

    it('应该能成功处理EMAIL类型的消息', async () => {
      // 修改消息类型为EMAIL
      await entityManager.update(
        PushMessageStatusEntity,
        { id: testPushMessageStatus.id },
        { method: MessageTypeEnum.EMAIL, status: DataStatusEnums.Disabled },
      );

      // 调用测试方法
      await messageService.processMessageConsumer(testPushMessageStatus);

      // 验证邮件服务是否被调用
      expect(mockMailerService.sendMail).toHaveBeenCalled();

      // 验证消息状态是否更新
      const updatedStatus = await entityManager.findOne(PushMessageStatusEntity, { where: { id: testPushMessageStatus.id } });
      expect(updatedStatus.status).toBe(DataStatusEnums.Enabled);
    });
  });

  describe('errorMessagRetryProducer', () => {
    it('应该在尝试次数小于3时重新发送消息', async () => {
      // 重置消息状态
      await entityManager.update(PushMessageStatusEntity, { id: testPushMessageStatus.id }, { status: DataStatusEnums.Disabled, attempts: 1 });

      const error = new Error('测试错误');

      // 调用测试方法
      await messageService.errorMessagRetryProducer(error, 2, testPushMessageStatus);

      // 验证消息队列是否被调用
      expect(mockQueueService.messageQueue.sendMessageV2).toHaveBeenCalledWith(
        testPushMessageStatus,
        expect.objectContaining({
          retries: 3,
          breakTrace: true,
        }),
      );
    });

    it('应该在尝试次数达到3时记录错误信息', async () => {
      // 重置消息状态
      await entityManager.update(PushMessageStatusEntity, { id: testPushMessageStatus.id }, { status: DataStatusEnums.Disabled, attempts: 2 });

      const error = new Error('测试错误');

      // 调用测试方法
      await messageService.errorMessagRetryProducer(error, 3, testPushMessageStatus);

      // 验证消息队列是否未被调用
      expect(mockQueueService.messageQueue.sendMessageV2).not.toHaveBeenCalled();

      // 验证错误信息是否被记录
      const updatedStatus = await entityManager.findOne(PushMessageStatusEntity, { where: { id: testPushMessageStatus.id } });
      expect(updatedStatus.errorMessage).toBeDefined();
    });
  });
});
