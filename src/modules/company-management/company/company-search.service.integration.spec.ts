import { KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EntityManager, getConnection } from 'typeorm';
import { CompanyEntity } from '@domain/entities/CompanyEntity';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { PlatformUser } from '@domain/model/common';
import { CompanyBasicInfo } from '@domain/model/company/CompanyBasicInfo';
import { AppTestModule } from '@app/app.test.module';
import { generateUniqueTestIds, getTestUser } from '@testing/test.user';
import { CompanyDetailService } from './company-detail.service';
import { CompanySearchService } from './company-search.service';
import { CompanyBusinessInfo } from './model/CompanyBusinessInfo';
import { SearchCertificationRequest } from './model/SearchCertificationRequest';
import { SearchMultiSelectionRequest } from './model/SearchMultiSelectionRequest';
import { SupplierCustomerWithFreeTextRequest } from './model/MultiMatchCompanyRequest';
import { intersection, isEqual } from 'lodash';
import { EsCompanySearchRequest } from './model/EsCompanySearchRequest';
import { CompanySearchEsService } from './company-search-es.service';

jest.setTimeout(600000);
describe('CompanySearchService 集成测试', () => {
  let module: TestingModule;
  let companySearchService: CompanySearchService;
  let companySearchEsService: CompanySearchEsService;
  let entityManager: EntityManager;

  // 真实的企业ID
  const realCompanyId = 'f625a5b661058ba5082ca508f99ffe1b';

  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('company-search.service.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId, ProductCodeEnums.Pro);
  let testCompany: CompanyEntity;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [AppTestModule, TypeOrmModule.forFeature([CompanyEntity])],
      providers: [CompanySearchService, CompanyDetailService, CompanySearchEsService],
    }).compile();
    companySearchEsService = module.get<CompanySearchEsService>(CompanySearchEsService);
    companySearchService = module.get<CompanySearchService>(CompanySearchService);
    entityManager = module.get<EntityManager>(EntityManager);

    // 清理之前的测试数据
    await cleanupTestData();

    // 创建测试数据
    await prepareTestData();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData();
    const connection = getConnection();
    await connection.close();
  });

  /**
   * 准备测试数据
   */
  async function prepareTestData() {
    // 创建测试公司或使用现有公司
    const existingCompany = await entityManager.findOne(CompanyEntity, { where: { companyId: realCompanyId } });

    if (existingCompany) {
      testCompany = existingCompany;
    } else {
      // 获取真实的企业信息
      try {
        // 使用companySearchService来获取真实的企业数据
        const companyInfo = await companySearchService.createCompanyInfo(realCompanyId, '');
        testCompany = await entityManager.findOne(CompanyEntity, { where: { companyId: realCompanyId } });
      } catch (error) {
        console.error('获取真实企业信息失败:', error);
        // 创建一个基本的测试公司记录
        testCompany = new CompanyEntity();
        testCompany.companyId = realCompanyId;
        testCompany.name = '测试企业';
        testCompany.updateDate = new Date();
        await entityManager.save(testCompany);
      }
    }
  }

  /**
   * 清理测试数据
   */
  async function cleanupTestData() {
    try {
      // 不删除真实企业数据，仅在需要时清理创建的测试数据
      const existingCompany = await entityManager.findOne(CompanyEntity, { where: { companyId: realCompanyId } });
      if (existingCompany && existingCompany.name === '测试企业') {
        await entityManager.delete(CompanyEntity, { companyId: realCompanyId });
      }
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }

  describe('companyDetailsKys', () => {
    it('应该能成功获取企业KYS详情', async () => {
      // 调用真实服务
      const result = await companySearchService.companyDetailsKys(realCompanyId);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.status).toBe(200);
      expect(result.result).toBeDefined();
      expect(result.result.id).toBe(realCompanyId);
    });
  });

  describe('companyDetailsQcc', () => {
    it('应该能成功获取企业QCC详情', async () => {
      // 调用真实服务
      const result = await companySearchService.companyDetailsQcc(realCompanyId);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.KeyNo).toBe(realCompanyId);
    });

    it.skip('当传入社会组织ID时能够正确处理', async () => {
      // 这个测试需要特定的社会组织ID和环境配置，所以我们先跳过
      console.log('社会组织测试已跳过，需要实际的社会组织ID才能进行测试');
    });
  });

  describe('getLogoByKeyNos', () => {
    it('应该能成功获取企业Logo', async () => {
      // 调用真实服务
      const result = await companySearchService.getLogoByKeyNos([realCompanyId]);

      // 验证结果
      expect(result).toBeDefined();
    });
  });

  describe('getCompanyCertificationSummary', () => {
    it('应该能成功获取企业认证摘要', async () => {
      // 调用真实服务
      const result = await companySearchService.getCompanyCertificationSummary(realCompanyId);

      // 验证结果
      expect(result).toBeDefined();
    });
  });

  describe('getCompanyCertificationList', () => {
    it('应该能成功获取企业认证列表', async () => {
      // 准备请求参数
      const request = new SearchCertificationRequest();
      request.keyNo = realCompanyId;
      request.pageIndex = 1;
      request.pageSize = 10;

      // 调用真实服务
      const result = await companySearchService.getCompanyCertificationList(request);

      // 验证结果
      expect(result).toBeDefined();
    });
  });

  describe('companySearchForKys', () => {
    it('应该能成功通过KYS搜索企业', async () => {
      // 准备请求参数
      const request = new KysCompanySearchRequest();
      request.pageIndex = 1;
      request.pageSize = 10;
      request.filter = { ids: [realCompanyId] };

      // 调用真实服务
      const result = await companySearchService.companySearchForKys(request);

      // 验证结果 - 只验证结果已定义，不检查具体字段
      expect(result).toBeDefined();
      // 打印结果以便调试，但不检查特定字段
      console.log(`KYS搜索结果: ${JSON.stringify(result).substring(0, 100)}...`);
    });
  });

  describe('companySearchForQcc', () => {
    it('应该能成功通过QCC搜索企业', async () => {
      // 首先获取企业名称
      const companyDetails = await companySearchService.companyDetailsQcc(realCompanyId);
      const companyName = companyDetails?.Name;

      if (!companyName) {
        console.log('跳过测试: 无法获取企业名称');
        return;
      }

      // 准备请求对象
      const request = new SearchMultiSelectionRequest();
      request.searchKey = companyName;
      request.pageIndex = 1;
      request.pageSize = 10;

      // 调用真实服务
      const result = await companySearchService.companySearchForQcc(request);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Status).toBe(200);
    });
  });

  describe('createCompanyInfo', () => {
    it('应该能成功创建企业信息记录', async () => {
      // 调用真实服务
      const result = await companySearchService.createCompanyInfo(realCompanyId, '', true);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.companyId).toBe(realCompanyId);
    });
  });

  describe('getCompanyBusinessInfoMap', () => {
    it('应该能成功生成企业业务信息映射', async () => {
      // 准备测试数据
      const companies: CompanyBasicInfo[] = [{ companyId: realCompanyId, companyName: testCompany.name }];

      // 调用真实服务
      const result = await companySearchService.getCompanyBusinessInfoMap(companies);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.size).toBe(1);
      expect(result.get(realCompanyId)).toBeInstanceOf(CompanyBusinessInfo);
    });
  });

  describe('multiMatchCompany', () => {
    it('应该能成功进行多企业匹配', async () => {
      // 首先获取企业名称
      const companyDetails = await companySearchService.companyDetailsQcc(realCompanyId);
      const companyName = companyDetails?.Name;

      if (!companyName) {
        console.log('跳过测试: 无法获取企业名称');
        return;
      }

      // 准备测试数据
      const request = {
        searchKey: [companyName],
      };

      // 调用真实服务
      const result = await companySearchService.multiMatchCompany(testUser as PlatformUser, request);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.length).toBe(1);
      expect(result[0].searchKey).toBe(companyName);
    });
  });

  describe('matchCompanyInfo', () => {
    it('应该能成功匹配企业信息', async () => {
      // 首先获取企业名称
      const companyDetails = await companySearchService.companyDetailsQcc(realCompanyId);
      const companyName = companyDetails?.Name;

      if (!companyName) {
        console.log('跳过测试: 无法获取企业名称');
        return;
      }

      // 调用真实服务
      const result = await companySearchService.matchCompanyInfo([companyName]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.matchedCompanyInfos.length).toBeGreaterThan(0);
    });
  });

  describe('checkCompanyStandardCode', () => {
    it('应该能成功检查企业标准代码', async () => {
      // 调用真实服务
      const result = await companySearchService.checkCompanyStandardCode([realCompanyId]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.supportedCompanies).toBeDefined();
      // 这里不检查具体长度，只验证返回的结构
    });
  });

  describe.skip('DEBUG 测试', () => {
    it('getSupplierCustomerWithFreeText', async () => {
      const req = Object.assign(new SupplierCustomerWithFreeTextRequest(), {
        text: '浙江天松医疗器械股份有限公司',
        dataType: 0,
      });
      const newVar = await companySearchService.getSupplierCustomerWithFreeText(req);
      // console.log(JSON.stringify(newVar));
      expect(newVar).toBeDefined();
    });

    it('should be defined', async () => {
      const names = ['Meta Platforms, Inc.'];
      const allowType = ['0', '1', '11', '12'];
      const { matchedCompanyInfos, unmatchedNames, matchedNames } = await companySearchService.matchCompanyInfo(
        names,
        ['id', 'name', 'creditcode', 'regno', 'province', 'areacode', 'address', 'industry', 'subind', 'econkindcode'],
        allowType,
        true,
      );
      expect(matchedCompanyInfos.length).toBe(1);
      // expect(batchImportService).toBeDefined();
      // expect(messageService).toBeDefined();
    });

    it('get company contact', async () => {
      const companyId = '84c17a005a759a5e0d875c1ebb6c9846';
      const res = await companySearchService.getContact(companyId);
      expect(res).toBeDefined();
    });

    it('check company match', async () => {
      const companyNames = ['小米集團', '台湾威爾比有限公司', '昭平县凤凰乡政府机关单位工会'];
      const includesFields = [
        'id',
        'name',
        'creditcode',
        'regno',
        'province',
        'areacode',
        'address',
        'industry',
        'subind',
        'econkindcode',
        'registcapi',
        'startdatecode',
        'registcapiamount',
        'status',
      ];
      const allowType = ['0', '1', '11', '12'];
      const extraType = ['3', '4', '5'];
      //测试未加过滤条件前
      const response = await companySearchService.matchCompanyInfo(companyNames, includesFields, allowType);
      expect(response?.matchedNames?.length).toEqual(0);
      //测试未加过滤条件后
      Array.prototype.push.apply(allowType, extraType);
      const response2 = await companySearchService.matchCompanyInfo(companyNames, includesFields, allowType);
      expect(isEqual(companyNames.sort(), response2?.matchedNames.sort())).toBe(true);
      expect(companyNames?.length).toEqual(response2?.matchedCompanyInfos?.length);
      //测试普通大陆企业
      const baseCompanyNames = ['小米科技有限责任公司'];
      const response3 = await companySearchService.matchCompanyInfo(baseCompanyNames);
      expect(intersection(response3.matchedNames, baseCompanyNames).length).toBeGreaterThanOrEqual(1);
    });

    it('test deceaseCapiNotice', async () => {
      const data = await companySearchService.getCompanyCreditList({
        type: 'deceaseCapiNotice', // 减资公告
        keyNo: '7bbfb2e2842a1327f85f50492e6777a3',
        ids: '3c1c54157241940e9afd42f1c7394920',
      } as any);
      expect(data.ReturnFields.length).toBeGreaterThan(0);
    });

    it('test company search es', async () => {
      //测试 companySearchEsService.companySearch
      const clientRequest: EsCompanySearchRequest = new EsCompanySearchRequest();
      clientRequest.id = ['g82a07fe914ca69ea9290712862c9bef'];
      clientRequest.pageIndex = 1;
      clientRequest.pageSize = clientRequest.id.length;
      const companyName = '广东省机械技师学院';
      const response = await companySearchEsService.companySearch(clientRequest);
      expect(response.Result.length).toBeGreaterThan(0);
    });

    it('test company search client', async () => {
      //测试 companySearchEsService.companySearch
      const companyName = '广东省机械技师学院';
      const companyId = 'g82a07fe914ca69ea9290712862c9bef';
      const response = await companySearchService.searchCompanyDetail(
        [companyId],
        ['id', 'address', 'insuredcount', 'creditcode', 'commonlist', 'econkind', 'yysramount', 'credit_score', 't_type'],
      );
      expect(response).not.toBeNull();
    });

    it.skip('海外公司 companySearchForQCC', async () => {
      const query: SearchMultiSelectionRequest = Object.assign(new SearchCertificationRequest(), { searchKey: 'Meta Platforms, Inc.' });
      const data = await companySearchService.companySearchForQcc(query);
      // console.log(JSON.stringify(data));
      expect(data.Result[0].KeyNo).toBe('l001574ebd3d67ac5ea4f81e3cf3f90b');
    });

    it('大陆企业 companySearchForQCC', async () => {
      const query: SearchMultiSelectionRequest = Object.assign(new SearchCertificationRequest(), { searchKey: '小米科技有限责任公司' });
      const data = await companySearchService.companySearchForQcc(query);
      expect(data.Result[0].KeyNo).toBe('9cce0780ab7644008b73bc2120479d31');
    });

    it('大陆企业 companyDetailsFromKys', async () => {
      const data = await companySearchService.getCompanyDetails('g9b3aeba587377afa51bc34760a55f92');
      expect(data.result.name).toBe('福建省广播影视集团');
    });

    it('大陆企业 companyDetailsQcc', async () => {
      const data = await companySearchService.companyDetailsQcc('9cce0780ab7644008b73bc2120479d31');
      expect(data.Name).toBe('小米科技有限责任公司');
    });

    it('获取企业的  standardCode', async () => {
      const data = await companySearchService.companyDetailsQcc('baf89be015c1b63b4cfa6cfdbfc45c32');
      expect(data.Name).toBe('广州市白云莱茵化妆品厂');
      expect(data.standardCode[0]).toBe('001002');
    });

    it('test createCompanyInfo', async () => {
      const data = await companySearchService.createCompanyInfo('c8076b1e8a68e358bda232b2584c2ccb', '北京苏高新经济咨询服务中心', true);
      expect(data.companyId).toBe('c8076b1e8a68e358bda232b2584c2ccb');
    });

    it('test createCompanyInfoBatch', async () => {
      const requestMap = new Map();
      requestMap.set('03d954c73305781db38c99bdaf8fd160', '天津海泽源房地产开发有限公司');
      const response: CompanyEntity[] = await companySearchService.createCompanyInfoBatch(requestMap);
      expect(response.length).toBe(1);
      expect(response[0].companyId).toEqual('03d954c73305781db38c99bdaf8fd160');
      expect(response[0].name).toEqual('天津海泽源房地产开发有限公司');
    });

    it('company details standard_code', async () => {
      const data = await companySearchService.getCompanyDetails('g87f367ab97185a634f1899ed7415ed0');
      expect(data.result.name).toBe('北京北大英华科技有限公司工会委员会');
      expect(data.result?.['standard_code']).toBeUndefined();
    });

    /*  it('大陆企业 companySearchForKys', async () => {
        const data = await companyService.companySearchForKys(
          Object.assign(new KzzCompanySearchRequest(), {
            includeFields: ['id', 'credit_score', 'standard_code', 'isvalid', 'listingstatuskw', 'reccap', 'reccapamount'],
            pageIndex: 1,
            pageSize: 10,
            includeInvalid: true,
            filter: {
              ids: ['s70e1a9805f80a0f8191416f7f2c1303', 'ff42008c034f6037bfe3141e8796f7fe'],
            },
          }),
        );
        expect(data.Result[0].id).toBe('s70e1a9805f80a0f8191416f7f2c1303');
      });*/

    it('companyDetailsKys test', async () => {
      const data = await companySearchService.companyDetailsKys('84c17a005a759a5e0d875c1ebb6c9846');
      expect(data).toBeUndefined();
    });

    it('test doGetCompanyFinance', async () => {
      const response = await companySearchService.doGetCompanyFinance('9cce0780ab7644008b73bc2120479d31');
      expect(response).toBeDefined();
    });

    it('test searchFNCCompany', async () => {
      const response = await companySearchService.searchFNCCompany(['FNC_JR', 'FNC_BX', 'FNC_ZJ'], ['99']);
      expect(response).toBeDefined();
    });
  });
});
