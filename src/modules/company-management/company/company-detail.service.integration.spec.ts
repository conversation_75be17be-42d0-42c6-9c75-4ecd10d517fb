import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EntityManager, getConnection } from 'typeorm';
import { ConfigService } from '@core/config/config.service';
import { HttpUtilsService } from '@core/config/httputils.service';
import { CompanyEntity } from '@domain/entities/CompanyEntity';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { PlatformUser } from '@domain/model/common';
import { AppTestModule } from '@app/app.test.module';
import { generateUniqueTestIds, getTestUser } from '@testing/test.user';
import { CompanyDetailService } from './company-detail.service';
import { CompanySearchService } from './company-search.service';

jest.setTimeout(600000);

describe('CompanyDetailService 集成测试', () => {
  let module: TestingModule;
  let companyDetailService: CompanyDetailService;
  let companySearchService: CompanySearchService;
  let httpUtilsService: HttpUtilsService;
  let configService: ConfigService;
  let entityManager: EntityManager;

  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('company-detail.service.integration.spec.ts');
  const testUser: PlatformUser = getTestUser(testOrgId, testUserId, ProductCodeEnums.Pro);

  // 使用真实的企业ID
  const realCompanyId = 'f625a5b661058ba5082ca508f99ffe1b';

  // 测试公司对象
  let testCompany: CompanyEntity;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [AppTestModule, TypeOrmModule.forFeature([CompanyEntity])],
      providers: [CompanyDetailService, CompanySearchService],
    }).compile();

    companyDetailService = module.get<CompanyDetailService>(CompanyDetailService);
    companySearchService = module.get<CompanySearchService>(CompanySearchService);
    httpUtilsService = module.get<HttpUtilsService>(HttpUtilsService);
    configService = module.get<ConfigService>(ConfigService);
    entityManager = module.get<EntityManager>(EntityManager);

    // 清理之前的测试数据
    await cleanupTestData();

    // 准备测试数据
    await prepareTestData();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData();
    const connection = getConnection();
    await connection.close();
  });

  /**
   * 准备测试数据
   */
  async function prepareTestData() {
    // 创建测试公司或使用现有公司
    const existingCompany = await entityManager.findOne(CompanyEntity, { where: { companyId: realCompanyId } });

    if (existingCompany) {
      testCompany = existingCompany;
    } else {
      // 获取真实的企业信息
      try {
        // 使用companySearchService来获取真实的企业数据
        await companySearchService.createCompanyInfo(realCompanyId, '');
        testCompany = await entityManager.findOne(CompanyEntity, { where: { companyId: realCompanyId } });
      } catch (error) {
        console.error('获取真实企业信息失败:', error);
        // 创建一个基本的测试公司记录
        testCompany = new CompanyEntity();
        testCompany.companyId = realCompanyId;
        testCompany.name = '测试企业';
        testCompany.updateDate = new Date();
        await entityManager.save(testCompany);
      }
    }
  }

  /**
   * 清理测试数据
   */
  async function cleanupTestData() {
    try {
      // 不删除真实企业数据，仅在需要时清理创建的测试数据
      const existingCompany = await entityManager.findOne(CompanyEntity, { where: { companyId: realCompanyId } });
      if (existingCompany && existingCompany.name === '测试企业') {
        await entityManager.delete(CompanyEntity, { companyId: realCompanyId });
      }
    } catch (error) {
      console.error('清理测试数据失败:', error);
    }
  }

  describe('getCompanyAllChattelMortgage 方法测试', () => {
    it('应该能成功获取所有动产抵押信息', async () => {
      // 当需要mock时，我们使用spy并返回预期结果
      jest.spyOn(httpUtilsService, 'getRequest').mockResolvedValue({
        Result: {
          Items: [
            { id: 1, title: '测试动产抵押1' },
            { id: 2, title: '测试动产抵押2' },
          ],
        },
      });

      // When
      const result = await companyDetailService.getCompanyAllChattelMortgage(realCompanyId);

      // Then
      expect(Array.isArray(result.Items)).toBeTruthy();
      expect(result.Items.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('getFinancialInstitutions 方法测试', () => {
    it('应该能成功获取金融机构标签', async () => {
      // 当需要mock时，我们使用spy并返回预期结果
      jest.spyOn(httpUtilsService, 'postRequest').mockResolvedValue({
        Result: {
          Tags: [
            { id: 1, name: '银行' },
            { id: 2, name: '保险' },
          ],
        },
      });

      // When
      const result = await companyDetailService.getFinancialInstitutions(realCompanyId);

      // Then
      expect(result).toBeDefined();
    });
  });

  describe('公司基本信息测试', () => {
    it('应该能找到公司信息', () => {
      // Then
      expect(testCompany).toBeDefined();
      expect(testCompany.companyId).toBe(realCompanyId);
    });
  });

  describe('DEBUG 测试', () => {
    it('ChangeRecords()', async () => {
      const result = await companyDetailService.ChangeRecords({
        keyNo: '9cce0780ab7644008b73bc2120479d31',
        pageSize: 10,
        pageIndex: 1,
      });
      expect(result).not.toBeNull();
    });

    it('getControlCompany()', async () => {
      const result = await companyDetailService.getInvestCompany('9cce0780ab7644008b73bc2120479d31', 2);
      expect(result).not.toBeNull();
    });
  });
});
