import { AppTestModule } from '@app/app.test.module';
import { QueueService } from '@core/config/queue.service';
import { BatchDiligenceEntity } from '@domain/entities/BatchDiligenceEntity';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { BatchJobEntity } from '@domain/entities/BatchJobEntity';
import { BatchMatchCompanyEntity } from '@domain/entities/BatchMatchCompanyEntity';
import { BatchMatchCompanyItemEntity } from '@domain/entities/BatchMatchCompanyItemEntity';
import { BatchResultEntity } from '@domain/entities/BatchResultEntity';
import { CompanyEntity } from '@domain/entities/CompanyEntity';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';
import { MonitorCompanyEntity } from '@domain/entities/MonitorCompanyEntity';
import { MonitorGroupEntity } from '@domain/entities/MonitorGroupEntity';
import { MonitorMetricsDynamicEntity } from '@domain/entities/MonitorMetricsDynamicEntity';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { UserEntity } from '@domain/entities/UserEntity';
import { BatchJobResultTypeEnums } from '@domain/enums/batch/BatchJobResultTypeEnums';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { DistributedResourceTypeEnums } from '@domain/enums/DistributedResourceTypeEnums';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { RiskModelTypeEnums } from '@domain/enums/RiskModelTypeEnums';
import { JobMonitorMessagePO, JobMonitorMessageTypeEnums } from '@domain/model/batch/po/message/JobMonitorMessagePO';
import { SnapshotStatus } from '@domain/model/diligence/SnapshotDetail';
import { QaTaskService } from '@modules/ai-intelligence/qa/qa.task.service';
import { CompanySearchService } from '@modules/company-management/company/company-search.service';
import { EvaluationService } from '@modules/risk-assessment/diligence/evaluation/evaluation.service';
import { DiligenceSnapshotEsService } from '@modules/risk-assessment/diligence/snapshot/diligence.snapshot.es.service';
import { DiligenceSnapshotHelper } from '@modules/risk-assessment/diligence/snapshot/diligence.snapshot.helper';
import { MonitorJobService } from '@modules/risk-assessment/monitor/monitor.job.service';
import { MonitorModule } from '@modules/risk-assessment/monitor/monitor.module';
import { MonitorCompanyMessagePO } from '@modules/risk-assessment/monitor/po/MonitorCompanyMessagePO';
import { RiskModelTemplateEnum } from '@modules/risk-assessment/risk-model/init_mode/constants/risk-model-init.enums';
import { RiskModelInitV2Service } from '@modules/risk-assessment/risk-model/init_mode/v2/risk-model-init-v2.service';
import { RiskModelModule } from '@modules/risk-assessment/risk-model/risk_model.module';
import { DimensionFieldsService } from '@modules/system/dimension/dimension.fields.service';
import { DimensionService } from '@modules/system/dimension/dimension.service';
import { UserService } from '@modules/user-management/user/user.service';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { clearDiligenceTestData, prepareTestDiligenceData } from '@testing/diligence.test.utils';
import { clearMonitorGroupTestData, generateMonitorGroupTestData } from '@testing/monitor.test.tools';
import { clearAllTestRiskModelTestData } from '@testing/riskmodel.test.utils';
import { generateUniqueTestIds, getTestUser } from '@testing/test.user';
import { Brackets, EntityManager, getConnection } from 'typeorm';
import { BatchModule } from '../batch.module';
import { BatchMessageService } from '../message.handler/batch.message.service';
import { BatchMessageHandlerDiligence } from '../message.handler/diligence/batch.message.handler.diligence';
import { BatchDiligenceProcessor } from '../message.handler/diligence/processors/batch.diligence.processor';
import { ContinuousDiligenceProcessor } from '../message.handler/diligence/processors/continuous.diligence.processor';
import { BatchMessageHandlerVerification } from '../message.handler/verification/batch.message.handler.verification';
import { BatchResultExportService } from '../service/batch.result.export.service';
import { BatchBaseHelper } from '../service/helper/batch.base.helper';

/**
 * 如果是一个监控的batch任务集成测试
 * 在执行任务的时候需要，把获取监控的尽调的数据mock
 */
jest.setTimeout(600000);
describe('监控的Batch任务 集成测试', () => {
  let entityManager: EntityManager;
  let batchMessageHandlerDiligence: BatchMessageHandlerDiligence;
  let batchMessageService: BatchMessageService;
  let riskModelInitV2Service: RiskModelInitV2Service;
  let monitorJobService: MonitorJobService;
  let queueService: QueueService;
  let evaluationService: EvaluationService;

  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('batch.monitor.service.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId, ProductCodeEnums.Pro);
  let testMonitorGroup: MonitorGroupEntity;
  let testRiskModel: any;
  let diligenceHistoryEntity: DiligenceHistoryEntity;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        AppTestModule,
        BatchModule,
        RiskModelModule,
        MonitorModule,
        TypeOrmModule.forFeature([
          BatchEntity,
          BatchJobEntity,
          BatchResultEntity,
          BatchDiligenceEntity,
          DiligenceHistoryEntity,
          CompanyEntity,
          BatchMatchCompanyItemEntity,
          BatchMatchCompanyEntity,
          UserEntity,
          MonitorGroupEntity,
          MonitorCompanyEntity,
          MonitorMetricsDynamicEntity,
          RiskModelEntity,
        ]),
      ],
      providers: [
        MonitorJobService,
        RiskModelInitV2Service,
        BatchMessageService,
        BatchMessageHandlerDiligence,
        BatchBaseHelper,
        BatchDiligenceProcessor,
        ContinuousDiligenceProcessor,
        BatchResultExportService,
        {
          provide: DiligenceSnapshotHelper,
          useValue: jest.fn(),
        },
        {
          provide: EvaluationService,
          useValue: {
            runMonitorRisk: jest.fn(),
          },
        },
        {
          provide: CompanySearchService,
          useValue: jest.fn(),
        },
        {
          provide: DimensionFieldsService,
          useValue: jest.fn(),
        },
        {
          provide: DimensionService,
          useValue: jest.fn(),
        },
        {
          provide: DiligenceSnapshotEsService,
          useValue: jest.fn(),
        },
        {
          provide: UserService,
          useValue: {
            getAdminList: jest.fn().mockResolvedValue([{ userId: testUserId }]),
          },
        },
        {
          provide: BatchMessageHandlerVerification,
          useValue: {
            getTargetQueue: jest.fn().mockReturnValue({ queueName: 'mock-verification-queue' }),
            getProperlyProcessor: jest.fn(),
            refreshBatchStatus: jest.fn(),
            onBatchSuccess: jest.fn(),
            onBatchError: jest.fn(),
          },
        },
        {
          provide: QaTaskService,
          useValue: {
            createQaTask: jest.fn(),
            updateQaTask: jest.fn(),
            getQaTask: jest.fn(),
            onTaskFinished: jest.fn(),
            onTaskError: jest.fn(),
          },
        },
      ],
    }).compile();
    entityManager = module.get<EntityManager>(EntityManager);
    queueService = module.get<QueueService>(QueueService);

    riskModelInitV2Service = module.get<RiskModelInitV2Service>(RiskModelInitV2Service);
    monitorJobService = module.get<MonitorJobService>(MonitorJobService);
    batchMessageService = module.get<BatchMessageService>(BatchMessageService);
    batchMessageHandlerDiligence = module.get<BatchMessageHandlerDiligence>(BatchMessageHandlerDiligence);
    evaluationService = module.get<EvaluationService>(EvaluationService);
    // 清理之前的测试数据
    await cleanupTestData();
    // 创建测试数据
    await prepareTestData();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    try {
      // 先清理测试数据，再关闭连接
      await cleanupTestData();
    } catch (error) {
      console.warn('清理测试数据时出错:', error.message);
    }

    try {
      const connection = getConnection();
      if (connection.isConnected) {
        await connection.close();
      }
    } catch (error) {
      console.warn('关闭数据库连接时出错:', error.message);
    }
  });

  /**
   * 准备测试数据
   * 创建监控模型，创建分组，创建分组企业
   */
  async function prepareTestData() {
    // 创建风险模型，使用时间戳确保名称唯一
    const timestamp = new Date().getTime();
    const modelName = `监控模型_${testOrgId}_${timestamp}`;
    testRiskModel = await riskModelInitV2Service.initModel(
      RiskModelTemplateEnum.MONITOR_DEFAULT,
      testUser,
      modelName,
      testUser.currentOrg,
      RiskModelTypeEnums.MonitorModel,
    );
    testRiskModel = await entityManager.findOne(RiskModelEntity, testRiskModel.modelId, {
      relations: [
        'groups',
        'groups.groupMetrics',
        'groups.groupMetrics.metricEntity',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.dimensionDef',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.strategyFields',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.strategyFields.dimensionField',
      ],
      cache: false,
    });
    testMonitorGroup = await generateMonitorGroupTestData(entityManager, testUser, testRiskModel.modelId);
    const diligenceList = await prepareTestDiligenceData(entityManager, {
      size: 4,
      overrideSnapshotDetails: false,
      orgId: testOrgId,
      userIds: [testUserId],
    });
    diligenceHistoryEntity = diligenceList.find((d) => d.snapshotDetails.status === SnapshotStatus.SUCCESS);
  }

  /**
   * 清理测试数据
   * 清除监控模型，清除分组，清除企业数据
   */
  async function cleanupTestData() {
    try {
      if (entityManager && testUser) {
        await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.MonitorRiskModel);
        await clearMonitorGroupTestData(entityManager, testUser);
      } else {
        console.warn('EntityManager or testUser is undefined, skipping cleanup');
      }
    } catch (error) {
      console.warn('清理测试数据时出错:', error.message);
    }
  }

  describe('【执行一次监控的batch任务】processJobMonitorMessage()', () => {
    let batchEntity;
    beforeEach(async () => {
      jest.clearAllMocks();
      // 创建一个监控的batch任务
      const spy1 = jest.spyOn(queueService.batchJobMonitorQueue, 'sendMessageV2').mockImplementation(async (msgBody: JobMonitorMessagePO) => {
        //await batchMessageService.processJobMonitorMessage(msgBody);
        console.log('batchMessageService.processJobMonitorMessage', msgBody);
      });
      batchEntity = await monitorJobService.startBatchForGroup(
        Object.assign(new MonitorCompanyMessagePO(), {
          orgId: testOrgId,
          product: ProductCodeEnums.Pro,
          monitorGroupId: testMonitorGroup.monitorGroupId,
          currentUser: testUser,
        }),
      );
      expect(spy1).toHaveBeenCalled();

      const batchJobEntityList = await entityManager.find(BatchJobEntity, {
        where: {
          batchId: batchEntity.batchId,
        },
      });
      expect(batchJobEntityList.length).toBeGreaterThan(0);
      // 创建batchdilligence
      await entityManager.save(
        BatchDiligenceEntity,
        batchJobEntityList.map((t) => {
          return Object.assign(new BatchDiligenceEntity(), {
            batchId: batchEntity.batchId,
            jobId: t.jobId,
            diligenceId: diligenceHistoryEntity.id,
          });
        }),
      );
    });

    afterEach(async () => {
      jest.clearAllMocks();
      // 清除当前batch产生的数据
      clearDiligenceTestData(entityManager, testUser.currentOrg);
    });

    it('模拟监控处理一个监控的batch任务——假设尽调结果正常返回【执行成功-付费】', async () => {
      expect(batchEntity).not.toBeNull();
      const batchDilligenceList = await entityManager.find(BatchDiligenceEntity, { batchId: batchEntity.batchId });
      expect(batchDilligenceList.length).toBeGreaterThan(0);
      await entityManager.delete(BatchDiligenceEntity, batchDilligenceList);
      const batchJobEntityList = await entityManager.find(BatchJobEntity, { batchId: batchEntity.batchId });
      expect(batchJobEntityList.length).toBeGreaterThan(0);
      const batchResult = await entityManager.findOne(BatchResultEntity, {
        batchId: batchEntity.batchId,
      });
      expect(batchResult).not.toBeNull();
      expect(batchEntity.status).toEqual(BatchStatusEnums.Waiting);
      // 发送消息JobMonitorMessageTypeEnums.Scan
      const msgBody: JobMonitorMessagePO = {
        batchId: batchEntity.batchId,
        batchType: batchEntity.batchType,
        businessType: batchEntity.businessType,
        startDate: Date.now(),
        batchTotalRecords: batchEntity.recordCount,
        operatorId: testUser.userId,
        orgId: testUser.currentOrg,
        index: 1,
        type: JobMonitorMessageTypeEnums.Scan,
        isUpdate: batchEntity.batchInfo?.isUpdate,
        product: batchEntity.product,
      };

      // 根据batch任务的类型调用不同的处理service发送一个BatchJob消息
      const spy2 = jest.spyOn(queueService.batchDiligenceQueue, 'sendMessageV2').mockImplementation(async (messageData: JobMonitorMessagePO) => {
        await batchMessageHandlerDiligence.handleJobMessage(messageData);
      });
      // 监控查询数据mock
      const monitorRiskResult = jest.spyOn(evaluationService, 'runMonitorRisk').mockReturnValue(diligenceHistoryEntity as any);
      // 监控动态消息处理mock
      const spy3 = jest.spyOn(queueService.continuousDiligenceAnalyzeQueue, 'sendMessageV2').mockImplementation(async (msgBody: JobMonitorMessagePO) => {
        console.log('queueService.continuousDiligenceAnalyzeQueue.sendMessageV2', msgBody);
      });
      // 循环发现有没有发送的batchjob，就创建一个一个scan任务
      let lastProceedJobId = 0;
      let jobs: BatchJobEntity[] = [];
      let batchIndex = 1;
      // 5个一批发送处理消息
      do {
        const newMessage = Object.assign(new JobMonitorMessagePO(), msgBody);
        newMessage.index = batchIndex;
        newMessage.lastProceedJobId = lastProceedJobId;

        const qb = await entityManager.createQueryBuilder(BatchJobEntity, 'job');
        qb.where('job.batchId = :batchId', { batchId: batchEntity.batchId });

        if (lastProceedJobId > 0) {
          qb.andWhere('job.jobId > :lastProceedJobId', { lastProceedJobId });
        }

        qb.andWhere(
          new Brackets((qb1) => {
            qb1
              .where('job.status = :status1', { status1: BatchStatusEnums.Waiting })
              .orWhere('(job.status = :status2 and (job.startDate is null or TIMESTAMPDIFF(MINUTE, job.startDate, NOW()) > 5))', {
                status2: BatchStatusEnums.Processing,
              });
          }),
        );

        jobs = await qb.take(5).orderBy('job.jobId', 'ASC').getMany();

        if (jobs.length > 0) {
          // 更新lastProceedJobId为当前批次最后一个job的ID
          lastProceedJobId = jobs[jobs.length - 1].jobId;

          // 处理当前批次的jobs
          await batchMessageService.processJobMonitorMessage(newMessage);

          // 增加批次索引
          batchIndex++;
        }
      } while (jobs.length > 0);

      // 所有数据处理完毕，发送监控消息
      const finalMessage = Object.assign(new JobMonitorMessagePO(), msgBody);
      finalMessage.lastProceedJobId = -1;
      finalMessage.type = JobMonitorMessageTypeEnums.Monitor;
      finalMessage.index = batchIndex;
      await batchMessageService.processJobMonitorMessage(finalMessage);
      expect(monitorRiskResult).toHaveBeenCalled();
      expect(spy2).toHaveBeenCalled();
      expect(spy3).toHaveBeenCalled();

      const resultBatchEntity = await entityManager.findOne(BatchEntity, { batchId: batchEntity.batchId });
      expect(resultBatchEntity).not.toBeNull();
      expect(resultBatchEntity.status).toEqual(BatchStatusEnums.Done);
      const resultBatchResultEntity = await entityManager.findOne(BatchResultEntity, { batchId: batchEntity.batchId });
      expect(resultBatchResultEntity).not.toBeNull();
      expect(resultBatchResultEntity.resultType).toEqual(BatchJobResultTypeEnums.SUCCEED_PAID); // batch_dilligence
      const resultBatchJobEntityList = await entityManager.find(BatchJobEntity, { batchId: batchEntity.batchId });
      expect(resultBatchJobEntityList).not.toBeNull();
      resultBatchJobEntityList.forEach((t) => {
        expect(t.status).toEqual(BatchStatusEnums.Done);
      });
      const resultBatchDiligenceEntityList = await entityManager.find(BatchDiligenceEntity, { batchId: batchEntity.batchId });
      expect(resultBatchDiligenceEntityList.length).toBeGreaterThan(0);
      expect(resultBatchDiligenceEntityList[0].diligenceId).toEqual(diligenceHistoryEntity.id);
    });

    it('模拟监控处理一个监控的batch任务——假设尽调结果正常返回【执行成功-数据重复】', async () => {
      expect(batchEntity).not.toBeNull();
      const batchJobEntityList = await entityManager.find(BatchJobEntity, { batchId: batchEntity.batchId });
      expect(batchJobEntityList.length).toBeGreaterThan(0);
      const batchResult = await entityManager.findOne(BatchResultEntity, {
        batchId: batchEntity.batchId,
      });
      expect(batchResult).not.toBeNull();
      expect(batchEntity.status).toEqual(BatchStatusEnums.Waiting);
      // 发送消息JobMonitorMessageTypeEnums.Scan
      const msgBody: JobMonitorMessagePO = {
        batchId: batchEntity.batchId,
        batchType: batchEntity.batchType,
        businessType: batchEntity.businessType,
        startDate: Date.now(),
        batchTotalRecords: batchEntity.recordCount,
        operatorId: testUser.userId,
        orgId: testUser.currentOrg,
        index: 1,
        type: JobMonitorMessageTypeEnums.Scan,
        isUpdate: batchEntity.batchInfo?.isUpdate,
        product: batchEntity.product,
      };

      // 根据batch任务的类型调用不同的处理service发送一个BatchJob消息
      const spy2 = jest.spyOn(queueService.batchDiligenceQueue, 'sendMessageV2').mockImplementation(async (messageData: JobMonitorMessagePO) => {
        await batchMessageHandlerDiligence.handleJobMessage(messageData);
      });
      // 监控查询数据mock
      const monitorRiskResult = jest.spyOn(evaluationService, 'runMonitorRisk').mockReturnValue(diligenceHistoryEntity as any);
      // 监控动态消息处理mock
      const spy3 = jest.spyOn(queueService.continuousDiligenceAnalyzeQueue, 'sendMessageV2').mockImplementation(async (msgBody: JobMonitorMessagePO) => {
        console.log('queueService.continuousDiligenceAnalyzeQueue.sendMessageV2', msgBody);
      });
      // 循环发现有没有发送的batchjob，就创建一个一个scan任务
      let lastProceedJobId = 0;
      let jobs: BatchJobEntity[] = [];
      let batchIndex = 1;
      // 5个一批发送处理消息
      do {
        const newMessage = Object.assign(new JobMonitorMessagePO(), msgBody);
        newMessage.index = batchIndex;
        newMessage.lastProceedJobId = lastProceedJobId;

        const qb = await entityManager.createQueryBuilder(BatchJobEntity, 'job');
        qb.where('job.batchId = :batchId', { batchId: batchEntity.batchId });

        if (lastProceedJobId > 0) {
          qb.andWhere('job.jobId > :lastProceedJobId', { lastProceedJobId });
        }

        qb.andWhere(
          new Brackets((qb1) => {
            qb1
              .where('job.status = :status1', { status1: BatchStatusEnums.Waiting })
              .orWhere('(job.status = :status2 and (job.startDate is null or TIMESTAMPDIFF(MINUTE, job.startDate, NOW()) > 5))', {
                status2: BatchStatusEnums.Processing,
              });
          }),
        );

        jobs = await qb.take(5).orderBy('job.jobId', 'ASC').getMany();

        if (jobs.length > 0) {
          // 更新lastProceedJobId为当前批次最后一个job的ID
          lastProceedJobId = jobs[jobs.length - 1].jobId;

          // 处理当前批次的jobs
          await batchMessageService.processJobMonitorMessage(newMessage);

          // 增加批次索引
          batchIndex++;
        }
      } while (jobs.length > 0);

      // 所有数据处理完毕，发送监控消息
      const finalMessage = Object.assign(new JobMonitorMessagePO(), msgBody);
      finalMessage.lastProceedJobId = -1;
      finalMessage.type = JobMonitorMessageTypeEnums.Monitor;
      finalMessage.index = batchIndex;
      await batchMessageService.processJobMonitorMessage(finalMessage);
      expect(monitorRiskResult).toHaveBeenCalled();
      expect(spy2).toHaveBeenCalled();
      expect(spy3).toHaveBeenCalled();

      const resultBatchEntity = await entityManager.findOne(BatchEntity, { batchId: batchEntity.batchId });
      expect(resultBatchEntity).not.toBeNull();
      expect(resultBatchEntity.status).toEqual(BatchStatusEnums.Done);
      const resultBatchResultEntity = await entityManager.findOne(BatchResultEntity, { batchId: batchEntity.batchId });
      expect(resultBatchResultEntity).not.toBeNull();
      expect(resultBatchResultEntity.resultType).toEqual(BatchJobResultTypeEnums.SUCCEED_DUPLICATED); // batch_dilligence
      const resultBatchJobEntityList = await entityManager.find(BatchJobEntity, { batchId: batchEntity.batchId });
      expect(resultBatchJobEntityList).not.toBeNull();
      resultBatchJobEntityList.forEach((t) => {
        expect(t.status).toEqual(BatchStatusEnums.Done);
      });
      const resultBatchDiligenceEntityList = await entityManager.find(BatchDiligenceEntity, { batchId: batchEntity.batchId });
      expect(resultBatchDiligenceEntityList.length).toBeGreaterThan(0);
      expect(resultBatchDiligenceEntityList[0].diligenceId).toEqual(diligenceHistoryEntity.id);
    });

    it('模拟监控处理一个监控的batch任务——假设尽调结果异常返回【执行失败 （代码执行过程中失败）】', async () => {
      expect(batchEntity).not.toBeNull();
      const batchJobEntityList = await entityManager.find(BatchJobEntity, { batchId: batchEntity.batchId });
      expect(batchJobEntityList.length).toBeGreaterThan(0);
      const batchResult = await entityManager.findOne(BatchResultEntity, {
        batchId: batchEntity.batchId,
      });
      expect(batchResult).not.toBeNull();
      expect(batchEntity.status).toEqual(BatchStatusEnums.Waiting);

      // 发送消息JobMonitorMessageTypeEnums.Scan
      const msgBody: JobMonitorMessagePO = {
        batchId: batchEntity.batchId,
        batchType: batchEntity.batchType,
        businessType: batchEntity.businessType,
        startDate: Date.now(),
        batchTotalRecords: batchEntity.recordCount,
        operatorId: testUser.userId,
        orgId: testUser.currentOrg,
        index: 1,
        type: JobMonitorMessageTypeEnums.Scan,
        isUpdate: batchEntity.batchInfo?.isUpdate,
        product: batchEntity.product,
      };

      // 根据batch任务的类型调用不同的处理service发送一个BatchJob消息
      const spy2 = jest.spyOn(queueService.batchDiligenceQueue, 'sendMessageV2').mockImplementation(async (messageData: JobMonitorMessagePO) => {
        await batchMessageHandlerDiligence.handleJobMessage(messageData);
      });
      // 监控查询数据mock， 模拟尽调返回一个异常
      const monitorRiskResult = jest.spyOn(evaluationService, 'runMonitorRisk').mockRejectedValue(new Error('模拟尽调异常'));
      // 监控动态消息处理mock
      const spy3 = jest.spyOn(queueService.continuousDiligenceAnalyzeQueue, 'sendMessageV2').mockImplementation(async (msgBody: JobMonitorMessagePO) => {
        console.log('queueService.continuousDiligenceAnalyzeQueue.sendMessageV2', msgBody);
      });
      // 循环发现有没有发送的batchjob，就创建一个一个scan任务
      let lastProceedJobId = 0;
      let jobs: BatchJobEntity[] = [];
      let batchIndex = 1;
      // 5个一批发送处理消息
      do {
        const newMessage = Object.assign(new JobMonitorMessagePO(), msgBody);
        newMessage.index = batchIndex;
        newMessage.lastProceedJobId = lastProceedJobId;

        const qb = await entityManager.createQueryBuilder(BatchJobEntity, 'job');
        qb.where('job.batchId = :batchId', { batchId: batchEntity.batchId });

        if (lastProceedJobId > 0) {
          qb.andWhere('job.jobId > :lastProceedJobId', { lastProceedJobId });
        }

        qb.andWhere(
          new Brackets((qb1) => {
            qb1
              .where('job.status = :status1', { status1: BatchStatusEnums.Waiting })
              .orWhere('(job.status = :status2 and (job.startDate is null or TIMESTAMPDIFF(MINUTE, job.startDate, NOW()) > 5))', {
                status2: BatchStatusEnums.Processing,
              });
          }),
        );

        jobs = await qb.take(5).orderBy('job.jobId', 'ASC').getMany();

        if (jobs.length > 0) {
          // 更新lastProceedJobId为当前批次最后一个job的ID
          lastProceedJobId = jobs[jobs.length - 1].jobId;

          // 处理当前批次的jobs
          await batchMessageService.processJobMonitorMessage(newMessage);

          // 增加批次索引
          batchIndex++;
        }
      } while (jobs.length > 0);

      // 所有数据处理完毕，发送监控消息
      const finalMessage = Object.assign(new JobMonitorMessagePO(), msgBody);
      finalMessage.lastProceedJobId = -1;
      finalMessage.type = JobMonitorMessageTypeEnums.Monitor;
      finalMessage.index = batchIndex;
      await batchMessageService.processJobMonitorMessage(finalMessage);
      expect(monitorRiskResult).toHaveBeenCalled();
      expect(spy2).toHaveBeenCalled();
      expect(spy3).toHaveBeenCalled();

      const resultBatchEntity = await entityManager.findOne(BatchEntity, { batchId: batchEntity.batchId });
      expect(resultBatchEntity).not.toBeNull();
      expect(resultBatchEntity.status).toEqual(BatchStatusEnums.Done);
      const resultBatchResultEntity = await entityManager.findOne(BatchResultEntity, { batchId: batchEntity.batchId });
      expect(resultBatchResultEntity).not.toBeNull();
      expect(resultBatchResultEntity.resultType).toEqual(BatchJobResultTypeEnums.FAILED_CODE);
      const resultBatchJobEntityList = await entityManager.find(BatchJobEntity, { batchId: batchEntity.batchId });
      expect(resultBatchJobEntityList).not.toBeNull();
      resultBatchJobEntityList.forEach((t) => {
        expect(t.status).toEqual(BatchStatusEnums.Done);
      });
      const resultBatchDiligenceEntityList = await entityManager.find(BatchDiligenceEntity, { batchId: batchEntity.batchId });
      expect(resultBatchDiligenceEntityList.length).toBeGreaterThan(0);
      expect(resultBatchDiligenceEntityList[0].diligenceId).toEqual(diligenceHistoryEntity.id);
    });
  });
});
