import { AppTestModule } from '@app/app.test.module';
import { QueueService } from '@core/config/queue.service';
import { BatchDiligenceEntity } from '@domain/entities/BatchDiligenceEntity';
import { BatchEntity } from '@domain/entities/BatchEntity';
import { BatchJobEntity } from '@domain/entities/BatchJobEntity';
import { BatchMatchCompanyEntity } from '@domain/entities/BatchMatchCompanyEntity';
import { BatchMatchCompanyItemEntity } from '@domain/entities/BatchMatchCompanyItemEntity';
import { BatchResultEntity } from '@domain/entities/BatchResultEntity';
import { CompanyEntity } from '@domain/entities/CompanyEntity';
import { DiligenceHistoryEntity } from '@domain/entities/DiligenceHistoryEntity';
import { MonitorCompanyEntity } from '@domain/entities/MonitorCompanyEntity';
import { MonitorGroupEntity } from '@domain/entities/MonitorGroupEntity';
import { MonitorMetricsDynamicEntity } from '@domain/entities/MonitorMetricsDynamicEntity';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { UserEntity } from '@domain/entities/UserEntity';
import { BatchBusinessTypeEnums } from '@domain/enums/batch/BatchBusinessTypeEnums';
import { BatchStatusEnums } from '@domain/enums/batch/BatchStatusEnums';
import { DistributedResourceTypeEnums } from '@domain/enums/DistributedResourceTypeEnums';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { RiskModelTypeEnums } from '@domain/enums/RiskModelTypeEnums';
import { BatchExportMessagePO, ExportConditionBase, ExportConditionRequest } from '@domain/model/batch/po/message/BatchExportMessagePO';
import { SnapshotStatus } from '@domain/model/diligence/SnapshotDetail';
import { MessageService } from '@modules/communication/message/message.service';
import { CompanySearchService } from '@modules/company-management/company/company-search.service';
import { DiligencePDFService } from '@modules/risk-assessment/diligence/diligence.pdf.service';
import { EvaluationService } from '@modules/risk-assessment/diligence/evaluation/evaluation.service';
import { DiligenceSnapshotEsService } from '@modules/risk-assessment/diligence/snapshot/diligence.snapshot.es.service';
import { DiligenceSnapshotHelper } from '@modules/risk-assessment/diligence/snapshot/diligence.snapshot.helper';
import { DiligenceSnapshotService } from '@modules/risk-assessment/diligence/snapshot/diligence.snapshot.service';
import { RiskModelTemplateEnum } from '@modules/risk-assessment/risk-model/init_mode/constants/risk-model-init.enums';
import { RiskModelInitV2Service } from '@modules/risk-assessment/risk-model/init_mode/v2/risk-model-init-v2.service';
import { RiskModelModule } from '@modules/risk-assessment/risk-model/risk_model.module';
import MyOssService from '@modules/system/basic/my-oss.service';
import { DimensionFieldsService } from '@modules/system/dimension/dimension.fields.service';
import { DimensionService } from '@modules/system/dimension/dimension.service';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { clearDiligenceTestData, prepareTestDiligenceData } from '@testing/diligence.test.utils';
import { clearAllTestRiskModelTestData } from '@testing/riskmodel.test.utils';
import { generateUniqueTestIds, getTestUser } from '@testing/test.user';
import { EntityManager, getConnection } from 'typeorm';
import { QaTaskService } from '../../ai-intelligence/qa/qa.task.service';
import { BatchModule } from '../batch.module';
import { BatchMessageService } from '../message.handler/batch.message.service';
import { BatchMessageHandlerDiligence } from '../message.handler/diligence/batch.message.handler.diligence';
import { BatchDiligenceProcessor } from '../message.handler/diligence/processors/batch.diligence.processor';
import { ContinuousDiligenceProcessor } from '../message.handler/diligence/processors/continuous.diligence.processor';
import { BatchMessageHandlerExport } from '../message.handler/export/batch.message.handler.export';
import { DiligenceExportProcessor } from '../message.handler/export/processors/diligence.export.processor';
import { VerificationExportProcessor } from '../message.handler/export/processors/verification.export.processor';
import { BatchMessageHandlerVerification } from '../message.handler/verification/batch.message.handler.verification';
import { BatchResultExportService } from '../service/batch.result.export.service';
import { BatchService } from '../service/batch.service';
import { BatchBaseHelper } from '../service/helper/batch.base.helper';

/**
 * 如果是一个PDF的batch任务集成测试
 * 在执行任务的时候需要，假设已经完成了尽调的快照
 *
 */
jest.setTimeout(600000);
describe('导出PDF的Batch任务 集成测试', () => {
  let entityManager: EntityManager;
  let batchBaseHelper: BatchBaseHelper;
  let batchMessageService: BatchMessageService;
  let riskModelInitService: RiskModelInitV2Service;
  let queueService: QueueService;
  let batchResultExportService: BatchResultExportService;
  let batchDiligenceProcessor: BatchDiligenceProcessor;
  let continuousDiligenceProcessor: ContinuousDiligenceProcessor;
  let batchService: BatchService;
  let batchMessageHandlerExport: BatchMessageHandlerExport;
  let diligencePDFService: DiligencePDFService;

  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('batch.pdf.service.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId, ProductCodeEnums.Pro);
  let testRiskModel: any;
  let diligence: DiligenceHistoryEntity;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        AppTestModule,
        BatchModule,
        RiskModelModule,
        TypeOrmModule.forFeature([
          BatchEntity,
          BatchJobEntity,
          BatchResultEntity,
          BatchDiligenceEntity,
          DiligenceHistoryEntity,
          CompanyEntity,
          BatchMatchCompanyItemEntity,
          BatchMatchCompanyEntity,
          UserEntity,
          MonitorGroupEntity,
          MonitorCompanyEntity,
          MonitorMetricsDynamicEntity,
          RiskModelEntity,
        ]),
      ],
      providers: [
        BatchService,
        RiskModelInitV2Service,
        BatchMessageService,
        BatchMessageHandlerDiligence,
        BatchBaseHelper,
        BatchDiligenceProcessor,
        ContinuousDiligenceProcessor,
        BatchMessageHandlerExport,
        BatchResultExportService,
        DiligenceExportProcessor,
        {
          provide: VerificationExportProcessor,
          useValue: {
            searchDataAndGeneratorFile: jest.fn(),
            getBusinessType: jest.fn(),
          },
        },
        {
          provide: DiligencePDFService,
          useValue: jest.fn(),
        },
        {
          provide: MyOssService,
          useValue: jest.fn(),
        },
        {
          provide: MessageService,
          useValue: jest.fn(),
        },
        {
          provide: DiligenceSnapshotService,
          useValue: jest.fn(),
        },
        {
          provide: DiligenceSnapshotHelper,
          useValue: jest.fn(),
        },
        {
          provide: EvaluationService,
          useValue: jest.fn(),
        },
        {
          provide: CompanySearchService,
          useValue: jest.fn(),
        },
        {
          provide: DimensionFieldsService,
          useValue: jest.fn(),
        },
        {
          provide: DimensionService,
          useValue: jest.fn(),
        },
        {
          provide: DiligenceSnapshotEsService,
          useValue: jest.fn(),
        },
        {
          provide: BatchMessageHandlerVerification,
          useValue: {
            getTargetQueue: jest.fn().mockReturnValue({ queueName: 'mock-verification-queue' }),
            getProperlyProcessor: jest.fn(),
            refreshBatchStatus: jest.fn(),
            onBatchSuccess: jest.fn(),
            onBatchError: jest.fn(),
          },
        },
        {
          provide: QaTaskService,
          useValue: {
            createQaTask: jest.fn(),
            updateQaTask: jest.fn(),
            getQaTask: jest.fn(),
            onTaskFinished: jest.fn(),
            onTaskError: jest.fn(),
          },
        },
      ],
    }).compile();
    batchService = module.get<BatchService>(BatchService);

    queueService = module.get<QueueService>(QueueService);
    entityManager = module.get<EntityManager>(EntityManager);

    diligencePDFService = module.get<DiligencePDFService>(DiligencePDFService);
    riskModelInitService = module.get<RiskModelInitV2Service>(RiskModelInitV2Service);
    batchMessageService = module.get<BatchMessageService>(BatchMessageService);
    batchBaseHelper = module.get<BatchBaseHelper>(BatchBaseHelper);
    batchDiligenceProcessor = module.get<BatchDiligenceProcessor>(BatchDiligenceProcessor);
    continuousDiligenceProcessor = module.get<ContinuousDiligenceProcessor>(ContinuousDiligenceProcessor);
    batchResultExportService = module.get<BatchResultExportService>(BatchResultExportService);
    batchMessageHandlerExport = module.get<BatchMessageHandlerExport>(BatchMessageHandlerExport);

    // 清理之前的测试数据
    await cleanupTestData();
    // 清除当前batch产生的数据
    await clearDiligenceTestData(entityManager, testUser.currentOrg);
    // 创建测试数据
    await prepareTestData();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // 清理测试数据
    await cleanupTestData();
    // 清除当前batch产生的数据
    await clearDiligenceTestData(entityManager, testUser.currentOrg);
    const connection = getConnection();
    await connection.close();
  });

  /**
   * 准备测试数据
   * 创建监控模型，创建分组，创建分组企业
   */
  async function prepareTestData() {
    // 创建风险模型，使用时间戳确保名称唯一
    const timestamp = new Date().getTime();
    const modelName = `监控模型_${testOrgId}_${timestamp}`;
    testRiskModel = await riskModelInitService.initModel(RiskModelTemplateEnum.HTF, testUser, modelName, testUser.currentOrg, RiskModelTypeEnums.MonitorModel);
    testRiskModel = await entityManager.findOne(RiskModelEntity, {
      where: { modelId: testRiskModel.modelId },
      relations: [
        'groups',
        'groups.groupMetrics',
        'groups.groupMetrics.metricEntity',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.dimensionDef',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.strategyFields',
        'groups.groupMetrics.metricEntity.dimensionHitStrategies.dimensionHitStrategyEntity.strategyFields.dimensionField',
      ],
      cache: false,
    });
    const diligenceList = await prepareTestDiligenceData(entityManager, {
      size: 4,
      overrideSnapshotDetails: false,
      orgId: testOrgId,
      userIds: [testUserId],
    });
    diligence = diligenceList.find((d) => d.snapshotDetails.status === SnapshotStatus.SUCCESS);
  }

  /**
   * 清理测试数据
   * 清除监控模型，清除分组，清除企业数据
   */
  async function cleanupTestData() {
    await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.MonitorRiskModel);
  }

  describe('【执行一次导出的batch任务】createExportBatchEntity()', () => {
    beforeEach(async () => {
      jest.clearAllMocks();
    });

    afterEach(async () => {
      jest.clearAllMocks();
    });

    it('模拟pdf导出的batch任务——假设尽调结果已经生成-【生成pdf接口成功返回】', async () => {
      // 创建一个监控的batch任务
      const spy1 = jest.spyOn(queueService.batchExportQueue, 'sendMessageV2').mockImplementation(async (message: BatchExportMessagePO<ExportConditionBase>) => {
        await batchMessageHandlerExport.handleJobMessage(message);
        return 'mock-message-id'; // 返回一个模拟的消息ID
      });
      // mock 生成pdf
      const fileResult = {
        fileUrl: 'XXXXXXfileUrl',
        fileName: 'XXXXXXfileName',
        previewUrl: 'XXXXXXpreviewUrl',
      };
      const spy2 = jest.spyOn(diligencePDFService, 'generatePdf').mockReturnValue(fileResult as any);
      // 模拟一个pdf导出的batch任务
      const condition: ExportConditionRequest = { diligenceId: diligence.id, product: testUser.currentProduct };
      const result = await batchService.createExportBatchEntity(testUser, BatchBusinessTypeEnums.Diligence_PDF_Export, condition);
      expect(spy1).toBeCalled();
      expect(spy2).toBeCalled();
      // 数据验证
      const batch = await entityManager.findOne(BatchEntity, { where: { batchId: result.batchId } });
      expect(batch).not.toBeNull();
      expect(batch.fileName).toEqual(fileResult.fileName);
      expect(batch.detailFile).toEqual(fileResult.fileUrl);
      expect(batch.previewUrl).toEqual(fileResult.previewUrl);
      expect(batch.status).toEqual(BatchStatusEnums.Done);
      const batchJobList = await entityManager.find(BatchJobEntity, { where: { batchId: result.batchId } });
      expect(batchJobList.length).toBeGreaterThan(0);
      batchJobList.forEach((batchJob) => {
        expect(batchJob.status).toEqual(BatchStatusEnums.Done);
      });

      const diligenceHistory = await entityManager.findOne(DiligenceHistoryEntity, { where: { id: diligence.id } });
      expect(diligenceHistory).not.toBeNull();
    });

    it('模拟pdf导出的batch任务——假设尽调结果已经生成-【生成pdf接口异常】', async () => {
      // 创建一个监控的batch任务
      const spy1 = jest.spyOn(queueService.batchExportQueue, 'sendMessageV2').mockImplementation(async (message: BatchExportMessagePO<ExportConditionBase>) => {
        await batchMessageHandlerExport.handleJobMessage(message);
        return 'mock-message-id'; // 返回一个模拟的消息ID
      });

      const spy2 = jest.spyOn(diligencePDFService, 'generatePdf').mockRejectedValue(new Error('模拟尽调异常'));
      // 模拟一个pdf导出的batch任务
      const condition: ExportConditionRequest = { diligenceId: diligence.id, product: testUser.currentProduct };
      const result = await batchService.createExportBatchEntity(testUser, BatchBusinessTypeEnums.Diligence_PDF_Export, condition);
      expect(spy1).toBeCalled();
      expect(spy2).toBeCalled();
      // 数据验证
      const batch = await entityManager.findOne(BatchEntity, { where: { batchId: result.batchId } });
      expect(batch).not.toBeNull();
      expect(batch.status).toEqual(BatchStatusEnums.Error);
      const batchJobList = await entityManager.find(BatchJobEntity, { where: { batchId: result.batchId } });
      expect(batchJobList.length).toBeGreaterThan(0);
      batchJobList.forEach((batchJob) => {
        expect(batchJob.status).toEqual(BatchStatusEnums.Error);
      });
    });
  });
});
