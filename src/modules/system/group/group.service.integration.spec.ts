import { AppTestModule } from '@app/app.test.module';
import { GroupEntity } from '@domain/entities/GroupEntity';
import { GroupMetricRelationEntity } from '@domain/entities/GroupMetricRelationEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { DataCategoryEnums } from '@domain/enums/DataCategoryEnums';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DistributedResourceTypeEnums } from '@domain/enums/DistributedResourceTypeEnums';
import { ProductCodeEnums } from '@domain/enums/ProductCodeEnums';
import { RiskModelTypeEnums } from '@domain/enums/RiskModelTypeEnums';
import { DimensionRiskLevelEnum } from '@domain/enums/diligence/DimensionRiskLevelEnum';
import { HitDimensionService } from '@modules/risk-assessment/diligence/evaluation/hit.dimension.service';
import { RiskModelTemplateEnum } from '@modules/risk-assessment/risk-model/init_mode/constants/risk-model-init.enums';
import { RiskModelInitV2Service } from '@modules/risk-assessment/risk-model/init_mode/v2/risk-model-init-v2.service';
import { RiskModelModule } from '@modules/risk-assessment/risk-model/risk_model.module';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { clearAllTestRiskModelTestData } from '@testing/riskmodel.test.utils';
import { generateUniqueTestIds, getTestUser } from '@testing/test.user';
import { EntityManager, getConnection, Repository } from 'typeorm';
import { GroupService } from './group.service';
import { AddGroupRequest } from './po/AddGroupRequest';
import { AddMetricToGroup } from './po/AddMetricToGroup';
import { SearchGroupRequest } from './po/SearchGroupRequest';
import { UpdateGroupRequest } from './po/UpdateGroupRequest';

/**
 * 分组服务集成测试
 */
jest.setTimeout(60000);
describe('GroupService 集成测试', () => {
  let groupService: GroupService;
  let riskModelInitV2Service: RiskModelInitV2Service;
  let entityManager: EntityManager;
  let groupRepository: Repository<GroupEntity>;
  let groupMetricRepository: Repository<GroupMetricRelationEntity>;

  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('group.service.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  let testRiskModel: RiskModelEntity;
  let testGroup: GroupEntity;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, RiskModelModule, TypeOrmModule.forFeature([GroupEntity, GroupMetricRelationEntity, MetricsEntity])],
      providers: [
        GroupService,
        {
          provide: HitDimensionService,
          useValue: {
            getDimensionHitResult: jest.fn(),
          },
        },
      ],
    }).compile();

    groupService = module.get<GroupService>(GroupService);
    riskModelInitV2Service = module.get<RiskModelInitV2Service>(RiskModelInitV2Service);
    entityManager = module.get<EntityManager>(EntityManager);
    groupRepository = entityManager.getRepository(GroupEntity);
    groupMetricRepository = entityManager.getRepository(GroupMetricRelationEntity);

    // 创建测试风险模型
    const modelName = `【集成测试】尽调模型-${Date.now()}`;
    testRiskModel = await riskModelInitV2Service.initModel(
      RiskModelTemplateEnum.HTF,
      testUser,
      modelName,
      testUser.currentOrg,
      RiskModelTypeEnums.GeneralRiskModel,
    );

    // 创建测试分组
    const addGroupRequest = new AddGroupRequest();
    addGroupRequest.createBy = testUser.userId;
    addGroupRequest.groupName = `测试分组-${Date.now()}`;
    addGroupRequest.riskLevel = DimensionRiskLevelEnum.Medium;
    addGroupRequest.productCode = ProductCodeEnums.Pro;
    addGroupRequest.comment = '集成测试分组';
    addGroupRequest.category = DataCategoryEnums.System;
    addGroupRequest.modelId = testRiskModel.modelId;

    testGroup = await groupService.addGroup(addGroupRequest, testUser);
  });

  afterAll(async () => {
    // 清理测试数据
    await entityManager.delete(GroupMetricRelationEntity, { groupId: testGroup.groupId });
    await entityManager.delete(GroupEntity, { groupId: testGroup.groupId });
    await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel, [testRiskModel.modelId]);
    const connection = getConnection();
    await connection.close();
  });

  describe('search 方法测试', () => {
    it('应该能够根据分组名称搜索分组', async () => {
      // 准备测试数据
      const searchRequest = new SearchGroupRequest();
      searchRequest.groupName = testGroup.groupName;
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await groupService.search(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.data.some((group) => group.groupId === testGroup.groupId)).toBeTruthy();
      expect(result.data.some((group) => group.groupName === testGroup.groupName)).toBeTruthy();
    });

    it('应该能够根据风险等级搜索分组', async () => {
      // 准备测试数据
      const searchRequest = new SearchGroupRequest();
      searchRequest.riskLevel = testGroup.riskLevel;
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await groupService.search(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.data.some((group) => group.groupId === testGroup.groupId)).toBeTruthy();
      expect(result.data.some((group) => group.riskLevel === testGroup.riskLevel)).toBeTruthy();
    });

    it('应该能够根据产品代码搜索分组', async () => {
      // 准备测试数据
      const searchRequest = new SearchGroupRequest();
      searchRequest.productCode = testGroup.productCode;
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await groupService.search(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.data.some((group) => group.groupId === testGroup.groupId)).toBeTruthy();
      expect(result.data.some((group) => group.productCode === testGroup.productCode)).toBeTruthy();
    });

    it('应该能够正确处理分页', async () => {
      // 准备测试数据
      const searchRequest = new SearchGroupRequest();
      searchRequest.pageSize = 5;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await groupService.search(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.pageSize).toBe(5);
      expect(result.pageIndex).toBe(1);
      expect(result.data.length).toBeLessThanOrEqual(5);

      // 测试第二页
      searchRequest.pageIndex = 2;
      const result2 = await groupService.search(searchRequest);
      expect(result2.pageIndex).toBe(2);
    });
  });

  describe('addGroup 方法测试', () => {
    it('应该能够成功创建新分组', async () => {
      // 准备测试数据
      const addGroupRequest = new AddGroupRequest();
      addGroupRequest.groupName = `新测试分组-${Date.now()}`;
      addGroupRequest.riskLevel = DimensionRiskLevelEnum.High;
      addGroupRequest.productCode = ProductCodeEnums.Pro;
      addGroupRequest.comment = '新集成测试分组';
      addGroupRequest.category = DataCategoryEnums.System;
      addGroupRequest.modelId = testRiskModel.modelId;
      addGroupRequest.createBy = testUser.userId;

      // 执行测试
      const result = await groupService.addGroup(addGroupRequest, testUser);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.groupName).toBe(addGroupRequest.groupName);
      expect(result.riskLevel).toBe(addGroupRequest.riskLevel);
      expect(result.productCode).toBe(addGroupRequest.productCode);
      expect(result.comment).toBe(addGroupRequest.comment);
      expect(result.category).toBe(addGroupRequest.category);
      expect(result.modelId).toBe(addGroupRequest.modelId);
      expect(result.status).toBe(DataStatusEnums.Developing);
      expect(result.createBy).toBe(testUser.userId);

      // 清理测试数据
      await entityManager.delete(GroupEntity, { groupId: result.groupId });
    });
  });

  describe('updateGroup 方法测试', () => {
    it('应该能够成功更新分组信息', async () => {
      // 准备测试数据
      const updateRequest = new UpdateGroupRequest();
      updateRequest.modelId = testRiskModel.modelId;
      updateRequest.groupId = testGroup.groupId;
      updateRequest.groupName = `更新后的分组名称-${Date.now()}`;
      updateRequest.riskLevel = DimensionRiskLevelEnum.High;
      updateRequest.comment = '更新后的备注';
      updateRequest.updateBy = testUser.userId;

      // 执行测试
      const result = await groupService.updateGroup(updateRequest, testUser);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.affected).toBeGreaterThanOrEqual(1);

      // 验证更新是否生效
      const updatedGroup = await groupRepository.findOne({ where: { groupId: testGroup.groupId } });
      expect(updatedGroup).toBeDefined();
      expect(updatedGroup.groupName).toBe(updateRequest.groupName);
      expect(updatedGroup.riskLevel).toBe(updateRequest.riskLevel);
      expect(updatedGroup.comment).toBe(updateRequest.comment);
      expect(updatedGroup.updateBy).toBe(testUser.userId);
    });
  });

  describe('addMetricToGroup 方法测试', () => {
    it('应该能够成功添加指标到分组', async () => {
      // 准备测试数据 - 创建测试指标
      const testMetric = await entityManager.save(MetricsEntity, {
        name: `测试指标-${Date.now()}`,
        riskLevel: DimensionRiskLevelEnum.Medium,
        isVeto: 0,
        metricType: 1,
        score: 10,
        productCode: ProductCodeEnums.Pro,
        comment: '集成测试指标',
        category: DataCategoryEnums.System,
        status: DataStatusEnums.Developing,
        createBy: testUser.userId,
      });

      const addMetricRequest = new AddMetricToGroup();
      addMetricRequest.groupId = testGroup.groupId;
      addMetricRequest.metricsId = testMetric.metricsId;
      addMetricRequest.modelId = testRiskModel.modelId;

      // 执行测试
      const result = await groupService.addMetricToGroup(addMetricRequest, testUser);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.affected).toBe(1);

      // 验证关联是否创建成功
      const relation = await groupMetricRepository.findOne({
        where: {
          groupId: testGroup.groupId,
          metricsId: testMetric.metricsId,
        },
      });
      expect(relation).toBeDefined();

      // 清理测试数据
      await entityManager.delete(GroupMetricRelationEntity, {
        groupId: testGroup.groupId,
        metricsId: testMetric.metricsId,
      });
      await entityManager.delete(MetricsEntity, { metricsId: testMetric.metricsId });
    });

    it('重复添加同一指标应执行更新操作', async () => {
      // 准备测试数据 - 创建测试指标
      const testMetric = await entityManager.save(MetricsEntity, {
        name: `测试指标-${Date.now()}`,
        riskLevel: DimensionRiskLevelEnum.Medium,
        isVeto: 0,
        metricType: 1,
        score: 10,
        productCode: ProductCodeEnums.Pro,
        comment: '集成测试指标',
        category: DataCategoryEnums.System,
        status: DataStatusEnums.Developing,
        createBy: testUser.userId,
      });

      const addMetricRequest = new AddMetricToGroup();
      addMetricRequest.groupId = testGroup.groupId;
      addMetricRequest.metricsId = testMetric.metricsId;
      addMetricRequest.modelId = testRiskModel.modelId;
      // 第一次添加
      const firstResult = await groupService.addMetricToGroup(addMetricRequest, testUser);
      expect(firstResult.affected).toBe(1);

      // 验证第一次添加是否成功
      const firstRelation = await groupMetricRepository.findOne({
        where: {
          groupId: testGroup.groupId,
          metricsId: testMetric.metricsId,
        },
      });
      expect(firstRelation).toBeDefined();

      // 第二次添加（重复添加）
      const secondResult = await groupService.addMetricToGroup(addMetricRequest, testUser);

      // 验证结果 - 由于使用 save 操作，重复添加会执行更新操作
      expect(secondResult).toBeDefined();
      expect(secondResult.affected).toBe(1);

      // 验证重复添加后关联关系仍然存在且是唯一的
      const relations = await groupMetricRepository.find({
        where: {
          groupId: testGroup.groupId,
          metricsId: testMetric.metricsId,
        },
      });
      expect(relations).toHaveLength(1); // 确保只有一条关联记录

      // 清理测试数据
      await entityManager.delete(GroupMetricRelationEntity, {
        groupId: testGroup.groupId,
        metricsId: testMetric.metricsId,
      });
      await entityManager.delete(MetricsEntity, { metricsId: testMetric.metricsId });
    });
  });
});
