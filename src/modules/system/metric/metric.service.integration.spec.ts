import { AppTestModule } from '@app/app.test.module';
import { DimensionFieldsEntity } from '@domain/entities/DimensionFieldsEntity';
import { DimensionHitStrategyEntity } from '@domain/entities/DimensionHitStrategyEntity';
import { DimensionHitStrategyFieldsEntity } from '@domain/entities/DimensionHitStrategyFieldsEntity';
import { GroupEntity } from '@domain/entities/GroupEntity';
import { GroupMetricRelationEntity } from '@domain/entities/GroupMetricRelationEntity';
import { MetricDimensionRelationEntity } from '@domain/entities/MetricDimensionRelationEntity';
import { MetricsEntity } from '@domain/entities/MetricsEntity';
import { RiskModelEntity } from '@domain/entities/RiskModelEntity';
import { DataCategoryEnums } from '@domain/enums/DataCategoryEnums';
import { DataStatusEnums } from '@domain/enums/DataStatusEnums';
import { DistributedResourceTypeEnums } from '@domain/enums/DistributedResourceTypeEnums';
import { RiskModelTypeEnums } from '@domain/enums/RiskModelTypeEnums';
import { SearchMetricsRequest } from '@domain/model/metric/SearchMetricsRequest';
import { UpdateMetricRequest } from '@domain/model/metric/UpdateMetricRequest';
import { RiskModelTemplateEnum } from '@modules/risk-assessment/risk-model/init_mode/constants/risk-model-init.enums';
import { RiskModelInitV2Service } from '@modules/risk-assessment/risk-model/init_mode/v2/risk-model-init-v2.service';
import { RiskModelModule } from '@modules/risk-assessment/risk-model/risk_model.module';
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { clearAllTestRiskModelTestData } from '@testing/riskmodel.test.utils';
import { generateUniqueTestIds, getTestUser } from '@testing/test.user';
import { EntityManager, getConnection, In, Repository } from 'typeorm';
import { MetricService } from './metric.service';
import { AddMetricRequest } from './po/AddMetricRequest';
import { BindHitStrategyToMetricRequest } from './po/BindHitStrategyToMetricRequest';
import { RemoveHitStrategyFromMetricRequest } from './po/RemoveHitStrategyFromMetricRequest';

/**
 * 指标服务集成测试
 */
jest.setTimeout(60000);
describe('MetricService 集成测试', () => {
  let metricService: MetricService;
  let riskModelInitV2Service: RiskModelInitV2Service;
  let entityManager: EntityManager;
  let metricRepository: Repository<MetricsEntity>;
  let metricDimensionRepository: Repository<MetricDimensionRelationEntity>;

  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('metric.service.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  let testRiskModel: RiskModelEntity;
  let testMetric: MetricsEntity;
  let testDimensionHitStrategy: DimensionHitStrategyEntity;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        AppTestModule,
        RiskModelModule,
        TypeOrmModule.forFeature([
          MetricsEntity,
          MetricDimensionRelationEntity,
          GroupEntity,
          DimensionHitStrategyEntity,
          DimensionFieldsEntity,
          DimensionHitStrategyFieldsEntity,
        ]),
      ],
      providers: [MetricService],
    }).compile();

    metricService = module.get<MetricService>(MetricService);
    riskModelInitV2Service = module.get<RiskModelInitV2Service>(RiskModelInitV2Service);
    entityManager = module.get<EntityManager>(EntityManager);
    metricRepository = entityManager.getRepository(MetricsEntity);
    metricDimensionRepository = entityManager.getRepository(MetricDimensionRelationEntity);

    // 创建测试风险模型
    const modelName = `【集成测试】尽调模型-${Date.now()}`;
    testRiskModel = await riskModelInitV2Service.initModel(
      RiskModelTemplateEnum.HTF,
      testUser,
      modelName,
      testUser.currentOrg,
      RiskModelTypeEnums.GeneralRiskModel,
    );

    // 获取测试维度命中策略
    const groupMetricRelations = await entityManager.find(GroupMetricRelationEntity, {
      where: {
        groupId: In((await entityManager.find(GroupEntity, { where: { modelId: testRiskModel.modelId } })).map((group) => group.groupId)),
      },
      take: 1,
    });

    if (groupMetricRelations.length > 0) {
      const metricDimensions = await entityManager.find(MetricDimensionRelationEntity, {
        where: { metricsId: groupMetricRelations[0].metricsId },
        relations: ['dimensionHitStrategyEntity'],
        take: 1,
      });

      if (metricDimensions.length > 0) {
        testDimensionHitStrategy = metricDimensions[0].dimensionHitStrategyEntity;
      }
    }

    // 创建测试指标
    const addMetricRequest = new AddMetricRequest();
    addMetricRequest.name = `测试指标-${Date.now()}`;
    addMetricRequest.riskLevel = 1;
    addMetricRequest.isVeto = 0; // 使用数字而不是布尔值
    addMetricRequest.metricType = 1;
    addMetricRequest.score = 10;
    addMetricRequest.productCode = 'pro';
    addMetricRequest.comment = '集成测试指标';
    addMetricRequest.category = DataCategoryEnums.System;
    addMetricRequest.status = DataStatusEnums.Developing;

    testMetric = await metricService.addMetric(addMetricRequest, testUser);
  });

  afterAll(async () => {
    // 清理测试数据
    if (testRiskModel.modelId) {
      await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.RiskModel, [testRiskModel.modelId]);
    }
    await entityManager.delete(MetricDimensionRelationEntity, { metricsId: testMetric.metricsId });
    await entityManager.delete(MetricsEntity, { metricsId: testMetric.metricsId });
    const connection = getConnection();
    await connection.close();
  });

  describe('searchMetrics 方法测试', () => {
    it('应该能够根据名称搜索指标', async () => {
      // 准备测试数据
      const searchRequest = new SearchMetricsRequest();
      searchRequest.name = testMetric.name;
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await metricService.searchMetrics(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.data.some((metric) => metric.metricsId === testMetric.metricsId)).toBeTruthy();
      expect(result.data.some((metric) => metric.name === testMetric.name)).toBeTruthy();
    });

    it('应该能够根据风险等级搜索指标', async () => {
      // 准备测试数据
      const searchRequest = new SearchMetricsRequest();
      searchRequest.riskLevel = testMetric.riskLevel;
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await metricService.searchMetrics(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.data.some((metric) => metric.metricsId === testMetric.metricsId)).toBeTruthy();
      expect(result.data.some((metric) => metric.riskLevel === testMetric.riskLevel)).toBeTruthy();
    });

    it('应该能够根据指标类型搜索指标', async () => {
      // 准备测试数据
      const searchRequest = new SearchMetricsRequest();
      searchRequest.metricType = testMetric.metricType;
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await metricService.searchMetrics(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.data.some((metric) => metric.metricsId === testMetric.metricsId)).toBeTruthy();
      expect(result.data.some((metric) => metric.metricType === testMetric.metricType)).toBeTruthy();
    });

    it('应该能够根据产品代码搜索指标', async () => {
      // 准备测试数据
      const searchRequest = new SearchMetricsRequest();
      searchRequest.productCode = testMetric.productCode;
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await metricService.searchMetrics(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.data.some((metric) => metric.metricsId === testMetric.metricsId)).toBeTruthy();
      expect(result.data.some((metric) => metric.productCode === testMetric.productCode)).toBeTruthy();
    });

    it('应该能够根据指标ID列表搜索指标', async () => {
      // 准备测试数据
      const searchRequest = new SearchMetricsRequest();
      searchRequest.metricsIds = [testMetric.metricsId];
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await metricService.searchMetrics(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(1);
      expect(result.data.some((metric) => metric.metricsId === testMetric.metricsId)).toBeTruthy();
    });

    it('应该能够正确处理分页', async () => {
      // 准备测试数据
      const searchRequest = new SearchMetricsRequest();
      searchRequest.pageSize = 5;
      searchRequest.pageIndex = 1;

      // 执行测试
      const result = await metricService.searchMetrics(searchRequest);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.pageSize).toBe(5);
      expect(result.pageIndex).toBe(1);
      expect(result.data.length).toBeLessThanOrEqual(5);

      // 测试第二页
      searchRequest.pageIndex = 2;
      const result2 = await metricService.searchMetrics(searchRequest);
      expect(result2.pageIndex).toBe(2);
    });
  });

  describe('updateMetric 方法测试', () => {
    it('应该能够更新指标基本信息', async () => {
      // 准备测试数据
      const updateRequest = Object.assign(new UpdateMetricRequest(), {
        metricsId: testMetric.metricsId,
        name: `更新后的指标名称-${Date.now()}`,
        riskLevel: 2,
        isVeto: 1, // 使用数字而不是布尔值
        score: 20,
        comment: '更新后的备注',
      });
      // 执行测试
      const result = await metricService.updateMetric(updateRequest, testUser);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.affected).toBeGreaterThanOrEqual(1);

      // 验证更新是否生效
      const updatedMetric = await metricRepository.findOne({ where: { metricsId: testMetric.metricsId } });
      expect(updatedMetric).toBeDefined();
      expect(updatedMetric.name).toBe(updateRequest.name);
      expect(updatedMetric.riskLevel).toBe(updateRequest.riskLevel);
      expect(updatedMetric.isVeto).toBe(updateRequest.isVeto);
      expect(updatedMetric.score).toBe(updateRequest.score);
      expect(updatedMetric.comment).toBe(updateRequest.comment);
      expect(updatedMetric.updateBy).toBe(testUser.userId);
    });

    it('应该能够更新指标命中策略', async () => {
      // 准备测试数据
      const updateRequest = new UpdateMetricRequest();
      updateRequest.metricsId = testMetric.metricsId;
      // 使用 any 类型避免类型错误
      updateRequest.hitStrategy = JSON.stringify({
        must: [{ key: 'test_key', op: 'eq', value: 'test_value' }],
        should: [],
        must_not: [],
      }) as any;

      // 执行测试
      const result = await metricService.updateMetric(updateRequest, testUser);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.affected).toBeGreaterThanOrEqual(1);

      // 验证更新是否生效
      const updatedMetric = await metricRepository.findOne({ where: { metricsId: testMetric.metricsId } });
      expect(updatedMetric).toBeDefined();
      expect(updatedMetric.hitStrategy).toBe(updateRequest.hitStrategy);
    });

    it('当指标状态不是开发中时应抛出异常', async () => {
      // 准备测试数据 - 先将指标状态改为已发布
      await metricRepository.update(testMetric.metricsId, { status: DataStatusEnums.Enabled });

      const updateRequest = Object.assign(new UpdateMetricRequest(), {
        metricsId: testMetric.metricsId,
        name: `不应该更新的名称-${Date.now()}`,
      });
      // 执行测试并验证异常
      await expect(metricService.updateMetric(updateRequest, testUser)).rejects.toThrow();

      // 恢复指标状态为开发中，以便后续测试
      await metricRepository.update(testMetric.metricsId, { status: DataStatusEnums.Developing });
    });
  });

  describe('bindDimensionHitStrategyToMetric 方法测试', () => {
    it('应该能够成功绑定维度命中策略到指标', async () => {
      // 跳过测试如果没有找到测试维度命中策略
      if (!testDimensionHitStrategy) {
        console.warn('跳过测试：未找到测试维度命中策略');
        return;
      }

      // 准备测试数据
      const bindRequest = new BindHitStrategyToMetricRequest();
      bindRequest.metricsId = testMetric.metricsId;
      bindRequest.dimensionStrategyId = testDimensionHitStrategy.strategyId;

      // 执行测试
      const result = await metricService.bindDimensionHitStrategyToMetric(bindRequest, testUser);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.affected).toBe(1);

      // 验证绑定是否成功
      const relation = await metricDimensionRepository.findOne({
        where: {
          metricsId: testMetric.metricsId,
          dimensionStrategyId: testDimensionHitStrategy.strategyId,
        },
      });
      expect(relation).toBeDefined();
    });

    it('重复绑定同一策略应返回affected=0', async () => {
      // 跳过测试如果没有找到测试维度命中策略
      if (!testDimensionHitStrategy) {
        console.warn('跳过测试：未找到测试维度命中策略');
        return;
      }

      // 准备测试数据
      const bindRequest = new BindHitStrategyToMetricRequest();
      bindRequest.metricsId = testMetric.metricsId;
      bindRequest.dimensionStrategyId = testDimensionHitStrategy.strategyId;

      // 执行测试
      const result = await metricService.bindDimensionHitStrategyToMetric(bindRequest, testUser);

      // 验证结果 - 已存在的绑定应返回affected=0
      expect(result).toBeDefined();
      expect(result.affected).toBe(0);
    });
  });

  describe('removeDimensionHitStrategyToMetric 方法测试', () => {
    it('应该能够成功解绑维度命中策略', async () => {
      // 跳过测试如果没有找到测试维度命中策略
      if (!testDimensionHitStrategy) {
        console.warn('跳过测试：未找到测试维度命中策略');
        return;
      }

      // 准备测试数据
      const removeRequest = new RemoveHitStrategyFromMetricRequest();
      removeRequest.metricsId = testMetric.metricsId;
      removeRequest.strategyId = testDimensionHitStrategy.strategyId;

      // 执行测试
      const result = await metricService.removeDimensionHitStrategyToMetric(removeRequest, testUser);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.affected).toBe(1);

      // 验证解绑是否成功
      const relation = await metricDimensionRepository.findOne({
        where: {
          metricsId: testMetric.metricsId,
          dimensionStrategyId: testDimensionHitStrategy.strategyId,
        },
      });
      expect(relation).toBeUndefined();
    });
  });

  describe('publishMetric 方法测试', () => {
    it('应该能够成功发布指标', async () => {
      // 执行测试
      await metricService.publishMetric(testMetric.metricsId, testUser);

      // 验证结果
      const publishedMetric = await metricRepository.findOne({ where: { metricsId: testMetric.metricsId } });
      expect(publishedMetric).toBeDefined();
      expect(publishedMetric.status).toBe(DataStatusEnums.Enabled); // 使用 Enabled 表示已发布状态
    });

    it('当指标已发布时再次发布应抛出异常', async () => {
      // 执行测试并验证异常
      await expect(metricService.publishMetric(testMetric.metricsId, testUser)).rejects.toThrow();
    });
  });
});
