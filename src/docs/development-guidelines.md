# 开发规范指南

## 📝 编码规范

### TypeScript 基础规范

```typescript
// ✅ 推荐写法
export interface CreateUserRequest {
  readonly name: string;
  readonly email: string;
  readonly orgId: string;
}

export class UserService {
  async createUser(request: CreateUserRequest): Promise<UserEntity> {
    // 实现逻辑
  }
}

// ❌ 避免写法
export class UserService {
  async createUser(name: any, email: any): Promise<any> {
    // 缺少类型定义
  }
}
```

### 命名约定

- **文件名**: `kebab-case` - `user-management.service.ts`
- **类名**: `PascalCase` - `UserManagementService`
- **方法名**: `camelCase` - `createUser`, `getUserById`
- **常量**: `UPPER_SNAKE_CASE` - `MAX_RETRY_COUNT`
- **接口**: `PascalCase` + 描述性后缀 - `CreateUserRequest`, `UserEntity`

### 文件结构规范

```typescript
// 导入顺序
import { Injectable } from '@nestjs/common'; // 1. 外部依赖
import { Repository } from 'typeorm';

import { UserEntity } from '@domain/entities/user.entity'; // 2. domain层
import { DatabaseService } from '@infrastructure/database/'; // 3. infrastructure层
import { LoggerUtil } from '@commons/utils/logger.util'; // 4. commons层

// 类定义
@Injectable()
export class UserService {
  // 依赖注入
  constructor(private readonly userRepository: Repository<UserEntity>, private readonly databaseService: DatabaseService) {}

  // 公共方法
  async createUser(request: CreateUserRequest): Promise<UserEntity> {
    // 实现
  }

  // 私有方法
  private validateUserData(userData: any): boolean {
    // 实现
  }
}
```

## 🏗️ 分层开发规范

### 1. Commons 层开发

**职责**: 纯工具函数，无业务逻辑

```typescript
// ✅ 正确示例
export const formatDate = (date: Date, format: string): string => {
  // 纯函数实现
};

// ❌ 错误示例 - 包含业务逻辑
export const validateUserAge = (user: UserEntity): boolean => {
  // 不应该在commons层引用domain实体
};
```

### 2. Domain 层开发

**职责**: 业务概念定义，不依赖技术实现

```typescript
// ✅ 正确示例 - domain/entities/user.entity.ts
export class UserEntity {
  readonly id: string;
  readonly name: string;
  readonly email: string;

  constructor(props: UserProps) {
    // 业务验证逻辑
    this.validateEmail(props.email);
  }

  private validateEmail(email: string): void {
    // 纯业务验证逻辑
  }
}
```

### 3. Infrastructure 层开发

**职责**: 技术实现，外部系统集成

```typescript
// ✅ 正确示例 - infrastructure/external-apis/user-verification.service.ts
@Injectable()
export class UserVerificationService {
  async verifyUserIdentity(userId: string): Promise<VerificationResult> {
    // 调用外部身份验证API
    const response = await this.httpClient.post('/verify', { userId });
    return this.mapToVerificationResult(response.data);
  }
}
```

### 4. Modules 层开发

**职责**: 业务用例实现，API 暴露

```typescript
// ✅ modules/user-management/user.controller.ts
@Controller('users')
@ApiTags('用户管理')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  @ApiOperation({ summary: '创建用户' })
  async createUser(@Body() request: CreateUserDto): Promise<UserResponse> {
    return await this.userService.createUser(request);
  }
}
```

## 🧪 测试规范

### 测试文件命名

- **单元测试**: `*.unittest.spec.ts`
- **集成测试**: `*.integration.spec.ts`
- **端到端测试**: `*.e2e-spec.ts`

### 测试数据生成

```typescript
// 使用统一的测试工具
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';

describe('UserService 单元测试', () => {
  let service: UserService;
  const [testOrgId, testUserId] = generateUniqueTestIds('user.service.unittest.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);

  beforeEach(async () => {
    // 测试初始化
  });

  afterEach(async () => {
    // 清理测试数据 - 使用testOrgId/testUserId控制范围
  });
});
```

## 🔄 开发流程

### 1. 需求分析

1. **确定业务域**: 新功能属于哪个 business domain
2. **分析依赖**: 需要调用哪些 external services
3. **设计接口**: 定义 DTO 和响应格式

### 2. 开发顺序

```
1. Domain层    → 定义业务实体和枚举
2. Infrastructure层 → 实现外部系统集成
3. Modules层   → 实现业务逻辑和API
4. 测试编写    → 单元测试 → 集成测试
5. 文档更新    → API文档 → 架构文档
```

### 3. 代码审查要点

- **架构合规**: 检查依赖关系是否符合分层原则
- **类型安全**: 避免 any 类型，确保类型定义完整
- **测试覆盖**: 核心业务逻辑测试覆盖率 ≥90%
- **性能考虑**: 避免 N+1 查询，合理使用缓存

## 📚 最佳实践

### 1. 错误处理

```typescript
// ✅ 统一错误处理
import { BusinessException } from '@commons/exceptions/business.exception';

export class UserService {
  async getUserById(id: string): Promise<UserEntity> {
    const user = await this.userRepository.findById(id);
    if (!user) {
      throw new BusinessException('用户不存在', 'USER_NOT_FOUND');
    }
    return user;
  }
}
```

### 2. 日志记录

```typescript
// ✅ 结构化日志
import { Logger } from '@commons/utils/logger.util';

export class UserService {
  private readonly logger = new Logger(UserService.name);

  async createUser(request: CreateUserRequest): Promise<UserEntity> {
    this.logger.info('开始创建用户', {
      email: request.email,
      orgId: request.orgId,
    });

    try {
      const user = await this.userRepository.save(request);
      this.logger.info('用户创建成功', { userId: user.id });
      return user;
    } catch (error) {
      this.logger.error('用户创建失败', error, { request });
      throw error;
    }
  }
}
```

### 3. 配置管理

```typescript
// ✅ 类型安全的配置
interface DatabaseConfig {
  readonly host: string;
  readonly port: number;
  readonly database: string;
}

@Injectable()
export class ConfigService {
  get database(): DatabaseConfig {
    return {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3306,
      database: process.env.DB_NAME || 'dd_platform',
    };
  }
}
```

## 🚀 性能优化指南

### 1. 数据库优化

- 合理使用索引
- 避免 N+1 查询
- 使用分页查询
- 合理使用缓存

### 2. API 设计

- 使用 GraphQL 进行精确数据获取
- 实现 API 版本控制
- 合理设计批量操作接口

### 3. 缓存策略

- Redis 缓存热点数据
- 应用层缓存计算结果
- CDN 缓存静态资源

## ⚠️ 开发注意事项

### 禁止事项

1. **跨层直接调用**: modules 不能直接调用 commons
2. **循环依赖**: 任何层级间不允许循环依赖
3. **硬编码**: 避免 magic numbers 和 hardcoded strings
4. **any 类型**: 除特殊情况外禁用 any

### 安全要求

1. **输入验证**: 所有外部输入必须验证
2. **SQL 注入防护**: 使用参数化查询
3. **认证授权**: API 接口必须有适当的权限控制
4. **敏感数据**: 日志中避免记录敏感信息
