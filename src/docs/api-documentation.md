# API 文档规范

## 📋 API 设计规范

### 1. RESTful API 设计原则

#### 资源命名规范

```http
# ✅ 推荐写法
GET    /api/v1/users                    # 获取用户列表
GET    /api/v1/users/{id}               # 获取特定用户
POST   /api/v1/users                    # 创建用户
PUT    /api/v1/users/{id}               # 更新用户
DELETE /api/v1/users/{id}               # 删除用户

GET    /api/v1/companies/{id}/employees # 获取公司员工列表
POST   /api/v1/companies/{id}/risk-assessments # 创建风险评估

# ❌ 避免写法
GET    /api/v1/getUsers                 # 动词形式
POST   /api/v1/user                     # 单数形式
GET    /api/v1/users/list               # 冗余路径
```

#### HTTP 状态码规范

| 状态码 | 说明           | 使用场景                       |
| ------ | -------------- | ------------------------------ |
| 200    | 成功           | GET、PUT 操作成功              |
| 201    | 创建成功       | POST 创建资源成功              |
| 204    | 无内容         | DELETE 操作成功                |
| 400    | 请求错误       | 参数验证失败、格式错误         |
| 401    | 未认证         | 缺少或无效的认证信息           |
| 403    | 无权限         | 用户无访问权限                 |
| 404    | 资源不存在     | 请求的资源不存在               |
| 409    | 冲突           | 资源状态冲突（如重复创建）     |
| 422    | 业务逻辑错误   | 参数格式正确但业务逻辑验证失败 |
| 500    | 服务器内部错误 | 系统异常                       |

### 2. API 版本控制

```http
# URL 版本控制（推荐）
GET /api/v1/users
GET /api/v2/users

# Header 版本控制（备选）
GET /api/users
Accept: application/vnd.dd-platform.v1+json
```

### 3. 分页规范

```typescript
// 请求参数
interface PaginationRequest {
  page: number; // 页码，从1开始
  pageSize: number; // 每页数量，最大100
  sortBy?: string; // 排序字段
  sortOrder?: 'asc' | 'desc'; // 排序方向
}

// 响应格式
interface PaginationResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
```

## 📝 Controller 开发规范

### 1. Controller 基础结构

```typescript
@Controller('users')
@ApiTags('用户管理')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({ summary: '获取用户列表' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码' })
  @ApiQuery({ name: 'pageSize', required: false, type: Number, description: '每页数量' })
  @ApiResponse({ status: 200, description: '获取成功', type: UserListResponse })
  async getUsers(@Query() query: GetUsersQuery): Promise<UserListResponse> {
    return await this.userService.getUsers(query);
  }

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiBody({ type: CreateUserDto, description: '用户信息' })
  @ApiResponse({ status: 201, description: '创建成功', type: UserResponse })
  @ApiResponse({ status: 400, description: '参数错误' })
  async createUser(@Body() createUserDto: CreateUserDto): Promise<UserResponse> {
    return await this.userService.createUser(createUserDto);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取用户详情' })
  @ApiParam({ name: 'id', description: '用户ID' })
  @ApiResponse({ status: 200, description: '获取成功', type: UserResponse })
  @ApiResponse({ status: 404, description: '用户不存在' })
  async getUserById(@Param('id') id: string): Promise<UserResponse> {
    return await this.userService.getUserById(id);
  }
}
```

### 2. DTO 设计规范

#### 请求 DTO

```typescript
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ description: '用户姓名', example: '张三' })
  @IsNotEmpty({ message: '姓名不能为空' })
  @IsString({ message: '姓名必须是字符串' })
  readonly name: string;

  @ApiProperty({ description: '邮箱地址', example: '<EMAIL>' })
  @IsEmail({}, { message: '邮箱格式不正确' })
  readonly email: string;

  @ApiProperty({ description: '组织ID', example: 'org_123456' })
  @IsNotEmpty({ message: '组织ID不能为空' })
  @IsString()
  readonly orgId: string;

  @ApiProperty({ description: '电话号码', example: '13800138000', required: false })
  @IsOptional()
  @IsString()
  readonly phone?: string;
}
```

#### 响应 DTO

```typescript
export class UserResponse {
  @ApiProperty({ description: '用户ID', example: 'user_123456' })
  readonly id: string;

  @ApiProperty({ description: '用户姓名', example: '张三' })
  readonly name: string;

  @ApiProperty({ description: '邮箱地址', example: '<EMAIL>' })
  readonly email: string;

  @ApiProperty({ description: '创建时间', example: '2024-01-01T00:00:00Z' })
  readonly createdAt: Date;

  @ApiProperty({ description: '更新时间', example: '2024-01-01T00:00:00Z' })
  readonly updatedAt: Date;
}
```

### 3. 错误响应规范

```typescript
export class ErrorResponse {
  @ApiProperty({ description: '错误码', example: 'USER_NOT_FOUND' })
  readonly code: string;

  @ApiProperty({ description: '错误消息', example: '用户不存在' })
  readonly message: string;

  @ApiProperty({ description: '请求ID', example: 'req_123456' })
  readonly requestId: string;

  @ApiProperty({ description: '时间戳', example: 1704067200000 })
  readonly timestamp: number;

  @ApiProperty({ description: '详细错误信息', required: false })
  readonly details?: any;
}
```

## 🏗️ API 模块组织结构

### 按业务域组织 API

```
src/modules/
├── risk-assessment/           # 风险评估业务域
│   ├── diligence/            # 尽职调查
│   │   ├── diligence.controller.ts
│   │   ├── dto/
│   │   │   ├── create-diligence.dto.ts
│   │   │   └── diligence-response.dto.ts
│   │   └── diligence.service.ts
│   ├── risk-model/           # 风险模型
│   └── monitor/              # 监控预警
├── company-management/        # 企业管理
│   ├── company.controller.ts
│   ├── dto/
│   └── company.service.ts
└── user-management/          # 用户管理
    ├── user.controller.ts
    ├── dto/
    └── user.service.ts
```

### Controller 文件组织

```typescript
// 一个controller文件包含一个主要资源的所有操作
@Controller('companies')
export class CompanyController {
  // 基本CRUD操作
  @Get() async getCompanies() {}
  @Post() async createCompany() {}
  @Get(':id') async getCompany() {}
  @Put(':id') async updateCompany() {}
  @Delete(':id') async deleteCompany() {}

  // 子资源操作
  @Get(':id/employees') async getCompanyEmployees() {}
  @Post(':id/risk-assessments') async createRiskAssessment() {}
}
```

## 📚 API 文档自动生成

### 1. Swagger 配置

```typescript
// main.ts
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const config = new DocumentBuilder()
    .setTitle('DD Platform API')
    .setDescription('企业风险评估服务平台 API 文档')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('用户管理', '用户相关操作')
    .addTag('企业管理', '企业信息管理')
    .addTag('风险评估', '风险评估相关功能')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  await app.listen(3000);
}
```

### 2. API 文档访问

```bash
# 开发环境
http://localhost:3000/api/docs

# 生产环境
https://api.dd-platform.com/api/docs
```

## 🧪 API 测试规范

### 1. Controller 测试

```typescript
describe('UserController', () => {
  let controller: UserController;
  let userService: jest.Mocked<UserService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        {
          provide: UserService,
          useValue: {
            getUsers: jest.fn(),
            createUser: jest.fn(),
            getUserById: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserController>(UserController);
    userService = module.get(UserService);
  });

  describe('POST /users', () => {
    it('应该成功创建用户', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        name: '张三',
        email: '<EMAIL>',
        orgId: 'org_123456',
      };
      const expectedResponse: UserResponse = {
        id: 'user_123456',
        name: '张三',
        email: '<EMAIL>',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      userService.createUser.mockResolvedValue(expectedResponse);

      // Act
      const result = await controller.createUser(createUserDto);

      // Assert
      expect(result).toEqual(expectedResponse);
      expect(userService.createUser).toHaveBeenCalledWith(createUserDto);
    });
  });
});
```

## 🔒 API 安全规范

### 1. 认证与授权

```typescript
// JWT 认证守卫
@UseGuards(JwtAuthGuard)
@Controller('users')
export class UserController {}

// 角色权限控制
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin', 'manager')
@Delete(':id')
async deleteUser(@Param('id') id: string) {}
```

### 2. 输入验证

```typescript
// 参数验证管道
@UsePipes(new ValidationPipe({
  transform: true,
  whitelist: true,
  forbidNonWhitelisted: true
}))
@Post()
async createUser(@Body() createUserDto: CreateUserDto) {}
```

### 3. 限流控制

```typescript
// 接口限流
@UseGuards(ThrottlerGuard)
@Throttle(10, 60) // 每分钟最多10次请求
@Post('login')
async login(@Body() loginDto: LoginDto) {}
```

## 📈 API 监控与日志

### 1. 请求日志

```typescript
@Injectable()
export class RequestLoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url, body } = request;

    console.log(`[${method}] ${url}`, { body });

    return next.handle();
  }
}
```

### 2. 性能监控

```typescript
@Injectable()
export class PerformanceInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const start = Date.now();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - start;
        console.log(`请求耗时: ${duration}ms`);
      }),
    );
  }
}
```

## 📋 API 文档维护

### 1. 文档更新流程

1. **开发阶段**: 同步更新 Swagger 注解
2. **测试阶段**: 验证 API 文档准确性
3. **发布阶段**: 生成最新版本文档
4. **维护阶段**: 定期清理过时文档

### 2. 版本管理

- 主版本变更：破坏性更改
- 次版本变更：新增功能
- 修订版本：bug 修复

### 3. 文档质量要求

- **完整性**: 所有公开 API 都有文档
- **准确性**: 文档与实际 API 保持一致
- **可读性**: 提供清晰的描述和示例
- **时效性**: 及时更新过时信息
