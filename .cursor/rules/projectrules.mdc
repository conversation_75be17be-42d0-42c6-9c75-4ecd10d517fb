---
description: clean-nestjs-typescript-cursor-rules
globs: *.ts,*.js
alwaysApply: true
---
# DD-Platform-Service 团队开发规则 V2.0

## 第一部分：DD-Platform-Service 项目专属规则

本部分规则具有最高优先级，旨在确保所有代码都符合项目特有的架构和业务目标。

### 1. 🎯 项目核心原则

- **项目定位**: 一个基于 NestJS 的现代化企业风险评估SaaS平台。
- **设计思想**: 遵循**分层架构**和**领域驱动设计(DDD)**，确保代码的模块化、可维护性和可扩展性。
- **语言与风格**:
  - 主要编程语言为 **TypeScript**。
  - 所有代码、注释和文档应优先使用**中文**，以方便团队沟通。
  - 遵循现代、简洁的编码风格。

### 2. 🏗️ 六层分层架构

项目采用严格的六层分层架构，所有开发活动必须严格遵守各层职责和依赖关系。

#### 主要业务层级 (严格单向依赖)

`app` → `modules` → `domain` → `commons`

- **`app` (应用层)**:
  - **职责**: 应用的启动入口和顶层配置。负责组装所有模块，启动应用。
  - **禁止**: 包含任何业务逻辑。
  - **示例**: `main.ts`, `app.module.ts`.

- **`modules` (业务功能层)**:
  - **职责**: 实现所有业务用例和功能。按业务域进行组织，是业务逻辑的核心实现层。
  - **组织方式**: 根目录按业务域划分 (`risk-assessment`, `data-processing`)，其下再划分子模块 (`diligence`, `risk-model`)。
  - **依赖规则**: 可以依赖 `domain` 和 `commons` 层。
  - **包含内容**: Controllers, Services, DTOs, 和与第三方系统的集成。

- **`domain` (领域概念层)**:
  - **职责**: 定义核心业务概念、领域模型和业务规则。
  - **特点**: **保持纯净**。不应包含任何与具体技术实现相关的代码（如数据库操作、框架特有功能）。
  - **依赖规则**: 只能依赖 `commons` 层。
  - **包含内容**: Entities (不带 `@Entity` 装饰器，仅为数据结构), 值对象, 领域服务 (纯业务逻辑), 业务相关的枚举和常量。

- **`commons` (公共基础层)**:
  - **职责**: 提供全项目通用的、无业务含义的纯工具函数和基础类型。
  - **特点**: 高度可复用，无任何副作用。
  - **依赖规则**: **不依赖任何其他层**。
  - **包含内容**: 日期处理、字符串工具、通用类型定义、自定义的通用异常类。

#### 横切关注点层级 (可被主要层级依赖)

- **`core` (框架核心层)**:
  - **职责**: 提供框架级别的基础设施和横切关注点功能。
  - **依赖规则**: 可被 `app` 和 `modules` 层依赖。
  - **包含内容**: 全局守卫 (Guards), 拦截器 (Interceptors), 过滤器 (Filters), 数据库连接配置, 封装的搜索服务等。

- **`client` (客户端 SDK 层)**:
  - **职责**: 为外部系统提供调用本服务的客户端SDK。
  - **特点**: 可独立发布，方便第三方集成。

- **`testing` (测试工具层)**:
  - **职责**: 提供测试相关的工具函数和辅助类。
  - **依赖规则**: 仅在测试环境中使用。

### 3. 🧪 项目测试策略

- **命名约定**:
  - 单元测试: `*.unittest.spec.ts`
  - 集成测试: `*.integration.spec.ts`
  - E2E测试: `*.e2e-spec.ts`
- **单元测试 (`unittest`)**:
  - **目标**: 测试单个类或方法的逻辑。
  - **要求**: 必须 Mock 所有外部依赖（如数据库、其他服务）。
- **集成测试 (`integration`)**:
  - **目标**: 测试模块内部或多个模块间的交互。
  - **要求**: 可连接真实数据库进行测试，但在每个测试后必须清理数据，确保测试的独立性。
- **测试数据**:
  - 使用 `testing/test.user.ts` 中的工具生成测试用户和组织，确保数据隔离。
  - 在 `afterEach` 或 `afterAll` 中清理由本测试用例创建的数据。

### 4. 📚 项目文档体系

- **目标**: 保持文档与代码同步，方便新成员快速上手。
- **执行**:
  - 每个模块（特别是 `modules` 层下的）都应有 `README.md` 文件。
  - 文档应遵循 `docs/doc-system.md` 中定义的层次化结构和导航标准。
  - 创建新模块或修改模块功能时，应同步更新对应的 `README.md`。
  - API 变更需要更新 Swagger/OpenAPI 文档。

---

## 第二部分：通用 TypeScript/NestJS 开发规范

本部分为团队通用的编码标准，作为项目专属规则的补充。

### TypeScript General Guidelines

#### Basic Principles

- Use English for all code and documentation (except where specified by project rules).
- Always declare the type of each variable and function (parameters and return value).
- Avoid using `any`.
- Create necessary types.
- Use JSDoc to document public classes and methods.
- Don't leave blank lines within a function.
- One export per file.

#### Nomenclature

- Use `PascalCase` for classes.
- Use `camelCase` for variables, functions, and methods.
- Use `kebab-case` for file and directory names.
- Use `UPPERCASE` for environment variables.
- Avoid magic numbers and define constants.
- Start each function with a verb.
- Use verbs for boolean variables. Example: `isLoading`, `hasError`, `canDelete`.
- Use complete words instead of abbreviations.

#### Functions

- Write short functions with a single purpose (less than 20 instructions).
- Reduce function parameters using RO-RO (Request Object - Response Object).
- Use a single level of abstraction within a function.

### Specific to NestJS

#### Basic Principles

- **Use modular architecture**: Encapsulate the API in modules. One module per main domain/route.
- **Controllers**: Keep controllers thin. Their job is to parse requests and call services.
- **Services**: Business logic and persistence logic reside in services. One service per entity/domain concept.
- **DTOs**: Use DTOs validated with `class-validator` for all inputs.
- **Entities**: Use MikroORM (or specified ORM) entities for data persistence.

#### Testing

- Use the standard Jest framework for testing.
- Follow the Arrange-Act-Assert (or Given-When-Then) convention for tests.
- Write tests for each controller and service.
- Add an `admin/test` method to each controller as a smoke test.

# 测试策略

## 测试数据生成相关
### 测试用户生成的基本原则

#### 使用以下代码片段生成测试用户：

```typescript
import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user';
const [testOrgId, testUserId] = generateUniqueTestIds('your.test.file.name.ts');
const testUser = getTestUser(testOrgId, testUserId);
```
#### 清理数据时候的规则
- 根据需要清理的entity，使用testOrgId或者testUserId 确保清理的数据范围可指定
- 清理数据时候尽可能控制范围，减少对其他测试文件可能产生的影响

## 单元测试 (Unit Tests)
- 目标：测试单个组件/服务的业务逻辑
- 使用 mock 替代外部依赖和数据库操作
- 文件命名：`*.unittest.spec.ts`
- 位置：与源文件同目录

## 集成测试 (Integration Tests)
- 目标：测试组件间的交互和数据流
- 使用真实数据库操作，但可能 mock 外部服务
- 使用测试数据库环境
- 每个测试后清理测试数据
- 文件命名：`*.integration.spec.ts`
- 位置：与源文件同目录

## 端到端测试 (E2E Tests)
- 目标：测试完整的用户场景
- 使用真实的数据库和外部服务
- 文件命名：`*.e2e-spec.ts`
- 位置：独立的 e2e 目录

## 数据库测试最佳实践
1. 使用事务包装每个测试
2. 在 `beforeEach` 中准备测试数据
3. 在 `afterEach` 中清理测试数据
4. 使用独立的测试数据库环境
5. 避免测试数据互相干扰

## 命名约定
- 测试描述使用中文，清晰表达测试意图
- 使用 "Given-When-Then" 或 "Arrange-Act-Assert" 模式组织测试
- 变量命名要具有描述性，避免 mock1、mock2 这样的命名

## 测试覆盖率要求
- 单元测试：业务逻辑覆盖率 > 80%
- 集成测试：主要业务流程覆盖率 > 70%
- 端到端测试：关键用户场景覆盖率 > 50%
- 具体覆盖率细分
1. **整体覆盖率目标**

   - 行覆盖率（Line Coverage）：≥ 85%
   - 分支覆盖率（Branch Coverage）：≥ 80%
   - 函数覆盖率（Function Coverage）：≥ 90%
   - 语句覆盖率（Statement Coverage）：≥ 85%

2. **关键模块覆盖率要求**

   - 核心业务逻辑模块：≥ 90% 行覆盖率
   - 错误处理和边界条件：≥ 85% 分支覆盖率
   - 公共 API 和接口方法：100% 函数覆盖率

3. **覆盖率测量与报告**

   - 使用 Jest 内置的覆盖率工具 (`--coverage` 参数)
   - 配置 `.coveragerc` 文件排除不需要测试的文件或代码块
   - 在 CI 流程中强制执行覆盖率检查
   - 定期生成覆盖率报告并进行团队审查

4. **低覆盖率处理策略**
   - 对于测试覆盖率低于标准的模块，需要提交改进计划
   - 允许通过文档说明合理忽略特定代码块的覆盖率
   - 新增代码必须满足覆盖率标准才能合并


## 自定义规则
#### 生成的md文件增加生成时间
- **在头部显眼位置标注上生成文档时候的用户本地电脑当前时间（不是 AI 的时间）**，格式为：`生成时间：2025-06-12 10:00:00`
- **⚠️ 重要：必须使用用户本地电脑的实际当前时间，而不是 AI 训练时的时间**




