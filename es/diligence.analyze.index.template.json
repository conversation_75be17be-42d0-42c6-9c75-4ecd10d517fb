{"index_patterns": ["kys_diligence_analyze_*"], "settings": {"number_of_shards": 1, "number_of_replicas": 2}, "mappings": {"_source": {"enabled": true}, "properties": {"id": {"type": "keyword"}, "batchIdCurrent": {"type": "keyword"}, "batchIdPrevious": {"type": "keyword"}, "diligenceIdCurrent": {"type": "keyword"}, "diligenceIdPrevious": {"type": "keyword"}, "dimensionKey": {"type": "keyword"}, "createDate": {"type": "date"}, "increasedTotal": {"type": "integer"}, "decreasedTotal": {"type": "integer"}, "customerId": {"type": "keyword"}, "companyId": {"type": "keyword"}, "companyName": {"type": "keyword"}, "orgId": {"type": "keyword"}, "customerRiskLevel": {"type": "keyword"}, "previousCustomerRiskLevel": {"type": "keyword"}, "customerGroups": {"type": "keyword"}, "customerLabels": {"type": "keyword"}, "customerDeps": {"type": "keyword"}, "changes": {"type": "nested", "properties": {"dimensionKey": {"type": "keyword"}, "dimensionLevel1": {"type": "keyword"}, "dimensionLevel2": {"type": "keyword"}, "increasedValue": {"type": "integer"}, "currentHitValue": {"type": "integer"}, "decreasedValue": {"type": "integer"}}}}}}