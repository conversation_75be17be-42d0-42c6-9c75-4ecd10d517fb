#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'src/modules/risk-assessment/monitor/company/monitor.company.service.spec.ts',
  'src/modules/risk-assessment/monitor/dynamic/process/dynamic-listener.migration.spec.ts',
  'src/modules/risk-assessment/monitor/group/monitor.group.service.spec.ts',
  'src/modules/risk-assessment/monitor/monitor.job.service.integration.spec.ts',
  'src/modules/risk-assessment/risk-model/risk_model.service.spec.ts',
  'src/modules/system/group/group.service.integration.spec.ts',
  'src/modules/system/metric/metric.service.integration.spec.ts'
];

// 修复模式
const fixPatterns = [
  // findOne with direct object -> findOne with {where: {...}}
  {
    pattern: /(\w+\.findOne\([^,]+,\s*)({[^}]+})/g,
    replacement: (match, prefix, objectPart) => {
      // 检查是否已经有where属性
      if (objectPart.includes('where:')) {
        return match; // 已经修复过了
      }
      // 检查是否有其他属性（如relations, cache等）
      if (objectPart.includes(':')) {
        return `${prefix}{ where: ${objectPart.slice(1, -1)} }`;
      } else {
        return `${prefix}{ where: ${objectPart} }`;
      }
    }
  },
  // find with direct object -> find with {where: {...}}
  {
    pattern: /(\w+\.find\([^,]+,\s*)({[^}]+})/g,
    replacement: (match, prefix, objectPart) => {
      // 检查是否已经有where属性
      if (objectPart.includes('where:')) {
        return match; // 已经修复过了
      }
      // 检查是否有其他属性（如relations, cache等）
      if (objectPart.includes(':')) {
        return `${prefix}{ where: ${objectPart.slice(1, -1)} }`;
      } else {
        return `${prefix}{ where: ${objectPart} }`;
      }
    }
  },
  // count with direct object -> count with {where: {...}}
  {
    pattern: /(\w+\.count\([^,]*,?\s*)({[^}]+})/g,
    replacement: (match, prefix, objectPart) => {
      // 检查是否已经有where属性
      if (objectPart.includes('where:')) {
        return match; // 已经修复过了
      }
      return `${prefix}{ where: ${objectPart} }`;
    }
  },
  // findOne with ID as second parameter -> findOne with {where: {id: ...}}
  {
    pattern: /(\w+\.findOne\([^,]+,\s*)(\w+\.\w+)(\s*[,)])/g,
    replacement: '$1{ where: { id: $2 } }$3'
  }
];

function fixFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  fixPatterns.forEach(({ pattern, replacement }) => {
    const originalContent = content;
    if (typeof replacement === 'function') {
      content = content.replace(pattern, replacement);
    } else {
      content = content.replace(pattern, replacement);
    }
    if (content !== originalContent) {
      modified = true;
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed: ${filePath}`);
  } else {
    console.log(`No changes needed: ${filePath}`);
  }
}

// 修复所有文件
filesToFix.forEach(fixFile);

console.log('TypeORM syntax fix completed!');
