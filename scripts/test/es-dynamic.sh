#!/bin/bash
# 功能：动态启动 Elasticsearch Docker 并加载本地模板
# 日期：2025-03-10

# 配置项（可修改）
C_NAME=es-dynamic-dd
ES_VERSION="7.17.11"                # Elasticsearch 版本
ES_PORT="9202"                    # HTTP 端口
TEMPLATE_PATH_Metric="./es_template_init/metrics.dynamic.index.template.json"    # 模板文件路径
TEMPLATE_PATH_Snapshot="./es_template_init/snapshot.index.template.v5.json"    # 模板文件路径
JAVA_HEAP="512m"                  # JVM 堆内存大小


# 启动 Elasticsearch 容器
start_es() {
  echo "正在启动 Elasticsearch ${ES_VERSION}..."
  docker run -d \
    --name $C_NAME \
    --network es-net \
    -p ${ES_PORT}:9200 \
    -e "discovery.type=single-node" \
    -e "ES_JAVA_OPTS=-Xms${JAVA_HEAP} -Xmx${JAVA_HEAP}" \
    elasticsearch:${ES_VERSION}

  # 等待 ES 健康状态（最长 30 秒）
  echo "等待 Elasticsearch 启动..."
  until curl -sS "http://localhost:${ES_PORT}/_cluster/health?wait_for_status=yellow&timeout=30s"; do
    sleep 5
  done
  echo -e "\n Elasticsearch 启动成功"
}

# 加载模板并创建索引
# 参数:
#   $1 - 模板文件路径
#   $2 - 模板名称
#   $3 - 索引名称
load_template() {
  local template_path="$1"
  local template_name="$2"
  local index_name="$3"

  if [ ! -f "${template_path}" ]; then
    echo "警告：未找到模板文件 ${template_path}"
    return 1
  fi

  echo "正在加载模板 ${template_name}..."

  # 创建/更新模板
  curl -XPUT "http://localhost:${ES_PORT}/_template/${template_name}" \
    -H "Content-Type: application/json" \
    -d "@${template_path}"
  
  # 自动创建索引
  echo -e "\n开始创建索引: ${index_name}"
  curl -XPUT "http://localhost:${ES_PORT}/${index_name}"
  echo -e "\n索引 ${index_name} 已创建"
  
  # 创建别名
  echo "开始创建别名: ${index_name}"
  curl -XPOST "http://localhost:${ES_PORT}/_aliases" -H "Content-Type: application/json" -d'
    {
    "actions": [
        { "add": { "index": "'"${index_name}"'", "alias": "'"${index_name}"'_write" } },
        { "add": { "index": "'"${index_name}"'", "alias": "'"${index_name}"'_query" } }
    ]
    }'
  echo -e "\n"
}

# 主流程
main() {
  docker network create es-net 2>/dev/null || true
  cleanup
  start_es
  echo -e "\n"
  
  # 加载 metric 模板
  load_template "./es_template_init/metrics.dynamic.index.template.json" "kys_metric_dynamics-template" "kys_metric_dynamics_test"
  
  echo -e "\n"
  
  # 加载 snapshot 模板
  load_template "./es_template_init/snapshot.index.template.v5.json" "kys_snapshot_-template" "kys_snapshot_test"
}

# 清理函数
cleanup() {
  docker stop $C_NAME 2>/dev/null && docker rm $C_NAME 2>/dev/null || true
  docker volume prune -f
  # 检查变量是否定义再使用
  if [ -n "${ES_DATA_DIR}" ]; then
    rm -rf "${ES_DATA_DIR}" || true
  fi
}

main "$@"