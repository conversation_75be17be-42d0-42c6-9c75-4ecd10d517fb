

-- 创建 saas_bundle_test 数据库
CREATE DATABASE IF NOT EXISTS `saas_bundle_test`;
USE saas_bundle_test;


CREATE TABLE `bundle_fields`
(
    `id`            int                                                              NOT NULL AUTO_INCREMENT,
    `name`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '套餐项名称',
    `product`       varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '产品code：\nSAAS_KZZ\nSAAS_ROVER\nSAAS_JZCC',
    `description`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci         DEFAULT NULL COMMENT '描述',
    `category`      int                                                              NOT NULL DEFAULT '0' COMMENT '类别：0-org；1-user；2 limitation；',
    `count_cycle`   int                                                              NOT NULL DEFAULT '0' COMMENT '套餐项计数周期：0- 跟随套餐周期 1-每天；2 -每周；3-每个月；4-季度；5-年；',
    `default_value` int                                                              NOT NULL DEFAULT '0' COMMENT '默认值',
    `field_key`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `create_date`   datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`   datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `product_field` (`product`, `field_key`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 97
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `bundles`
(
    `bundle_id`          int                                                              NOT NULL AUTO_INCREMENT COMMENT '主键',
    `service_code`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'SAAS_KZZ' COMMENT '应用\n\\nSAAS_KZZ\\nSAAS_ROVER\\nSAAS_JZCC\\nSAAS_ZBCC',
    `name`               varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '套餐名称',
    `service_category`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '产品标识符',
    `province`           varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '省份',
    `goods_id`           int                                                                       DEFAULT NULL COMMENT '对应企查查产品ID',
    `type`               tinyint                                                          NOT NULL COMMENT '套餐类型：1-基础套餐（标准套餐）；2-升级包；3-自定义套餐',
    `version`            tinyint                                                          NOT NULL DEFAULT '1' COMMENT '版本',
    `parameters`         json                                                             NOT NULL COMMENT '参数\n例如：\n{"memberLimit": "4", "subscriptionQuantity": "3", "exportQuantityMonthly": "8000", "orgSubscriptionQuantity": "12"}\n\nfieldKey对应表 bundle_fields 中的fieldKey',
    `parameters_setting` json                                                                      DEFAULT NULL COMMENT '套餐项设置',
    `duration`           int                                                              NOT NULL DEFAULT '5' COMMENT '有效期：0- 跟随套餐周期；1-一天；2 -一周；3-一月；4-一季度；5-一年；6-半年；',
    `price`              decimal(19, 2)                                                   NOT NULL COMMENT '价格',
    `apple_price`        decimal(19, 2)                                                   NOT NULL DEFAULT '0.00' COMMENT '苹果内购价格',
    `active`             tinyint                                                          NOT NULL DEFAULT '1' COMMENT '是否生效：0-无效；1-有效；',
    `create_by`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '创建人手机号',
    `update_by`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT '更新人手机号',
    `create_date`        datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`        datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `description`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL,
    PRIMARY KEY (`bundle_id`),
    KEY `idx_code_tp` (`service_code`, `type`, `active`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 14553
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='套餐表';

CREATE TABLE `bundle_usage_accumulated`
(
    `id`            int      NOT NULL AUTO_INCREMENT,
    `org_bundle_id` int      NOT NULL COMMENT 'org_bundle_id \\nuser_id',
    `usage`         json     NOT NULL,
    `user_id`       int               DEFAULT NULL COMMENT '0 org_bundle\\n1 user',
    `create_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `version`       bigint   NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `bundle_user` (`org_bundle_id`, `user_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `bundle_usage_daily`
(
    `id`            int      NOT NULL AUTO_INCREMENT,
    `org_bundle_id` int      NOT NULL COMMENT 'org_bundle_id or user_bundle_id',
    `user_id`       int      NOT NULL COMMENT '对应bundle_fields表中的category字段，例如：category=0, category_key=org_<orgId>',
    `parameters`    json     NOT NULL,
    `used_time`     bigint   NOT NULL,
    `create_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `bundle_user_date` (`org_bundle_id`, `user_id`, `used_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `dep_bundles`
(
    `dep_bundle_id` int      NOT NULL AUTO_INCREMENT,
    `org_bundle_id` int      NOT NULL,
    `dep_id`        int      NOT NULL,
    `params`        json     NOT NULL,
    `create_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`dep_bundle_id`),
    UNIQUE KEY `uniq` (`org_bundle_id`, `dep_id`),
    KEY `depId` (`dep_id`),
    KEY `orgBundleId` (`org_bundle_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 565
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_action_log`
(
    `action_id`   bigint                                                            NOT NULL AUTO_INCREMENT,
    `product`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '产品名',
    `user_id`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '用户guid',
    `phone`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '用户手机号',
    `action_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '变动原因',
    `create_time` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变动时间',
    `o_bundle`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '变动前会员状态',
    `o_end_time`  datetime                                                                   DEFAULT NULL COMMENT '变动前会员到期时间',
    `n_bundle`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '变动后会员状态',
    `n_end_time`  datetime                                                                   DEFAULT NULL COMMENT '变动后会员到期时间',
    `oper_name`   varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '操作人',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '备注',
    PRIMARY KEY (`action_id`),
    KEY `guid` (`user_id`),
    KEY `product` (`product`),
    KEY `phone` (`phone`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4986
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_crm_org_last_active`
(
    `org_id`           int      NOT NULL,
    `last_active_date` datetime NOT NULL,
    PRIMARY KEY (`org_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_department`
(
    `dep_id`          int                                                                NOT NULL AUTO_INCREMENT,
    `qcc_group_id`    int                                                                         DEFAULT NULL COMMENT 'qcc分组id',
    `name`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL,
    `parent_id`       int                                                                         DEFAULT NULL,
    `depth`           int                                                                NOT NULL DEFAULT '1',
    `path`            varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
    `org_id`          int                                                                NOT NULL,
    `manager_user_id` int                                                                         DEFAULT NULL,
    `create_date`     datetime                                                           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`     datetime                                                           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `user_count`      int                                                                NOT NULL DEFAULT '0' COMMENT '部门下的员工的数量',
    `qcc_dept_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci            DEFAULT NULL COMMENT '企查查部门id',
    `active`          int                                                                NOT NULL DEFAULT '1',
    PRIMARY KEY (`dep_id`),
    UNIQUE KEY `org_dep_name_UNIQUE` (`org_id`, `name`, `active`),
    KEY `org_depth` (`org_id`, `depth`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5835
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_group`
(
    `id`          int                                                              NOT NULL AUTO_INCREMENT,
    `name`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `product`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `create_date` datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `order`       int                                                              NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10280
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_group_permission`
(
    `id`            int      NOT NULL AUTO_INCREMENT,
    `group_id`      int      NOT NULL,
    `permission_id` int      NOT NULL,
    `order`         int      NOT NULL DEFAULT '0',
    `create_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 15207
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;


CREATE TABLE `e_invitation_note`
(
    `id`              int                                                               NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id`         int                                                               NOT NULL,
    `org_id`          int                                                               NOT NULL,
    `code`            varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL,
    `org_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `expiration_date` datetime                                                          NOT NULL,
    `create_date`     datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `user_name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `org_bundle_id`   int                                                                        DEFAULT NULL COMMENT '指定的套餐id',
    `ent_service_id`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT '企业套餐id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2564
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci
  ROW_FORMAT = DYNAMIC;

CREATE TABLE `e_login_buser`
(
    `id`            int                                        NOT NULL AUTO_INCREMENT,
    `login_user_id` int                                        NOT NULL COMMENT '关联e_login_user表主键',
    `phone`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL COMMENT '手机号',
    `email`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '电子邮箱',
    `username`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL COMMENT '用户名',
    `password`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL COMMENT '密码',
    `product`       varchar(45) COLLATE utf8mb4_unicode_520_ci NOT NULL               DEFAULT 'KYS' COMMENT '产品',
    `create_date`   datetime                                   NOT NULL               DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`   datetime                                   NOT NULL               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `login_user_id_UNIQUE` (`login_user_id`, `product`),
    UNIQUE KEY `phone_UNIQUE` (`phone`, `product`),
    UNIQUE KEY `email_UNIQUE` (`email`, `product`),
    UNIQUE KEY `username_UNIQUE` (`username`, `product`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4661
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_login_history`
(
    `id`            bigint                                                            NOT NULL AUTO_INCREMENT,
    `login_user_id` int                                                               NOT NULL,
    `login_from`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '登录来源',
    `login_type`    tinyint                                                           NOT NULL COMMENT '登录方式：1-短信验证码登录；2-密码登录；3-扫码登录；4-SSO登录；5-一键登录；',
    `account`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '登录账号',
    `ip`            varchar(39) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '登录ip地址',
    `user_agent`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '浏览器标识',
    `create_date`   datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    PRIMARY KEY (`id`),
    KEY `loginUserId` (`login_user_id`),
    KEY `account` (`account`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2976
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_login_user`
(
    `login_user_id` int                                                              NOT NULL AUTO_INCREMENT,
    `guid`          char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci    NOT NULL DEFAULT '' COMMENT 'qcc guid',
    `open_user_id`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT '' COMMENT '企业微信 openUserId',
    `username`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `phone`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
    `nickname`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `personal_org`  int                                                                       DEFAULT NULL,
    `current_org`   int                                                              NOT NULL,
    `create_date`   datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`   datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `faceimg`       varchar(255) COLLATE utf8mb4_unicode_520_ci                               DEFAULT NULL COMMENT '头像',
    PRIMARY KEY (`login_user_id`),
    UNIQUE KEY `guid_UNIQUE` (`guid`, `open_user_id`),
    KEY `phone` (`phone`),
    KEY `update_date` (`update_date` DESC)
) ENGINE = InnoDB
  AUTO_INCREMENT = 12151
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='登录用户表';

CREATE TABLE `e_login_user_org`
(
    `id`            int                                                              NOT NULL AUTO_INCREMENT,
    `login_user_id` int                                                              NOT NULL COMMENT '登录用户id',
    `product`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '产品code: 招标查查-SAAS_CZB；建筑查查-SAAS_JZCC；Rover-SAAS_ROVER；',
    `org_id`        int                                                              NOT NULL COMMENT '组织id',
    `active`        tinyint                                                          NOT NULL DEFAULT '1' COMMENT '状态：1-正常；2-冻结；',
    `expire`        tinyint                                                          NOT NULL DEFAULT '0' COMMENT '是否过期：0-未过期；1-已过期；',
    `create_date`   datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`   datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `guid`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `login_product` (`login_user_id`, `product`, `org_id`) USING BTREE,
    KEY `product` (`product`),
    KEY `orgId` (`org_id`),
    KEY `idx_guid_prod` (`guid`, `product`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4804
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_module`
(
    `id`          int                                                               NOT NULL AUTO_INCREMENT,
    `module_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `module_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
    `product`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT 'SAAS_ROVER' COMMENT '产品 Code',
    `create_by`   int                                                               DEFAULT NULL,
    `create_date` datetime                                                          DEFAULT CURRENT_TIMESTAMP,
    `update_by`   int                                                               DEFAULT NULL,
    `update_date` datetime                                                          DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `e_module_module_name_uindex` (`module_name`),
    UNIQUE KEY `e_module_module_type_uindex` (`module_type`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 213
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='权限-模块管理';

CREATE TABLE `e_module_group`
(
    `id`          int NOT NULL AUTO_INCREMENT,
    `module_id`   int NOT NULL,
    `group_id`    int NOT NULL,
    `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2328
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_module_permission`
(
    `id`            int NOT NULL AUTO_INCREMENT,
    `module_id`     int NOT NULL,
    `permission_id` int NOT NULL,
    `create_date`   datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10456
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_operate_log`
(
    `id`          int unsigned                                                     NOT NULL AUTO_INCREMENT,
    `org_id`      int                                                              NOT NULL COMMENT '组织id',
    `type`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '操作类型',
    `params`      json                                                             NOT NULL COMMENT '操作参数',
    `operate_by`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '操作人手机号',
    `create_date` datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作日期',
    PRIMARY KEY (`id`),
    KEY `orgId` (`org_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 14
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_org`
(
    `org_id`                  int                                                                NOT NULL AUTO_INCREMENT,
    `name`                    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL,
    `comment`                 varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
    `create_date`             datetime                                                           NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`             datetime                                                           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `active`                  tinyint                                                            NOT NULL DEFAULT '1' COMMENT '0 禁用;1 启用',
    `cert_status`             tinyint                                                            NOT NULL DEFAULT '0',
    `expired_date`            datetime                                                                    DEFAULT NULL,
    `scale`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL DEFAULT '' COMMENT '企业规模',
    `website`                 varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '企业官网',
    `phone`                   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL DEFAULT '' COMMENT '联系电话',
    `email`                   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '联系邮箱',
    `email_validate`          tinyint                                                            NOT NULL DEFAULT '0' COMMENT '是否开启邮箱验证：1-是；0-否；',
    `address`                 varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '联系地址',
    `business`                varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '主营业务',
    `introduction`            varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '企业简介',
    `team_id`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL DEFAULT '' COMMENT '主站企业id',
    `owner_id`                varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL DEFAULT '' COMMENT '归属人guid',
    `open_user_id`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci            DEFAULT '' COMMENT '企业微信 openUserId',
    `owner_phone`             varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL DEFAULT '' COMMENT '归属人手机',
    `owner_name`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL DEFAULT '' COMMENT '归属人姓名',
    `remark_name`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL DEFAULT '' COMMENT '备注名称',
    `industry`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL DEFAULT '' COMMENT '行业',
    `leader_id`               int                                                                         DEFAULT NULL COMMENT '负责人',
    `source_from`             tinyint                                                            NOT NULL DEFAULT '0' COMMENT '租户来源：0-无；1-企查查加入CRM；2-BO；3-慧算账；4-企业微信；5-企查查移动端；6-客找找原生app；10-招标建筑；11-企查查企业套餐；',
    `enterprise_id`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci            DEFAULT NULL COMMENT '企业Id',
    `convenience_industry_id` int                                                                         DEFAULT NULL,
    `crm_user_count`          int                                                                NOT NULL DEFAULT '0',
    `crm_tracking_count`      int                                                                NOT NULL DEFAULT '0',
    `crm_customer_count`      int                                                                NOT NULL DEFAULT '0',
    `company_id`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci            DEFAULT NULL COMMENT '企查查工商企业id',
    PRIMARY KEY (`org_id`),
    UNIQUE KEY `enterpriseId` (`enterprise_id`) USING BTREE,
    KEY `idx_owner_id` (`owner_id`),
    KEY `idx_phone_ct` (`owner_phone`, `active`, `create_date`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1003680
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='租户组织信息表';

CREATE TABLE `e_org_api_setting`
(
    `id`               int                                                               NOT NULL AUTO_INCREMENT,
    `org_id`           int                                                               NOT NULL,
    `access_key`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `access_secret`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `create_date`      datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`      datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `token_expires_in` int                                                               NOT NULL DEFAULT '3600' COMMENT '令牌有效期 单位秒',
    PRIMARY KEY (`id`),
    KEY `org` (`org_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 73
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='租户接口调用配置表';

CREATE TABLE `e_org_disband_log`
(
    `id`          int                                                               NOT NULL AUTO_INCREMENT,
    `org_id`      int                                                               NOT NULL COMMENT '组织id',
    `reason`      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '解散原因：1-停止运营；2-功能不满足；3-即将离职；',
    `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '需要解决的问题',
    `user_id`     int                                                               NOT NULL COMMENT '拥有者userId',
    `phone`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '拥有者手机号',
    `create_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 48
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='租户结算组织记录表';

CREATE TABLE `e_org_invoice_info`
(
    `id`                int                                                               NOT NULL AUTO_INCREMENT COMMENT '主键',
    `org_id`            int                                                               NOT NULL COMMENT '组织id',
    `invoice_type`      tinyint                                                           NOT NULL COMMENT '发票类型：1-增值税普通发票；2-增值税专用发票；',
    `invoice_title`     varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '发票抬头',
    `company_tax_id`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '纳税人识别号',
    `company_address`   varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '注册场所地址',
    `company_telephone` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '注册固定电话',
    `bank_name`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '开户银行名称',
    `bank_account`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '开户银行账户',
    `address`           varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '收件人地址',
    `detailed_address`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '详细地址',
    `contact_name`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '收件人姓名',
    `contact_phone`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '收件人电话',
    `create_by`         int                                                               NOT NULL COMMENT '创建人',
    `update_by`         int                                                               NOT NULL COMMENT '更新人',
    `create_date`       datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`       datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `org` (`org_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 66
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='租户开票信息表';

CREATE TABLE `e_org_setting`
(
    `id`          int      NOT NULL AUTO_INCREMENT,
    `org_id`      int      NOT NULL,
    `content`     json     NOT NULL,
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `org` (`org_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 8058
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_permission`
(
    `id`          int                                                              NOT NULL AUTO_INCREMENT,
    `name`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `product`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `key`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `description` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL,
    `create_date` datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `key` (`key`),
    KEY `product` (`product`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 20063
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_permission_resource`
(
    `id`            int      NOT NULL AUTO_INCREMENT,
    `permission_id` int      NOT NULL,
    `resource_id`   int      NOT NULL,
    `create_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UNIQUE` (`permission_id`, `resource_id`),
    KEY `permissionId_idx` (`permission_id`),
    KEY `resourceId_idx` (`resource_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1276
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_resource`
(
    `id`          int                                                               NOT NULL AUTO_INCREMENT,
    `name`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '',
    `comment`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '',
    `operation`   varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci   NOT NULL DEFAULT '',
    `uri`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `create_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `product`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '' COMMENT '对应业务产品：KZZ_ENT - 企业中心，SAAS_CZB - 查招标，SAAS_JZCC - 建筑 ， SAAS_ROVER - 客商排查',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uri_operation_unique` (`uri`, `operation`) USING BTREE,
    UNIQUE KEY `operation_uri_unique` (`operation`, `uri`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 30055
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_role`
(
    `role_id`     int                                                              NOT NULL AUTO_INCREMENT,
    `org_id`      int                                                                       DEFAULT NULL,
    `role_name`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `scope`       tinyint                                                          NOT NULL DEFAULT '1' COMMENT '1-本人；2-本部门；3-本部门和下属部门；4-全公司；',
    `level`       int                                                              NOT NULL DEFAULT '0',
    `comment`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci         DEFAULT NULL,
    `is_system`   tinyint                                                          NOT NULL DEFAULT '1',
    `active`      tinyint                                                          NOT NULL DEFAULT '1' COMMENT '状态：0-被删除；1-正常；',
    `create_by`   int                                                              NOT NULL,
    `update_by`   int                                                              NOT NULL,
    `create_date` datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`role_id`),
    KEY `org` (`org_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 28979
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_role_permission`
(
    `id`            int NOT NULL AUTO_INCREMENT,
    `role_id`       int NOT NULL,
    `permission_id` int NOT NULL,
    PRIMARY KEY (`id`),
    KEY `index_role` (`role_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3901072
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_sms_log`
(
    `id`            int                                                               NOT NULL AUTO_INCREMENT,
    `org_id`        int                                                                        DEFAULT NULL COMMENT '组织id',
    `phone`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '手机号',
    `is_system`     tinyint                                                           NOT NULL COMMENT '是否系统级短信：1-是；0-否；',
    `tpl_id`        varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '模版id：验证码-3221312；订阅推送-3287040；拒绝试用-3536700；试用开通-3529646；加入企业-3529702；邀请加入-3810174；短信不足-4743778；话费不足-4743776；发票失败-4743774；线索掉保-4531330；客户掉保-4531326；跟进回款-4747324；商机成交-4747234；商机阶段-4747236；合同到期-4747240；',
    `content`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '发送内容',
    `status`        tinyint                                                           NOT NULL COMMENT '发送状态：1-发送成功；2-发送失败；',
    `error_message` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '错误消息',
    `create_date`   datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`   datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `orgId` (`org_id`, `status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 7726
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='短信发送记录';

CREATE TABLE `e_system_message`
(
    `id`           int                                                               NOT NULL AUTO_INCREMENT COMMENT '主键',
    `title`        varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '消息内容',
    `publish_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布日期',
    `create_date`  datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_date`  datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    PRIMARY KEY (`id`),
    KEY `publish_date` (`publish_date` DESC)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_tracking_count`
(
    `org_id` int unsigned NOT NULL COMMENT '组织id',
    `cdate`  date         NOT NULL COMMENT '跟进日期',
    `count`  int unsigned NOT NULL COMMENT '跟进数量',
    PRIMARY KEY (`org_id`, `cdate`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_user`
(
    `user_id`       int                                                               NOT NULL AUTO_INCREMENT,
    `email`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
    `phone`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '',
    `create_date`   datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`   datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `name`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '',
    `active`        tinyint                                                           NOT NULL DEFAULT '1',
    `used`          tinyint                                                           NOT NULL DEFAULT '1' COMMENT '是否使用过该组织：0-未使用；1-使用过；',
    `login_user_id` int                                                               NOT NULL,
    `org_id`        int                                                               NOT NULL,
    `guid`          char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci     NOT NULL DEFAULT '',
    `last_login`    datetime                                                                   DEFAULT NULL,
    `b_user_id`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL,
    `faceimg`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT '头像',
    `position`      tinyint                                                                    DEFAULT '0' COMMENT '0 职员 1 主管',
    `staff_id`      varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT '员工工号',
    `str_id`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT '用户id（字符串）',
    PRIMARY KEY (`user_id`),
    KEY `orgId` (`org_id`),
    KEY `phoneExists` (`org_id`, `phone`, `active`),
    KEY `idx_loguid` (`login_user_id`, `org_id`, `active`),
    KEY `idx_guid_org_act` (`guid`, `org_id`, `active`),
    KEY `idx_org_id` (`org_id`, `active`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 103349
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='组织用户表';

CREATE TABLE `e_user_coupon`
(
    `id`           bigint                                     NOT NULL AUTO_INCREMENT,
    `guid`         char(32) COLLATE utf8mb4_unicode_520_ci    NOT NULL COMMENT '用户guid',
    `coupon_code`  char(32) COLLATE utf8mb4_unicode_520_ci    NOT NULL COMMENT '优惠券码',
    `service_code` varchar(45) COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '产品code',
    `client`       varchar(45) COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '领取渠道',
    `create_date`  datetime                                   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    PRIMARY KEY (`id`),
    KEY `guid` (`guid`),
    KEY `couponCode` (`coupon_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 107
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_user_daily_active`
(
    `id`           int                                                               NOT NULL AUTO_INCREMENT,
    `org_id`       int                                                               NOT NULL,
    `user_id`      int                                                               NOT NULL,
    `date`         date                                                              NOT NULL,
    `times`        int                                                               NOT NULL DEFAULT '0',
    `ip_address`   varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT '',
    `ip_info`      json                                                              NOT NULL,
    `user_agent`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
    `create_date`  datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`  datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `service_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT 'KZZ_ENT' COMMENT '应用',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq` (`user_id`, `date`),
    KEY `orgId` (`org_id`),
    KEY `userId` (`user_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 22800
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='用户每日活跃统计表';

CREATE TABLE `e_user_dep`
(
    `id`                int      NOT NULL AUTO_INCREMENT,
    `user_id`           int      NOT NULL,
    `dep_id`            int      NOT NULL,
    `create_date`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`       datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `status`            tinyint  NOT NULL DEFAULT '0' COMMENT '0 可用；1 不可用',
    `create_date_copy1` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date_copy1` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `index_uid` (`user_id`),
    KEY `index_depid` (`dep_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 14595
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_user_invitation`
(
    `id`              int                                                               NOT NULL AUTO_INCREMENT COMMENT '主键',
    `invitation_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL,
    `phone`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `org_id`          int                                                               NOT NULL,
    `name`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL,
    `status`          tinyint                                                           NOT NULL DEFAULT '0' COMMENT '邀请状态：0-待确认；1-已接受；2-已拒绝；',
    `create_date`     datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `approvel_user`   int                                                                        DEFAULT NULL COMMENT '审批人id/邀请人id',
    `operate_date`    datetime                                                                   DEFAULT NULL,
    `mode_type`       varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL DEFAULT 'apply' COMMENT '来源模式 apply-用户申请, invitation-管理员邀请,email-邮箱验证',
    `content`         json                                                                       DEFAULT NULL COMMENT '邀请用户时填写的用户信息',
    `expiration_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '邀请记录到期时间',
    `verify_code`     varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT '邮箱验证码',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1801
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci
  ROW_FORMAT = DYNAMIC COMMENT ='邀请用户加入记录';

CREATE TABLE `e_user_role`
(
    `id`          int      NOT NULL AUTO_INCREMENT,
    `user_id`     int      NOT NULL,
    `role_id`     int      NOT NULL,
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UNIQUE` (`user_id`, `role_id`),
    KEY `fk.user_role.userId_idx` (`user_id`),
    KEY `fk.user_role.roleId_idx` (`role_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 12505
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `org_bundle_order`
(
    `id`               int                                                              NOT NULL AUTO_INCREMENT COMMENT '服务id(自增)',
    `org_id`           int                                                              NOT NULL COMMENT '组织id',
    `org_bundle_id`    int                                                              NOT NULL COMMENT '套餐id',
    `service_code`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '服务类型：SAAS_ROVER-第三方排查；',
    `service_type`     tinyint                                                                   DEFAULT '1' COMMENT '类型：1-套餐新增；2-套餐增购；3-套餐续约; 4-修改',
    `contract_code`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '合同编号',
    `price`            decimal(19, 2)                                                   NOT NULL COMMENT '合同价格',
    `contract_product` json                                                                      DEFAULT NULL COMMENT '合同产品明细',
    `type`             tinyint                                                          NOT NULL DEFAULT '1' COMMENT '套餐类型：0-赠送；1-付费；',
    `parameters`       json                                                                      DEFAULT NULL COMMENT '套餐配置',
    `modules`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci         DEFAULT NULL COMMENT '开通模块',
    `start_date`       datetime                                                                  DEFAULT NULL COMMENT '开始时间',
    `end_date`         datetime                                                                  DEFAULT NULL COMMENT '截止时间',
    `customer_name`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci         DEFAULT NULL COMMENT '合同对应客户名称',
    `op_name`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT '管理员姓名',
    `note`             varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci         DEFAULT NULL COMMENT '备注',
    `active`           tinyint                                                          NOT NULL DEFAULT '1' COMMENT '状态：0-删除；1-正常；',
    `create_date`      datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`      datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `service_category` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT '套餐版本：试用-trial; 标准版 standard-A; 高级版 standard-B; 大客户版 standard-C; 定制版 custom',
    `work_flow_code`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci          DEFAULT NULL COMMENT '关联工单编号',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1765
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `org_bundles`
(
    `org_bundle_id`      int            NOT NULL AUTO_INCREMENT,
    `org_id`             int            NOT NULL COMMENT '组织id，对应e_org表的org_id',
    `bundle_id`          int            NOT NULL COMMENT '套餐id，对应bundles表的bundle_id',
    `create_date`        datetime       NOT NULL                                           DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`        datetime       NOT NULL                                           DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `active_date`        datetime       NOT NULL                                           DEFAULT CURRENT_TIMESTAMP COMMENT '套餐开始时间：默认是创建时间，如果传值则覆盖',
    `expire_date`        datetime       NOT NULL COMMENT '套餐过期时间',
    `lock_date`          datetime                                                          DEFAULT NULL COMMENT '冻结日期',
    `active`             tinyint        NOT NULL                                           DEFAULT '1' COMMENT '是否生效：0-无效(过期)；1-有效；2-冻结；3-待生效；4-被抵扣；5-已退款；6-被关闭；7-被取消抵扣；',
    `belong_user`        int                                                               DEFAULT NULL COMMENT '私有用户的userId,\\\\n如果不为空，该套餐只能归指定的user使用',
    `period`             int            NOT NULL                                           DEFAULT '0' COMMENT '有效期，单位天数，用于计算expire_date.',
    `order_code`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL COMMENT '订单号',
    `trail_code`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL COMMENT '试用编号',
    `employee_id`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL COMMENT '试用申请工号',
    `contract_code`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL COMMENT '合同编号',
    `fee_type`           tinyint        NOT NULL                                           DEFAULT '1' COMMENT '费用类型：0-赠送；1-付费；2-抵扣；',
    `free_vip`           int            NOT NULL                                           DEFAULT '0' COMMENT '赠送vip数量',
    `price_paid`         decimal(19, 2) NOT NULL                                           DEFAULT '0.00' COMMENT '价格',
    `note`               varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT '备注',
    `create_by`          int            NOT NULL                                           DEFAULT '0' COMMENT '创建人id  bo_admin',
    `update_by`          int            NOT NULL                                           DEFAULT '0' COMMENT '最后更新人id  bo_admin',
    `lock_code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL,
    `related_order`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL COMMENT '关联c端订单号',
    `pre_credit_code`    varchar(45) COLLATE utf8mb4_unicode_520_ci                        DEFAULT NULL COMMENT '预授信编号',
    `pre_credit_end`     datetime                                                          DEFAULT NULL COMMENT '预授信结束日期',
    `parameters_setting` json                                                              DEFAULT NULL COMMENT '组织套餐项设置',
    PRIMARY KEY (`org_bundle_id`),
    KEY `org_id` (`org_id`),
    KEY `order_code` (`order_code`),
    KEY `bundle_id` (`bundle_id`),
    KEY `create_date` (`create_date` DESC),
    KEY `lock_code` (`lock_code`),
    KEY `idx_trailcode` (`trail_code`),
    KEY `idx_bud_org_id_ct` (`bundle_id`, `org_id`, `create_date`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 86550
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `org_bundles_log`
(
    `id`                  int                                                               NOT NULL AUTO_INCREMENT COMMENT '服务id(自增)',
    `parent_id`           int                                                                        DEFAULT NULL COMMENT '父级服务id',
    `order_code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '订单号/合同号',
    `order_from`          tinyint                                                           NOT NULL COMMENT '订单来源：1-线上；2-线下；',
    `price_paid`          decimal(19, 2)                                                    NOT NULL COMMENT '订单金额/合同金额',
    `order_time`          datetime                                                          NOT NULL COMMENT '下单时间',
    `phone`               varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '用户手机号码',
    `guid`                varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '用户guid',
    `org_id`              int                                                               NOT NULL COMMENT '组织id',
    `org_bundle_id`       int                                                               NOT NULL COMMENT '套餐id',
    `service_code`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '服务类型：SAAS_CZB-招标查查；SAAS_JZCC-建筑查查；',
    `service_category`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '套餐类型：personal-标准版；enterprise-高级版；custom-定制版；export-项目加量包；subscription-订阅加量包；province-区域加订包；',
    `service_name`        varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '套餐名称，如：招标查查标准版',
    `member_limit`        int                                                               NOT NULL COMMENT '套餐人数',
    `service_price`       decimal(19, 2)                                                    NOT NULL COMMENT '服务金额',
    `start_date`          datetime                                                          NOT NULL COMMENT '服务开始时间',
    `end_date`            datetime                                                          NOT NULL COMMENT '服务结束日期',
    `change_source`       tinyint                                                           NOT NULL DEFAULT '0' COMMENT '变动来源：0-默认值；1-C端开通；2-C端续费；3-C端升级；4-BOSS开通；5-BOSS续费；6-BOSS升级；7-BOSS变更；8-BOSS退款；9-BOSS取消退款；10-企业团队管理-添加成员；11-企业团队管理-删除用户；',
    `change_source_order` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci           DEFAULT NULL COMMENT '变动来源合同编号/订单号',
    `change_source_id`    int                                                                        DEFAULT NULL COMMENT '变更服务来源id',
    `is_hedge`            tinyint                                                           NOT NULL DEFAULT '0' COMMENT '是否对冲：0-否；1-是；',
    `hedge_reason`        tinyint                                                                    DEFAULT NULL COMMENT '对冲原因，如：1-折算时长；2-冻结；3-延期；4-退款；5-取消退款；6-解冻；',
    `create_date`         datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`         datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `orgBundleId` (`org_bundle_id`),
    KEY `orderCode` (`order_code`),
    KEY `orgId` (`org_id`),
    KEY `phone` (`phone`),
    KEY `guid` (`guid`),
    KEY `serviceCode` (`service_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 9906
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='租户套餐变更记录';

CREATE TABLE `renew_order`
(
    `id`            int                                                               NOT NULL AUTO_INCREMENT,
    `guid`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '用户guid',
    `phone`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '用户手机号',
    `goods_id`      int                                                               NOT NULL COMMENT '产品id',
    `agreement_no`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '签约号',
    `start_date`    datetime                                                          NOT NULL COMMENT '开始时间',
    `end_date`      datetime                                                          NOT NULL COMMENT '截止时间',
    `order_total`   decimal(19, 2)                                                    NOT NULL COMMENT '扣款金额',
    `status`        tinyint                                                           NOT NULL DEFAULT '0' COMMENT '订单状态：0-扣款中；1-扣款成功；2-扣款失败；',
    `result`        json                                                              NOT NULL COMMENT '返回结果',
    `error_message` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT '错误消息',
    `create_date`   datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`   datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 800
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `setting_release`
(
    `setting_id`    bigint    NOT NULL,
    `creator`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
    `type`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
    `group_version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
    `release_note`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
    `org_whitelist` json                                                              DEFAULT NULL,
    `creation_time` timestamp NULL                                                    DEFAULT NULL,
    `title`         varchar(100) COLLATE utf8mb4_unicode_520_ci                       DEFAULT NULL,
    PRIMARY KEY (`setting_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `tender_bundles`
(
    `id`                 int                                                               NOT NULL AUTO_INCREMENT,
    `org_id`             int                                                               NOT NULL COMMENT '企业id',
    `org_name`           varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '企业名称',
    `org_owner_id`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '企业归属人guid',
    `org_owner_phone`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '企业归属人手机号',
    `login_user_id`      int                                                               NOT NULL COMMENT 'loginUserId',
    `user_id`            int                                                               NOT NULL COMMENT '用户id',
    `guid`               char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci     NOT NULL COMMENT '用户guid',
    `phone`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '用户手机号',
    `org_bundle_id`      int                                                               NOT NULL COMMENT '组织套餐id',
    `bundle_id`          int                                                               NOT NULL COMMENT '套餐id',
    `service_code`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '套餐产品',
    `service_category`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '套餐类型',
    `service_name`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '套餐名称',
    `service_version`    tinyint                                                           NOT NULL COMMENT '套餐版本',
    `bundle_code`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '套餐代号',
    `start_date`         datetime                                                          NOT NULL COMMENT '套餐开始时间',
    `end_date`           datetime                                                          NOT NULL COMMENT '套餐结束日期',
    `subscription_count` int                                                               NOT NULL COMMENT '订阅数量',
    `monitor_count`      int                                                               NOT NULL COMMENT '监控企业数量',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq` (`login_user_id`, `service_code`),
    KEY `orgName` (`org_name`),
    KEY `guid` (`guid`),
    KEY `phone` (`phone`),
    KEY `serviceCode` (`service_code`),
    KEY `bundleCode` (`bundle_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1078181
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `update_log`
(
    `log_id`         bigint                                                           NOT NULL AUTO_INCREMENT,
    `name`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '更新日志名称',
    `status`         int                                                               DEFAULT '1' COMMENT '当前状态:1-待发布;2-已发布',
    `release_time`   datetime                                                          DEFAULT NULL COMMENT '发布时间',
    `content`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci COMMENT '更新日志内容',
    `notice_content` json                                                              DEFAULT NULL COMMENT '公告内容,JSONArray字符串,包含标题/内容/图片',
    `log_type`       int                                                               DEFAULT NULL COMMENT '日志更新类型:1-PC;2-ios;3-Android',
    `app_version`    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  DEFAULT NULL COMMENT 'ios/安卓更新的版本号',
    `create_date`    datetime                                                          DEFAULT CURRENT_TIMESTAMP,
    `update_date`    datetime                                                          DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_by`      int                                                              NOT NULL COMMENT '创建人',
    `update_by`      int                                                               DEFAULT NULL COMMENT '最近更新人',
    `release_by`     int                                                               DEFAULT NULL COMMENT '发布人',
    `service_code`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT 'SAAS_ROVER' COMMENT 'SAAS_ROVER',
    PRIMARY KEY (`log_id`),
    KEY `name_index` (`name`),
    KEY `release_time_index` (`release_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 10009
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='更新日志表';

CREATE TABLE `user`
(
    `user_id` int                                        NOT NULL AUTO_INCREMENT,
    `name`    varchar(45) COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `phone`   varchar(45) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
    `email`   varchar(45) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
    PRIMARY KEY (`user_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `user_bundles`
(
    `user_bundle_id` int      NOT NULL AUTO_INCREMENT,
    `org_bundle_id`  int      NOT NULL COMMENT '组织套餐id，对应org_bundles表的org_bundle_id',
    `user_id`        int      NOT NULL COMMENT '用户id，对应e_user表的user_id',
    `params`         json     NOT NULL COMMENT '如果用户被分配了 org bundle一部分，通过该json进行赋值，并在计数器中使用该值\n用户层面可以使用的套餐项来自与：\n1. org_bundle 可以所有成员共同使用的部门 \n2. org_bundle 被分配到个人头上的额度。 对应parameters字段的内容 \n3. 购买只属于指定用户的叠加包',
    `create_date`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`    datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_bundle_id`),
    UNIQUE KEY `user_bundle` (`user_id`, `org_bundle_id`),
    KEY `org_bundle_id` (`org_bundle_id`),
    KEY `user_id` (`user_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 43405
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `user_bundles_log`
(
    `id`                  int                                                               NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `service_id`          int                                                               NOT NULL COMMENT '套餐履约表中的id',
    `org_id`              int                                                               NOT NULL COMMENT '组织id',
    `user_id`             int                                                               NOT NULL COMMENT '用户id',
    `phone`               varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '用户手机号',
    `guid`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '用户guid',
    `org_bundle_id`       int                                                               NOT NULL COMMENT '套餐id',
    `order_code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '订单号/合同号',
    `service_code`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '服务类型：SAAS_CZB-招标查查；SAAS_JZCC-建筑查查；',
    `service_category`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci  NOT NULL COMMENT '套餐类型：personal-标准版；enterprise-高级版；custom-定制版；',
    `service_name`        varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT '套餐名称：如招标查查标准版；',
    `member_limit`        int                                                               NOT NULL COMMENT '套餐人数',
    `start_date`          datetime                                                          NOT NULL COMMENT '开始时间',
    `end_date`            datetime                                                          NOT NULL COMMENT '预计结束日期',
    `real_end_date`       datetime                                                                   DEFAULT NULL COMMENT '实际结束日期',
    `start_change_source` tinyint                                                           NOT NULL DEFAULT '0' COMMENT '开始变动来源：0-默认值；1-套餐生效；2-套餐到期；3-套餐被抵扣；4-套餐被冻结；5-套餐被解冻；6-企业中心添加用户权益；7-企业中心移除用户权益；8-退款；9-取消退款；',
    `end_change_source`   tinyint                                                           NOT NULL DEFAULT '0' COMMENT '结束变动来源：0-默认值；1-套餐生效；2-套餐到期；3-套餐被抵扣；4-套餐被冻结；5-套餐被解冻；6-企业中心添加用户权益；7-企业中心移除用户权益；8-退款；9-取消退款；',
    `create_date`         datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`         datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `serviceId` (`service_id`),
    KEY `orgBundleId` (`org_bundle_id`),
    KEY `phone` (`phone`),
    KEY `guid` (`guid`),
    KEY `orgId` (`org_id`),
    KEY `userId` (`user_id`),
    KEY `orderCode` (`order_code`),
    KEY `serviceCode` (`service_code`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5579
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci COMMENT ='用户套餐变更记录';

CREATE TABLE `user_custom_bundles`
(
    `user_bundle_id` int                                                              NOT NULL AUTO_INCREMENT,
    `org_id`         int                                                              NOT NULL,
    `product`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'SAAS_KZZ' COMMENT '应用\\\\n\\\\\\\\nSAAS_KZZ\\\\\\\\nSAAS_ROVER\\\\\\\\nSAAS_JZCC\\\\\\\\nSAAS_ZBCC',
    `user_id`        int                                                              NOT NULL,
    `params`         json                                                             NOT NULL COMMENT '如果用户被分配了 org bundle一部分，通过该json进行赋值，并在计数器中使用该值\\\\n用户层面可以使用的套餐项来自与：\\\\n1. org_bundle 可以所有成员共同使用的部门\\\\n2. org_bundle 被分配到个人头上的额度。 对应parameters字段的内容\\\\n3. 购买只属于指定用户的叠加包\\\\n',
    `create_date`    datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date`    datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_bundle_id`),
    UNIQUE KEY `org_product_user` (`org_id`, `product`, `user_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;
CREATE TABLE `e_idp_account`
(
    `idp_id`      int                                                               NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `type`        int                                                               NOT NULL COMMENT '身份类型：1-钉钉，2-企业微信，3-飞书，4-蔡司(定制)',
    `org_id`      int                                                               NOT NULL COMMENT '组织Id',
    `create_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `active`      tinyint(1)                                                        NOT NULL DEFAULT '0' COMMENT '状态：0-关闭；1-正常；',
    `create_by`   int                                                               NOT NULL COMMENT '创建人',
    `update_by`   int                                                               NOT NULL COMMENT '更新人',
    `app_id`      varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'app_id',
    `app_secret`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'app_secret',
    `corp_id`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT 'corp_id',
    `test`        tinyint(1)                                                        NOT NULL DEFAULT '0' COMMENT '测试成功：0-未成功；1-成功；',
    `test_date`   datetime                                                                   DEFAULT NULL COMMENT '测试时间',
    `visible`     tinyint                                                           NOT NULL DEFAULT '1' COMMENT '是否可见：1-可见，0-不可见',
    PRIMARY KEY (`idp_id`) USING BTREE,
    UNIQUE KEY `uniq.idp` (`type`, `org_id`) USING BTREE,
    KEY `fk.idp.org_id_idx` (`org_id`) USING BTREE,
    CONSTRAINT `fk.idp.org_id` FOREIGN KEY (`org_id`) REFERENCES `e_org` (`org_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB
  AUTO_INCREMENT = 56
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_idp_feedback`
(
    `feedback_id` int      NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `org_id`      int      NOT NULL COMMENT '组织Id',
    `source_type` int      NOT NULL COMMENT 'IDP身份源：1-企业微信，2-飞书，3-其他',
    `note`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT '' COMMENT '备注',
    `create_date` datetime NOT NULL                                                 DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by`   int      NOT NULL COMMENT '创建人',
    PRIMARY KEY (`feedback_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 38
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;

CREATE TABLE `e_idp_login`
(
    `login_id`    int                                                              NOT NULL AUTO_INCREMENT COMMENT '主键Id',
    `user_id`     int                                                              NOT NULL COMMENT '用户Id',
    `type`        int                                                              NOT NULL COMMENT '身份类型：1-钉钉，2-企业微信',
    `open_id`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'openId',
    `create_date` datetime                                                         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`login_id`),
    UNIQUE KEY `uniq.idplogin` (`user_id`, `type`, `open_id`) USING BTREE,
    CONSTRAINT `fk.idplogin.user_id` FOREIGN KEY (`user_id`) REFERENCES `e_user` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB
  AUTO_INCREMENT = 17
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_520_ci;
