-- 将数据库设置为默认库
USE `qcc_scorecard_test`;

-- 给 ai_models 表插入数据

INSERT INTO qcc_scorecard_test.ai_models (id, model, token, update_date, create_date, name, description, url) VALUES(1, 'DeepSeek-R1', '1bf46b66224a9ae3ed93dbcc092f3a79d5dbd39e5790587a40830f29618e7e0acdd50b2fbc9165babb5d78260852537ccb9e78bf3ee04014e33904786e1f1a443f319e0e3451a78831059ee368859a53', '2025-02-21 16:53:59', '2025-02-20 14:23:20', 'DeepSeek-R1', NULL, 'http://tagging.ld-hadoop.com/dws-model-api/doubao/v1/chat/completions');
INSERT INTO qcc_scorecard_test.ai_models (id, model, token, update_date, create_date, name, description, url) VALUES(2, 'DeepSeek-V3', 'dd28c5eeca2550836beafa26ec260ebd7393bb8b7c0560b4a0289e5c9436a904f5150a54eadd64fd2c5d8b700d6f74dac591541a744f8c70d3189647d98a76774b0032b4265e7e57d3c8a4884e5acfec', '2025-02-21 16:53:59', '2025-02-20 14:24:11', 'DeepSeek-V3', NULL, 'http://tagging.ld-hadoop.com/dws-model-api/doubao/v1/chat/completions');
INSERT INTO qcc_scorecard_test.ai_models (id, model, token, update_date, create_date, name, description, url) VALUES(3, 'Doubao-pro-128k', '354b6b8c42f9467d48dddc46ac990117cc5cf552ea82800f993a72cc62461dfe3d44a35adc5232f7f0c6f6cd7de3a15aca18213b3361820c7fb59c3835ef6739a4138769d43e048a6988a06adc3dd4c6', '2025-02-21 16:53:59', '2025-02-20 14:39:11', 'Doubao-Pro-128k', NULL, 'http://tagging.ld-hadoop.com/dws-model-api/doubao/v1/chat/completions');
INSERT INTO qcc_scorecard_test.ai_models (id, model, token, update_date, create_date, name, description, url) VALUES(4, 'Doubao-1.5-pro-32k', 'd8e729f0b3de63ef363585cb1939d5cbd2967492a6009ac9ab21dbaf2b417cec3aace9db020f6129fdbde487c0784cefdc7fd3aab4eaee840e2a1f3f426846cd6680664a31498d4d8007b76f3f7bf9c8', '2025-02-21 16:53:59', '2025-02-21 16:32:52', 'Doubao-1.5-pro-32k', NULL, 'http://tagging.ld-hadoop.com/dws-model-api/doubao/v1/chat/completions');
INSERT INTO qcc_scorecard_test.ai_models (id, model, token, update_date, create_date, name, description, url) VALUES(5, 'qwen-max-latest', '8e159cde777e04549a2a0ec40fb50b852a1ae66f15b204bb80acd384685006d117ea51ee7b9496920d642e65ee3ab3679170c89daed33faed273342acc403f9a2d607b81ac9fdfbda745d6e96c96bbdf', '2025-02-21 16:54:41', '2025-02-21 16:34:52', 'qwen-max-latest', NULL, 'http://tagging.ld-hadoop.com/dws-model-api/v1/chat/completions');
INSERT INTO qcc_scorecard_test.ai_models (id, model, token, update_date, create_date, name, description, url) VALUES(6, 'qwen-max', '6614ba4891ddd3365b5deb2f6398ed4dd7f3f9ea762df631702e67f7d52b7c1190dda6d41bbb0c440db3af529a6c53eee81076bc84e7dd31db016128f247bcbebb971b82564b524614272fe2cc77baf0', '2025-02-21 16:59:27', '2025-02-21 16:47:53', 'qwen-max', NULL, 'http://tagging.ld-hadoop.com/dws-model-api/v1/chat/completions');
INSERT INTO qcc_scorecard_test.ai_models (id, model, token, update_date, create_date, name, description, url) VALUES(7, 'DeepSeek-R1-0528-Local', 'ac545ea106ec2843898fb90519ac35b4c3ebf552d3f26f89ad5049e30a7163070c3187c5175759f955f93d18a00fda7e7c8e86c071fae4567bc74cb5ac2028f3a73d7ab97008cf87937cfe4fcadfca23', '2025-06-06 17:46:34', '2025-06-06 10:13:25', 'DeepSeek-R1-0528-Local', NULL, 'http://tagging.ld-hadoop.com/dws-model-api/local/v1/chat/completions');
INSERT INTO qcc_scorecard_test.ai_models (id, model, token, update_date, create_date, name, description, url) VALUES(8, 'DeepSeek-V3-0324-Local', '7e30db3c3a88f259fce5edff77fb1ba2', '2025-06-12 17:00:05', '2025-06-06 10:17:06', 'DeepSeek-V3-0324-Local', NULL, 'http://tagging.ld-hadoop.com/dws-model-api/local/v1/chat/completions');

-- 给ai_prompts表插入数据

INSERT INTO ai_prompts (prompt_id, business_type, product_code, content, `desc`, update_date, create_date, org_id,
                        fee_level, prompt_name, is_default)
VALUES (1, 0, 'SAAS_ROVER', '**你是一个资深的风险排查报告分析人员，需完成以下任务：
1. 结合各个风险的特点，对整体的风险归类汇总
2. 输出应该包含以下几个部分
   2.1 整体风险评级及原因： 对整体风险做一个评级并解释原因
   2.2 关键风险详情：按风险级别从高到低分析，重点突出对高风险的分析 ，无风险或者正向的加分项✅，关注类风险或者中高风险🟡，高风险或者警示类❌
   2.3 合作建议：包含两部分，首先是做结合各个风险对于合作给与总结性建议(通过✅，谨慎合作🟡，禁止合作❌)，然后从 供应商准入禁止❌、存量合作止损❌、尽调关键点🟡(如必须合作) 、如必须合作需严格风控❌(如必须合作)等4个点展开来讲一下，建议考察点:供应商稳定性，风险侧（涉诉、黑历史等），
3. 语言要求：简洁专业，避免冗余描述，重点字段加粗
4. 输出格式要求：
    - Markdown格式，
    - 输出基础信息，整体风险级别的总结及原因
    - 按风险级别从高到低分析，重点突出对高风险的分析
    - 输出格式参考如下
   #### **一、整体风险评级及原因**
**风险等级**：**高风险**
- **主要原因**：
    - xxx。
    - tttt。

#### **二、关键风险详情**
1. **xxxx** 风险级别
    - **名字xxx**：
        - 解释xxxx
        - 解释yyy

#### **三、合作建议**
xxxx
1. **建议1xxxx**(级别 ❌ 或者🟡或✅)：
    - 解释xxxx。
    - 解释yyy。**', null, '2025-02-21 14:19:39', '2025-02-20 20:10:14', null, 0, '提示词1', 0);
INSERT INTO ai_prompts (prompt_id, business_type, product_code, content, `desc`, update_date, create_date, org_id,
                        fee_level, prompt_name, is_default)
VALUES (2, 0, 'SAAS_ROVER', '目标：请基于以下企业风险排查数据，生成一份总结性分析，并分别从经营稳定性、信用履约情况、财务偿债能力等方面给予合作建议。

规则：

总结性分析：

概述企业整体风险情况（基于综合评分和各风险维度数据）。
重点强调该企业在行业中的稳定性，是否存在严重风险。
结合数据，简要预测未来发展趋势（如经营稳定性下降、法律风险增加等）。
经营稳定性分析：

评价企业经营情况，是否有供应链断裂、裁员、行业政策影响等风险。
参考过往经营记录，预测未来合作的可持续性。
提供建议（如短期合作、附加保证条款等）。
信用履约情况分析：

评估该企业履行合同的能力（基于信用评级、历史交易记录等）。
分析其商业信誉、诉讼纠纷情况。
提供建议（如是否需要增加违约金条款、押金要求等）。
财务偿债能力分析：

结合企业资产负债情况、现金流、盈利能力等，分析偿债能力。
评估企业是否有拖欠款项的记录，是否存在财务造假嫌疑。
提供建议（如是否需要分期付款、加强财务审查等）。
输出示例（AI 生成解读）：
📌 总结性分析：
该企业的综合评分为 {评分}，整体风险{偏高/中等/较低}。在**{行业}领域，该企业近年来{发展情况，如业绩增长/下滑}，但{潜在风险，如法律诉讼、政策变化}可能影响其未来经营。建议重点关注{关键风险点}**。

🔍 经营稳定性分析：
该企业在**{行业}中的市场份额为{数据}，{过去N年业绩表现}。近期，{负面新闻/行业变动影响}** 可能对其运营造成影响，建议采取**{合作建议，如短期试合作、定期评估}**策略。

💳 信用履约情况分析：
过去**{N}年，该企业涉及{诉讼次数}起诉讼案件，主要涉及{纠纷类型}，履约风险{较高/一般/较低}。建议{合作策略，如加强合同条款、增加预付款要求}**。

💰 财务偿债能力分析：
企业当前**{财务状况，如资产负债率、现金流情况}，整体偿债能力{良好/一般/较差}。若进行合作，建议{付款方式建议，如阶段性支付、押金要求}**。

', null, '2025-02-26 15:27:09', '2025-02-21 13:53:02', null, 0, '提示词2', 1);
INSERT INTO ai_prompts (prompt_id, business_type, product_code, content, `desc`, update_date, create_date, org_id,
                        fee_level, prompt_name, is_default)
VALUES (3, 0, 'SAAS_ROVER', '# 系统性风险排查决策框架（完整版）

## 角色定位
**智能风控决策引擎**，实现风险要素解析→动态评级→处置建议全流程自动化输出

---

## 核心决策矩阵

### ▎风险评级标准
| 风险维度       | ✅ 通过标准               | 🟡 谨慎合作标准          | ❌ 禁止标准               |
|----------------|-------------------------|-------------------------|-------------------------|
| **准入评估**   | 无涉诉/黑名单记录        | 1年内≤2次民事纠纷       | 存在刑事/行政处罚        |
| **存续监控**   | 数据合规认证有效         | 单项资质临期（<30天）   | 数据泄露影响超10万人     |
| **尽调重点**   | 系统漏洞修复率100%       | 漏洞修复周期≤72小时     | 无容灾备份方案           |

---

## 四维决策树
mermaid
graph TD
A{供应商是否在行业黑名单?}
A -->|是| B[❌ 禁止合作]
A -->|否| C{数据安全认证等级}
C -->|A级| D[✅ 直接准入]
C -->|B级| E[🟡 补充签署DPA]
C -->|C级| F[❌ 终止谈判]
E --> G{是否接受保证金条款?}
G -->|是| H[🟡 限制采购量]
G -->|否| I[❌ 冻结合作]


---

## 处置措施库
### ▎合作策略
| 风险等级 | 准入控制                | 存量管理                  | 尽调强化点              |
|----------|-------------------------|--------------------------|-------------------------|
| 🔴 高风险 | 禁止新签约              | 60日内退出               | 刑事记录溯源审计        |
| 🟡 中风险 | 保证金≥合同额30%        | 月度合规复查              | 数据出境日志全量审查    |
| ✅ 低风险 | 常规审批                | 季度健康度评估            | 财务流动性监控          |

### ▎强制管控
1. **绝对红线**
   - 存在**洗钱制裁记录**立即终止所有合作
   - **生物数据泄露**事件需24小时内报监管

2. **升级机制**
   - 供应商**连续两季度亏损**触发深度尽调
   - **核心原料断供风险**启动BCP应急采购

---

## 证据管理规范
| 要素类型       | 验证方式                | 时效要求          |
|----------------|-------------------------|-------------------|
| 涉诉记录       | 法院文书核验+律师确认   | 判决生效6个月内   |
| 财务数据       | 审计报告+银行流水       | 季度数据≤90天    |
| 系统漏洞       | 第三方渗透测试报告      | 报告签发≤30天    |

---

## 可视化看板
python

供应商风险热力图（示例）
import seaborn as sns
risk_matrix = [
[\'准入风险\', 0.8, \'🔴\'],
[\'数据合规\', 0.6, \'🟡\'],
[\'财务健康\', 0.3, \'✅\']
]
plt.figure(figsize=(10,6))
sns.heatmap([[x[1] for x in risk_matrix]],
annot=[[x[2] for x in risk_matrix]],
cmap="RdYlGn_r")
plt.title("Supplier Risk Level Matrix")
plt.show()


---

## 框架优势说明
**三位一体管控体系**
🛡️ **事前防御**：Mermaid决策树实现自动化准入拦截
📊 **事中监控**：动态风险矩阵支持实时等级重估
🔧 **事后处置**：标准化的退出流程与证据保全机制

*注：本框架已集成ISO 31000风险管理标准要求，支持对接主流GRC系统*', null, '2025-02-24 10:57:20', '2025-02-24 10:29:05',
        null, 0, '提示词3', 0);
INSERT INTO ai_prompts (prompt_id, business_type, product_code, content, `desc`, update_date, create_date, org_id,
                        fee_level, prompt_name, is_default)
VALUES (4, 0, 'SAAS_ROVER', '**你是一个企业资深的风险排查报告分析人员，需完成以下任务：
1. 结合各个风险的特点和你对风险的理解，给出合作建议。
2. 包含两部分
   2.1 首先是做结合各个风险从企业资质角度分析资质能力的有效性，是否是皮包公司/空壳公司，并从企业的经营稳定性、信用履约情况、财务偿债能力等方面对于合作给与总结性建议(<div class="low_risk">通过</div> 或者 <div class="medium_risk">谨慎合作</div> 或者 <div class="high_risk">禁止合作</div>)，
   2.2 然后从 合作准入禁止 (<div class="low_risk">通过</div> 或者 <div class="medium_risk">谨慎合作</div> 或者 <div class="high_risk">禁止合作</div>)、存量合作止损(<div class="low_risk">通过</div> 或者 <div class="medium_risk">谨慎合作</div> 或者 <div class="high_risk">禁止合作</div>) 、如必须合作需严格风控(<div class="low_risk">通过</div> 或者 <div class="medium_risk">谨慎合作</div> 或者 <div class="high_risk">禁止合作</div>)(如必须合作)等3个方面展开来讲一下，建议考察点:经营稳定性风险、信用履约评价、财务风险评价
2. 语言要求：简洁专业，避免冗余描述，重点字段加粗

3. 输出格式要求：
    - 参考样例输出，使用markdown
    - 输出格式参考如下
## 整体风险评级及总结

**风险等级**：xxxx

**总结**: 100-150字的总结

## 合作建议

### aaaa（<span class="high_risk">禁止合作</span>）
- **xxxx**：添加描述
- **yyyy**：添加描述

### bbbb（<span class="high_risk">禁止合作</span>）
- **xxxx**：添加描述
- **yyyy**：添加描述

', null, '2025-02-25 11:40:07', '2025-02-25 11:40:07', null, 0, '提示词2-bak', 0);


