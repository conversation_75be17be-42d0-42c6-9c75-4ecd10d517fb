CREATE DATABASE IF NOT EXISTS `qcc_scorecard_test`;


-- 将数据库设置为默认库
USE `qcc_scorecard_test`;

CREATE TABLE `access_control`
(
    `id`          int                                                               NOT NULL AUTO_INCREMENT,
    `access_key`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
    `product`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci      NOT NULL,
    `expire`      int                                                               NOT NULL,
    `status`      int                                                                        DEFAULT '0',
    `create_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime                                                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `comment`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci              DEFAULT NULL,
    `scope`       int                                                                        DEFAULT '1' COMMENT '1 只能能访问所有外部接口\n2 能访问 所有外部接口 + 平台接口\n',
    `secret_key`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci              DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `ai_chat_history`
(
    `chat_history_id` int                                                          NOT NULL AUTO_INCREMENT,
    `chat_id`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `prompt_id`       int      DEFAULT NULL,
    `input_content`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci        NOT NULL,
    `api_response`    json     DEFAULT NULL,
    `update_date`     datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`     datetime DEFAULT CURRENT_TIMESTAMP,
    `prompt_content`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'prompt_id 和 prompt_content 都可以',
    PRIMARY KEY (`chat_history_id`),
    KEY `index1` (`chat_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 589
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `ai_models`
(
    `id`          int                                                           NOT NULL AUTO_INCREMENT,
    `model`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL,
    `token`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `update_date` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date` datetime                                                      DEFAULT CURRENT_TIMESTAMP,
    `name`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `description` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `url`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 7
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `ai_prompts`
(
    `prompt_id`     int                                                          NOT NULL AUTO_INCREMENT,
    `business_type` int                                                          NOT NULL COMMENT '0 尽调\n1 风险巡检\n2 风险监控',
    `product_code`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '第三方，风险洞察',
    `content`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    `desc`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `update_date`   datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`   datetime                                                              DEFAULT CURRENT_TIMESTAMP,
    `org_id`        int                                                                   DEFAULT NULL COMMENT '如果没有值，就是系统级别的prompt，都可以用',
    `fee_level`     int                                                          NOT NULL DEFAULT '0' COMMENT '0 免费版\n1 收费1\n2 收费2',
    `prompt_name`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '报告分析员',
    `is_default`    int                                                                   DEFAULT '0',
    PRIMARY KEY (`prompt_id`),
    KEY `index` (`product_code`, `business_type`, `org_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `ai_report_chat`
(
    `chat_id`       varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `chat_name`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '截取用户输入的前几个字符',
    `update_date`   datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`   datetime                                                              DEFAULT CURRENT_TIMESTAMP,
    `report_id`     int                                                                   DEFAULT NULL COMMENT '生成报告之后的报告ID',
    `business_type` int                                                          NOT NULL DEFAULT '0' COMMENT '0 尽调\n1 监控\n2 风险巡检',
    `business_id`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'diligence_id 或者其他唯一的字符串',
    `org_id`        int                                                          NOT NULL,
    `user_id`       int                                                          NOT NULL,
    PRIMARY KEY (`chat_id`),
    KEY `index1` (`report_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `ai_report_comment`
(
    `comment_id`  int NOT NULL AUTO_INCREMENT,
    `report_id`   int NOT NULL,
    `org_id`      int NOT NULL COMMENT '组织 ID',
    `comment`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    `like_it`     int      DEFAULT '0' COMMENT '-1 踩,0 默认值,1 赞',
    `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
    `user_id`     int      DEFAULT NULL,
    PRIMARY KEY (`comment_id`),
    KEY `ai_report_comment_report_id_index` (`report_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 8
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `ai_reports`
(
    `report_id`      int                                                          NOT NULL AUTO_INCREMENT,
    `business_type`  int                                                          NOT NULL DEFAULT '0' COMMENT '0 尽调\n1 监控\n2 风险巡检',
    `business_id`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'diligence_id 或者其他唯一的字符串',
    `org_id`         int                                                          NOT NULL,
    `user_id`        int                                                          NOT NULL,
    `update_date`    datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`    datetime                                                              DEFAULT CURRENT_TIMESTAMP,
    `report_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci        NOT NULL,
    PRIMARY KEY (`report_id`),
    KEY `index1` (`business_id`, `business_type`, `user_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 590
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `batch`
(
    `batch_id`                 int                                                          NOT NULL AUTO_INCREMENT,
    `file_name`                varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `update_date`              datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`              datetime                                                              DEFAULT CURRENT_TIMESTAMP,
    `org_id`                   int                                                          NOT NULL,
    `dep_id`                   int                                                          NOT NULL DEFAULT '-1' COMMENT '部门id',
    `create_by`                int                                                          NOT NULL,
    `status`                   int                                                                   DEFAULT '0' COMMENT '0 待处理 1 处理中 2 处理成功 3处理失败',
    `comment`                  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '描述信息，可是是处理失败的时候的错误原因等',
    `batch_type`               int                                                          NOT NULL DEFAULT '0' COMMENT '0 导入\n1 导出',
    `result_file`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '批量（导入或者导出）结果对应的文件',
    `business_type`            int                                                                   DEFAULT '0' COMMENT '0 尽职调查\\n1 客商',
    `batch_info`               json                                                                  DEFAULT NULL COMMENT '批量排查任务：记录使用的排查模型； 年检任务： 记录年检设置规则；',
    `start_date`               datetime                                                              DEFAULT NULL,
    `record_count`             int                                                          NOT NULL COMMENT '该批次中包含的记录的count',
    `end_date`                 datetime                                                              DEFAULT NULL,
    `statistics_info`          json                                                         NOT NULL,
    `origin_file`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '批量导入的原始文件',
    `detail_file`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '排查详情对应的文件地址',
    `preview_url`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '预览地址',
    `can_retry`                int                                                                   DEFAULT '0' COMMENT '0-不可重试；1-可以重试',
    `paid_count`               int                                                                   DEFAULT '0',
    `error_count`              int                                                                   DEFAULT '0',
    `success_count`            int                                                                   DEFAULT '0',
    `updated_count`            int                                                                   DEFAULT '0',
    `duplicated_count`         int                                                                   DEFAULT '0',
    `withholding_count`        int                                                                   DEFAULT '0',
    `withholding_record_count` int                                                                   DEFAULT '0',
    `product`                  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
    PRIMARY KEY (`batch_id`),
    KEY `batch_status` (`batch_id`, `status`),
    KEY `org_batch` (`org_id`, `dep_id`, `batch_type`, `business_type`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 50014388
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `batch_diligence`
(
    `id`               int      NOT NULL AUTO_INCREMENT,
    `batch_id`         int      NOT NULL,
    `diligence_id`     int      NOT NULL,
    `job_id`           int               DEFAULT NULL,
    `create_date`      datetime          DEFAULT CURRENT_TIMESTAMP,
    `update_date`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `changing_version` int               DEFAULT NULL,
    `changing_detail`  json              DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `batch_id` (`batch_id`, `diligence_id`),
    KEY `job_id` (`job_id`),
    KEY `diligence_id` (`diligence_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 623695
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `batch_job`
(
    `job_id`      int  NOT NULL AUTO_INCREMENT,
    `job_info`    json NOT NULL COMMENT '类型根据 batch_type 不同可以分别映射',
    `status`      int  NOT NULL                                                  DEFAULT '0' COMMENT '0 待处理\n1 处理中\n2 处理成功\n3 处理失败',
    `update_date` datetime                                                       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date` datetime                                                       DEFAULT CURRENT_TIMESTAMP,
    `batch_id`    int  NOT NULL,
    `comment`     varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理失败的时候的错误原因',
    `start_date`  datetime                                                       DEFAULT NULL,
    `end_date`    datetime                                                       DEFAULT NULL,
    `error_date`  datetime                                                       DEFAULT NULL,
    PRIMARY KEY (`job_id`),
    KEY `batch_id` (`batch_id`, `status`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 166337
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `batch_match_company`
(
    `id`              int      NOT NULL AUTO_INCREMENT,
    `org_id`          int      NOT NULL,
    `file_name`       varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_by`       int      NOT NULL,
    `create_date`     datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP,
    `update_date`     datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP,
    `origin_file`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `statistics_info` json                                                          DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `batch_match_company_item`
(
    `id`          int      NOT NULL AUTO_INCREMENT,
    `batch_id`    int      NOT NULL,
    `name`        varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `company_id`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `create_date` datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP,
    `flag`        tinyint  NOT NULL,
    `match_by`    tinyint  NOT NULL,
    `parsed_item` json                                                          DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `batch_result`
(
    `result_id`      int  NOT NULL AUTO_INCREMENT,
    `result_type`    int  NOT NULL COMMENT '10 执行成功-付费\\n11 执行成功-未付费\\n12 执行成功-数据重复\\n20 执行失败 （代码执行过程中失败）\\n21 执行失败- 数据不合规\\n',
    `update_date`    datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`    datetime                                                      DEFAULT CURRENT_TIMESTAMP,
    `batch_id`       int  NOT NULL,
    `info`           json NOT NULL COMMENT '类型根据 batch_type 不同可以分别映射',
    `job_id`         int  NOT NULL,
    `comment`        varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注信息',
    `result_hashkey` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `result`         json                                                          DEFAULT NULL COMMENT '招标排查job执行的结果',
    PRIMARY KEY (`result_id`),
    KEY `batch_id_type` (`batch_id`, `result_type`),
    KEY `job_id` (`job_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 749181
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `company`
(
    `id`                int                                                           NOT NULL AUTO_INCREMENT,
    `name`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `company_id`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL,
    `econkind`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '企业类型code',
    `econkind_desc`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '企业类型中文描述',
    `province`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL,
    `city`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL,
    `district`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL,
    `industry1`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '国民行业1级',
    `industry2`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '国民行业2级',
    `industry3`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '国民行业3级',
    `industry4`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '国民行业4级',
    `registcapi`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '注册资本',
    `status_code`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '0' COMMENT '登记状态',
    `start_date_code`   datetime                                                               DEFAULT NULL COMMENT '成立时间',
    `registcapi_amount` int                                                                    DEFAULT NULL COMMENT '注册资本数值',
    `credit_rate`       int                                                                    DEFAULT NULL,
    `econ_type`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '0' COMMENT '企业性质',
    `enterprise_type`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '0' COMMENT '机构类型code',
    `update_date`       datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `list_status`       int                                                                    DEFAULT '2' COMMENT '上市状态:1-已上市,2-未上市',
    `reccap`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '实缴资本',
    `reccapamount`      int                                                                    DEFAULT NULL COMMENT '实缴资本金额数字(万元)',
    `scale`             varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '企业规模',
    PRIMARY KEY (`id`),
    UNIQUE KEY `company_id` (`company_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 109572
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='第三方和黑名单关联工商信息表';

CREATE TABLE `dataset`
(
    `id`          int                                                          NOT NULL AUTO_INCREMENT,
    `name`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `create_by`   int                                                          NOT NULL,
    `org_id`      int                                                          NOT NULL,
    `product`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
    `comment`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_date` datetime                                                      DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime                                                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `status`      int                                                          NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `dataset_items`
(
    `id`          int      NOT NULL AUTO_INCREMENT,
    `dataset_id`  int      NOT NULL,
    `item_id`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_date` datetime NOT NULL                                            DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL                                            DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      int      NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `data_source`
(
    `id`          int                                                          NOT NULL AUTO_INCREMENT,
    `source_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `source_type` int                                                          NOT NULL COMMENT '1  es\\n2 api\\n3 mysql\\n4 postgres\\n5 mongodb',
    `comment`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `url`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `username`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `passwd`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `create_date` datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `call_params` json                                                                  DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `dimension_definition`
(
    `dimension_id`         int                                                          NOT NULL AUTO_INCREMENT,
    `dimension_key`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `name`                 varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `create_date`          datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`          datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`               int                                                                   DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    `extend_from`          int                                                                   DEFAULT NULL COMMENT '从哪个维度继承过来的， 快速基于某个维度创建新的维度的时候，会保留这个字段',
    `source`               varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '''数据维度的数据源类型''\n\n  /**\n   * 专业版\n   */\n  Pro = ''Pro'',\n  /**\n   * 信用用大数据\n   */\n  Credit = ''Credit'',\n  /**\n   * 企业库\n   */\n  EnterpriseLib = ''EnterpriseLib'',\n  /**\n   * 企业详情\n   */\n  CompanyDetail = ''CompanyDetail'',\n\n  /**\n   * Rover\n   */\n  Rover = ''Rover'',\n\n  /**\n   * 标讯\n   */\n  Tender = ''Tender'',\n  /**\n   * 司法案件\n   */\n  Case = ''Case'',\n\n  /**\n   * 负面新闻\n   */\n  NegativeNews = ''NegativeNews'',\n\n  /**\n   * 裁判文书\n   */\n  Judgement = ''Judgement'',\n\n  /**\n   * 税务公告\n   */\n  TaxAnnouncement = ''TaxAnnouncement'',\n\n  /**\n   * 股权出质\n   */\n  Pledge = ''Pledge'',\n  /**\n   * 风险ES\n   */\n  RiskChange = ''RiskChange'',\n  /**\n   * 特殊黑名单 仅用来详情搜索\n   */\n  SpecialBlacklist = ''SpecialBlacklist'',\n  OuterBlacklist = ''OuterBlacklist'',\n\n  /**\n   * 行政处罚\n   */\n  SupervisePunish = ''SupervisePunish'',',
    `indicator_type`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'generalItems' COMMENT ' /**\n   * 指标类型：关键项\n   */\n  keyItems = ''keyItems'',\n  /**\n   * 指标类型：一般项\n   */\n  generalItems = ''generalItems'',',
    `create_by`            int                                                          NOT NULL,
    `update_by`            int                                                                   DEFAULT NULL,
    `modified_date`        datetime                                                              DEFAULT NULL,
    `description`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `deprecated_date`      datetime                                                              DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
    `deprecate_start_date` datetime                                                              DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
    `detail_source`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '维度详情的数据来源',
    `source_path`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '维度数据接口地址',
    `detail_source_path`   varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '数据详情接口地址',
    `type_code`            varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '维度对应专业版维度code',
    PRIMARY KEY (`dimension_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4628
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `dimension_fields`
(
    `field_id`             int                                                          NOT NULL AUTO_INCREMENT,
    `input_type`           int                                                                   DEFAULT '1' COMMENT '0 文本框 1 下拉框单选 2 下拉多选 3 单选框 4 复选框',
    `comment`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `field_key`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `data_type`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字段类型，  Integer,Float,String,Date',
    `is_array`             int                                                          NOT NULL DEFAULT '0' COMMENT '0 false, 1 true',
    `dimension_id`         int                                                          NOT NULL,
    `deprecated_date`      datetime                                                              DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
    `deprecate_start_date` datetime                                                              DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
    `options`              json                                                                  DEFAULT NULL COMMENT '如果是下拉框，下拉框中的选项',
    `name`                 varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `create_date`          datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`          datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`               int                                                          NOT NULL DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    `field_order`          int                                                                   DEFAULT '0',
    `modified_date`        datetime                                                              DEFAULT NULL,
    `default_value`        json                                                                  DEFAULT NULL COMMENT '维度属性默认值',
    `default_compare_type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '默认比较类型',
    PRIMARY KEY (`field_id`),
    KEY `index1` (`dimension_id`, `field_key`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 9675
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `dimension_hit_strategy`
(
    `strategy_id`          int                                                          NOT NULL AUTO_INCREMENT,
    `dimension_id`         int                                                          NOT NULL,
    `strategy_name`        varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `status`               int                                                          NOT NULL DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    `deprecated_date`      datetime                                                              DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
    `deprecate_start_date` datetime                                                              DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
    `comment`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `create_date`          datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`          datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `modified_date`        datetime                                                              DEFAULT NULL,
    `hit_strategy`         json                                                                  DEFAULT NULL,
    `create_by`            int                                                          NOT NULL,
    `update_by`            int                                                                   DEFAULT NULL,
    `template`             varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `extend_from`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `category`             int                                                                   DEFAULT '1' COMMENT '1 系统级别\n2 用户级别',
    `org_id`               int                                                                   DEFAULT '0' COMMENT '模型发布时候对应的组织',
    `published_date`       datetime                                                              DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
    `publish_by`           int                                                                   DEFAULT NULL,
    `strategy_role`        int                                                          NOT NULL DEFAULT '1' COMMENT '维度策略角色 1 普通策略维度，排查正常计数和结果展示； 2 仅过滤策略维度，只参与指标命中的判定，不参与排查技术和结果展示;',
    PRIMARY KEY (`strategy_id`),
    KEY `dimension_id` (`dimension_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 221880
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `dimension_hit_strategy_fields_relation`
(
    `id`                   int      NOT NULL AUTO_INCREMENT,
    `strategy_id`          int      NOT NULL,
    `dimension_id`         int      NOT NULL,
    `dimension_field_id`   int      NOT NULL,
    `status`               int      NOT NULL                                             DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    `deprecated_date`      datetime                                                      DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
    `deprecate_start_date` datetime                                                      DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
    `field_value`          json     NOT NULL COMMENT '命中的value',
    `search_type`          int                                                           DEFAULT '0' COMMENT '0 常规查询\\n1 xx 关联关系查询\\n2 yy 关联关系查询',
    `compare_type`         varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `comment`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_date`          datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`          datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `modified_date`        datetime                                                      DEFAULT NULL,
    `create_by`            int      NOT NULL,
    `update_by`            int                                                           DEFAULT NULL,
    `category`             int                                                           DEFAULT '1' COMMENT '1 系统级别\\n2 用户级别',
    `extend_from`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `org_id`               int                                                           DEFAULT '0' COMMENT '模型发布时候对应的组织',
    `published_date`       datetime                                                      DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
    `publish_by`           int                                                           DEFAULT NULL,
    `dimension_field_key`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `dimension_field_name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `access_scope`         int      NOT NULL                                             DEFAULT '0' COMMENT '数据状态：0-完整权限, 1-不可见不可修改',
    `options`              json                                                          DEFAULT NULL COMMENT '这里可以配置选项、文本框的约束等',
    PRIMARY KEY (`id`),
    KEY `index1` (`dimension_id`, `strategy_id`, `dimension_field_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 506568
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `distributed_system_resource`
(
    `id`                int                                                          NOT NULL AUTO_INCREMENT,
    `resource_type`     int                                                          NOT NULL COMMENT '1 模型\n2 指标\n3 维度命中规则',
    `resource_id`       int                                                          NOT NULL,
    `distributed_by`    int                                                          NOT NULL,
    `is_org_default`    int                                                                   DEFAULT '0' COMMENT '是否是组织的默认模型\\n1 是\\n0 否 默认值',
    `create_date`       datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`       datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `org_id`            int                                                          NOT NULL COMMENT '资源被发布到的组织',
    `product_code`      varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
    `distribute_status` int                                                          NOT NULL DEFAULT '1' COMMENT 'Disable, // 禁用\\n  Enable, // 启用\\n  Trial = 2, // 试用\\n  Deprecated = 3, // 用户自己废弃',
    `expire_date`       datetime                                                              DEFAULT NULL,
    `branch_code`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '模型或者指标的 branchCode ，暂时只有模型有branchCode',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 29178
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `due_diligence`
(
    `id`                int                                                          NOT NULL AUTO_INCREMENT,
    `name`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `company_id`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `score`             int                                                          NOT NULL DEFAULT '0',
    `result`            tinyint                                                      NOT NULL DEFAULT '0' COMMENT '0 通过\n1 风险较高\n2 慎重考虑',
    `operator`          int                                                          NOT NULL,
    `update_date`       datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`       datetime                                                              DEFAULT CURRENT_TIMESTAMP,
    `org_id`            int                                                          NOT NULL,
    `details`           json                                                                  DEFAULT NULL,
    `snapshot_date`     datetime                                                              DEFAULT NULL,
    `snapshot_id`       varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT '',
    `snapshot_details`  json                                                                  DEFAULT NULL,
    `should_update`     tinyint                                                      NOT NULL DEFAULT '0',
    `credit_rate`       int                                                                   DEFAULT NULL,
    `org_model_id`      int                                                          NOT NULL COMMENT '排查使用的排查模型id',
    `product`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
    `is_first_to_org`   int                                                                   DEFAULT '0',
    `is_first_to_model` int                                                                   DEFAULT '0',
    `paid`              int                                                                   DEFAULT '0',
    `model_branch_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `type`              int                                                          NOT NULL DEFAULT '0' COMMENT '类别：0-风险洞察尽调，1-风险洞察监控',
    PRIMARY KEY (`id`),
    KEY `org_company_uniq` (`org_id`, `company_id`, `snapshot_id`) USING BTREE,
    KEY `idx_org_comp_ct` (`org_id`, `operator`, `create_date`, `company_id`) USING BTREE,
    KEY `snapshot_id` (`snapshot_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 50638761
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `due_diligence_excludes`
(
    `id`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `org_id`        int                                                          NOT NULL,
    `company_id`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `dimension_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `record_id`     varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '指定维度记录的es id',
    `update_date`   datetime                                                     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`   datetime                                                     DEFAULT CURRENT_TIMESTAMP,
    `operator`      int                                                          NOT NULL,
    `dimension_id`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `org_company_dimension` (`org_id`, `company_id`, `dimension_key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `due_diligence_remark`
(
    `id`           int NOT NULL AUTO_INCREMENT,
    `diligence_id` int NOT NULL,
    `update_date`  datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`  datetime DEFAULT CURRENT_TIMESTAMP,
    `operator`     int NOT NULL,
    `details`      json     DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `group`
(
    `group_id`        int                                                          NOT NULL AUTO_INCREMENT,
    `group_name`      varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `parent_group_id` int                                                                   DEFAULT NULL,
    `is_virtual`      tinyint                                                               DEFAULT NULL COMMENT '是否是虚拟分组',
    `details_json`    json                                                                  DEFAULT NULL,
    `product_code`    varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属的产品',
    `risk_level`      int                                                          NOT NULL,
    `create_date`     datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`     datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `comment`         varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `create_by`       int                                                          NOT NULL,
    `update_by`       int                                                                   DEFAULT NULL,
    `status`          int                                                                   DEFAULT '2' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    `extend_from`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `category`        int                                                                   DEFAULT '1' COMMENT '1 系统级别\n2 用户级别',
    `model_id`        int                                                          NOT NULL,
    `order`           int                                                                   DEFAULT '0',
    `org_id`          int                                                                   DEFAULT '0' COMMENT '模型发布时候对应的组织',
    `published_date`  datetime                                                              DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
    `publish_by`      int                                                                   DEFAULT NULL,
    PRIMARY KEY (`group_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 50013686
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `group_label_relation`
(
    `id`          int      NOT NULL AUTO_INCREMENT,
    `group_id`    int      NOT NULL,
    `label_id`    int      NOT NULL,
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`, `group_id`, `label_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `group_metric_relation`
(
    `id`          int      NOT NULL AUTO_INCREMENT,
    `metrics_id`  int      NOT NULL,
    `group_id`    int      NOT NULL,
    `order`       int               DEFAULT NULL,
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`      int               DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    PRIMARY KEY (`id`, `group_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 158147
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `label`
(
    `id`            int                                                          NOT NULL AUTO_INCREMENT,
    `name`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `product_code`  int                                                          NOT NULL COMMENT '归属的产品',
    `create_date`   datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`   datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `business_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT 'Common' COMMENT '0 通用的\\n1 维度标签\\n2 指标标签',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 50000000
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `message`
(
    `id`          int                                                           NOT NULL AUTO_INCREMENT,
    `title`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息标题',
    `content`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
    `org_id`      int                                                           NOT NULL COMMENT '组织id',
    `user_id`     int                                                           NOT NULL COMMENT '用户id',
    `product`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '归属产品',
    `msg_type`    tinyint                                                       NOT NULL COMMENT '消息类型：1-任务提醒；2-下载提醒；',
    `object_id`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '对象id',
    `status`      tinyint                                                       NOT NULL DEFAULT '1' COMMENT '消息状态：1-未读；2-已读；-1-删除；',
    `create_date` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `url`         varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '消息链接',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 104
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `metric_dimension_relation`
(
    `id`                    int      NOT NULL AUTO_INCREMENT,
    `metrics_id`            int      NOT NULL,
    `dimension_strategy_id` int      NOT NULL,
    `priority`              int      NOT NULL                                             DEFAULT '1' COMMENT '优先级',
    `order`                 int      NOT NULL                                             DEFAULT '1' COMMENT '排序',
    `is_pre_condition`      int      NOT NULL                                             DEFAULT '0' COMMENT '是否是前置条件\n0 否\n1 是',
    `create_date`           datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`           datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `template`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 218981
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `metric_label_relation`
(
    `id`          int      NOT NULL AUTO_INCREMENT,
    `label_id`    int      NOT NULL,
    `metrics_id`  int      NOT NULL,
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `metrics`
(
    `id`                   int                                                          NOT NULL AUTO_INCREMENT,
    `name`                 varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `create_date`          datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`          datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `risk_level`           tinyint                                                               DEFAULT NULL,
    `is_veto`              tinyint                                                      NOT NULL DEFAULT '0' COMMENT '是否具有一票否决权',
    `product_code`         varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属的产品',
    `metric_type`          int                                                          NOT NULL DEFAULT '0' COMMENT '0 和 dimension 一对一\\n1 对应多个 dimension\\n2 compound  复合型 , 可能有若干个 dimension，metric 组成，暂时不开放',
    `score`                int                                                                   DEFAULT NULL,
    `status`               int                                                                   DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    `comment`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `deprecated_date`      datetime                                                              DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
    `deprecate_start_date` datetime                                                              DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
    `hit_strategy`         json                                                                  DEFAULT NULL,
    `modified_date`        datetime                                                              DEFAULT NULL,
    `details_json`         json                                                                  DEFAULT NULL,
    `create_by`            int                                                          NOT NULL,
    `update_by`            int                                                                   DEFAULT NULL,
    `extend_from`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `category`             int                                                                   DEFAULT '1' COMMENT '1 系统级别\n2 用户级别',
    `org_id`               int                                                                   DEFAULT '0' COMMENT '模型发布时候对应的组织',
    `published_date`       datetime                                                              DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
    `publish_by`           int                                                                   DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 139418
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `monitor_company`
(
    `id`                       int                                                          NOT NULL AUTO_INCREMENT,
    `company_id`               varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `company_name`             varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `update_date`              datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`              datetime                                                              DEFAULT CURRENT_TIMESTAMP,
    `org_id`                   int                                                          NOT NULL,
    `dep_id`                   int                                                          NOT NULL DEFAULT '-1' COMMENT '部门id',
    `create_by`                int                                                          NOT NULL,
    `batch_id`                 int                                                                   DEFAULT '-1',
    `status`                   int                                                                   DEFAULT '2' COMMENT '0:等待， 1: 处理中， 2:  处理完成 3：执行错误， 4：队列中排队',
    `product_code`             varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
    `monitor_group_id`         int                                                          NOT NULL,
    `related_dynamic_hash_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '监控企业的关联方发生变化时，记录关联方变化的动态的hashKey',
    `risk_level`               int                                                                   DEFAULT NULL COMMENT '最新一次执行监控批次是，监控公司动态中最高的等级',
    `primary_object`           int                                                          NOT NULL DEFAULT '1' COMMENT '是否是主要的监控对象， 如果是被作为关联方加进来的， 这个字段赋值 0 ',
    `related_party_count`      int                                                                   DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 146904
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='持续排查企业表';

CREATE TABLE `monitor_company_realted_party`
(
    `id`                 int                                                           NOT NULL AUTO_INCREMENT,
    `company_id_primary` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL,
    `company_id_related` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL,
    `monitor_group_id`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL,
    `update_date`        datetime                                                               DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`        datetime                                                               DEFAULT CURRENT_TIMESTAMP,
    `related_types`      varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `status`             int                                                           NOT NULL DEFAULT '1',
    `org_id`             int                                                           NOT NULL,
    `product`            varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 42710
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `monitor_dynamic_remark`
(
    `id`          int                                                          NOT NULL AUTO_INCREMENT,
    `org_id`      int                                                          NOT NULL,
    `dynamic_id`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'monitor_metrics_dynamic.uuid',
    `grade`       tinyint                                                      NOT NULL DEFAULT '0' COMMENT '跟进等级 0一般, 1-重要, 2 非常重要',
    `way`         tinyint                                                      NOT NULL DEFAULT '1' COMMENT '核实方式：1-无需核实, 2-电话/短信核实, 3-实地核实, 4-网络核实, 5-其他方式',
    `comment`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT '' COMMENT '处理结果',
    `attachments` json                                                                  DEFAULT NULL COMMENT '附件信息',
    `update_by`   int                                                          NOT NULL,
    `create_date` datetime                                                              DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `org_dynamic_index` (`org_id`, `dynamic_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 114
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='合作监控企业动态跟进';

CREATE TABLE `monitor_group`
(
    `id`               int                                                          NOT NULL AUTO_INCREMENT,
    `name`             varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `order`            int                                                          NOT NULL DEFAULT '0',
    `org_id`           int                                                          NOT NULL,
    `owner_id`         int                                                          NOT NULL,
    `update_date`      datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`      datetime                                                              DEFAULT CURRENT_TIMESTAMP,
    `changes_count`    int                                                                   DEFAULT '0',
    `monitor_status`   int                                                          NOT NULL DEFAULT '0' COMMENT '0 待开启\n1 已开启\n2 已关闭',
    `comment`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `product_code`     varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
    `monitor_model_id` int                                                                   DEFAULT NULL COMMENT '监控绑定的模型的ID，原则上不能为空',
    `company_count`    int                                                                   DEFAULT '0',
    `status`           int                                                          NOT NULL DEFAULT '1' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    `push_enable`      tinyint                                                      NOT NULL DEFAULT '0' COMMENT '是否开启推送：0-不开启，1-开启',
    `update_by`        int                                                                   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_group` (`monitor_model_id`, `org_id`, `product_code`) USING BTREE,
    KEY `idx_status` (`status`, `org_id`, `product_code`) USING BTREE,
    KEY `idx_updateBy` (`update_by`, `org_id`, `product_code`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3493
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `monitor_metrics_dynamic`
(
    `id`                       int                                                          NOT NULL AUTO_INCREMENT,
    `metrics_id`               int                                                          NOT NULL,
    `company_id`               varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `metrics_name`             varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `risk_level`               int                                                          NOT NULL,
    `risk_score`               int                                                          NOT NULL,
    `monitor_group_id`         int                                                          NOT NULL,
    `status`                   int                                                          NOT NULL DEFAULT '0' COMMENT '\n-1 第一次生成的数据，无需处理\n0  待处理\n1  已处理 ',
    `metrics_content`          json                                                                  DEFAULT NULL,
    `update_date`              datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date`              datetime                                                              DEFAULT CURRENT_TIMESTAMP,
    `risk_model_id`            int                                                          NOT NULL,
    `risk_model_branch_code`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `diligence_id`             int                                                          NOT NULL,
    `batch_id`                 int                                                          NOT NULL,
    `company_metrics_hash_key` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `org_id`                   int                                                          NOT NULL,
    `product_code`             varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
    `diligence_result`         int                                                          NOT NULL,
    `diligence_score`          int                                                          NOT NULL,
    `metric_type`              varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `pre_batch_id`             int                                                          NOT NULL DEFAULT '0' COMMENT '如果是两次尽调比对产生的动态，记录上次尽调batch_id',
    `unique_hashkey`           varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `company_name`             varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 723059
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `push_content`
(
    `id`               int      NOT NULL AUTO_INCREMENT COMMENT 'Primary Key',
    `org_id`           int      NOT NULL COMMENT '组织ID',
    `push_rule_id`     int      NOT NULL COMMENT '关联的推送规则ID',
    `pushed_info_json` json              DEFAULT NULL COMMENT '已经推送的信息',
    `create_date`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status`           int      NOT NULL DEFAULT '0' COMMENT '数据状态：0-未推送, 1-已推送',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 741
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='推送内容表';

CREATE TABLE `push_message_status`
(
    `id`              int         NOT NULL AUTO_INCREMENT COMMENT 'Primary Key',
    `org_id`          int         NOT NULL COMMENT '组织ID',
    `push_content_id` int         NOT NULL COMMENT '关联的推送内容Id',
    `method`          tinyint(1)  NOT NULL DEFAULT '0' COMMENT '消息类型：1-短信，2-邮件',
    `recipient`       varchar(45) NOT NULL COMMENT '联系方式信息（电话号码，或者邮箱）',
    `create_date`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `attempts`        int         NOT NULL DEFAULT '0' COMMENT '重试次数，最多3次',
    `status`          int         NOT NULL DEFAULT '0' COMMENT '发送状态：0-未发送, 1-发送',
    `error_message`   text COMMENT '错误信息',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1117
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='推送消息表';

CREATE TABLE `push_rule`
(
    `id`          int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key',
    `org_id`      int NOT NULL COMMENT '组织ID',
    `type`        int NOT NULL COMMENT '业务类型：1-风险尽调监控类消息',
    `business_id` int NOT NULL COMMENT '关联业务Id，目前支持分组Id',
    `rule_Json`   json     DEFAULT NULL COMMENT '推送的规则',
    `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`   int NOT NULL COMMENT '创建者',
    `update_by`   int      DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`),
    KEY `idx_push_rule` (`business_id`, `org_id`, `type`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 669
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='Push Rule Table';

CREATE TABLE `risk_model`
(
    `model_id`             int                                                          NOT NULL AUTO_INCREMENT,
    `model_name`           varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `product_code`         varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关联的产品',
    `comment`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `create_date`          datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date`          datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `score_strategy`       int                                                                   DEFAULT '1' COMMENT '1 默认方式 - 分数加减\n2 自定义方式 x\n3 自定义方式 y',
    `version_major`        int                                                                   DEFAULT '1',
    `version_minor`        int                                                                   DEFAULT '0',
    `version_patch`        int                                                                   DEFAULT '0',
    `status`               int                                                                   DEFAULT '0' COMMENT '0-无效, 1-启用, 2-开发中, 3-待废弃, 4-已废弃',
    `published_date`       datetime                                                              DEFAULT NULL COMMENT '如果已经正式发布，记录发布日期',
    `deprecated_date`      datetime                                                              DEFAULT NULL COMMENT '如果已经废弃，废弃的日期',
    `deprecate_start_date` datetime                                                              DEFAULT NULL COMMENT '如果已经进入到 废弃阶段(进入维护计划，不再更新) 日期',
    `modified_date`        datetime                                                              DEFAULT NULL,
    `publish_by`           int                                                                   DEFAULT NULL,
    `create_by`            int                                                          NOT NULL,
    `update_by`            int                                                                   DEFAULT NULL,
    `published_details`    json                                                                  DEFAULT NULL COMMENT '最终生成的模型json',
    `model_type`           int                                                          NOT NULL DEFAULT '1' COMMENT '1 风险模型\n2 最终受益人模型\n3 实控人模型',
    `category`             int                                                                   DEFAULT '1' COMMENT '1 系统模型\n2 用户模型',
    `org_id`               int                                                                   DEFAULT '0' COMMENT '模型发布时候对应的组织',
    `extend_from`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         DEFAULT NULL,
    `result_setting`       json                                                                  DEFAULT NULL COMMENT '尽调结果等级设置',
    `branch_code`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '模型的分组编码\\\\n同一个编码的模型，认为是同一个模型的不同版本。 ',
    `branch_tier`          int                                                          NOT NULL DEFAULT '0' COMMENT '0 根目录级别模型\\\\\\\\n1 子目录级别模型',
    `branch_count`         int                                                          NOT NULL DEFAULT '1',
    PRIMARY KEY (`model_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 3463
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `risk_model_group_relation`
(
    `id`          int      NOT NULL AUTO_INCREMENT,
    `group_id`    int      NOT NULL,
    `model_id`    int      NOT NULL,
    `order`       int               DEFAULT NULL,
    `create_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 123
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `ryg_passage_company_wgcl_incr`
(
    `id`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   NOT NULL COMMENT '主键',
    `type`              varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类型',
    `markedman`         varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处罚对象',
    `markedmankey`      varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   NOT NULL COMMENT '处罚对象key',
    `markedmanorg`      int                                                            NOT NULL DEFAULT '0',
    `disposition`       varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处分类型',
    `violation`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          NOT NULL COMMENT '违规行为',
    `punishmentmeasure` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci          NOT NULL COMMENT '处分措施',
    `processman`        varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处理人',
    `punishmentamount`  varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '处罚金额',
    `publicdate`        datetime                                                       NOT NULL COMMENT '公告日期',
    `isadd`             int                                                                     DEFAULT NULL COMMENT '有效标识:1-新增；0-更新；-1-删除',
    `updatedtime`       timestamp                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `keyno`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   NOT NULL DEFAULT '' COMMENT '企业id',
    `relatedtype`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '关联类型，多个逗号分隔（1法人，2历史法人，3主要人员，4历史主要人员，5股东，6历史股东）',
    `job`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci           DEFAULT '' COMMENT '职务（多个逗号分隔）',
    PRIMARY KEY (`id`),
    KEY `idx_keyno_isadd` (`keyno`, `isadd`, `publicdate`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='上市违规处理';

CREATE TABLE `settings_dimension`
(
    `id`          int                                                           NOT NULL,
    `org_id`      int                                                           NOT NULL,
    `key`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `content`     json                                                          NOT NULL,
    `sort`        int                                                           NOT NULL DEFAULT '0',
    `editor_id`   int                                                           NOT NULL,
    `create_date` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_date` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`org_id`, `key`),
    UNIQUE KEY `org_key` (`org_id`, `key`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `user`
(
    `user_id` int                                                          NOT NULL AUTO_INCREMENT,
    `name`    varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `phone`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `email`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`user_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `user_socket`
(
    `id`          int                                                          NOT NULL AUTO_INCREMENT,
    `user_id`     int                                                          NOT NULL,
    `org_id`      int                                                          NOT NULL,
    `product`     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '归属产品',
    `session_id`  varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `socket_id`   varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    `update_date` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `create_date` datetime DEFAULT CURRENT_TIMESTAMP,
    `status`      int      DEFAULT '1' COMMENT '1 在线\n2 离线',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 174494
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

