{"index_patterns": ["kys_metric_dynamics_*"], "settings": {"number_of_shards": 1, "number_of_replicas": 0, "max_result_window": 50000}, "mappings": {"_source": {"enabled": true}, "properties": {"id": {"type": "keyword"}, "metricsId": {"type": "keyword"}, "metricsName": {"type": "keyword"}, "metricsFeatureKeyL1": {"type": "keyword"}, "metricsFeatureKeyL2": {"type": "keyword"}, "riskLevel": {"type": "keyword"}, "riskScore": {"type": "integer"}, "orgId": {"type": "keyword"}, "uniqueHashkey": {"type": "keyword"}, "batchId": {"type": "keyword"}, "preBatchId": {"type": "keyword"}, "companyId": {"type": "keyword"}, "companyName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "relatedCompany": {"type": "nested", "properties": {"companyIdPrimary": {"type": "keyword"}, "relatedType": {"type": "keyword"}}}, "diligenceId": {"type": "keyword"}, "diligenceScore": {"type": "integer"}, "diligenceResult": {"type": "keyword"}, "riskModelId": {"type": "keyword"}, "riskBranchCode": {"type": "keyword"}, "monitorGroupId": {"type": "keyword"}, "metricType": {"type": "keyword"}, "companyMetricsHashkey": {"type": "keyword"}, "status": {"type": "keyword"}, "metricsContent": {"type": "text", "index": false}, "relatedStrategyId": {"type": "keyword"}, "product": {"type": "keyword"}, "createDate": {"type": "date"}, "updateDate": {"type": "date"}}}}