const fs = require('fs');
const path = require('path');

/**
 * 修复客户端构建后的路径别名
 * 将 @domain、@commons、@modules、@client 等路径别名转换为相对路径
 */
function fixClientAliases() {
  const distClientDir = path.join(process.cwd(), 'dist_client');

  if (!fs.existsSync(distClientDir)) {
    console.log('dist_client directory not found');
    return;
  }

  // 路径别名映射
  const aliasMap = {
    '@domain/': 'domain/',
    '@commons/': 'commons/',
    '@modules/': 'modules/',
    '@client/': 'client/',
    '@libs/': 'client/model/',
  };

  // 递归处理所有JavaScript文件
  function processDirectory(dir) {
    const files = fs.readdirSync(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        processDirectory(filePath);
      } else if (file.endsWith('.js')) {
        processJavaScriptFile(filePath);
      }
    }
  }

  // 处理单个JavaScript文件
  function processJavaScriptFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 获取当前文件相对于dist_client的深度
    const relativePath = path.relative(distClientDir, filePath);
    const depth = relativePath.split(path.sep).length - 1;
    const backPath = '../'.repeat(depth);

    // 替换require语句中的路径别名
    for (const [alias, replacement] of Object.entries(aliasMap)) {
      const regex = new RegExp(`require\\("${alias.replace('/', '\\/')}([^"]+)"\\)`, 'g');
      const newContent = content.replace(regex, (match, subpath) => {
        modified = true;
        const newPath = backPath + replacement + subpath;
        return `require("${newPath}")`;
      });
      content = newContent;
    }

    // 如果文件被修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed aliases in: ${path.relative(process.cwd(), filePath)}`);
    }
  }

  console.log('Starting to fix client aliases...');
  processDirectory(distClientDir);
  console.log('Client aliases fix completed!');
}

// 如果直接运行此脚本
if (require.main === module) {
  fixClientAliases();
}

module.exports = fixClientAliases;
