const fs = require('fs');
const path = require('path');

/**
 * 测试客户端构建后的路径别名是否正确转换
 */
function testClientBuild() {
  const distClientDir = path.join(process.cwd(), 'dist_client');

  if (!fs.existsSync(distClientDir)) {
    console.log('❌ dist_client directory not found');
    process.exit(1);
  }

  console.log('🔍 检查客户端构建结果...');

  // 检查ModelScorePO.js文件的路径别名转换
  const modelScorePoPath = path.join(distClientDir, 'domain/model/diligence/ModelScorePO.js');
  if (fs.existsSync(modelScorePoPath)) {
    const content = fs.readFileSync(modelScorePoPath, 'utf8');

    // 检查是否还有路径别名
    const hasAliases = /@domain\/|@commons\/|@modules\/|@client\//.test(content);
    if (hasAliases) {
      console.log('❌ ModelScorePO.js 仍然包含路径别名');
      process.exit(1);
    }

    // 检查是否正确转换为相对路径
    const hasRelativePaths = /require\("\.\.\//.test(content);
    if (hasRelativePaths) {
      console.log('✅ ModelScorePO.js 路径别名已正确转换为相对路径');
    } else {
      console.log('❌ ModelScorePO.js 路径转换可能有问题');
      process.exit(1);
    }
  } else {
    console.log('❌ ModelScorePO.js 文件未找到');
    process.exit(1);
  }

  // 检查其他关键文件
  const filesToCheck = ['client/model/index.js', 'domain/model/common.js', 'domain/entities/DiligenceHistoryEntity.js'];

  let totalChecked = 0;
  let totalFixed = 0;

  filesToCheck.forEach((file) => {
    const filePath = path.join(distClientDir, file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      const hasAliases = /@domain\/|@commons\/|@modules\/|@client\//.test(content);
      totalChecked++;

      if (!hasAliases) {
        totalFixed++;
      } else {
        console.log(`❌ ${file} 仍然包含路径别名`);
      }
    }
  });

  console.log(`📊 检查结果: ${totalFixed}/${totalChecked} 文件路径别名修复成功`);

  if (totalFixed === totalChecked) {
    console.log('🎉 所有检查的文件路径别名都已正确修复！');
    console.log('✅ 构建的客户端包可以被其他项目正常引入');
  } else {
    console.log('❌ 部分文件路径别名修复失败');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testClientBuild();
}

module.exports = testClientBuild;
